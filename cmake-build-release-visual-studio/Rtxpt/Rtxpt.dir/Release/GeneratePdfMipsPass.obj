d嗙 駡�# �      .drectve        P  ,$               
 .debug$S         |%  凚        @ B.debug$T        l   訠             @ B.rdata          @   @C             @ @@.text$mn        :   �C 篊         P`.debug$S          谻 銭        @B.text$mn          pF ~G         P`.debug$S        P  腉 N     2   @B.text$mn           P              P`.debug$S          P #Q        @B.text$mn          _Q wR         P`.debug$S          砇 肶     4   @B.text$mn           薣              P`.debug$S        �   蟍         @B.text$mn        �  鉢      +    P`.debug$S        T?  Pf ぅ     >  @B.text$x            � �         P`.text$x            &� 2�         P`.text$x            <� L�         P`.text$x            V� f�         P`.text$x            p� ��         P`.text$x            娂 毤         P`.text$x            ぜ 凹         P`.text$x            杭 萍         P`.text$x            屑 芗         P`.text$x         -   婕 �         P`.text$x            '� 3�         P`.text$x            =� I�         P`.text$x            S� _�         P`.text$x            i� u�         P`.text$x            � 嫿         P`.text$x            暯 〗         P`.text$x             方         P`.text$mn        <   两          P`.debug$S        0  � K�     
   @B.text$mn        <    肟         P`.debug$S        L  	� U�     
   @B.text$mn        !   沽 诹         P`.debug$S        <  盍 *�        @B.text$mn        2   f� 樏         P`.debug$S        <   枘        @B.text$mn        "   `�              P`.debug$S        �  偱 �        @B.text$mn        "   呵              P`.debug$S        �  芮 p�        @B.text$mn        "   �              P`.debug$S        �  2� 嗡        @B.text$mn        "   n�              P`.debug$S        �  愄 �        @B.text$mn        "   嘉              P`.debug$S        �  尬 j�        @B.text$mn        "   
�              P`.debug$S        �  ,� 敢        @B.text$mn        
   X� e�         P`.debug$S        �  o� �        @B.text$mn        2   C� u�         P`.debug$S        �  � c�        @B.text$mn        ^   圩 9�         P`.debug$S        X  M� ホ        @B.text$mn        K   m�              P`.debug$S        �  杠 屴        @B.text$mn           � �         P`.debug$S        h  '� 忇        @B.text$mn        ?   肃 
�         P`.debug$S        \  � z�        @B.text$mn        |   蜮              P`.debug$S        h  n� 宙     &   @B.text$mn        �   R�          P`.debug$S        �  � №     $   @B.text$mn           	� �         P`.debug$S        �   0� �        @B.text$mn           <� O�         P`.debug$S        �   c� C�        @B.text$mn        B   � 榴         P`.debug$S           唏 唑        @B.text$mn        B   � ]�         P`.debug$S          {� 嬼        @B.text$mn        B   囚 	�         P`.debug$S        �   '� #�        @B.text$mn        H   _�              P`.debug$S        �   k�        @B.text$mn        |  凒 ��         P`.debug$S        H  	� Q�     "   @B.text$mn            �  �          P`.debug$S        �   �  �        @B.text$mn        �   � n         P`.debug$S        H  � �        @B.text$mn           � �         P`.debug$S        �   � �        @B.text$mn           �           P`.debug$S        �   
 �        @B.xdata             	             @0@.pdata             .	 :	        @0@.xdata             X	             @0@.pdata             `	 l	        @0@.xdata             �	             @0@.pdata             �	 �	        @0@.xdata             �	             @0@.pdata             �	 �	        @0@.xdata             �	             @0@.pdata             �	 

        @0@.xdata             (
             @0@.pdata             0
 <
        @0@.xdata             Z
             @0@.pdata             f
 r
        @0@.xdata             �
             @0@.pdata             �
 �
        @0@.xdata             �
             @0@.pdata             �
 �
        @0@.xdata             �
             @0@.pdata                      @0@.xdata             2             @0@.pdata             : F        @0@.xdata             d t        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata                      @0@.xdata          	   0 9        @@.xdata             M S        @@.xdata             ]             @@.xdata             ` p        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata          $   � 
        @0@.pdata             
 $
        @0@.xdata          	   B
 K
        @@.xdata          �   _
 �
        @@.xdata          J   �             @@.xdata             I             @0@.pdata             Q ]        @0@.voltbl            {                .xdata              �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata                           @0@.pdata             # /        @0@.xdata             M ]        @0@.pdata             q }        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	    "        @@.xdata             6 <        @@.xdata             F             @@.xdata             I Y        @0@.pdata             m y        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             � �        @0@.pdata             � �        @0@.xdata          	            @@.xdata             2 8        @@.xdata             B             @@.xdata             E             @0@.pdata             M Y        @0@.xdata             w             @0@.pdata              �        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata                      @0@.pdata             3 ?        @0@.voltbl            ]               .xdata             _             @0@.pdata             k w        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             �         @0@.pdata              +        @0@.xdata             I             @0@.pdata             ] i        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.xdata             ; O        @0@.pdata             m y        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             �             @0@.pdata             �         @0@.xdata             %             @0@.pdata             - 9        @0@.rdata             W o        @@@.rdata             �             @@@.rdata             � �        @@@.rdata             � �        @@@.rdata                          @@@.xdata$x             <        @@@.xdata$x           P l        @@@.data$r         /   � �        @@�.xdata$x        $   � �        @@@.data$r         $   �         @@�.xdata$x        $   ) M        @@@.data$r         $   a �        @@�.xdata$x        $   � �        @@@.rdata             �             @@@.data               �             @ @�.rdata          !   �             @@@.rdata                          @0@.rdata                          @0@.rdata                          @@@.rdata             2             @0@.rdata          (   7             @@@.rdata          
   _             @@@.rdata             l             @@@.rdata$r        $   | �        @@@.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   �         @@@.rdata$r        $   * N        @@@.rdata$r           l �        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $   �         @@@.rdata$r           , @        @@@.rdata$r           J f        @@@.rdata$r        $   � �        @@@.debug$S        4   � �        @B.debug$S        4    8        @B.debug$S        @   L �        @B.chks64         8  �              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   p  a     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\GeneratePdfMipsPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Ensure_adl  $_Weak_order  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $vfs  $math 	 $colors  $log 	 $stdext �   灝  N U  ��Q std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Dx2 L U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx2 P U  ��Q std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gx K U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gy 2 U   std::integral_constant<__int64,24>::value  U   std::ratio<1,24>::num  U   std::ratio<1,24>::den N U  �r ( std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Dx2 L U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx2 P U  �r ( std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gx K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gy  U   std::ratio<1,1>::num � :   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified  U   std::ratio<1,1>::den 2 U  
 std::integral_constant<__int64,10>::value # U  
 std::ratio<10,438291>::num ' U  �� std::ratio<10,438291>::den J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N U  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 ; :   std::atomic<unsigned __int64>::is_always_lock_free L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 O U  �X呩std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Nx2 ( U  ��枠 std::ratio<10000000,1>::num K U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Dx2 $ U   std::ratio<10000000,1>::den E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment P U  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy M U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx1 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx2 Q U  �X呩std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gx L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gy < U  ��枠 std::integral_constant<__int64,10000000>::value ) <   nvrhi::ObjectTypes::SharedHandle 1 U   std::integral_constant<__int64,5>::value - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 1 <   donut::math::vector<unsigned int,2>::DIM 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + <  �  nvrhi::ObjectTypes::VK_Sampler 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule . <  �
  nvrhi::ObjectTypes::VK_RenderPass / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo 6 :   std::_Iterator_base0::_Unwrap_when_unverified # U   std::ratio<5,2629746>::num ' U  �r ( std::ratio<5,2629746>::den - :   std::chrono::steady_clock::is_steady & U   std::ratio<1,1000000000>::num # <   kPolymorphicLightTypeShift " <   kPolymorphicLightTypeMask * U  � 蕷;std::ratio<1,1000000000>::den . <  �   kPolymorphicLightShapingEnableBit 1 <  �    kPolymorphicLightIesProfileEnableBit - ;  �   羕PolymorphicLightMinLog2Radiance - ;  �   BkPolymorphicLightMaxLog2Radiance L U  ��Q std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Nx2 C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE H U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Dx2 E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity J U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx1 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx1 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx2 7 :   std::_Iterator_base12::_Unwrap_when_unverified N U  ��Q std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx2 I U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gx I U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gy d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity 4 U  �std::integral_constant<__int64,1440>::value q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size   U   std::ratio<1,1440>::num   U  �std::ratio<1,1440>::den C :   std::atomic<std::chrono::tzdb_list *>::is_always_lock_free O U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Nx2 S U  � 蕷;std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Dx2 ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos Q U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx1 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx1 * U  � 蕷;std::ratio<1000000000,1>::num & U   std::ratio<1000000000,1>::den U U  � 蕷;std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx2 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx2 P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gx P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gy E U  
� 牳0F  std::integral_constant<__int64,3600000000000>::value 8 :   std::atomic<unsigned long>::is_always_lock_free 1 U  
� 牳0F  std::ratio<3600000000000,1>::num ) U   std::ratio<3600000000000,1>::den ( <   donut::math::vector<int,2>::DIM / <  � nvrhi::rt::cluster::kClasByteAlignment . <   nvrhi::rt::cluster::kClasMaxTriangles - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex ( �    std::chrono::local_info::unique - �   std::chrono::local_info::nonexistent + �   std::chrono::local_info::ambiguous . :    std::integral_constant<bool,0>::value : _   std::integral_constant<unsigned __int64,1>::value % _   std::ctype<char>::table_size G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 7 :   std::atomic<unsigned int>::is_always_lock_free M U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gy C _   std::_Locked_pointer<std::_Stop_callback_base>::_Lock_mask D _    std::_Locked_pointer<std::_Stop_callback_base>::_Not_locked R _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_not_needed N _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_needed I _   �黶td::_Locked_pointer<std::_Stop_callback_base>::_Ptr_value_mask . :   std::integral_constant<bool,1>::value L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gy > U  � 蕷;std::integral_constant<__int64,1000000000>::value C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gx D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gy E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment K :   std::atomic<std::_Stop_callback_base const *>::is_always_lock_free W _   std::allocator<donut::engine::ShaderMacro>::_Minimum_asan_allocation_alignment F U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Nx2 F U  �std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Dx2 * :    std::chrono::utc_clock::is_steady H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx1   U  �std::ratio<1000,1>::num   U   std::ratio<1000,1>::den H U  �std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gy C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE 4 U  �std::integral_constant<__int64,1000>::value E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity * :    std::chrono::tai_clock::is_steady d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE 6 v,  �奮�std::chrono::tai_clock::_Tai_epoch_adjust f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size L U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Nx2 P U  � 蕷;std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Dx2 j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gy * :    std::chrono::gps_clock::is_steady 6 v,  �w�*韘td::chrono::gps_clock::_Gps_epoch_adjust   �   std::_Iosb<int>::skipws ! �   std::_Iosb<int>::unitbuf ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos # �   std::_Iosb<int>::uppercase " �   std::_Iosb<int>::showbase # �   std::_Iosb<int>::showpoint ! �    std::_Iosb<int>::showpos  �  @ std::_Iosb<int>::left  �  � std::_Iosb<int>::right 5 :    std::filesystem::_File_time_clock::is_steady " �   std::_Iosb<int>::internal  �   std::_Iosb<int>::dec  �   std::_Iosb<int>::oct  �   std::_Iosb<int>::hex $ �   std::_Iosb<int>::scientific  �    std::_Iosb<int>::fixed " �   0std::_Iosb<int>::hexfloat # �   @std::_Iosb<int>::boolalpha " �  � �std::_Iosb<int>::_Stdio % �  �std::_Iosb<int>::adjustfield # �   std::_Iosb<int>::basefield $ �   0std::_Iosb<int>::floatfield ! �    std::_Iosb<int>::goodbit   �   std::_Iosb<int>::eofbit ! �   std::_Iosb<int>::failbit   �   std::_Iosb<int>::badbit  �   std::_Iosb<int>::in  �   std::_Iosb<int>::out  �   std::_Iosb<int>::ate  �   std::_Iosb<int>::app  �   std::_Iosb<int>::trunc # �  @ std::_Iosb<int>::_Nocreate $ �  � std::_Iosb<int>::_Noreplace   �    std::_Iosb<int>::binary  �    std::_Iosb<int>::beg  �   std::_Iosb<int>::cur  �   std::_Iosb<int>::end , �  @ std::_Iosb<int>::_Default_open_prot  U  < std::ratio<60,1>::num  U   std::ratio<60,1>::den M U  �r ( std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Nx2 I U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx2 O U  �r ( std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx2 J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gx J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gy N U  �X呩std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx2 P U  �X呩std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gy _ _   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment K U  ��Q std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Dx2 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx2 M U  ��Q std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gx H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gy   U   std::ratio<1,1000>::num   U  �std::ratio<1,1000>::den N :   std::_Trivial_cat<char,char,char &,char &>::_Same_size_and_compatible K :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_constructible H :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_assignable # U   std::ratio<1,1000000>::num ' U  �@B std::ratio<1,1000000>::den � :   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable + :    std::_Aligned_storage<72,8>::_Fits * :    std::_Aligned<72,8,char,0>::_Fits + :    std::_Aligned<72,8,short,0>::_Fits ) :   std::_Aligned<72,8,int,0>::_Fits D _   ��std::basic_string_view<char,std::char_traits<char> >::npos � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable : _    std::integral_constant<unsigned __int64,0>::value 9 �   std::chrono::_Time_parse_fields::_Era_begin_wday Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment S _   std::allocator<std::chrono::time_zone>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable A _   std::allocator<bool>::_Minimum_asan_allocation_alignment / U   std::ratio<1,1000000000000000000>::num 7 U  
�  dС多
std::ratio<1,1000000000000000000>::den I _   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable U :   std::_String_view_iterator<std::char_traits<char> >::_Unwrap_when_unverified \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment X _   std::allocator<std::chrono::time_zone_link>::_Minimum_asan_allocation_alignment ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Same_size_and_compatible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_constructible T U  �r ( std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Nx2 � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_assignable P U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Dx2 U _   std::allocator<std::chrono::leap_second>::_Minimum_asan_allocation_alignment R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx1 V U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx1  ;  ��I@donut::math::PI_f " s  
�-DT�!	@donut::math::PI_d R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx2 ! ;  ��7�5donut::math::epsilon " ;  �  �donut::math::infinity V U  �r ( std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx2  ;  �  �donut::math::NaN Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gy F U  
� 泌�  std::integral_constant<__int64,26297460000000>::value J U  std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Dx2 P U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx1 L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos   U   std::ratio<1,3600>::num   U  std::ratio<1,3600>::den * U   std::ratio<1,26297460000000>::num 2 U  
� 泌�  std::ratio<1,26297460000000>::den L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx2 L U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gx K U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gy U U  �X呩std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Nx2 Q U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Dx2 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx1 W U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx1 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx2 W U  �X呩std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx2 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gx R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gy G U  
� <$A std::integral_constant<__int64,315569520000000>::value F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 H U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx1 + U   std::ratio<1,315569520000000>::num H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 3 U  
� <$A std::ratio<1,315569520000000>::den H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gy 4 U  std::integral_constant<__int64,3600>::value R U  ��Q std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Dx2 P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx1 T U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gy D U  
� 纈*�   std::integral_constant<__int64,864000000000>::value  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment ( U   std::ratio<1,864000000000>::num 0 U  
� 纈*�   std::ratio<1,864000000000>::den ' U  �r ( std::ratio<2629746,1>::num # U   std::ratio<2629746,1>::den : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent b <    std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::fractional_width ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; _  �std::_Floating_type_traits<double>::_Exponent_mask J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ' �  ��std::chrono::year::_Year_min % �  �std::chrono::year::_Year_max 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices # �        nvrhi::AllSubresources $ �   ��std::strong_ordering::less $ �    std::strong_ordering::equal & �   std::strong_ordering::greater ) <   donut::math::vector<bool,2>::DIM ) <   donut::math::vector<bool,3>::DIM ) <   donut::math::vector<bool,4>::DIM ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong           nvrhi::EntireBuffer T U  �r ( std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Dx2 V U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx1 # U   std::ratio<1,2629746>::num ' U  �r ( std::ratio<1,2629746>::den R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gy 2 U   std::integral_constant<__int64,12>::value A _   std::allocator<char>::_Minimum_asan_allocation_alignment R U  ��Q std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Dx2 T U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx1 ! U   std::ratio<1,86400>::num % U  ��Q std::ratio<1,86400>::den : _   std::integral_constant<unsigned __int64,3>::value P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gy : U  ��: std::integral_constant<__int64,146097>::value ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity 3 U  �std::integral_constant<__int64,400>::value X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val - �    std::integral_constant<int,0>::value a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size T U  �X呩std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Dx2 � :   std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro const &,donut::engine::ShaderMacro &>::_Same_size_and_compatible � :    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro const &,donut::engine::ShaderMacro &>::_Bitcopy_constructible � :    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro const &,donut::engine::ShaderMacro &>::_Bitcopy_assignable V U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx1 $ U   std::ratio<1,31556952>::num : _   std::integral_constant<unsigned __int64,2>::value ( U  �X呩std::ratio<1,31556952>::den R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx2 3 �  \ std::filesystem::path::preferred_separator V U  �X呩std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gx Q U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gy T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos  H    std::denorm_absent  H   std::denorm_present  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity & :    std::_Num_base::has_quiet_NaN * :    std::_Num_base::has_signaling_NaN # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix ) H   std::_Num_float_base::has_denorm + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed - :   std::_Num_float_base::is_specialized 4 _  @ _Mtx_internal_imp_t::_Critical_section_size * K   std::_Num_float_base::round_style 5 _   _Mtx_internal_imp_t::_Critical_section_align $ �   std::_Num_float_base::radix + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits * �   std::numeric_limits<bool>::digits + :    std::_Aligned<64,8,short,0>::_Fits ) :   std::_Aligned<64,8,int,0>::_Fits � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Same_size_and_compatible - :   std::numeric_limits<char>::is_signed - :    std::numeric_limits<char>::is_modulo � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_constructible * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_assignable 4 :   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 :   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10  抏   _Mtx_try  抏   _Mtx_recursive 1 :   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10  蔱   std::_INVALID_ARGUMENT  蔱   std::_NO_SUCH_PROCESS & 蔱   std::_OPERATION_NOT_PERMITTED , 蔱   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蔱   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN 1 :   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits % �    _Atomic_memory_order_relaxed 0 �  	 std::numeric_limits<char32_t>::digits10 % �   _Atomic_memory_order_consume % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 � :   std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_assignable R :    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , :   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 * <   donut::math::vector<float,3>::DIM 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 � :   std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_assignable 6 :   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 Q U  �r ( std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Dx2 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 S U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx2 S U  �r ( std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx2 N U  6 std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gx N U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gy + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 U  @std::integral_constant<__int64,1600>::value 4 �   �踫td::numeric_limits<float>::min_exponent10 D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 7 U  �;緎td::integral_constant<__int64,48699>::value 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent $ U  @std::ratio<1600,48699>::num H :    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified 9 �  4std::numeric_limits<long double>::max_exponent10 & U  �;緎td::ratio<1600,48699>::den 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 R U  �X呩std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Dx2 T U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx2 T U  �X呩std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gx O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gy @ �   std::_General_precision_tables_2<float>::_Max_special_P $ U  �std::ratio<400,146097>::num ( U  ��: std::ratio<400,146097>::den 8 �  ' std::_General_precision_tables_2<float>::_Max_P ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos   �   �   A �   std::_General_precision_tables_2<double>::_Max_special_P 9 �  5std::_General_precision_tables_2<double>::_Max_P * <   donut::math::vector<float,4>::DIM * 錏        donut::math::lumaCoefficients   �   &  $ �   std::_Locbase<int>::collate " �   std::_Locbase<int>::ctype % �   std::_Locbase<int>::monetary $ �   std::_Locbase<int>::numeric ! �   std::_Locbase<int>::time % �    std::_Locbase<int>::messages   �  ? std::_Locbase<int>::all ! �    std::_Locbase<int>::none   �   7 � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment * <   donut::math::vector<float,2>::DIM   U  std::ratio<3600,1>::num   U   std::ratio<3600,1>::den a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment :    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi :   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment ':   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed  U   std::ratio<24,1>::num J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed  U   std::ratio<24,1>::den E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx1 I U  std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gy 9 U  ��Q std::integral_constant<__int64,86400>::value - <  `std::_Big_integer_flt::_Maximum_bits - <    std::_Big_integer_flt::_Element_bits . <  s std::_Big_integer_flt::_Element_count 1 U   std::integral_constant<__int64,1>::value % U  ��Q std::ratio<86400,1>::num ! U   std::ratio<86400,1>::den x _   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment  U   std::ratio<7,1>::num  U   std::ratio<7,1>::den I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx1 M U  ��Q std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx2 D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gy  �1   std::_Consume_header  �1   std::_Generate_header : U  ��:	 std::integral_constant<__int64,604800>::value + �!        nvrhi::rt::c_IdentityTransform ( U  ��: std::ratio<146097,400>::num $ U  �std::ratio<146097,400>::den ) <   donut::math::frustum::numCorners T U  ��: std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx1 P U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx1 T U  ��Q std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx2 P U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gx O U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gy � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible < U  �X呩std::integral_constant<__int64,31556952>::value � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets ':    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi ( U  �X呩std::ratio<31556952,1>::num $ U   std::ratio<31556952,1>::den a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size P U  �r ( std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Dx2  U   std::ratio<12,1>::num  U   std::ratio<12,1>::den K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Dx2 N U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx2 R U  �r ( std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gx M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gy Q U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx1  U   std::ratio<1,12>::num  U   std::ratio<1,12>::den 3 U  � std::integral_constant<__int64,200>::value ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx2 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gx L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gy ; U  �r ( std::integral_constant<__int64,2629746>::value $ U  � std::ratio<200,146097>::num ( U  ��: std::ratio<200,146097>::den _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment Q U  �X呩std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Dx2 O U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx2 S U  �X呩std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx2 N U  H std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gx N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gy 2 U  2 std::integral_constant<__int64,50>::value / :   std::atomic<long>::is_always_lock_free - :    std::chrono::system_clock::is_steady : U  �� std::integral_constant<__int64,438291>::value $ U   std::ratio<1,10000000>::num ( U  ��枠 std::ratio<1,10000000>::den J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 # U  2 std::ratio<50,438291>::num L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 ' U  �� std::ratio<50,438291>::den K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment  �  _CatchableType " �  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  '0  __std_tzdb_error & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �0  _Ctypevec  酕  PolymorphicLightInfo & �  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  齚  _Thrd_result  #   rsize_t  �>  __std_fs_find_data &   $_TypeDescriptor$_extraBytes_23 - cZ  $_s__CatchableTypeArray$_extraBytes_32 # )D  __std_fs_reparse_data_buffer Z _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^  _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> `   __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �>  __std_fs_dir_handle  �/  __std_access_rights    _TypeDescriptor & NZ  $_TypeDescriptor$_extraBytes_34  G  PathTracerCameraData  渇  _Stl_critical_section 	 I  tm % �  _s__RTTICompleteObjectLocator2 ( �  PreprocessEnvironmentMapConstants & kZ  $_TypeDescriptor$_extraBytes_30 A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 焃  $_TypeDescriptor$_extraBytes_46  �  _s__CatchableType & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const>  銩  __std_fs_filetime E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & �  $_TypeDescriptor$_extraBytes_20  貴  LightingControlData  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16   SD  __std_fs_copy_file_result  �5  __std_code_page � 薧  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � 赹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > z 韃  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> i鋇  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � 躛  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 淾  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > : 榝  std::_Vector_val<std::_Simple_types<unsigned int> > � O]  std::_Non_trivial_copy<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > � 蚟  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > p 巉  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> M k^  std::_Normal_allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > � 萟  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > . 綶  std::_Ptr_base<donut::vfs::IFileSystem> �糬  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � 匽  std::_Non_trivial_move_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 盺  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > � 猑  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> k ,]  std::_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0> � a]  std::_Non_trivial_move<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 瀆  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > 廭  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> g\  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > [ 坁  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' {I  std::default_delete<wchar_t [0]> . /  std::_Conditionally_enabled_hash<int,1> A C  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit  坃  std::_Stop_callback_base  b  std::timed_mutex D �;  std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty - �?  std::reverse_iterator<wchar_t const *> 6 刕  std::allocator_traits<std::allocator<char8_t> > " 媂  std::_Char_traits<char,int>  0  std::_Fs_file  �=  std::optional<int> � 俕  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >  "   std::_Atomic_counter_t  N  std::_Num_base & $/  std::hash<std::error_condition> K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 5 $0  std::_Tzdb_deleter<__std_tzdb_time_zones_info> � z^  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > # �  std::numeric_limits<char8_t>  /,  std::_Big_uint128    std::condition_variable  �=  std::optional<__int64> E m^  std::allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > G ^^  std::allocator_traits<std::allocator<std::chrono::leap_second> > ) 怷  std::_Narrow_char_traits<char,int>  j  std::hash<float> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc> ! �4  std::__floating_decimal_64 S 蘕  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone_link> > \^  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > " 唂  std::_Align_type<double,64>  �5  std::less<void>  /  std::hash<int>  P  std::_Num_int_base  i3  std::ctype<wchar_t> 8 P�  std::initializer_list<donut::engine::ShaderMacro> " �/  std::_System_error_category / 揚  std::_Conditionally_enabled_hash<bool,1> + 琠  std::_Atomic_storage<unsigned int,4>  �5  std::_Format_arg_index  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > / y2  std::codecvt<char32_t,char8_t,_Mbstatet> k =]  std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > f �;  std::pair<std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >,bool> 碶  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> u 訵  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 餧  std::allocator_traits<std::allocator<wchar_t> > � s]  std::_Non_trivial_copy_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  `  std::bad_cast  JR  std::equal_to<void> 3 僄  std::_Ptr_base<donut::engine::ShaderFactory> � 粿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > o 踈  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1>     std::_Compare_t " u  std::numeric_limits<double> $ d  std::atomic<unsigned __int64>  �  std::__non_rtti_object , K2  std::_Codecvt_guard<char8_t,char16_t> ( @  std::_Basic_container_proxy_ptr12 2 #L  std::allocator<std::chrono::time_zone_link> = 6`  std::_Atomic_pointer<std::_Stop_callback_base const *> > 刦  std::vector<unsigned int,std::allocator<unsigned int> > T Tf  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy   �5  std::_Decode_result<char> 1    std::array<nvrhi::FramebufferAttachment,8>  q  std::_Num_float_base  抈  std::stop_token  �-  std::logic_error 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety / 頚  std::allocator<std::chrono::leap_second> ! 頬  std::char_traits<char32_t>  Q1  std::locale  �1  std::locale::_Locimp  b1  std::locale::facet   k1  std::locale::_Facet_guard  1  std::locale::id - VL  std::allocator<std::chrono::time_zone>  �  std::_Compare_ncmp s 竂  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   R  std::numeric_limits<bool>  S6  std::_Fmt_codec<char,0> # *Y  std::_WChar_traits<char16_t> P 獻  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> Z W  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> >  綻  std::stop_source R �9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > h {9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >::_Reallocation_policy M i?  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  F.  std::overflow_error " x5  std::_Basic_format_arg_type , b2  std::_Codecvt_guard<char16_t,char8_t> % L  std::_One_then_variadic_args_t W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * jb  std::_Vb_val<std::allocator<bool> > E 鞢  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j 靅  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   闿  std::char_traits<wchar_t> \ :  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > r �9  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> >::_Reallocation_policy  pa  std::recursive_mutex   �  std::pmr::memory_resource  	>  std::pair<int,int> ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> � 鑍  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  砞  std::false_type  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � �:  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! a  std::hash<std::thread::id> $ 
6  std::_Fmt_fixed_buffer_traits  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > c 蠶  std::_Alloc_construct_ptr<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  扟  std::fpos<_Mbstatet> b gQ  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone> > > , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  俛  std::adopt_lock_t o 奓  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_current_zone_info>,__std_tzdb_current_zone_info *,1>  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , n  std::numeric_limits<unsigned __int64> F L  std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >  �0  std::_Locinfo 6 B  std::_Ptr_base<std::filesystem::_Dir_enum_impl> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 4 婯  std::_Atomic_padded<std::chrono::tzdb_list *> s 奤  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > N 諼  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone> > � 萞  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > 9 DJ  std::basic_streambuf<char,std::char_traits<char> > $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16>  �  std::string_view  �  std::wstring_view % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound  Qa  std::_Mutex_base  Z5  std::money_base  縘  std::money_base::pattern  y0  std::_Timevec  胉  std::nostopstate_t  f  std::defer_lock_t   �.  std::_Init_once_completer  絔  std::endian j sC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � BC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �a  std::scoped_lock<> + �2  std::codecvt<wchar_t,char,_Mbstatet> h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> d xK  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::leap_second> > > Q 籡  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>    std::_Iterator_base12 � 颸  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  mN  std::_Pocma_values . B0  std::_Tzdb_deleter<__std_tzdb_sys_info> 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> ! /  std::hash<std::error_code> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > N 	@  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > ( 筣  std::ratio<1,1000000000000000000> @ nY  std::_Default_allocator_traits<std::allocator<char32_t> > ( d  std::_Atomic_padded<unsigned int>  �?  std::allocator<char32_t> ? !D  std::unique_ptr<char [0],std::default_delete<char [0]> > $ �  std::_Atomic_integral<long,4>  L<  std::_Flist_unchecked_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,std::_Iterator_base0> R =K  std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >  桺  std::hash<bool>     std::streamsize 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty � �;  std::_Compressed_pair<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >,std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,1> ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> m �  std::_Tidy_guard<std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > > 2 鼿  std::initializer_list<nvrhi::IBindingSet *> � �:  std::tuple<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > " b  std::lock_guard<std::mutex> N 蔝  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone> > ] �:  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > S 礥  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone_link> >  �  std::hash<long double> � 侤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � Q@  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy [ �<  std::_Flist_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > >   �  std::_Comparison_category  f  std::try_to_lock_t H 蠬  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ �5  std::_Decode_result<char32_t> # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0 R f  std::_Default_allocator_traits<std::allocator<donut::engine::ShaderMacro> >  w  std::hash<double> H f  std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> > O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> n �:  std::_Tuple_val<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > " 猘  std::_Align_type<double,72> G �;  std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >  �5  std::_Lazy_locale / Y  std::_Char_traits<char32_t,unsigned int>  >/  std::_System_error < 朿  std::_Atomic_padded<std::_Stop_callback_base const *> ( �)  std::hash<nvrhi::FramebufferInfo>  螸  std::_Fmt_buffer<char> 9 桰  std::allocator<std::filesystem::_Find_file_handle>  �.  std::error_condition % 砞  std::integral_constant<bool,0>  �  std::bad_exception 1 鷈  std::allocator<donut::engine::ShaderMacro> & 肐  std::_Zero_then_variadic_args_t / 甝  std::_General_precision_tables_2<double> ? $`  std::_Atomic_storage<std::_Stop_callback_base const *,8> � ]L  std::_Compressed_pair<std::allocator<std::chrono::time_zone>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >,1>  �  std::u32string N {J  std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>  �-  std::invalid_argument   玗  std::char_traits<char8_t> [   std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > . ]  std::_General_precision_tables_2<float> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 1 臺  std::_Atomic_integral_facade<unsigned int>  瀉  std::cv_status S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > b L:  std::unique_ptr<__std_tzdb_time_zones_info,std::_Tzdb_deleter<__std_tzdb_time_zones_info> > + rD  std::pair<enum __std_win_error,bool> S  ?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char8_t> > >  �5  std::_Fmt_codec_base<0> J ]  std::allocator_traits<std::allocator<std::chrono::time_zone_link> >  鑐  std::thread  a  std::thread::id S Y?  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �-  std::length_error  3a  std::jthread F TU  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 烮  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! s  std::numeric_limits<float>  �4  std::time_base   }4  std::time_base::dateorder ) �  std::_Atomic_integral_facade<long>  絘  std::mutex % 盤  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " VP  std::hash<unsigned __int64> f 9  std::unique_ptr<__std_tzdb_current_zone_info,std::_Tzdb_deleter<__std_tzdb_current_zone_info> > 1 癒  std::_Flist_node<std::chrono::tzdb,void *>  ]  std::ratio<60,1> k 軰  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_time_zones_info>,__std_tzdb_time_zones_info *,1> : +6  std::_String_view_iterator<std::char_traits<char> >  '  std::exception_ptr  �  std::strong_ordering  ]  std::ratio<1,1000000> % �  std::_Itraits_pointer_strategy C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > )   std::hash<enum nvrhi::BlendFactor> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t>  �.  std::once_flag  �.  std::error_code T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy ' �4  std::pair<char *,enum std::errc> * �=  std::_Optional_destruct_base<int,1> ! �4  std::__floating_decimal_32  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < �\  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  �3  std::_Iosb<int>   �3  std::_Iosb<int>::_Seekdir ! �3  std::_Iosb<int>::_Openmode   �3  std::_Iosb<int>::_Iostate ! �3  std::_Iosb<int>::_Fmtflags # �3  std::_Iosb<int>::_Dummy_enum 7 齖  std::allocator_traits<std::allocator<char32_t> >  ~[  std::nano I f  std::allocator_traits<std::allocator<donut::engine::ShaderMacro> > I 7L  std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > � 馶  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 Y  std::_Char_traits<char16_t,unsigned short> a _<  std::_Flist_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > $ �)  std::hash<nvrhi::BufferRange> V ^;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> > l -;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >::_Reallocation_policy T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  "1  std::_Locbase<int> S bX  std::forward_list<std::chrono::tzdb,std::_Crt_allocator<std::chrono::tzdb> > ! 郳  std::char_traits<char16_t>  �  std::tuple<> 5 �<  std::_Atomic_pointer<std::chrono::tzdb_list *> P 燯  std::_Uninitialized_backout_al<std::allocator<std::chrono::leap_second> >  �  std::_Container_base12 W 轡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  	/  std::io_errc  4  std::ios_base  )4  std::ios_base::_Fnarray  #4  std::ios_base::_Iosarray  �3  std::ios_base::Init  �3  std::ios_base::failure  �3  std::ios_base::event ) X  std::numeric_limits<unsigned char> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type   d  std::numeric_limits<long> " 衆  std::initializer_list<char>  �  std::_Invoker_strategy  鯟  std::nothrow_t  檁  std::_Stop_state 荺  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �  std::_Default_allocate_traits   璍  std::_Fmt_buffer<wchar_t> M 0?  std::_String_iterator<std::_String_val<std::_Simple_types<char8_t> > > N �?  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 瞈  std::allocator_traits<std::allocator<char> > ! `  std::numeric_limits<short>  u   std::_Vbase . S  std::allocator<nvrhi::rt::GeometryDesc> # 醓  std::unique_lock<std::mutex>  痋  std::ratio<2629746,1> ; �  std::basic_string_view<char,std::char_traits<char> > �璡  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! �3  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > + �5  std::_Grapheme_Break_property_values < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty O 镻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock � 鯧  std::_Compressed_pair<std::allocator<std::chrono::leap_second>,std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >,1>  �  std::bad_alloc  ^.  std::underflow_error B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> ) 礚  std::_Fmt_counting_buffer<wchar_t> c 蔏  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_leap_info [0]>,__std_tzdb_leap_info *,1> J 侷  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D oI  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>   騙  std::atomic<unsigned int>  L5  std::messages_base - 遊  std::_Crt_allocator<std::chrono::tzdb> � f  std::_Compressed_pair<std::allocator<donut::engine::ShaderMacro>,std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> >,1> g 癥  std::_Normal_allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  .  std::out_of_range # f  std::numeric_limits<__int64> i 鮅  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > " 骵  std::initializer_list<bool>  +3  std::ctype<char> R �  std::_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> > d �:  std::_Tuple_val<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > >  h  std::memory_order ! (b  std::recursive_timed_mutex  �4  std::chars_format  T4  std::nullopt_t  V4  std::nullopt_t::_Tag  賉  std::ratio<3600,1> # �  std::_Atomic_storage<long,4> , �<  std::atomic<std::chrono::tzdb_list *> # *  std::hash<nvrhi::BlendState> / 譡  std::shared_ptr<donut::vfs::IFileSystem>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  U  std::in_place_t D jL  std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >  a/  std::system_error < {X  std::_Default_allocator_traits<std::allocator<char> > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > g 楺  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> > >    std::ratio<1,1> ^ �;  std::unique_ptr<__std_tzdb_leap_info [0],std::_Tzdb_deleter<__std_tzdb_leap_info [0]> > 2 F6  std::_Measure_string_prefix_iterator_legacy ) �=  std::_Optional_construct_base<int> 3 闔  std::initializer_list<nvrhi::BindingSetItem>   D[  std::forward_iterator_tag  ..  std::runtime_error   
  std::bad_array_new_length E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > / 綾  std::_Atomic_storage<unsigned __int64,8>  �0  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z WY  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  鑕  std::allocator<bool>  �  std::u16string  ^  std::nested_exception  �  std::_Distance_unknown H jX  std::pointer_traits<std::_Flist_node<std::chrono::tzdb,void *> *> 7 K  std::basic_ostream<char,std::char_traits<char> > ( j  std::numeric_limits<unsigned int> < 7U  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> E   std::allocator_traits<std::allocator<std::chrono::time_zone> > , 2  std::codecvt<char32_t,char,_Mbstatet>  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> , �2  std::_Codecvt_guard<char8_t,char32_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' w  std::numeric_limits<long double>  /  std::errc " hX  std::pointer_traits<char *> , 凷  std::default_delete<std::_Facet_base>  v.  std::range_error  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �4  std::to_chars_result  颽  std::_UInt_is_zero  �  std::_Compare_eq  ~[  std::ratio<1,1000000000>  �?  std::allocator<char16_t> $ hI  std::default_delete<char [0]> . 糱  std::vector<bool,std::allocator<bool> > ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  |[  std::ratio<1,1000>  z[  std::ratio<1,10000000> ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  $1  std::_Crt_new_delete % �/  std::_Iostream_error_category2 * 糛  std::_String_constructor_concat_tag 4 c`  std::atomic<std::_Stop_callback_base const *>  �8  std::allocator<char>    std::nullptr_t . 鏧  std::_Char_traits<char8_t,unsigned int> ' x[  std::_Floating_to_chars_overload & H[  std::random_access_iterator_tag 4 汫  std::shared_ptr<donut::engine::ShaderFactory> ; RP  std::_Conditionally_enabled_hash<unsigned __int64,1> R 酙  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> 3 P0  std::_Tzdb_deleter<__std_tzdb_leap_info [0]> @ 闎  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> # 鄀  std::allocator<unsigned int>  �0  std::_Yarn<wchar_t> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  b  std::wstring 5 譪  std::_Atomic_integral_facade<unsigned __int64> ' V  std::numeric_limits<signed char> � 獲  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �-  std::domain_error  �  std::u32string_view  �  std::_Container_base  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  5  std::_Big_integer_flt  �  std::_Literal_zero $ cP  std::hash<nvrhi::IResource *>  �4  std::from_chars_result � *L  std::_Compressed_pair<std::allocator<std::chrono::time_zone_link>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >,1> " w4  std::_Nontrivial_dummy_type 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char> D 譭  std::_Default_allocator_traits<std::allocator<unsigned int> > 8 �6  std::chrono::duration<int,std::ratio<2629746,1> > i u;  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<3600,1> > > & 28  std::chrono::year_month_weekday  u6  std::chrono::day 9 D-  std::chrono::duration<__int64,std::ratio<1,1000> > j %7  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<86400,1> > > n :=  std::chrono::time_point<std::chrono::gps_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  7  std::chrono::weekday  �,  std::chrono::nanoseconds  �6  std::chrono::year ' 8  std::chrono::year_month_day_last  -  std::chrono::minutes  �6  std::chrono::days y N=  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �,  std::chrono::duration<__int64,std::ratio<1,1000000000> > # L7  std::chrono::weekday_indexed 9 �6  std::chrono::duration<int,std::ratio<31556952,1> >  �6  std::chrono::years , fX  std::chrono::duration_values<__int64>  v,  std::chrono::seconds " �8  std::chrono::time_zone_link 6 �6  std::chrono::duration<int,std::ratio<86400,1> > 3 -  std::chrono::duration<int,std::ratio<60,1> > 6 v,  std::chrono::duration<__int64,std::ratio<1,1> > s �,  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   q[  std::chrono::steady_clock   X  std::chrono::system_clock 6 *-  std::chrono::duration<double,std::ratio<60,1> > + X8  std::chrono::year_month_weekday_last  <  std::chrono::tzdb_list + l<  std::chrono::tzdb_list::_Unique_lock + *<  std::chrono::tzdb_list::_Shared_lock ; �-  std::chrono::duration<double,std::ratio<1,1000000> > > �-  std::chrono::duration<double,std::ratio<1,1000000000> > = \,  std::chrono::duration<__int64,std::ratio<1,10000000> > " �7  std::chrono::month_day_last  %7  std::chrono::sys_days n =  std::chrono::time_point<std::chrono::utc_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  �8  std::chrono::time_zone q L,  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >   `7  std::chrono::weekday_last ! �7  std::chrono::month_weekday 5 �,  std::chrono::duration<int,std::ratio<3600,1> >  i7  std::chrono::last_spec & �7  std::chrono::month_weekday_last  :7  std::chrono::local_days j �8  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,1> > > N SK  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > > Z .>  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::<lambda_1>  �8  std::chrono::sys_seconds  �7  std::chrono::year_month 8 Z-  std::chrono::duration<double,std::ratio<1,1000> >  s7  std::chrono::month_day " �7  std::chrono::year_month_day  �,  std::chrono::hours < q-  std::chrono::duration<__int64,std::ratio<1,1000000> > @ m[  std::chrono::zoned_traits<std::chrono::time_zone const *> 5 �,  std::chrono::duration<double,std::ratio<1,1> >  �6  std::chrono::months  89  std::chrono::tzdb e :7  std::chrono::time_point<std::chrono::local_t,std::chrono::duration<int,std::ratio<86400,1> > >  @Y  std::chrono::gps_clock  =Y  std::chrono::tai_clock  �6  std::chrono::month n "=  std::chrono::time_point<std::chrono::tai_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  dX  std::chrono::utc_clock  h[  std::chrono::local_t & �=  std::chrono::_Time_parse_fields 8 y=  std::chrono::_Time_parse_fields::_Leap_second_rep = w=  std::chrono::_Time_parse_fields::_Parse_tp_or_duration 3 Y=  std::chrono::_Time_parse_fields::_FieldFlags H g[  std::chrono::duration<__int64,std::ratio<1,1000000000000000000> >  �8  std::chrono::leap_second 8 �,  std::chrono::duration<double,std::ratio<3600,1> >  �2  std::ctype_base  EA  std::filesystem::perms ' 繟  std::filesystem::directory_entry $ JA  std::filesystem::copy_options ( 5A  std::filesystem::filesystem_error 7 rR  std::filesystem::_Path_iterator<wchar_t const *> ) �>  std::filesystem::_Find_file_handle & y>  std::filesystem::_Is_slash_oper . 螧  std::filesystem::_Should_recurse_result $   std::filesystem::perm_options 4 滳  std::filesystem::recursive_directory_iterator . �A  std::filesystem::_File_status_and_error & 9B  std::filesystem::_Dir_enum_impl 0 KB  std::filesystem::_Dir_enum_impl::_Creator @ QB  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! TA  std::filesystem::file_type . nB  std::filesystem::_Directory_entry_proxy " 揇  std::filesystem::space_info * 桞  std::filesystem::directory_iterator & N=  std::filesystem::file_time_type 0 虰  std::filesystem::_Recursive_dir_enum_impl ) 鞟  std::filesystem::directory_options # gA  std::filesystem::file_status u 霡  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( U[  std::filesystem::_File_time_clock  ?  std::filesystem::path $ �>  std::filesystem::path::format * ?R  std::filesystem::_Normal_conversion < gU  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �1  std::codecvt<char16_t,char,_Mbstatet> , �2  std::_Codecvt_guard<char32_t,char8_t>  R[  std::char_traits<char> � 镮  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �.  std::error_category ) �.  std::error_category::_Addr_storage i o:  std::tuple<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! �/  std::_System_error_message  �  std::_Unused_parameter h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  l4  std::bad_optional_access A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > , 確  std::_Atomic_integral<unsigned int,4> 7 !B  std::shared_ptr<std::filesystem::_Dir_enum_impl> " DT  std::_Floating_point_string = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> 2 �5  std::_Extended_Pictographic_property_values  �1  std::_Codecvt_mode @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn $ q?  std::ranges::_Iter_move::_Cpo ) o?  std::ranges::_Iter_move::_Cpo::_St % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn    std::_Exact_args_t  P[  std::ratio<86400,1>  V6  std::default_sentinel_t � A  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > >  N[  std::ratio<31556952,1> 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8>  =K  std::ostringstream  6  std::_Fmt_buffer_traits \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  g0  std::_Facet_base ' �)  std::hash<nvrhi::BindingSetItem> " 甔  std::_WChar_traits<wchar_t> 2 �2  std::codecvt<unsigned short,char,_Mbstatet> . �=  std::_Optional_destruct_base<__int64,1> - �=  std::_Optional_construct_base<__int64> # �/  std::_Generic_error_category  扟  std::streampos  B[  std::input_iterator_tag 2 JU  std::_Wrap<std::filesystem::_Dir_enum_impl> X 孲  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> 0 芻  std::_Atomic_integral<unsigned __int64,8> , 塪  std::_Atomic_padded<unsigned __int64> ' 綪  std::hash<enum nvrhi::ColorMask> / ,2  std::codecvt<char16_t,char8_t,_Mbstatet> 5 
`  std::_Locked_pointer<std::_Stop_callback_base> 3 豃  std::basic_ios<char,std::char_traits<char> >  �1  std::codecvt_base & 譒  std::_Fmt_counting_buffer<char>  錎  std::bad_function_call O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag ; 賓  std::allocator_traits<std::allocator<unsigned int> > ' 蜠  std::hash<std::filesystem::path> _ ?[  std::allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > > 7 �<  std::_Atomic_storage<std::chrono::tzdb_list *,8>  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > 7 40  std::_Tzdb_deleter<__std_tzdb_current_zone_info> " �  std::_Asan_aligned_pointers F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  q5  std::format_error  �  std::partial_ordering # |5  std::_Decode_result<wchar_t> . <#  std::array<nvrhi::BindingLayoutItem,16>  9[  std::_Default_sentinel P 耎  std::_Default_allocator_traits<std::allocator<std::chrono::leap_second> > $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int> Z 僣  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > p Rc  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Reallocation_policy   �5  std::_GB11_LeftHand_regex * �5  std::_GB11_LeftHand_regex::_State_t 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access 
 !   wint_t # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling " �  nvrhi::SamplerReductionType $ �(  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets  &  nvrhi::GraphicsState * DH  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16>  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray ) 艷  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! �(  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc 2 馟  nvrhi::RefCountPtr<nvrhi::IComputePipeline>  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray . 鮞  nvrhi::RefCountPtr<nvrhi::ICommandList>  艷  nvrhi::DeviceHandle   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture ! 蒪  nvrhi::utils::ScopedMarker $ =b  nvrhi::utils::BitSetAllocator . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  DH  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # �(  nvrhi::DescriptorTableHandle     nvrhi::ShaderType  �(  nvrhi::TimerQueryHandle   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �(  nvrhi::HeapHandle # 馟  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - H  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  �(  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  �(  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  lH  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  H  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 繸  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   �(  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �(  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # �(  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  鮞  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable * lH  nvrhi::RefCountPtr<nvrhi::ITexture>  �&  nvrhi::ComputeState  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery  �/  __std_win_error  S0  __std_tzdb_leap_info  �0  lconv   �  __RTTIBaseClassDescriptor 
    _off_t    stat  t   int32_t  Z  timespec & JZ  $_TypeDescriptor$_extraBytes_37  cD  __std_fs_file_id 
 !   _ino_t 
 沘  _Cnd_t # 70  __std_tzdb_current_zone_info ' ID  __std_fs_create_directory_result  !   uint16_t  �/  __std_fs_stats ! 﨑  donut::engine::ShaderMacro # 紿  donut::engine::ShaderFactory " 沍  donut::engine::StaticShader  WI  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3  EG  donut::math::float2 # 錏  donut::math::vector<float,3>  u   donut::math::uint  /F  donut::math::plane # F  donut::math::vector<float,4>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes % 慫  donut::math::matrix<float,3,4>  (G  donut::math::uint2  F  donut::math::float4 % WI  donut::math::matrix<float,4,4> # 揈  donut::math::affine<float,3> " *E  donut::math::vector<bool,2>  GF  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> * (G  donut::math::vector<unsigned int,2> M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>    _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ! )0  __std_tzdb_time_zones_info     intmax_t    terminate_handler  �  _s__RTTIBaseClassArray & VZ  $_TypeDescriptor$_extraBytes_52 
 Y  ldiv_t  �/  __std_fs_file_flags  �0  _Cvtvec  u   _Thrd_id_t - �  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray  �/  __std_fs_copy_options     ptrdiff_t  
  _stat64i32  E0  __std_tzdb_sys_info  �  _PMD      uint8_t  ,  type_info ' �  _s__RTTIClassHierarchyDescriptor  t   errno_t  �/  __std_fs_reparse_tag  \  _lldiv_t    GenerateMipsPass  C  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  G  _s__ThrowInfo  c6  __std_fs_convert_result  �/  __std_fs_stats_flags  �  __RTTIBaseClassArray  �  __crt_locale_data_public - �  $_s__CatchableTypeArray$_extraBytes_24  Ya  _Mtx_internal_imp_t & 4Z  $_TypeDescriptor$_extraBytes_25 % �  __RTTIClassHierarchyDescriptor  �0  _Collvec   艭  __std_fs_volume_name_kind 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t 
    fpos_t  鏔  PolymorphicLightInfoEx  �  FILE 
 Ma  _Mtx_t 3 �  __vcrt_va_list_is_reference<wchar_t const *>    mbstate_t  ?  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray  DD  __std_fs_remove_result  騀  PolymorphicLightInfoFull  靈  _Thrd_t - �  $_s__RTTIBaseClassArray$_extraBytes_16 - /Z  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  �/  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  紺  __std_ulong_and_error  A   __std_tzdb_epoch_milli  \  lldiv_t  Y  _ldiv_t  [  _timespec64     intptr_t  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers  �   x      G�膢刉^O郀�/耦��萁n!鮋W VS  @    o�椨�4梠"愜��
}z�$ )鰭荅珽X  �    黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �    ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  d   zY{���睃R焤�0聃
扨-瘜}  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  &   溶�$椉�
悇� 騐`菚y�0O腖悘T  {    狾闘�	C縟�&9N�┲蘻c蟝2  �   J�(S�	皓&r悖,@椎�輮� ��*{�  �   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  $   �'稌� 变邯D)\欅)	@'1:A:熾/�  m   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  �   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  %   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  d   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   +4[(広
倬禼�溞K^洞齹誇*f�5  C   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   �X�& 嗗�鹄-53腱mN�<杴媽1魫  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg     ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  a   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��  �   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  (   <AE#(�/y鍹籲贚痶蹒-	蒸|邨VWTR  p   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  (   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  z   )鎋]5岽B鑯 �誽|寋獸辪牚  �   檒Gq$�#嗲RR�錨账��K諻刮g�   �   険L韱#�簀O闚样�4莿Y丳堟3捜狰  (	   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  w	   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �	   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�   
   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  Y
   揾配饬`vM|�%
犕�哝煹懿鏈椸  �
   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �
   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  /   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  u   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   猯�諽!~�:gn菾�]騈购����'  -   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  j   �"睱建Bi圀対隤v��cB�'窘�n  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  
   瘩��:濣渏]�(J丌爱�+垇兜R�)k�  3
   Pフ+
V
*戋zydD躩=壠�jA�N盪{u5  z
   D���0�郋鬔G5啚髡J竆)俻w��  �
   仫減f╋萩}_遫R衵9弧0Up*q蹩鎍,  
   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  P   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   f扥�,攇(�
}2�祛浧&Y�6橵�      �芮�>5�+鮆"�>fw瘛h�=^���  T   [届T藎秏1潴�藠?鄧j穊亘^a  �   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  �   +FK茂c�G1灈�7ほ��F�鳺彷餃�     A縏 �;面褡8歸�-構�壋馵�2�-R癕  A   蜅�萷l�/费�	廵崹
T,W�&連芿  ~   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   饵嶝{郀�穮炗
AD2峵濝k鴖N     鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  `   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4     ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  N   妇舠幸佦郒]泙茸餈u)	�位剎  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   靋!揕�H|}��婡欏B箜围紑^@�銵     交�,�;+愱`�3p炛秓ee td�	^,  Q   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   �颠喲津,嗆y�%\峤'找_廔�Z+�  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  2   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  r   t�j噾捴忊��
敟秊�
渷lH�#  �   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  �   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  3   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  u   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇     *u\{┞稦�3壅阱\繺ěk�6U�  E   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   悯R痱v 瓩愿碀"禰J5�>xF痧     矨�陘�2{WV�y紥*f�u龘��  e   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  0   L�9[皫zS�6;厝�楿绷]!��t  n   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`     �9俅�(� .]嬝暧烵啤�秾*~w�=[@�  C   嵮楖"qa�$棛獧矇oPc续忴2#
  �   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9     )�Q�Ａ9氫�&煝]R埬�楜� 磛庋p�  i   �)D舼PS橼鈝{#2{r�#獷欲3x(  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰      {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  @   鏀q�N�&}
;霂�#�0ncP抝  y   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  9   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  x   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   鮪蛬r遙	I疍瞠�#Y_*Ж%�E峩�  �   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  "   チ畴�
�&u?�#寷K�資 +限^塌>�j  V   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈     癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  ]   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �   V� c鯐鄥杕me綻呥EG磷扂浝W)  $   穫農�.伆l'h��37x,��
fO��  a   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �   郖�Χ葦'S詍7,U若眤�M进`  �   5�\營	6}朖晧�-w氌rJ籠騳榈  5    E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  u    }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �    dhl12� 蒑�3L� q酺試\垉R^{i�  �    蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  4!   �*o驑瓂a�(施眗9歐湬

�  |!    z�漼嶜q镛�F苜�:壗Wア燤PEx�  �!   �0�*е彗9釗獳+U叅[4椪 P"��  �!    I嘛襨签.濟;剕��7啧�)煇9触�.  "   ,┭0甗�+天没2骟Bw蛁�%"艠E�  W"   �=蔑藏鄌�
艼�(YWg懀猊	*)  �"   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �"   龀缬@唶�鱮�瓗禽鐑�^]>Xz豹tN  &#   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  v#   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �#   _O縋[HU-銌�鼪根�鲋薺篮�j��  �#   l籴靈LN~噾2u�< 嵓9z0iv&jザ  O$   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �$   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �$   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  %   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  S%   繃S,;fi@`騂廩k叉c.2狇x佚�  �%   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �%   `k�"�1�^�`�d�.	*貎e挖芺
脑�  &   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  ^&   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �&   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �&   �
bH<j峪w�/&d[荨?躹耯=�  '   j轲P[塵5m榤g摏癭 鋍1O骺�*�  Z'   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �'   +椬恡�
	#G許�/G候Mc�蜀煟-  �'   S仄�p�'�/2H��g'浗o$鏊^藵捯�  !(   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  m(   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �(   觑v�#je<d鼋^r
u��闑鯙珢�  �(   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  $)   鹴y�	宯N卮洗袾uG6E灊搠d�  l)   Fp{�悗鉟壍Au4DV�`t9���&*I  �)   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �)   R冈悚3Y	�1P#��(勁灱�涰n跲
  .*   x)憂^恛閶{籮>鐼末BA<颭~儽&'%;-g�  n*   副謐�斦=犻媨铩0
龉�3曃譹5D   �*   .QVBL籺'Z鬖b濁蟂�8w,/kr彨�$擠�  �*   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  3+   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  p+   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �+   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  ,   豊+�丟uJo6粑'@棚荶v�g毩笨C  E,   V8追i顚�^�k细�;>牧惺扴	�\s  �,   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �,   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  -   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  X-   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �-   匐衏�$=�"�3�a旬SY�
乢�骣�  �-      ;.   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �.   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �.   鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  �.   曀"�H枩U传嫘�"繹q�>窃�8  </   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �/   �(M↙溋�
q�2,緀!蝺屦碄F觡  �          �  �   B   �  �   H   �  �   Y   �  �   �   b   
  �  r  �  U   s  �  �   �  X  \   �  X  �  �  X    �  X  [  �  X  �  �   
  �  �      K   �      K   �  �  m   
  �  Q   �   
  �  �  x
  �   �  x
    �  x
  �   �  x
  �   �  x
  �   �  x
  �   �  x
    �  x
  �   �  x
  �   �  �  q   �  �  @   �  �  5   �  �  @   �  �  5   �  x
    �  x
  �   �  x
  �   �  x
  �   �  x
  �   �  x
  �   �  x
  �   �  �  q   �  �  @   �  �  5   J  �  B  O  �  �	  �   
  t  �  x
  �   �  x
    �  x
  �   �  x
  �   �  x
  �   �  x
    �  x
  �   �  x
  �   �  �  q   �  x
    �  x
  �   �  x
  �   �  x
  �   �  x
    �  x
  �   �  x
  �   %  �  �  /  �  �  3  �  �  >  �  �  ?  �  0   j  x
  �   k  x
  �   �  �  D
  �  �  �  �  x
  �   �  �  �  @  �  �  A  �  �  C  �  L
  D  �  L
  c  �  �   �  x
  �   �  �  �  �  �  �  �  �  �    �  s    �  �  �  �  )
  (  �  �   9   �  �   I   �  @   �'  P  2   �'  �  �  
(  �  �  5(  �  �  t(  �  >  �(  �  �  )  �  �  )  �  �  )  �  �  1  X  �  1  X  �  �1  �  �  �1  (   ,   >2  �  �  ?2  �    a2  �  �  v2  �  �  �2  �    �2  �    �2  �  �  �2    �   u5  �  x   �   �/   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\RTXPT\Rtxpt\Shaders\Bindings\BindingDataTypes.hlsli D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\RTXPT\External\Rtxdi\Include\Rtxdi\DI\ReSTIRDIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_print.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiParameters.h D:\RTXPT\External\Donut\include\donut\core\math\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\PolymorphicLight.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\RTXPT\Rtxpt\RTXDI\GeneratePdfMipsPass.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_tzdb.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\format D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_format_ucd_tables.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf D:\RTXPT\External\Rtxdi\Include\Rtxdi\ReGIR\ReGIRParameters.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bit C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stop_token D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu_tables.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\Rtxpt\RTXDI\GeneratePdfMipsPass.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\Rtxpt\RTXDI\ShaderParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Donut\include\donut\shaders\view_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\forward_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\RTXPT\Rtxpt\Shaders\PathTracer\PathTracerShared.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\RTXPT\Rtxpt\Shaders\PathTracer\Config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\LightingTypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\RTXPT\External\Donut\include\donut\shaders\sky_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\charconv D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\GI\ReSTIRGIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\predefined C++ types (compiler internal) C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp   �       LZ}  uw  �   yw  �  
 8y  �   <y  �  
 薏      獠     
 9�      =�     
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁裥砓>Y7?樰�=      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   9         �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >_   _Bytes  AJ        9  $  >_    _Block_size  AH       1 
   >_    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        r  
 Z      N Z   �  q   (                      H 
 h   r         $LN14  0   _  O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 s  *   w  *  
 �  �    �  �   
 H塡$H塴$H墊$ AVH冹 H�������I嬝H嬯L嬹L;�囎   H茿   H凔wH塝�    B�3 閿   H嬅H塼$0H內H;莢)H�       �H兝'H嬋�    H吚t~H峱'H冩郒塅8�   H孁H;罤B鵋峅H侚   rH岮'H;羦Q刖H吷t
�    H嬸�3鯨嬅I�6H嬚I塣H嬑I墌�    � H媡$0H媆$8H媗$@H媩$HH兡 A^描    惕    惕    藾      u   �    �   �    �      �   �      �    	  �       �   s  � G                   C        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *> 
 >�   this  AJ        (  AV  (     � �  
 >�   _Arg  AK        %  AN  %     � �   >_   _Count  AI  "     � �   AP        "  >#     _New_capacity  AH  m       AJ  �     h   % >  AM       � N  � i  AH q       AJ q     c  P  AM �      % M        �  q	*I >p    _Fancy_ptr  AL  �       AL �     "  M        �  q.I M        �  q.I/ M        (  q.		
%
:. M        9   q(%"
P	 Z   �  q   >_    _Block_size  AH  �     [  O  AH q       >_    _Ptr_container  AH  y     �  p  AH �      
 >�    _Ptr  AL  �       AL �     "  M        r  q
 Z      N N M        r  ��
 Z      N N N N N M        �  R2! M          R') >_    _Masked  AH  ^     f   N  _   AH �       M        �  �� N N N M        ?   C N M        ?   �� N
 Z   ~                         H Z h   �  �  r  x  y  �  $  ?  �  �  �  �  �  �    �  �  '  (  /   9          $LN87  0   �  Othis  8   �  O_Arg  @   _  O_Count  O �   �             �     �       L
 �   V
 �1   ^
 �?   _
 �C   f
 �H   g
 �M   q
 �R   u
 �q   v
 ��   u
 ��   v
 ��   
 ��   w
 ��   
 ��   y
 ��   
 ��   z
 ��   
 ��   �
 ��   �
 ��   v
 �  W
 �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 '  �    +  �   
 O  �    S  �   
 _  �    c  �   
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 �  �    �  �   
 �  �       �   
 %  �    )  �   
 9  �    =  �   
 X  �    \  �   
 h  �    l  �   
 '  �    +  �   
 C  �    G  �   
 3  '   7  '  
 �  �    �  �   
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AJ                                 H�     b%  Othis  O   �   0              x
     $       �  �    �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$H塼$H墊$AVH冹 3�W�H堿L嬺H堿H嬞H儂H媟vL�2H�������H;�嚻   H茿   H凗wH塹A閮   H嬑H兩H;蟰,H�       �H兝'H嬋�    H嬋H吚trH兝'H冟郒塇1�   H孂H;蔋B鶫峅H侚   rH岮'H;羦E牖H吷t�    L岶H�I嬛H塻H嬋H墈�    H媡$8H嬅H媆$0H媩$@H兡 A^描    惕    惕    虉   �    �   �    �        �    
  �      �       �   �  � G                   Q        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >�   this  AI  +     � �   AJ        +  >�   _Right  AK        �  � k  AK �     �  a U M        D  9.


?
 Z   ~   >#     _New_capacity  AH  �       AJ  �     \  9  AM  C     � = " r ]  AH �       AJ �     `  L  AM �      & M        �  ��	-
= >p   _Fancy_ptr  AH �       C            "  C      9     � G " �  �    M        �  ��1
=  M        �  ��1
=. M        (  ��1		

8/ M        9   ��+%"
D	 Z   �  q   >_    _Block_size  AH  �     O  C  AH �       >_    _Ptr_container  AJ  �     |  d  AJ �      
 >�    _Ptr  AH  �       AH �       M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  j8 M          j*, >_    _Masked  AJ  q     D    AJ �       M        �  �� N N N M        ?   ^ N M        ?   �� >_   _Count  AP  �       N N M        /  +	 >�    _Result  AV  $     � �   M        �  + N N M        @  
$ M        �  ������ M           N N N                       @ v h   �  �  r  x  y  �  #  $  /  2  ?  �  �  �  @  D  �  �  �  �      �  �  '  (  /   9          $LN106  0   �  Othis  8   �  O_Right  O �   8             �     ,       �	 �+   �	 ��   �	 �  �	 �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �    	  �   
   �    !  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 Y  �    ]  �   
 m  �    q  �   
 �  �    �  �   
 h  �    l  �   
 |  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 .  �    2  �   
 t  �    x  �   
 �     �    
 �  �    �  �   
 H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >d#   this  AJ                                 H     d#  Othis  O   �                  X             �,   �    0   �   
 p   �    t   �   
 �   �    �   �   
 @USVWATAUAVAWH崿$皋��窰"  �    H+郒�    H3腍墔0!  I嬞M嬥L孃H嬹H塎餖塃鳨3鞮�)L峲M�.H塝M吷t
I�H嬎�P怘媿�!  H塏H吷tH��P怢塶 H�
    �    H婲H��P H孁H荅�  �?f荅� W�E惽E�艵�I�L岴怘峊$`I嬒�惾   I嬚H峂 H;萾H�L�(H婲 H塚 H吷tH��P怘婰$`H吷tL塴$`H��P惼�  D塵盖E�
   L塵癓塵繦荅�   H婩 D塵厍E�   H塃蠰塵郘塵鐸嬇H墔 !  H峂癴�     H拎�   I�0  H媴 !  H�繦墔 !  H兞 H峌餒;蕌茿�  H崟   H崓  �    H媴  H呟tCD塴$x荄$|   H塡$p
    H拎D$p�  �   H媴  H�繦墔  A嬐D9ovZH媀f怘拎H墧  墝  莿     墝   H莿$     莿,     H媴  H�繦墔  ��;Or琇塴$0�    嬜L塼$(H岲$0H塂$ L崓  E3繧嬒�    W�厛   嬒�    H嬋H墔�   H菂�      H菂�           �   堿�   圓艫 H�    H�    H呟HE�W�叏   W审嵏   I瞧����M嬈f�     I�繠�< u鯤崓�   �    怘崟�   H崓�   �    怘崟�   H崓�   �    �W荔D$@L塴$P笯   �    H嬝H塂$@H塂$HH峹@H墊$PH岲$@H塃H塡$pH塡$xH岲$@H塃�H塡$8H崟�   H嬎�    怘岾 H崟�   �    怘墊$xH墊$HL�
    篅   D岯罤崓�   �    怘嫊�   H凓v5H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐噥  �    怘嫊�   H凓v4H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘐  �    f荄$(  H岲$@H塂$ L�
    L�    H峊$XI�$�    怢塵 W�3�E(E8H塃HH�    H塡$ L�
    峆D岪H峂(�    怢塵PH婰$0H塋$hH吷tH��P怘塡$ L�
    �   D岯鼿峂X�    怚孆L壄�   H媆$hH呟tH�H嬎�PH嫿�   H9\齒t,H呟t
H�H嬎�P怘婰齒H塡齒H吷tH��P怘嫿�   H�荋壗�   H呟t
H�H嬎�P怚嬢@ I嬚H岴XH肏峂H;萾H�L�(H婰(H塗(H吷tH��P怘兠H凔(r荋媴�   H塃PL�
    �   D岯鼿峂X�    怢�
    �   D岯鵋峀$h�    H婱 H媆$XH;藅"H呟t
H�H嬎�PH婱 H塢 H吷tH��P怚�L岴 H峊$8I嬒��8  I嬚H峂H;萾H�L�(H�H�H吷tH��P怘婰$8H吷tL塴$8H��P怢�
    �   D岯鼿峂(�    怘婱 H吷tL塵 H��P怘婰$XH吷tL塴$XH��P怘媆$@H呟thH媩$HH;遲怘嬎�    H兠@H;遳颒媆$@H婽$PH+親冣繦嬅H侜   rH兟'H媅鳫+肏兝鳫凐噯   H嬎�    W荔D$@L塴$PH婰$0H吷tL塴$0H��P怚媆$H呟t*A嬈�罜凐uH�H嬎�餌羢A凗u	H�H嬎�RH嬈H媿0!  H3惕    H伳H"  A_A^A]A\_^[]描    愯    愯    �      %   �   �   �   �   �    �     �  �   �  �    �  �    �  �   �  �   �  �   �  �   �  �   2  �    F  �    Z  �    s  �    �  �    �  �    �  �    �  �    3  �    u  �    �  �   �  �   �  �    �  �    �  �    �  �      �      �    �  �    �  �    �  �      �    �  �    �  �    �  �    +  �    �     �  �    �  �    �  �       �   
  H G            �  3   �  �6        �GenerateMipsPass::GenerateMipsPass 
 >曛   this  D�    AJ        ?  AL  ?     �k  D�"   >))   device  AK        <  AW  <     �f  >mH   shaderFactory  D�    AP        9  AT  9     �o  D�"   >�   sourceEnvironmentMap  AI  6     D AQ        6  >�   destinationTexture  AJ  n       EO  (           D�"   >�    samplerDesc  D�    >�    destinationDesc  AM  �     � >�$    shader  DX    >�%    pipelineDesc  D    >�%    bindingLayout  AJ  �      D0    >僣    macros  D@    >�#   bindingSetDesc  CH      �    f 
 R  D   >u     mipLevel  A       �  M        �  
両
, >�#    <begin>$L0  AJ  W    V  M        �  乣 N N! M        �  �2$' >n#   sampler  AH  2      N M        �  �$'d N M        �  �� M        �  ��HB
 >n#    temp  AJ  �       AJ     K  B )  �     � D`    N N M        �  %�� M        �  �� M        �  ��
 >n#    temp  AJ  �       AJ �       B )  e    H  N N M        �  �� >n#    tmp  AK  �     "  AK �     �    N M        �  ��C	 M        �  �� N N N M        1  �� N M        �  �� N M        1  �� N M        �  ~ N M        �  g M        �  r N N M        �  T M        �  X	 N N M        �  Q
 >鱃   this  AV  Q     � N M        �  G N M        u5  佔 N M        �  伨%( N M        �  俤 N M        u5  �< N M        �  #哅 M        �  唀 M        �  唀
 >�&    temp  AJ  b      AJ q      Bh(  e    H  N N M        �  哶 >�&    tmp  AK  P       AK q    "    N M        �  哅C	 M        �  哬 N N N M        �  4嘢 M        �  嘢*
 M        �  嘳-
 >�   this  AI  X    S  M        b  噐	 N N N N M        �  �= M        �  �=HB
 >t$    temp  AJ  B      AJ S    >    N N  M        �'  喫	9<{. M        
(  喫

	9<	u M        5(  1嘅��  M        c  �)��
 Z   �  
 >   _Ptr  AH        AI  �    5    AH '      AI �    x  Q  >#    _Bytes  AK  �    �   4 ~  M        s  �d��
 Z   �   >_    _Ptr_container  AH        AI        N N N M        t(  嗇	 >c   _First  AI  �    
  AI �      >霥   _Last  AM  �    � c p  AM =    l  N N N M        �  喌 M        �  喌HB
 >k$    temp  AJ  �      AJ �    �   _  w k  N N M        �  啞 M        �  啞GB
 >k$    temp  AJ  �      AJ �      N N M        �  唓 M        �  唓HB
 >�&    temp  AJ  v      AJ �      B8   �    3 BP'  M    f N N M        �  �! M        �  �, M        �  �,
 >k$    temp  AJ      +    AJ 8      B@'  e    H  N N M        �  �( >k$    tmp  AI      �  N M        �  � M        k  �#
 N N N M        �  .厫 M        �  叧 M        �  叧
 >t$    temp  B'  e    H  N N M        �  叐 N M        �  厫C M        �  叄 N N N# M        �  �J 
 >�%    i  AI  (    d  M        �  厇 M        �  厇	
 N N M        �  �=
 M        j  �= M        �  匽 M        �  匽
 >t$    temp  B�&  e    H  N N M        �  匰 N M        �  匘 M        �  匘#	 N N N N M        �  �# M        �  �(# N N N M        �  勪
 M        �  勵# N N M        �  劗2 N M        �  劎 N M        J  A�8儊 M        %  �84
僼 M        �  4凟僼 M        3  1凥僸  M        c  凴)僂
 Z   �  
 >   _Ptr  AH  R      AJ  O      AH t      >#    _Bytes  AK  H    v1 @ M        s  刐d僑
 Z   �   >_    _Ptr_container  AH  f      AJ  c      N N N N N N M        �'  �7 Z   Q  Q   N M        J  A凍兘 M        %  凍4
儼 M        �  4�儼 M        3  1�儹  M        c  �)儊
 Z   �  
 >   _Ptr  AH        AJ  
      AH 2      >#    _Bytes  AK      �1 | M        s  �d儚
 Z   �   >_    _Ptr_container  AH  $      AJ  !      N N N N N N M        �1  僢A
b M        ?2  僲 	
9 >�    _Guard  D   M        a2   僲 M        v2  僲-%	 >霥    _Newvec  AI  z    EI� M        )  
僲 M        )  
僲 M        (  
僲 M        r  
僲
 Z      N N N N N N M        �2  F儢& >�    _Backout  Bp   �    $ M        )  儢 N M        �2  儵& M        �2  儵 M        �2  儵 N N N N N M        >2  僢 M        �(  僢 N N N M        O  傷


 Z   C  
 >�   _Ptr  AK  �    E  M        >  
�

 N M        A  傷
 M        �  傷��
 M          傷 N N N N M        O  倲H" M        C  T倹*G+
 M        �  
倹 >p    _Fancy_ptr  AJ  �    �  M        �  
倹 M        �  
倹 M        (  
倹 M        r  
倹
 Z      N N N N N M        ?   偮 N N M        A  倲 M        �  倲 M          倲 N N N N Z   �6  �6  �!  �!  �!   H"          @         A bh�   �  �  b  r  s  t  x  y  �  �  �  �  �  �  �  �  �  �  �  �  �             	  
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  J  K  O  S  n  o  z  {  |  }  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  $  %  3  >  ?  j  k  �  �  �  �  �  �  �  �  <  A  C  ^  c  �  �  �  �  �      �  �  '  (  /   9   I   �'  �'  
(  4(  5(  t(  �(  �(  �(  �(  �(  )  )  )  )  )  )  )  �*  1  
1  1  �1  �1  �1  �1  �1  >2  ?2  Y2  a2  v2  �2  �2  �2  �2  �2  �2  �2  u5  
 :0"  O        $LN640  �"  曛  Othis  �"  ))  Odevice  �"  mH  OshaderFactory ! �"  �  OsourceEnvironmentMap  �"  �  OdestinationTexture  �   �  OsamplerDesc  X   �$  Oshader     �%  OpipelineDesc  0   �%  ObindingLayout  @   僣  Omacros    �#  ObindingSetDesc  9c       E   9z       E   9�       �   9�       �(   9�       E   9      E   9�      E   93      E   9O      E   9e      E   9�      E   9�      E   9!      E   94      E   9G      �(   9m      E   9�      E   9�      E   9�      E   9O      E   9p      �   9�      �   O  �              �  �     �         �?     �G     �T     �g     �~     ��     ��      ��   &  ��   #  ��   $  ��   &  ��   '  �  *  ��  /  ��  1  �  4  �
  6  �]  4  �d  =  �i  >  ��  A  �y  C  ��  E  ��  F  �  G  �8  H  ��  I  ��  A  ��   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$0 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$1 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$2 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$3 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$4 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$5 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$7 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$8 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  W F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$9 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O  �   �  X F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$28 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O �   �  X F            -      '             �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$11 
 >曛   this  EN  �         '  EN  �"        '  >mH   shaderFactory  EN  �         '  EN  �"        '  >�    samplerDesc  EN  �         '  >�$    shader  EN  X         '  >�%    pipelineDesc  EN           '  >�%    bindingLayout  EN  0         '  >僣    macros  EN  @         '  >�#    bindingSetDesc  EN          '                        �  O �   �  X F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$23 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O �   �  X F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$24 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O �   �  X F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$25 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O �   �  X F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$12 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O �   �  X F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$13 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O �   �  X F                                �`GenerateMipsPass::GenerateMipsPass'::`1'::dtor$14 
 >曛   this  EN  �           EN  �"          >mH   shaderFactory  EN  �           EN  �"          >�    samplerDesc  EN  �           >�$    shader  EN  X           >�%    pipelineDesc  EN             >�%    bindingLayout  EN  0           >僣    macros  EN  @           >�#    bindingSetDesc  EN                                   �  O ,   �    0   �   
 u   �    y   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    
  �   
 A  �    E  �   
 Q  �    U  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 B  �    F  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 j  �    n  �   
 �  �    �  �   
 �  �    �  �   
   �    	  �   
 z  �    ~  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 @  �    D  �   
 :  �    >  �   
 J  �    N  �   
 Z  �    ^  �   
 �  �    �  �   
 �  �    �  �   
 _  �    c  �   
 �  �    �  �   
 �  �    �  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 

  �    
  �   
 g
  �    k
  �   
 w
  �    {
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
   �      �   
 `  �    d  �   
 p  �    t  �   
 �  �    �  �   
 �  �    �  �   
 B  �    F  �   
 R  �    V  �   
 b  �    f  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
 
  �    
  �   
 C
  �    G
  �   
 �
  �    �
  �   
 �  �    �  �   
 h  �    l  �   
 <  �    @  �   
 L  �    P  �   
 \  �    `  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
 '  �    +  �   
 �  �    �  �   
 �  �    �  �   
 S  �    W  �   
   �      �   
   �      �   
 �  �    �  �   
 �     �    
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
   �      �   
 &  �    *  �   
 6  �    :  �   
 F  �    J  �   
 V  �    Z  �   
 f  �    j  �   
 v  �    z  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
    �    $  �   
 H  �    L  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 ?  �    C  �   
 j  �    n  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 <   �    @   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 !  �    !  �   
 3!  �    7!  �   
 ^!  �    b!  �   
 �!  �    �!  �   
 �!  �    �!  �   
 �!  �    �!  �   
 0"  �    4"  �   
 �"  �    �"  �   
 �"  �    �"  �   
 �"  �    �"  �   
 �"  �    �"  �   
 #  �    #  �   
 '#  �    +#  �   
 R#  �    V#  �   
 ~#  �    �#  �   
 �#  �    �#  �   
 �#  �    �#  �   
 $$  �    ($  �   
 x$  �    |$  �   
 �$  �    �$  �   
 �$  �    �$  �   
 �$  �    �$  �   
 �$  �    �$  �   
 %  �    %  �   
 F%  �    J%  �   
 r%  �    v%  �   
 �%  �    �%  �   
 �%  �    �%  �   
 &  �    &  �   
 l&  �    p&  �   
 �&  �    �&  �   
 �&  �    �&  �   
 �&  �    �&  �   
 �&  �    �&  �   
 '  �    '  �   
 :'  �    >'  �   
 f'  �    j'  �   
 �'  �    �'  �   
 �'  �    �'  �   
 (  �    (  �   
 `(  �    d(  �   
 t(  �    x(  �   
 �(  �    �(  �   
 �(  �    �(  �   
 �(  �    �(  �   
 )  �    )  �   
 .)  �    2)  �   
 Z)  �    ^)  �   
 )  �    �)  �   
 �)  �    �)  �   
  *  �    *  �   
 T*  �    X*  �   
 h*  �    l*  �   
 �*  �    �*  �   
 �*  �    �*  �   
 �*  �    �*  �   
 �*  �    �*  �   
 "+  �    &+  �   
 N+  �    R+  �   
 s+  �    w+  �   
 �+  �    �+  �   
 �+  �    �+  �   
 H,  �    L,  �   
 \,  �    `,  �   
 �,  �    �,  �   
 �,  �    �,  �   
 �,  �    �,  �   
 �,  �    �,  �   
 -  �    -  �   
 B-  �    F-  �   
 g-  �    k-  �   
 �-  �    �-  �   
 �-      �-     
 <.      @.     
 P.      T.     
 |.      �.     
 �.      �.     
 �.      �.     
 �.      �.     
 
/      /     
 6/      :/     
 [/      _/     
 �/      �/     
 �/  �    �/  �   
 10  �    50  �   
 E0  �    I0  �   
 q0  �    u0  �   
 �0  �    �0  �   
 �0  �    �0  �   
 �0  �    �0  �   
 �0  �    1  �   
 +1  �    /1  �   
 P1  �    T1  �   
 }1  �    �1  �   
 �1  �    �1  �   
 %2  �    )2  �   
 92  �    =2  �   
 e2  �    i2  �   
 y2  �    }2  �   
 �2  �    �2  �   
 �2  �    �2  �   
 �2  �    �2  �   
 3  �    #3  �   
 D3  �    H3  �   
 q3  �    u3  �   
 �3  �    �3  �   
 4  �    4  �   
 -4  �    14  �   
 Y4  �    ]4  �   
 m4  �    q4  �   
 �4  �    �4  �   
 �4  �    �4  �   
 �4  �    �4  �   
 5  �    5  �   
 85  �    <5  �   
 e5  �    i5  �   
 �5  �    �5  �   
 
6  �    6  �   
 !6  �    %6  �   
 M6  �    Q6  �   
 a6  �    e6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 7  �    7  �   
 ,7  �    07  �   
 Y7  �    ]7  �   
 �7  �    �7  �   
 8  �    8  �   
 8  �    8  �   
 A8  �    E8  �   
 U8  �    Y8  �   
 8  �    �8  �   
 �8  �    �8  �   
 �8  �    �8  �   
 �8  �    �8  �   
  9  �    $9  �   
 M9  �    Q9  �   
 �9  �    �9  �   
 �9  �    �9  �   
 	:  �    
:  �   
 5:  �    9:  �   
 I:  �    M:  �   
 s:  �    w:  �   
 �:  �    �:  �   
 �:  �    �:  �   
 �:  �    �:  �   
 ;  �    ;  �   
 A;  �    E;  �   
 �;  �    �;  �   
 �;  �    �;  �   
 �;  �    <  �   
 )<  �    -<  �   
 =<  �    A<  �   
 g<  �    k<  �   
 �<  �    �<  �   
 �<  �    �<  �   
 �<  �    �<  �   
 =  �    =  �   
 5=  �    9=  �   
 �=  �    �=  �   
 �=  �    �=  �   
 �=  �    �=  �   
 >  �    !>  �   
 1>  �    5>  �   
 [>  �    _>  �   
 �>  �    �>  �   
 �>  �    �>  �   
 �>  �    �>  �   
 �>  �     ?  �   
 )?  �    -?  �   
 H媻�   �       �    H媻�   �       �    H媻�   H兞�       �    H媻�   H兞�       �    H媻�   H兞�       �    H媻�   H兞 �       �    H崐0   �       �    H崐�  �       �    H崐�  �       �    @UH冹 H嬯L�
    A�   篅   H崓�  �    H兡 ]�   �    #   �    H崐@   �       �    H崐X   �       �    H崐   �       �    H崐  �       �    H崐p   �       �    H媻8   �       �    H崐�  �       �    @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   ]   %   �    ,   c      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   ]   %   �    ,   f      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   i      f      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !   �      ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   ]   %   �       �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �      $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AH         AJ          AH        M        �  GCE
 >t$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   b%  Othis  9       E   O�   0           "   x
     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 P  �    T  �   
 h  �    l  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >鱃   this  AH         AJ          AH        M        �  GCE
 >�%    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   鱃  Othis  9       E   O  �   0           "   x
     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �      �   
 J  �    N  �   
 d  �    h  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   X  x G            "         �        �nvrhi::RefCountPtr<nvrhi::IComputePipeline>::~RefCountPtr<nvrhi::IComputePipeline> 
 >蜧   this  AH         AJ          AH        M        �  GCE
 >�&    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   蜧  Othis  9       E   O�   0           "   x
     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 T  �    X  �   
 l  �    p  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ISampler>::~RefCountPtr<nvrhi::ISampler> 
 >H   this  AH         AJ          AH        M        �  GCE
 >n#    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   H  Othis  9       E   O�   0           "   x
     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 D  �    H  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�$   this  AH         AJ          AH        M        �  GCE
 >k$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �$  Othis  9       E   O  �   0           "   x
     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >JH   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   JH  Othis  9       E   O�   0           "   x
     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 D  �    H  �   
 \  �    `  �   
 H�	H吷�    �   �       �   R  � G            
          Y2        �std::_Tidy_guard<std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > >::~_Tidy_guard<std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > > 
 >�   this  AJ         
 Z   
(                          H�     �  Othis  O  �   0           
   �     $       *  �    +  �   .  �,   �    0   �   
   �      �   
 h  �    l  �   
 H塡$WH冹 H媦H�H;遲H嬎�    H兠@H;遳颒媆$0H兡 _�   �       �   �  � G            2   
   '   )        �std::_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> >::~_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> > 
 >�   this  AJ          AJ          M        t(  	
 >c   _First  AI         >霥   _Last  AM       #  N                       H�  h   t(  �(  �(  )  )   0   �  Othis  O   �   0           2   �     $        �
    �'    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 ,  �    0  �   
 L  �    P  �   
 �  �    �  �   
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �    Y   �       �   �  � G            ^      ^   J        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >�   this  AI  
     Q J   AJ        
 ) M        %  ,(
	 M        �   N M        �  ,E M        3  &? M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        s  
"#
!
 Z   �   >_    _Ptr_container  AP  &     7    AP :       >_    _Back_shift  AJ  -     0 
   N N N N N                       H� : h
   �  s  t  K  $  %  3  �  �  �  �  ^  c         $LN35  0   �  Othis  O�   H           ^   �     <       B �   C �
   B �
   C �R   J �X   C �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �  �    �  �   
 �  �    �  �   
 ,  �    0  �   
 @  �    D  �   
 f  �    j  �   
 �     �    
   �      �   
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   �        �std::shared_ptr<donut::engine::ShaderFactory>::~shared_ptr<donut::engine::ShaderFactory> 
 >nG   this  AJ        +  AJ @       M        �  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �   0   nG  Othis  9+       �   9=       �   O�   0           K    
     $       � �   � �E   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �    
  �   
 |  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �       �       �   ,  � G                       �'        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::~vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > 
 >$c   this  AJ         
 Z   
(                          H�     $c  Othis  O�   (              �            � �    � �,   �    0   �   
 �   �    �   �   
 @  �    D  �   
 @SH冹 H嬞H兞L�
    �   D岯    怘�H吷tH�    H��P怘兡 [�   �       �       �   M  V G            ?      9   �        �nvrhi::ComputePipelineDesc::~ComputePipelineDesc 
 >�%   this  AI  	     5  AJ        	  M        �  # M        �  #CE
 >k$    temp  AJ  &       AJ 9       N N                      0H�  h        �  �   0   �%  Othis  95       E   O   ,   �    0   �   
 {   �       �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 I  �    M  �   
 H塡$WH冹 H嬞H婭 3�H吷tH墈 H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘婯H吷tH墈H��P怘�H吷t
H�;H��P怘媆$0H兡 _�   �   2  I G            |   
   q   �6        �GenerateMipsPass::~GenerateMipsPass 
 >曛   this  AI  
     i  AJ        
  M        �  _ M        �  _CE
 >�&    temp  AJ  b       AJ q       N N M        �  K M        �  KDE
 >�%    temp  AJ  O       AJ _       N N M        �  7 M        �  7DE
 >�    temp  AJ  ;       AJ K       N N M        �  # M        �  #DE
 >�    temp  AJ  '       AJ 7       N N M        �  
 M        �  MDG
 >n#    temp  AJ         AJ #       N N                      @� & h   �  �  �  �  �  �  �  �   0   曛  Othis  9       E   93       E   9G       E   9[       E   9m       E   O  �               |   �            K  �,   �    0   �   
 n   �    r   �   
 ~   �    �   �   
 �   �    �   �   
 �   �    �   �   
 8  �    <  �   
 H  �    L  �   
 �  �    �  �   
 �  �    �  �   
 
  �      �   
   �      �   
 q  �    u  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
   �    "  �   
 .  �    2  �   
 H  �    L  �   
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐wkI嬋�    H荂0    H荂8   艭  H婼H凓v,H�H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �<   �    �   �    �   �       �   |  N G            �      �   �'        �donut::engine::ShaderMacro::~ShaderMacro 
 >霥   this  AI  
     � �   AJ        
  M        J  ITO& M        %  T
,(
	 M        �  T N M        �  ,^E M        3  ^&? M        c  d)
 Z   �  
 >   _Ptr  AJ  a     )  
  >#    _Bytes  AK  d     &  AK �      " M        s  
m#
!
 Z   �   >_    _Ptr_container  AP  q       AP �     #    >_    _Back_shift  AJ  x     
  AJ �       N N N N N N M        J  G$ M        %  -( M        �   N M        �  - M        3  & M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       &  AK �       M        s  
##
 >_    _Ptr_container  AP  '       AP ;     m  c  >_    _Back_shift  AJ  .     
  AJ �       N N N N N N                       @� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN74  0   霥  Othis  O,   �    0   �   
 s   �    w   �   
 �   �    �   �   
 ^  �    b  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �       �   
 &  �    *  �   
 6  �    :  �   
   �      �   
 @  �    D  �   
 P  �    T  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 d  "   h  "  
 H�    H�H兞�       ]      �       �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       ]      �       �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   8                          H�     �  Othis  O  �   (              �             Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   ]      �    0   �       �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   ]      �    0   �       �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �  �   0   �  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   ]      �    0   �       �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @� 
 h   �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 H塡$H塴$H塼$H墊$ ATAVAWH侅�   H�H嬟L嬹H�    H嬎�怭  I婲H��P E3銱孁E孅�0媓D9`嗱   @ I�H峊$`L$@H塂$`H嬎I婩H塂$0D$0H�H荄$X   D$hL墹$�   D$PL$x�$�   �愗   婳H峊$ 3缐L$$H塂$(A�   �H嬎塂$ 婫塂$,H�D墊$(�惃   L�岴橝�   冣H嬎D�岶A柳檭�辛�A�掄   令H嬎嬈�   ;�G鹆�嬇�   ;�G鐷��P0A兦D;����H�H嬎L崪$�   I媅 I媖(I媠0I媨8I嬨A_A^A\H�燲  -   �      �   c  ? G            |  !   T  �6        �GenerateMipsPass::Process 
 >曛   this  AJ        *  AV  *     I > )   commandList  AI  '     9 AK        '  >u     width  A   O     � 
  >�    destDesc  AM  J     " >u     height  A   R     �   >u     sourceMipLevel  Ao  M     $ >�    constants  D     >�&    state  D`    M        �  
�0 N M        �  
�! N M        �  �	 N M        �  ��
 N M        
  �� N M        �  y	
 M        �  y	
 N N �                     @ B h   �  �        
  �  �  �  �  �  �  x  y  �   �   曛  Othis  �    )  OcommandList      �  Oconstants  `   �&  Ostate  94       t)   9A       �   9�       U)   9�       H)   9      V)   9=      *)   9u      *)   O �   �           |  �     �       N  �!   O  �:   Q  �D   W  �`   Z  �c   \  �y   [  ��   \  ��   [  ��   \  ��   _  ��   b  ��   _  ��   b  ��   d  �  f  �  i  �!  f  �+  g  �:  i  �N  l  �T  m  �u  l  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 ?  �    C  �   
 �  �      �   
   �      �   
   �    #  �   
 /  �    3  �   
 ?  �    C  �   
 O  �    S  �   
 _  �    c  �   
 x  �    |  �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       l            �   �   F G                       q        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �      �     
 �   �    �   �   
 H塡$VH冹 H�H嬹H呟teH墊$0H媦H;遲H嬎�    H兠@H;遳颒�H媀H媩$0H+袶冣繦侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媆$8H兡 ^描    �'   �    i   �    �   �       �   �  � G            �   
   �   
(        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Tidy 
 >$c   this  AJ          AL       { t   M        5(  ;*B M        c  G)
 Z   �  
 >   _Ptr  AJ h       >#    _Bytes  AK  ;     O   2  " M        s  
P#
 
 Z   �   >_    _Ptr_container  AP  T     6    AP h       >_    _Back_shift  AJ  7     S 1   AJ h       N N N M        t(  	
 >c   _First  AI  
     ~ r   >霥   _Last  AM       "  N                       H� 2 h   �  s  t  c  4(  5(  t(  �(  �(  )  )         $LN44  0   $c  Othis  O  �   `           �   �  	   T       � �
    �    �4    �m   	 �r   
 �v    �z   
 ��    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �    #  �   
 @  �    D  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 4  �    8  �   
 X  �    \  �   
 �  %   �  %  
 �  �    �  �   
 H冹(H�
    �    �   �      �       �   w   7 G                     ~        坰td::_Xlen_string 
 Z   �!   (                      @        $LN3  O �   (              �            		 �   
	 �,   �    0   �   
 s      w     
 �   �    �   �   
 H婹H�    H呉HE旅   `      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �      $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H                       .    20    2           	      	      4   
 
4 
2p    B           
      
      :    20    <                       @   
 
4 
2p    B                       F    20    <           
      
      L   
 
4 
2p    B                       R    �                              X    B                             ^    t d 4 2�                          d    20    ^                       j    B                   v       "                       p   h           y      |          �    2 B                   �       "                          h           �      �          �    2 20                 �       ?                       �   h           �      �          �    :8 3
 "I
��	��p`0P        2"           �       �                      �   (           �      �   4    �<    �>    .    V    .    V    .    .    R    `2    !:    �:    Av    *    !:    �4    p�    ��    ��    �u    �:    �6    Z    �>    �       �    	   �       �       �       �       �    $   �    )   �    .   �    3   �    9   �    @   �    G   �    N   �    S   �    Z   �    `   �    f   �    m   �    t   �    {   �    �   �    �   �    �   �    �   �    �   �    F4 �.�D��((nD ""N&�*r.82F042@0$2H0d2f0P2$02&0L24.\4,�  2P    -           �       �       �   `r
 
4 
2p                 �       |                       �   h           �      �          �    >� !
 !t !d !T !4 ! ���      |                      �    B                   �       "                       �   h           �      �          �    2 B                   �       "                       �   h           �      �          �    2 B                   �       "                         �   h           �      �          �    2 B                   �       "           !      !      �   h                            �    2 20    �           #      #          20               $      $         ! t               $      $            E           $      $         !                 $      $         E   K           $      $         -
 
4 
2`               &      &      !   ! t               &      &      !      P           &      &      '   !                 &      &      !   P   �           &      &      -    t	 T 4 2�    U           (      (      3   ! d     U          (      (      3   U   �           (      (      9   !       U          (      (      3   �   �           (      (      ?   !   d     U          (      (      3   �             (      (      E   !       U          (      (      3               (      (      K   
 
4 
2p    2           )      )      Q    B      :           +      +      W                               �      �       �    Unknown exception                             �      �       �                                �      �       �    bad array new length                                �       o                                 u      {      �                   .?AVbad_array_new_length@std@@     �               ����                      r      �                    .?AVbad_alloc@std@@     �              ����                      x      �                    .?AVexception@std@@     �               ����                      ~      �    string too long     ����    ����        ��������Initializing GenerateMipsPass... 1 0 INPUT_ENVIRONMENT_MAP main app/RTXDI/PreprocessEnvironmentMap.hlsl GenerateMips vector too long                                       ~      �      �                         �                   �               ����    @                   ~      �                                         x      �      �                         �                           �      �              ����    @                   x      �                                         r      �      �                         �                                   �      �      �              ����    @                   r      �      �   (   & 
�        std::exception::`vftable'    ]      ]  
    �   (   & 
�        std::bad_alloc::`vftable'    c      c  
    �   3   1 
�        std::bad_array_new_length::`vftable'     f      f  
 �"+�a榓覫啺蹞但U刦k網叜月o"0h�K蜌�(N	}Q滔偩�\膴凖�鑒3>飖9屓咄驓�<侭髯P�<��1
嫱k肆峖=f瓵绘鲧碻帘展b�3>FIc＾埙9.襝F7�騷�,�%岹覤泴陷h:筅泌xbshVF疻_蛪�4K駈�昸鳐3鑁裟浈t>��4�9阝廳Tk裊掏;�(� �3覎X@G�&�"朩燹B秩m痺Y+倧A糲�瑝q鞇炟k2火�%;飑嚤踖p禭�?As贛嚤踖p禭飓D堦縵�6萪O�揟喐�~K霵婬(��D腔O��'項j�盡n紼'項jO0懽v
�宴'項j\裠bS篐�'項j u袣觪侲�'項j�!齪�.旂'項j罱@箵s�:z傷┩Q賑茄ex�(繢徨;xOQ耈t>�:b8�4n�:�
皿w�)$愜w獛啯頵�.鞦娔桦�'洋m|�?m�.W灍鲢�6�<#�(棙\A|\�q颔{�3虮5亐煬�0:�6O鴯犷A棊膬/S;圾j硘嶀預棊膬茂#脈虱箩邆5>(H7и篼}箩邆5>螬�0|⒙徇�5>;ㄓ$7� �蹰k�%禾)課伀J�'梶pf黩�D#�%I栶賑?T賗敒7+(4Y癏^塔垅;�^�柝f]{謑p>a�-Gr矾nN鵘J�豈�釋`F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这�5�蘫� 菘U睃-雵J-WV8o��腫62V了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埳S�8萀D罵��$m翲咞taR�,F_棢杻#Q惕屖碋箎�k"�iM桭壄攜dd�a�:P孫归]?j�駕!樢閣yQ朏菜{.鶞盹堡*﨏帲晗D<O鈑�+}咞taR�,F_棢杻#QM�/氤
�汸.  �傁&O笉了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8o�.w⒇衞雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛坁裗呔屸懚獲r貂筦绬靻3;�F瞁:2&櫇�"`Z_餔旉>R啟^@�(潮螣P雟禑)媗ㄣ(PI^ub&鷜3!敲:G火s昖廘�汚$S匜*媵-/�謚帊敲:G火�Ｈ屌座9E\$L釉�3,�4q胭-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H�	愑 濧嶩b5i漩
ψ笥f邅��$樐蜆{絫y|e��9ギ�'g��%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �;� タY�脌祦�
監�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       P                .debug$S                     .debug$T       l                 .rdata         @       5G碚                         7          .text$mn       :      眡�     .debug$S                    .text$mn            �<N�     .debug$S       P  2           .text$mn    	          �邆     .debug$S    
            	    .text$mn            0润�     .debug$S         4           .text$mn    
          恶Lc     .debug$S       �          
    .text$mn       �  +   雎F�     .debug$S       T?  >          .text$x              1.b�    .text$x              )尯     .text$x              烂菨    .text$x              6CBr    .text$x              [��    .text$x              汥8v    .text$x              "E萷    .text$x              9'轔    .text$x              殽/�    .text$x        -      羣6[    .text$x              鲉k�    .text$x              瀎s�    .text$x              ��    .text$x              v'�)    .text$x              %FZ�    .text$x               y-�#    .text$x     !         W腡�    .text$mn    "   <      .ズ     .debug$S    #   0  
       "    .text$mn    $   <      .ズ     .debug$S    %   L  
       $    .text$mn    &   !      :著�     .debug$S    '   <         &    .text$mn    (   2      X于     .debug$S    )   <         (    .text$mn    *   "       坼	     .debug$S    +   �         *    .text$mn    ,   "       坼	     .debug$S    -   �         ,    .text$mn    .   "       坼	     .debug$S    /   �         .    .text$mn    0   "       坼	     .debug$S    1   �         0    .text$mn    2   "       坼	     .debug$S    3   �         2    .text$mn    4   "       坼	     .debug$S    5   �         4    .text$mn    6   
      m張�     .debug$S    7   �         6    .text$mn    8   2      �<�     .debug$S    9   �         8    .text$mn    :   ^      wP�     .debug$S    ;   X         :    .text$mn    <   K       }'     .debug$S    =   �         <    .text$mn    >         �%     .debug$S    ?   h         >    .text$mn    @   ?      劸惂     .debug$S    A   \         @    .text$mn    B   |       �'D�     .debug$S    C   h  &       B    .text$mn    D   �      f綛a     .debug$S    E   �  $       D    .text$mn    F         ��#     .debug$S    G   �          F    .text$mn    H         ��#     .debug$S    I   �          H    .text$mn    J   B      贘S     .debug$S    K             J    .text$mn    L   B      贘S     .debug$S    M            L    .text$mn    N   B      贘S     .debug$S    O   �          N    .text$mn    P   H       襶.      .debug$S    Q   �         P    .text$mn    R   |     ��/     .debug$S    S   H  "       R    .text$mn    T          aJ鄔     .debug$S    U   �          T    .text$mn    V   �      8耾^     .debug$S    W   H         V    .text$mn    X         �ッ     .debug$S    Y   �          X    .text$mn    Z         崪覩     .debug$S    [   �          Z        \       P        x                �                �                �                �                �                �                               1               I      (        j      H        �      Z        �      N        �          i�                    �      "              J        "          i�                    A      &        f      F        �      $        �      L        �          i�                          T        3      X        L              �      :        �      
              2        5      	        m      *        �      @        �              <      B        Y      R        �      .        �      ,              4        5      0        g      D        �               V               
               )      <        d      >        �      V        .	              �	      6        
      8        b
              �
              7              �              9              �              ;
              �
              <              �               >      !        �              ?              �              ?              �              ?              �              ?               R               e           __chkstk             z           memcpy           memset           $LN13       P    $LN5        (    $LN10       N    $LN7        "    $LN13       J    $LN10       $    $LN16       L    $LN3        T    $LN4        T    $LN3       X    $LN4        X    $LN106        $LN111          $LN35   ^   :    $LN38       :    $LN10       2    $LN10       *    $LN18       @    $LN640  �      $LN646          $LN36       B    $LN40       R    $LN10       .    $LN10       ,    $LN10       4    $LN10       0    $LN74   �   D    $LN77       D    $LN18       <    $LN44   �   V    $LN47       V    $LN87         $LN92           $LN22       8    $LN14   :       $LN17           .xdata      \          F┑@P        �      \    .pdata      ]         X賦鶳        �      ]    .xdata      ^          （亵(        �      ^    .pdata      _          T枨(              _    .xdata      `          %蚘%N        *      `    .pdata      a         惻竗N        Q      a    .xdata      b          （亵"        w      b    .pdata      c         2Fb�"        �      c    .xdata      d          %蚘%J        �      d    .pdata      e         惻竗J        �      e    .xdata      f          （亵$              f    .pdata      g         2Fb�$        I      g    .xdata      h          %蚘%L        |      h    .pdata      i         惻竗L        �      i    .xdata      j          懐j濼        �      j    .pdata      k         Vbv鵗              k    .xdata      l          �9�X        >      l    .pdata      m         �1癤        _      m    .xdata      n          �F�              n    .pdata      o         *!)	        �      o    .xdata      p          （亵:        ,      p    .pdata      q         翎珸:        |      q    .xdata      r         /
�2        �      r    .pdata      s         +eS�2              s    .xdata      t   	      �#荤2        <      t    .xdata      u         j2        w      u    .xdata      v          3狷 2        �      v    .xdata      w         /
�*        �      w    .pdata      x         +eS�*        3      x    .xdata      y   	      �#荤*        r      y    .xdata      z         j*        �      z    .xdata      {          3狷 *        �      {    .xdata      |         蚲7M@        >      |    .pdata      }         袮韁@        l      }    .xdata      ~   	      �#荤@        �      ~    .xdata               j@        �          .xdata      �          愔
~@        �      �    .xdata      �   $      鍁�        /      �    .pdata      �         咜q�        �      �    .xdata      �   	      � )9               �    .xdata      �   �      {w疒        �      �    .xdata      �   J       彣�              �    .xdata      �          k�        �      �    .pdata      �         噖sb               �    .voltbl     �          Qo�    _volmd      �    .xdata      �         �酑B        �      �    .pdata      �         邉�鸅        �      �    .xdata      �   	      �#荤B        �      �    .xdata      �         jB              �    .xdata      �          褎�,B        E      �    .xdata      �           "R        l      �    .pdata      �         k張]R        �      �    .xdata      �         /
�.        �      �    .pdata      �         +eS�.        3       �    .xdata      �   	      �#荤.        t       �    .xdata      �         j.        �       �    .xdata      �          3狷 .        !      �    .xdata      �         /
�,        F!      �    .pdata      �         +eS�,        �!      �    .xdata      �   	      �#荤,        �!      �    .xdata      �         j,        �!      �    .xdata      �          3狷 ,        C"      �    .xdata      �         /
�4        �"      �    .pdata      �         +eS�4        �"      �    .xdata      �   	      �#荤4        �"      �    .xdata      �         j4        1#      �    .xdata      �          3狷 4        s#      �    .xdata      �         /
�0        �#      �    .pdata      �         +eS�0        �#      �    .xdata      �   	      �#荤0        "$      �    .xdata      �         j0        ^$      �    .xdata      �          3狷 0        �$      �    .xdata      �          （亵D        �$      �    .pdata      �         礑        	%      �    .xdata      �          （亵<        5%      �    .pdata      �         � �<        x%      �    .xdata      �         范^�<        �%      �    .pdata      �         鳶�<        �%      �    .xdata      �         @鴚`<        B&      �    .pdata      �         [7�<        �&      �    .voltbl     �          飾殪<    _volmd      �    .xdata      �          �搀V        �&      �    .pdata      �         O?[4V        9'      �    .xdata      �         T�%~V        �'      �    .pdata      �         *i澚V        (      �    .xdata      �         Ｕ峍        �(      �    .pdata      �         ��*2V        �(      �    .xdata      �          �-th        g)      �    .pdata      �         �        �)      �    .xdata      �         銎�        8*      �    .pdata      �         �g�        �*      �    .xdata      �         N懁        +      �    .pdata      �         
        v+      �    .xdata      �         Z�	W        �+      �    .pdata      �         敵4        J,      �    .xdata      �         N懁        �,      �    .pdata      �         赴t        -      �    .xdata      �          %蚘%8        �-      �    .pdata      �          T枨8        �-      �    .xdata      �          �9�        M.      �    .pdata      �         礝
        �.      �    .rdata      �                      /     �    .rdata      �          �;�         /      �    .rdata      �                      D/     �    .rdata      �                      [/     �    .rdata      �          �)         }/      �    .xdata$x    �                      �/      �    .xdata$x    �         虼�)         �/      �    .data$r     �   /      嶼�         �/      �    .xdata$x    �   $      4��         0      �    .data$r     �   $      鎊=         h0      �    .xdata$x    �   $      銸E�         �0      �    .data$r     �   $      騏糡         �0      �    .xdata$x    �   $      4��         �0      �        1           .rdata      �          燺渾         -1      �    .data       �           烀�          S1      �        �1     �    .rdata      �   !       d锲         �1      �    .rdata      �          �]�         �1      �    .rdata      �          �6F�         �1      �    .rdata      �          �N�         
2      �    .rdata      �          旲^         72      �    .rdata      �   (       1	�         N2      �    .rdata      �   
       R骨q         �2      �    .rdata      �          IM         �2      �    .rdata$r    �   $      'e%�         �2      �    .rdata$r    �         �          �2      �    .rdata$r    �                      �2      �    .rdata$r    �   $      Gv�:         3      �    .rdata$r    �   $      'e%�         -3      �    .rdata$r    �         }%B         E3      �    .rdata$r    �                      [3      �    .rdata$r    �   $      `         q3      �    .rdata$r    �   $      'e%�         �3      �    .rdata$r    �         �弾         �3      �    .rdata$r    �                      �3      �    .rdata$r    �   $      H衡�         �3      �        4           _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �   8                14  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ??0BindingSetItem@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1ComputePipelineDesc@nvrhi@@QEAA@XZ ??0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z ??1GenerateMipsPass@@QEAA@XZ ?Process@GenerateMipsPass@@QEAAXPEAVICommandList@nvrhi@@@Z ??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ ??1ShaderMacro@engine@donut@@QEAA@XZ ?CreateShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ?CreateBindingSetAndLayout@utils@nvrhi@@YA_NPEAVIDevice@2@W4ShaderType@2@IAEBUBindingSetDesc@2@AEAV?$RefCountPtr@VIBindingLayout@nvrhi@@@2@AEAV?$RefCountPtr@VIBindingSet@nvrhi@@@2@@Z ?debug@log@donut@@YAXPEBDZZ ??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ ?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ ??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ??1?$_Tidy_guard@V?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@@std@@QEAA@XZ ??1?$_Uninitialized_backout_al@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?dtor$0@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$11@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$12@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$13@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$14@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$1@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$23@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$24@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$25@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$28@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$2@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$3@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$4@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$5@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$7@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$8@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA ?dtor$9@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $pdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $unwind$??0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z $pdata$??0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z $cppxdata$??0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z $stateUnwindMap$??0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z $ip2state$??0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z $unwind$?dtor$11@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA $pdata$?dtor$11@?0???0GenerateMipsPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@PEAVITexture@2@2@Z@4HA $unwind$??1GenerateMipsPass@@QEAA@XZ $pdata$??1GenerateMipsPass@@QEAA@XZ $cppxdata$??1GenerateMipsPass@@QEAA@XZ $stateUnwindMap$??1GenerateMipsPass@@QEAA@XZ $ip2state$??1GenerateMipsPass@@QEAA@XZ $unwind$?Process@GenerateMipsPass@@QEAAXPEAVICommandList@nvrhi@@@Z $pdata$?Process@GenerateMipsPass@@QEAAXPEAVICommandList@nvrhi@@@Z $unwind$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1ShaderMacro@engine@donut@@QEAA@XZ $pdata$??1ShaderMacro@engine@donut@@QEAA@XZ $unwind$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $unwind$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $pdata$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $chain$0$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $pdata$0$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $chain$1$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $pdata$1$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $unwind$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $unwind$??1?$_Uninitialized_backout_al@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$_Uninitialized_backout_al@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0CB@JMEFCKBG@Initializing?5GenerateMipsPass?4?4@ ??_C@_01HIHLOKLC@1@ ??_C@_01GBGANLPD@0@ ??_C@_0BG@OEJIEILA@INPUT_ENVIRONMENT_MAP@ ??_C@_04GHJNJNPO@main@ ??_C@_0CI@BHNPMLHP@app?1RTXDI?1PreprocessEnvironment@ ??_C@_0N@IBEMAACP@GenerateMips@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __security_cookie 