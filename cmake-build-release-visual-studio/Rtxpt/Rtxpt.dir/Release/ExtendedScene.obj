d嗺荏Gh澨 
	      .drectve        P  瑅               
 .debug$S        8a 黽  4�     
   @ B.debug$T        l   樫             @ B.rdata          L   �             @ @@.text$mn           P� f�         P`.debug$S           p� 愛     
   @B.text$mn        b   糅 V�         P`.debug$S        �  j� R�        @B.text$mn           兽 廪         P`.debug$S           燹 �     
   @B.text$mn        d   p� 脏         P`.debug$S        �  栲 遭        @B.text$mn        -   L� y�         P`.debug$S        8  冦 讳     
   @B.text$mn        �   � ゅ         P`.debug$S        X  噱 8�        @B.text$mn        _   (� 囮         P`.debug$S        �  涥 囲        @B.text$mn           ��              P`.debug$S        0  � 7�        @B.text$mn           囶              P`.debug$S        0  忣 匡        @B.text$mn           �              P`.debug$S        @  � W�        @B.text$mn        :   я 狁         P`.debug$S          �� �        @B.text$mn        j   楐 �         P`.debug$S        �  � �        @B.text$mn        j   � 轺         P`.debug$S        �  � 簌        @B.text$mn        j   k� 怔         P`.debug$S        �  篾 唿        @B.text$mn        �   W� C�         P`.debug$S        T  u� �        @B.text$mn        �   A �         P`.debug$S        �        
   @B.text$mn        �   h          P`.debug$S        �  5 %     
   @B.text$mn           �              P`.debug$S        P  � �	        @B.text$mn        (   0
              P`.debug$S        �  X
 <        @B.text$mn        �  � �         P`.debug$S        P  � �        @B.text$mn        <    H         P`.debug$S        0  f �     
   @B.text$mn        <   � 6         P`.debug$S        L  T �     
   @B.text$mn        !    %         P`.debug$S        <  9 u        @B.text$mn        2   � �         P`.debug$S        <  � 3        @B.text$mn           � �         P`.debug$S          � �        @B.text$mn                      P`.debug$S           !        @B.text$mn           ] h         P`.debug$S          r v        @B.text$mn           � �         P`.debug$S          � �        @B.text$mn                       P`.debug$S          (  0!        @B.text$mn           l! w!         P`.debug$S          �! �"        @B.text$mn           �" �"         P`.debug$S          �" �#        @B.text$mn        K   .$              P`.debug$S        �  y$ E&        @B.text$mn        K   �&              P`.debug$S        �  ' �(        @B.text$mn        K   d)              P`.debug$S        �  �) s+        @B.text$mn        K   �+              P`.debug$S        �  J, .        @B.text$mn        �   �. K/         P`.debug$S        �  _/ 4         @B.text$mn        �   W5 $6         P`.debug$S          86 @;     "   @B.text$mn        �   �< a=         P`.debug$S        �  u= aB     "   @B.text$mn        r   礐              P`.debug$S        8  'D _F        @B.text$mn        I  G \I         P`.debug$S        T
  zI 蜸     V   @B.text$mn        K   *W              P`.debug$S        �  uW )Y        @B.text$mn        K   礩              P`.debug$S        �   Z 腫        @B.text$mn          P\ Q]         P`.debug$S        �  o] gb     &   @B.text$mn        W   鉩 :d         P`.debug$S        �  Xd f        @B.text$mn            爁              P`.debug$S        p  纅 0h     
   @B.text$mn           攈              P`.debug$S        �   梙 wi        @B.text$mn           砳 苅         P`.debug$S        �   趇 緅        @B.text$mn           鎗 鵭         P`.debug$S        �   
k 韐        @B.text$mn        �   )l 磍         P`.debug$S        �  萳 宲        @B.text$mn        +   |q          P`.debug$S        �   籷 焤        @B.text$mn        +   踨 s         P`.debug$S        �   s t        @B.text$mn        +   >t it         P`.debug$S        �   }t au        @B.text$mn        +   漸 萿         P`.debug$S        �   躸 膙        @B.text$mn        +    w +w         P`.debug$S        �   ?w #x        @B.text$mn        +   _x 妜         P`.debug$S        �   瀤 妝        @B.text$mn        +   苰 駓         P`.debug$S        �   z 韟        @B.text$mn        �   ){ 瞷         P`.debug$S        �  趝 獈        @B.text$mn        �   ^ �         P`.debug$S        �  �         @B.text$mn        4   搫 莿         P`.debug$S        �   蹌         @B.text$mn        4   銋 �         P`.debug$S        �   +� 麊        @B.text$mn        �   7� 紘         P`.debug$S        �  袊 l�        @B.text$mn          鋲 ��         P`.debug$S        �  	� 墢     *   @B.text$mn        z   -�          P`.debug$S        �  睉 }�        @B.text$mn        z   鯎 o�         P`.debug$S        �  y� Y�        @B.text$mn        4   褨 �         P`.debug$S        �   � 闂        @B.text$mn        y   %� 灅         P`.debug$S        �  茦 帤        @B.text$mn        N   � T�         P`.debug$S        �  ^� �        @B.text$mn        N   z� 葷         P`.debug$S        �  覞 j�        @B.text$mn        N   鉄 0�         P`.debug$S        �  :� 啤        @B.text$mn        N   >� 將         P`.debug$S        �  枹 .�        @B.text$mn        N   Δ 簸         P`.debug$S        �   枽        @B.text$mn        B   � P�         P`.debug$S           n� n�        @B.text$mn        B    歙         P`.debug$S          
� �        @B.text$mn        B   V� 槳         P`.debug$S        �   丢 搏        @B.text$mn        H   瞰              P`.debug$S        �  6�         @B.text$mn        �   �  �         P`.debug$S        �  � 舸     (   @B.text$x            劧 惗         P`.text$mn        5   毝 隙         P`.debug$S        �  俣 q�        @B.text$mn        �    5�         P`.debug$S          ?� [�     
   @B.text$mn        5   炕 艋         P`.debug$S        �   柦        @B.text$mn        �  医 r�         P`.debug$S          0� L�     6   @B.text$mn        �   h� �         P`.debug$S        �  C� �        @B.text$mn           椨 吩         P`.debug$S        H  咴 '�        @B.text$mn        �   熦 U�         P`.debug$S        L  s� 扣        @B.text$mn           7� U�         P`.debug$S        d  _� 幂     
   @B.text$mn        Q  '� x�         P`.debug$S        �   傡     Z   @B.text$x            � �         P`.text$x            � (�         P`.text$mn           2�              P`.debug$S        �   8� �        @B.text$mn           T�              P`.debug$S        �   Z� >�        @B.text$mn           z�              P`.debug$S        �   }� e�        @B.text$mn           ◇              P`.debug$S        �    �        @B.text$mn        3   霍 铘         P`.debug$S        h  � j�        @B.text$mn           流         P`.debug$S        �  k� #�     "   @B.text$mn        �   w� *�         P`.debug$S        �  z� b        @B.text$x            � �         P`.text$mn        �  � T         P`.debug$S          & *
         @B.text$mn        �  j =     (    P`.debug$S        �
  � ]     4   @B.text$mn           e              P`.debug$S          h �         @B.text$mn          �  �!         P`.debug$S        p  " q)     &   @B.text$x            �* �*         P`.text$x            + +         P`.text$mn          +  -         P`.debug$S        
  f- z7     0   @B.text$mn           Z9              P`.debug$S        L  ]9 �:     
   @B.text$mn           
; ;         P`.debug$S        L  ; h<     
   @B.text$mn           �<              P`.debug$S        X  �< '>     
   @B.text$mn           �>              P`.debug$S          �> �?        @B.text$mn           �?              P`.debug$S            @  A        @B.text$mn           PA              P`.debug$S          bA fB        @B.text$mn           禕              P`.debug$S           菳 菴        @B.text$mn           D              P`.debug$S          *D .E        @B.text$mn           ~E              P`.debug$S           怑 怓        @B.text$mn           郌              P`.debug$S          騀 鶪        @B.text$mn           JH              P`.debug$S          \H `I        @B.text$mn        
   癐              P`.debug$S        <  絀 鵍     
   @B.text$mn        
   ]K              P`.debug$S        @  jK 狶     
   @B.text$mn        
   M              P`.debug$S        <  M WN     
   @B.text$mn        
   籒              P`.debug$S        @  萅 P     
   @B.text$mn        
   lP              P`.debug$S        <  yP 礠     
   @B.text$mn        
   R              P`.debug$S        D  &R jS     
   @B.text$mn        
   蜸              P`.debug$S        @  跾 U     
   @B.text$mn           U              P`.debug$S        ,  俇 甐        @B.text$mn             W         P`.debug$S        �   <W  X        @B.text$mn           <X MX         P`.debug$S        �   aX Y        @B.text$mn        `  QY 盳         P`.debug$S        �  [ 乧     B   @B.text$mn           f (f         P`.debug$S        �   2f g        @B.xdata             Bg             @0@.pdata             Vg bg        @0@.xdata             �g             @0@.pdata             坓 攇        @0@.xdata             瞘             @0@.pdata             緂 蔳        @0@.xdata             鑗             @0@.pdata             餲 黦        @0@.xdata             h             @0@.pdata             &h 2h        @0@.xdata             Ph             @0@.pdata             Xh dh        @0@.xdata             俬             @0@.pdata             巋 歨        @0@.xdata             竓             @0@.pdata             纇 蘦        @0@.xdata             阧             @0@.pdata             騢         @0@.xdata             i             @0@.pdata             (i 4i        @0@.xdata             Ri             @0@.pdata             fi ri        @0@.xdata             恑         @0@.pdata             耰 蝘        @0@.xdata             靑 黫        @0@.pdata             j &j        @0@.xdata             Dj Xj        @0@.pdata             vj 俲        @0@.xdata             爅 竕        @0@.pdata             蘪 豭        @0@.xdata          	   鰆 �j        @@.xdata             k k        @@.xdata             #k             @@.voltbl            &k                .xdata             Bk             @0@.pdata             Nk Zk        @0@.xdata             xk             @0@.pdata             �k 宬        @0@.xdata             猭 緆        @0@.pdata             躪 鑛        @0@.xdata             l l        @0@.pdata             4l @l        @0@.voltbl            ^l               .xdata             `l             @0@.pdata             hl tl        @0@.xdata             抣         @0@.pdata             膌 衛        @0@.xdata             頻         @0@.pdata             m (m        @0@.voltbl            Fm               .xdata             Hm             @0@.pdata             Pm \m        @0@.xdata             zm 抦        @0@.pdata             癿 糾        @0@.xdata             趍 頼        @0@.pdata             n n        @0@.xdata             6n Fn        @0@.pdata             dn pn        @0@.xdata             巒 瀗        @0@.pdata             糿 萵        @0@.voltbl            鎛               .xdata             鑞             @0@.pdata             鬾  o        @0@.xdata             o 2o        @0@.pdata             Po \o        @0@.xdata             zo 巓        @0@.pdata             琽 竜        @0@.xdata             謔 鎜        @0@.pdata             p p        @0@.xdata             .p >p        @0@.pdata             \p hp        @0@.voltbl            唒               .xdata             坧 爌        @0@.pdata             磒 纏        @0@.xdata          	   辮 鏿        @@.xdata             鹥 q        @@.xdata             q             @@.voltbl            q               .xdata             q             @0@.pdata             q *q        @0@.voltbl            Hq               .xdata             Iq             @0@.pdata             Uq aq        @0@.voltbl            q               .xdata             �q             @0@.pdata             宷 榪        @0@.voltbl            秖               .xdata             穛             @0@.pdata             胵 蟩        @0@.voltbl            韖               .xdata             顀             @0@.pdata              
r        @0@.voltbl            (r               .xdata             +r             @0@.pdata             7r Cr        @0@.xdata             ar             @0@.pdata             ir ur        @0@.xdata             搑         @0@.pdata             舝 裷        @0@.xdata             飏 �r        @0@.pdata             s )s        @0@.voltbl            Gs               .xdata             Is             @0@.pdata             Us as        @0@.xdata             s 搒        @0@.pdata             眘 絪        @0@.xdata             踫 飐        @0@.pdata             
t t        @0@.xdata             7t Gt        @0@.pdata             et qt        @0@.xdata             弔 焧        @0@.pdata             絫 蓆        @0@.voltbl            鐃               .xdata             閠 u        @0@.pdata             u u        @0@.xdata             5u Mu        @0@.pdata             au mu        @0@.xdata          	   媢 攗        @@.xdata             ╱ 痷        @@.xdata             箄             @@.voltbl            緐               .xdata             羥             @0@.pdata             蓇 誹        @0@.xdata             髐             @0@.pdata             �u v        @0@.xdata             )v             @0@.pdata             5v Av        @0@.xdata             _v             @0@.pdata             ov {v        @0@.xdata             檝             @0@.pdata              眝        @0@.voltbl            蟰               .xdata             衯             @0@.pdata             豽 鋠        @0@.xdata             w             @0@.pdata             w "w        @0@.xdata             @w             @0@.pdata             Lw Xw        @0@.voltbl            vw               .xdata             ww             @0@.pdata             w 媤        @0@.xdata             ﹚ 羨        @0@.pdata             誻 醱        @0@.xdata          	   �w x        @@.xdata             x #x        @@.xdata             -x             @@.xdata             4x             @0@.pdata             @x Lx        @0@.voltbl            jx               .xdata             kx             @0@.pdata             儀 弜        @0@.voltbl            瓁                .xdata             眡             @0@.pdata             義 蛒        @0@.xdata             離 �x        @0@.pdata             y )y        @0@.xdata             Gy Wy        @0@.pdata             uy 亂        @0@.xdata             焬             @0@.pdata             痽 粂        @0@.xdata             賧 韞        @0@.pdata             z z        @0@.xdata             5z Ez        @0@.pdata             cz oz        @0@.xdata             峼             @0@.pdata             檢         @0@.xdata             脄 踷        @0@.pdata             飠 鹺        @0@.xdata          	   { "{        @@.xdata          
   6{ C{        @@.xdata             W{             @@.voltbl            ^{               .xdata              d{ 剓        @0@.pdata             榹         @0@.xdata          	   聓 藍        @@.xdata             遻 鍆        @@.xdata             飡             @@.voltbl            鮷                .xdata          $   
| 1|        @0@.pdata             E| Q|        @0@.xdata          	   o| x|        @@.xdata             寍 焲        @@.xdata             絴             @@.voltbl            脇                .xdata             讄             @0@.pdata             遼 雦        @0@.xdata             	} !}        @0@.pdata             ?} K}        @0@.xdata             i} }}        @0@.pdata             泒         @0@.xdata             舽 諁        @0@.pdata             髛 �}        @0@.xdata             ~ -~        @0@.pdata             K~ W~        @0@.voltbl            u~               .xdata             w~ 搤        @0@.pdata              硚        @0@.xdata          	   褈 趡        @@.xdata             額 魚        @@.xdata                          @@.voltbl                           .xdata             	             @0@.pdata                      @0@.xdata             ; O        @0@.pdata             m y        @0@.xdata             � �        @0@.pdata             � �        @0@.voltbl            �               .xdata             �             @0@.pdata             � �        @0@.xdata             #� ;�        @0@.pdata             Y� e�        @0@.xdata             儉 梹        @0@.pdata             祤 羳        @0@.xdata             還 飥        @0@.pdata             
� �        @0@.xdata             7� G�        @0@.pdata             e� q�        @0@.voltbl            弫               .xdata             憗 瓉        @0@.pdata             羴 蛠        @0@.xdata          	   雭 魜        @@.xdata             � �        @@.xdata             �             @@.xdata             � +�        @0@.pdata             ?� K�        @0@.xdata          	   i� r�        @@.xdata             唫 寕        @@.xdata             杺             @@.xdata             檪 瓊        @0@.pdata             羵 蛡        @0@.xdata          	   雮 魝        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             '� 3�        @0@.xdata             Q�             @0@.pdata             Y� e�        @0@.xdata             儍 梼        @0@.pdata             祪 羶        @0@.xdata             邇 飪        @0@.pdata             
� �        @0@.voltbl            7�               .voltbl            9�               .xdata             :�             @0@.pdata             B� N�        @0@.xdata             l�             @0@.pdata             t� ��        @0@.xdata             瀯             @0@.pdata              矂        @0@.xdata             袆 鄤        @0@.pdata             陝 鰟        @0@.xdata             �             @0@.pdata              � ,�        @0@.xdata             J�             @0@.pdata             V� b�        @0@.xdata             �� 攨        @0@.pdata             ▍ 磪        @0@.xdata             覅 讌        @@.xdata             釁             @@.voltbl            鋮               .xdata             鍏 鶇        @0@.pdata             
� �        @0@.xdata             7� <�        @@.xdata             F�             @@.voltbl            I�               .xdata             J�             @0@.pdata             R� ^�        @0@.xdata             |� 悊        @0@.pdata             畣 簡        @0@.xdata             貑 鑶        @0@.pdata             � �        @0@.voltbl            0�               .xdata             2� F�        @0@.pdata             Z� f�        @0@.xdata             剣 墖        @@.xdata             搰             @@.voltbl            枃               .xdata             棁             @0@.pdata              瘒        @0@.xdata             蛧             @0@.pdata             賴 鍑        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             9�             @0@.pdata             A� M�        @0@.xdata             k�             @0@.pdata             s� �        @0@.xdata             潏             @0@.pdata              眻        @0@.xdata             蠄             @0@.pdata             讏 銏        @0@.xdata             �             @0@.pdata             	� �        @0@.xdata             3�             @0@.pdata             ;� G�        @0@.xdata             e�             @0@.pdata             m� y�        @0@.xdata             棄             @0@.pdata             焿 珘        @0@.rdata             蓧 釅        @@@.rdata             ��             @@@.rdata             � )�        @@@.rdata             G� _�        @@@.rdata             }�             @@@.xdata$x           拪 畩        @@@.xdata$x           聤 迠        @@@.data$r         /   鼕 +�        @@�.xdata$x        $   5� Y�        @@@.data$r         $   m� 憢        @@�.xdata$x        $   泲 繈        @@@.data$r         $   計 鲖        @@�.xdata$x        $   � %�        @@@.rdata             9�             @@@.data               I�             @ @�.rdata             i� y�        @@@.rdata             崒 潓        @@@.rdata             睂 翆        @@@.rdata          8   諏 
�        @@@.rdata          8   S� 媿        @@@.rdata          8   褝 	�        @@@.rdata          P   O� 煄     
   @@@.rdata          P   � S�     
   @@@.rdata          8   窂 飶        @@@.rdata          8   5� m�        @@@.rdata          8   硱 霅        @@@.rdata             1� A�        @@@.rdata             U� e�        @@@.rdata             y� 墤        @@@.rdata             潙             @@@.rdata          
   珣             @@@.rdata          	   笐             @@@.rdata             翍             @0@.rdata             茟             @@@.rdata             讘             @@@.rdata             閼             @@@.rdata             龖             @@@.rdata             �             @@@.rdata          
   �             @@@.rdata             '�             @@@.rdata             :�             @@@.rdata             O�             @@@.rdata             ]�             @@@.rdata             n�             @@@.rdata          
   �             @@@.rdata             寬             @@@.rdata             潚             @@@.rdata             瑨             @@@.rdata             粧             @@@.rdata             蕭             @@@.rdata             鄴             @@@.rdata             霋             @@@.rdata             �             @@@.rdata              �             @@@.rdata          (   /� W�        @@@.rdata          (   墦 睋        @@@.rdata          (   銚 �        @@@.rdata          (   =� e�        @@@.rdata          (   棓 繑        @@@.rdata          (   駭 �        @@@.rdata          (   K� s�        @@@.data$r         2    讜        @@�.data$r         %   釙 �        @@�.data$r         #   � 3�        @@�.data$r         )   =� f�        @@�.data$r         '   p� 棖        @@�.rdata$r        $    艝        @@@.rdata$r           銝 鳀        @@@.rdata$r           � 
�        @@@.rdata$r        $   � ;�        @@@.rdata$r        $   O� s�        @@@.rdata$r           憲         @@@.rdata$r           瘲 脳        @@@.rdata$r        $   讞 麠        @@@.rdata$r        $   � 3�        @@@.rdata$r           Q� e�        @@@.rdata$r           o� 嫎        @@@.rdata$r        $    蜆        @@@.data$rs        *   針 �        @@�.rdata$r           � )�        @@@.rdata$r           3� ?�        @@@.rdata$r        $   I� m�        @@@.rdata$r        $   仚         @@@.data$rs        ,   脵 餀        @@�.rdata$r           鶛 
�        @@@.rdata$r           � #�        @@@.rdata$r        $   -� Q�        @@@.rdata$r        $   e� 墯        @@@.data$rs        0    讱        @@�.rdata$r           釟 鯕        @@@.rdata$r           �� �        @@@.rdata$r        $   � 9�        @@@.rdata$r        $   M� q�        @@@.data$rs        ,   彌 粵        @@�.rdata$r           艣 贈        @@@.rdata$r           銢 餂        @@@.rdata$r        $   鶝 �        @@@.rdata$r        $   1� U�        @@@.rdata$r           s� 嚋        @@@.rdata$r           憸 潨        @@@.rdata$r        $    藴        @@@.rdata$r        $   邷 �        @@@.data$rs        /   !� P�        @@�.rdata$r           Z� n�        @@@.rdata$r           x� 対        @@@.rdata$r        $   牆 臐        @@@.rdata$r        $   貪 鼭        @@@.data$rs        5   � O�        @@�.rdata$r           Y� m�        @@@.rdata$r           w� 摓        @@@.rdata$r        $   睘 諡        @@@.rdata$r        $   闉 
�        @@@.rdata$r           +� ?�        @@@.rdata$r           I� ]�        @@@.rdata$r        $   q� 暉        @@@.rdata$r        $    蜔        @@@.rdata$r           霟 ��        @@@.rdata$r           	� %�        @@@.rdata$r        $   C� g�        @@@.rdata$r        $   {� 煚        @@@.data$rs        *   綘 鐮        @@�.rdata$r           駹 �        @@@.rdata$r        $   � 3�        @@@.rdata$r        $   [� �        @@@.rdata$r        $   摗 贰        @@@.rdata$r           铡 椤        @@@.rdata$r           蟆 �        @@@.rdata$r        $   � ?�        @@@.rdata$r        $   S� w�        @@@.rdata$r           暍         @@@.rdata$r           尝 洽        @@@.rdata$r        $   邰 ��        @@@.rdata$r        $   � 7�        @@@.data$rs        !   U� v�        @@�.rdata$r           �� 敚        @@@.rdata$r           灒 玻        @@@.rdata$r        $   疲 辏        @@@.rdata$r        $    "�        @@@.data$rs        %   @� e�        @@�.rdata$r           o� 儰        @@@.rdata$r           崵 ·        @@@.rdata$r        $   丹 伽        @@@.rdata$r        $   恧 �        @@@.data$rs        !   /� P�        @@�.rdata$r           Z� n�        @@@.rdata$r           x� 尌        @@@.rdata$r        $   牓 磨        @@@.rdata$r        $   廿         @@@.data$rs        @   � Z�        @P�.rdata$r           d� x�        @@@.rdata$r           偊 枽        @@@.rdata$r        $    桅        @@@.rdata$r        $   猞 �        @@@.data$rs        C   $� g�        @P�.rdata$r           q� 収        @@@.rdata$r           彠 ＇        @@@.rdata$r        $   阀 郄        @@@.rdata$r        $   铵 �        @@@.data$rs        >   1� o�        @@�.rdata$r           y� 崹        @@@.rdata$r           棬         @@@.rdata$r        $   卡 悒        @@@.rdata$r        $   鳕 �        @@@.data$rs        <   9� u�        @@�.rdata$r           � 摡        @@@.rdata$r           潻 暴        @@@.rdata$r        $   农 椹        @@@.rdata$r        $    !�        @@@.data$rs        :   ?� y�        @@�.rdata$r           儶 棯        @@@.rdata$r           — 氮        @@@.rdata$r        $   瑟 愍        @@@.rdata$r        $   � %�        @@@.data$rs        >   C� 伀        @@�.rdata$r           嫬 煫        @@@.rdata$r           ┇ 将        @@@.rdata$r        $   勋 醌        @@@.rdata$r        $   	� -�        @@@.data$rs        :   K� 叕        @@�.rdata$r           彫 ，        @@@.rdata$r            连        @@@.rdata$r        $   宅         @@@.rdata             
�             @0@.rdata             �             @0@.debug$S        <   � Q�        @B.debug$S        0   e� 暛        @B.debug$S        D   ┉ 憝        @B.debug$S        @   � A�        @B.debug$S        4   U� 壆        @B.debug$S        4   澁 旬        @B.debug$S        4   瀹 �        @B.debug$S        @   -� m�        @B.debug$S        0   伅 悲        @B.debug$S        8   暖         @B.debug$S        <   � M�        @B.debug$S        4   a� 暟        @B.debug$S        4   ┌ 莅        @B.debug$S        @   癜 1�        @B.debug$S        D   E� 壉        @B.debug$S        L   澅 楸        @B.debug$S        P    M�        @B.debug$S        L   a�         @B.debug$S        H   敛 	�        @B.debug$S        H   � e�        @B.debug$S        L   y� 懦        @B.debug$S        H   俪 !�        @B.debug$S        @   5� u�        @B.debug$S        H   壌 汛        @B.chks64         �  宕              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   �  [     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\ExtendedScene.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $tf  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Ensure_adl  $_Weak_order  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $animation  $json  $vfs 	 $status  $math 	 $colors  $log  $Json 	 $stdext �   �  n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size ( U  ��: std::ratio<146097,400>::num $ U  �std::ratio<146097,400>::den T U  ��: std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx1 P U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx1 T U  ��Q std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx2 P U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gx O U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gy < U  �X呩std::integral_constant<__int64,31556952>::value O U  �X呩std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Dx2 M U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx1 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx2 Q U  �X呩std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gx L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gy ( U  �X呩std::ratio<31556952,1>::num $ U   std::ratio<31556952,1>::den 1 U   std::integral_constant<__int64,5>::value W _   std::allocator<Json::PathArgument const *>::_Minimum_asan_allocation_alignment    �   �)  ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos # U   std::ratio<5,2629746>::num ' U  �r ( std::ratio<5,2629746>::den h _   std::allocator<std::shared_ptr<donut::engine::IShadowMap> >::_Minimum_asan_allocation_alignment L U  ��Q std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Nx2 / :   std::atomic<long>::is_always_lock_free H U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Dx2  U   std::ratio<12,1>::num  U   std::ratio<12,1>::den J U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx1 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx1 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Nx2 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Dx2 N U  ��Q std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx2 I U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gx I U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gy Q U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx1 i _   std::allocator<std::shared_ptr<donut::engine::SceneCamera> >::_Minimum_asan_allocation_alignment M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx1 4 U  �std::integral_constant<__int64,1440>::value  U   std::ratio<1,12>::num  U   std::ratio<1,12>::den M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx2 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gx L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gy ; U  �r ( std::integral_constant<__int64,2629746>::value   U   std::ratio<1,1440>::num   U  �std::ratio<1,1440>::den R _   std::allocator<std::_Container_proxy>::_Minimum_asan_allocation_alignment B _   std::allocator<float>::_Minimum_asan_allocation_alignment C :   std::atomic<std::chrono::tzdb_list *>::is_always_lock_free T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment � :   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Same_size_and_compatible - :    std::chrono::system_clock::is_steady � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_assignable $ U   std::ratio<1,10000000>::num ( U  ��枠 std::ratio<1,10000000>::den / �  �   SubInstanceData::Flags_AlphaTested 2 �  �   SubInstanceData::Flags_ExcludeFromNEE 3 �  �   �SubInstanceData::Flags_AlphaOffsetMask 1 �   SubInstanceData::Flags_AlphaOffsetOffset O _   std::allocator<Json::PathArgument>::_Minimum_asan_allocation_alignment . �   PTMaterialFlags_UseSpecularGlossModel 7 �   PTMaterialFlags_UseMetalRoughOrSpecularTexture 0 �   PTMaterialFlags_UseBaseOrDiffuseTexture + �   PTMaterialFlags_UseEmissiveTexture ) �    PTMaterialFlags_UseNormalTexture / �  � PTMaterialFlags_UseTransmissionTexture . �   PTMaterialFlags_MetalnessInRedChannel $ �   PTMaterialFlags_ThinSurface # �   PTMaterialFlags_PSDExclude / �  �   餚TMaterialFlags_NestedPriorityMask , �   PTMaterialFlags_NestedPriorityShift 7 �  �   PTMaterialFlags_PSDDominantDeltaLobeP1Mask 4 �   PTMaterialFlags_PSDDominantDeltaLobeP1Shift � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable ; :   std::atomic<unsigned __int64>::is_always_lock_free � :   std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_assignable ( �    std::chrono::local_info::unique - �   std::chrono::local_info::nonexistent + �   std::chrono::local_info::ambiguous c _   std::allocator<std::shared_ptr<donut::engine::Light> >::_Minimum_asan_allocation_alignment ) <   nvrhi::ObjectTypes::SharedHandle - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature  U   std::ratio<1,1>::num 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator  U   std::ratio<1,1>::den * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + <  �  nvrhi::ObjectTypes::VK_Sampler 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule . <  �
  nvrhi::ObjectTypes::VK_RenderPass / �   PTMaterial::kMaterialMaxNestedPriority / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 M U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gy l _   std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >::_Minimum_asan_allocation_alignment J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N U  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 � :   std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_assignable L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( U  ��枠 std::ratio<10000000,1>::num $ U   std::ratio<10000000,1>::den C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE P U  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy < U  ��枠 std::integral_constant<__int64,10000000>::value d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size . �   donut::math::box<float,3>::numCorners : _   std::integral_constant<unsigned __int64,1>::value  �   �   - :   std::chrono::steady_clock::is_steady & U   std::ratio<1,1000000000>::num * U  � 蕷;std::ratio<1,1000000000>::den q _   std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >::_Minimum_asan_allocation_alignment   �     ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos F U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Nx2 F U  �std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Dx2 * :    std::chrono::utc_clock::is_steady 8 :   std::atomic<unsigned long>::is_always_lock_free � :    std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0>::_Multi H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx1   U  �std::ratio<1000,1>::num   U   std::ratio<1000,1>::den H U  �std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gy 4 U  �std::integral_constant<__int64,1000>::value � _   std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> >::_Minimum_asan_allocation_alignment * :    std::chrono::tai_clock::is_steady O U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Nx2 / <  � nvrhi::rt::cluster::kClasByteAlignment 6 v,  �奮�std::chrono::tai_clock::_Tai_epoch_adjust . <   nvrhi::rt::cluster::kClasMaxTriangles S U  � 蕷;std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Dx2 - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex Q U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx1 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx1 * U  � 蕷;std::ratio<1000000000,1>::num & U   std::ratio<1000000000,1>::den U U  � 蕷;std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx2 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx2 P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gx P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gy � :    std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Multi � :    std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Is_set E U  
� 牳0F  std::integral_constant<__int64,3600000000000>::value * :    std::chrono::gps_clock::is_steady 6 v,  �w�*韘td::chrono::gps_clock::_Gps_epoch_adjust 1 U  
� 牳0F  std::ratio<3600000000000,1>::num ) U   std::ratio<3600000000000,1>::den 5 :    std::filesystem::_File_time_clock::is_steady + <   donut::math::vector<double,3>::DIM M U  �r ( std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Nx2 I U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx2 O U  �r ( std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx2 j _   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gx J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gy : _    std::integral_constant<unsigned __int64,0>::value % _   std::ctype<char>::table_size N U  �X呩std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Dx2 ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx1 N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx1 E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx2 P U  �X呩std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gy K U  ��Q std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Dx2 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx1 E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment Y _   std::allocator<std::shared_ptr<PTMaterial> >::_Minimum_asan_allocation_alignment I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx2 M U  ��Q std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gx H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gy L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gy + <   donut::math::vector<double,4>::DIM > U  � 蕷;std::integral_constant<__int64,1000000000>::value C _   std::_Locked_pointer<std::_Stop_callback_base>::_Lock_mask D _    std::_Locked_pointer<std::_Stop_callback_base>::_Not_locked R _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_not_needed N _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_needed I _   �黶td::_Locked_pointer<std::_Stop_callback_base>::_Ptr_value_mask C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size   �   SE  K :   std::atomic<std::_Stop_callback_base const *>::is_always_lock_free ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy L U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Nx2 P U  � 蕷;std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Dx2 - �   std::_Invoker_pmf_refwrap::_Strategy ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx1 - �   std::_Invoker_pmf_pointer::_Strategy R _   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx2 M _   std::allocator<unsigned __int64>::_Minimum_asan_allocation_alignment M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gy _ _   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy K _   std::allocator<PTMaterialData>::_Minimum_asan_allocation_alignment   �   std::_Iosb<int>::skipws ! �   std::_Iosb<int>::unitbuf # �   std::_Iosb<int>::uppercase " �   std::_Iosb<int>::showbase # �   std::_Iosb<int>::showpoint ! �    std::_Iosb<int>::showpos  �  @ std::_Iosb<int>::left  �  � std::_Iosb<int>::right " �   std::_Iosb<int>::internal  �   std::_Iosb<int>::dec O _   std::allocator<unsigned __int64 *>::_Minimum_asan_allocation_alignment  �   std::_Iosb<int>::oct  �   std::_Iosb<int>::hex $ �   std::_Iosb<int>::scientific  �    std::_Iosb<int>::fixed " �   0std::_Iosb<int>::hexfloat # �   @std::_Iosb<int>::boolalpha " �  � �std::_Iosb<int>::_Stdio % �  �std::_Iosb<int>::adjustfield # �   std::_Iosb<int>::basefield $ �   0std::_Iosb<int>::floatfield ! �    std::_Iosb<int>::goodbit   �   std::_Iosb<int>::eofbit ! �   std::_Iosb<int>::failbit   �   std::_Iosb<int>::badbit  �   std::_Iosb<int>::in  �   std::_Iosb<int>::out  �   std::_Iosb<int>::ate  �   std::_Iosb<int>::app  �   std::_Iosb<int>::trunc # �  @ std::_Iosb<int>::_Nocreate $ �  � std::_Iosb<int>::_Noreplace   �    std::_Iosb<int>::binary  �    std::_Iosb<int>::beg  �   std::_Iosb<int>::cur  �   std::_Iosb<int>::end , �  @ std::_Iosb<int>::_Default_open_prot  �    donut::vfs::status::OK $ �   ��donut::vfs::status::Failed * �   �onut::vfs::status::PathNotFound , �   �齞onut::vfs::status::NotImplemented Z �   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Minimum_map_size  U  < std::ratio<60,1>::num  U   std::ratio<60,1>::den M _   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Bytes R �   std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >::_Block_size T �   std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Block_size 9 �   std::chrono::_Time_parse_fields::_Era_begin_wday � _   std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >::_Minimum_asan_allocation_alignment S _   std::allocator<std::chrono::time_zone>::_Minimum_asan_allocation_alignment  ;  ��I@donut::math::PI_f " s  
�-DT�!	@donut::math::PI_d ! ;  ��7�5donut::math::epsilon " ;  �  �donut::math::infinity  ;  �  �donut::math::NaN   U   std::ratio<1,1000>::num   U  �std::ratio<1,1000>::den � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >::_Minimum_asan_allocation_alignment / U   std::ratio<1,1000000000000000000>::num 7 U  
�  dС多
std::ratio<1,1000000000000000000>::den i _   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment �:    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >,0>::_Multi �:   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >,0>::_Standard :    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi ":   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard # U   std::ratio<1,1000000>::num ' U  �@B std::ratio<1,1000000>::den � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture>,void *> >::_Minimum_asan_allocation_alignment U :   std::_String_view_iterator<std::char_traits<char> >::_Unwrap_when_unverified � _   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment _ _   std::allocator<donut::engine::animation::Keyframe>::_Minimum_asan_allocation_alignment D _   ��std::basic_string_view<char,std::char_traits<char> >::npos X _   std::allocator<std::chrono::time_zone_link>::_Minimum_asan_allocation_alignment x _   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >::_Minimum_asan_allocation_alignment + :    std::_Aligned_storage<72,8>::_Fits * :    std::_Aligned<72,8,char,0>::_Fits + :    std::_Aligned<72,8,short,0>::_Fits ) :   std::_Aligned<72,8,int,0>::_Fits : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; _  �std::_Floating_type_traits<double>::_Exponent_mask J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask T U  �r ( std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Dx2 Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx1 V U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx1 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx2 Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gy F U  
� 泌�  std::integral_constant<__int64,26297460000000>::value � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IFileSystem> > >::_Minimum_asan_allocation_alignment �_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >,0> >::_Bucket_size �_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >,0> >::_Min_buckets �:    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >,0> >::_Multi * U   std::ratio<1,26297460000000>::num 2 U  
� 泌�  std::ratio<1,26297460000000>::den U U  �X呩std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Nx2 Q U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Dx2 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx1 2_   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size W U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx1 2_   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx2 W U  �X呩std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx2 ,:    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gx R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gy G U  
� <$A std::integral_constant<__int64,315569520000000>::value U _   std::allocator<std::chrono::leap_second>::_Minimum_asan_allocation_alignment + U   std::ratio<1,315569520000000>::num 3 U  
� <$A std::ratio<1,315569520000000>::den R U  ��Q std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Dx2 J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx1 T U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gy D U  
� 纈*�   std::integral_constant<__int64,864000000000>::value $ �   ��std::strong_ordering::less $ �    std::strong_ordering::equal & �   std::strong_ordering::greater � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > >::_Minimum_asan_allocation_alignment ( U   std::ratio<1,864000000000>::num 0 U  
� 纈*�   std::ratio<1,864000000000>::den ) <   donut::math::vector<bool,2>::DIM � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >::_Minimum_asan_allocation_alignment \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ) <   donut::math::vector<bool,3>::DIM � _   std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >::_Minimum_asan_allocation_alignment ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos ::    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Multi ) <   donut::math::vector<bool,4>::DIM =:   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0>::_Standard a:    std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Multi d:   std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0>::_Standard � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >::_Minimum_asan_allocation_alignment � _   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos ' U  �r ( std::ratio<2629746,1>::num # U   std::ratio<2629746,1>::den  �   C   A _   std::allocator<bool>::_Minimum_asan_allocation_alignment  �   m  m _   std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> >::_Minimum_asan_allocation_alignment J _   std::allocator<Json::Value *>::_Minimum_asan_allocation_alignment L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos I _   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment ' �  ��std::chrono::year::_Year_min % �  �std::chrono::year::_Year_max N :   std::_Trivial_cat<char,char,char &,char &>::_Same_size_and_compatible ] _   std::allocator<donut::engine::SceneImportResult>::_Minimum_asan_allocation_alignment K :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_constructible H :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_assignable L _   std::allocator<Json::Value * *>::_Minimum_asan_allocation_alignment * <   donut::math::vector<float,3>::DIM M_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Bucket_size M_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Min_buckets G:    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Multi : _   std::integral_constant<unsigned __int64,2>::value t_   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Bucket_size t_   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Min_buckets n:    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> >::_Multi T �   std::deque<Json::Value *,std::allocator<Json::Value *> >::_Minimum_map_size J _   std::_Deque_val<std::_Deque_simple_types<Json::Value *> >::_Bytes O �   std::_Deque_val<std::_Deque_simple_types<Json::Value *> >::_Block_size  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment N �   std::deque<Json::Value *,std::allocator<Json::Value *> >::_Block_size . :    std::integral_constant<bool,0>::value _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >::_Minimum_asan_allocation_alignment b <    std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::fractional_width C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gx D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gy � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment    �   %  * <   donut::math::vector<float,4>::DIM ' 錏        donut::math::colors::white * 錏        donut::math::lumaCoefficients * <   donut::math::vector<float,2>::DIM W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified T _   std::allocator<Json::Reader::ErrorInfo>::_Minimum_asan_allocation_alignment  �    LightType_None  �   LightType_Directional  �   LightType_Spot  �   LightType_Point  H    std::denorm_absent  H   std::denorm_present  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity T U  �r ( std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Nx2 & :    std::_Num_base::has_quiet_NaN R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified * :    std::_Num_base::has_signaling_NaN P U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Dx2 # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 V U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx1 % �    std::_Num_base::max_digits10 R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx1 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 # U   std::ratio<1,2629746>::num % �    std::_Num_base::min_exponent ' U  �r ( std::ratio<1,2629746>::den ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gx V _   std::allocator<Json::Reader::ErrorInfo *>::_Minimum_asan_allocation_alignment Q U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gy ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix 2 U   std::integral_constant<__int64,12>::value ) H   std::_Num_float_base::has_denorm + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed  �  �LightType_Environment - :   std::_Num_float_base::is_specialized * K   std::_Num_float_base::round_style $ �   std::_Num_float_base::radix    �   �  * �   std::numeric_limits<bool>::digits h �   std::deque<Json::Reader::ErrorInfo,std::allocator<Json::Reader::ErrorInfo> >::_Minimum_map_size - :   std::numeric_limits<char>::is_signed - :    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 R U  ��Q std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Dx2 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices 4 :   std::numeric_limits<signed char>::is_signed T U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx1 1 �   std::numeric_limits<signed char>::digits P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx1 3 �   std::numeric_limits<signed char>::digits10 ! U   std::ratio<1,86400>::num % U  ��Q std::ratio<1,86400>::den T _  @ std::_Deque_val<std::_Deque_simple_types<Json::Reader::ErrorInfo> >::_Bytes Y �   std::_Deque_val<std::_Deque_simple_types<Json::Reader::ErrorInfo> >::_Block_size : _   std::integral_constant<unsigned __int64,3>::value P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gy b �   std::deque<Json::Reader::ErrorInfo,std::allocator<Json::Reader::ErrorInfo> >::_Block_size 6 :   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 # �        nvrhi::AllSubresources : U  ��: std::integral_constant<__int64,146097>::value 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 3 U  �std::integral_constant<__int64,400>::value 7 :   std::atomic<unsigned int>::is_always_lock_free 1 :   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 :   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 T U  �X呩std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Dx2 . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10   �   .  V U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx1 $ U   std::ratio<1,31556952>::num ( U  �X呩std::ratio<1,31556952>::den , :   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx2 V U  �X呩std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx2   �   \  U U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gx � _   std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >::_Minimum_asan_allocation_alignment Q U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gy ) <   donut::math::frustum::numCorners �   ]
  - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 a:    std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Multi d:   std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0>::_Standard 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 :   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits � _   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >::_Minimum_asan_allocation_alignment 5 �  	 std::numeric_limits<unsigned long>::digits10 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10           nvrhi::EntireBuffer + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent R :    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified 6 �  �威std::numeric_limits<double>::min_exponent10    �   �  1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 A _   std::allocator<char>::_Minimum_asan_allocation_alignment 6 :   std::_Iterator_base0::_Unwrap_when_unverified 7 :   std::_Iterator_base12::_Unwrap_when_unverified O:   std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Same_size_and_compatible L:    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_constructible I:    std::_Trivial_cat<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &&,std::basic_string<char,std::char_traits<char>,std::allocator<char> > &>::_Bitcopy_assignable ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE 3 �  \ std::filesystem::path::preferred_separator A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity - �    std::integral_constant<int,0>::value � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Same_size_and_compatible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_assignable X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size J U  std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Dx2 P U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx1   U   std::ratio<1,3600>::num   U  std::ratio<1,3600>::den O _   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx2 L U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gx ( <   donut::math::vector<int,4>::DIM K U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gy t_   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Bucket_size    �     t_   std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Min_buckets n:    std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> >::_Multi � :   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable 1 <   donut::math::vector<unsigned int,2>::DIM T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos Q U  �r ( std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Dx2 F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 S U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx2 S U  �r ( std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx2 N U  6 std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gx N U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gy H U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gy 4 U  @std::integral_constant<__int64,1600>::value 4 U  std::integral_constant<__int64,3600>::value 4 _  @ _Mtx_internal_imp_t::_Critical_section_size 7 U  �;緎td::integral_constant<__int64,48699>::value 5 _   _Mtx_internal_imp_t::_Critical_section_align + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits + :    std::_Aligned<64,8,short,0>::_Fits ) :   std::_Aligned<64,8,int,0>::_Fits $ U  @std::ratio<1600,48699>::num & U  �;緎td::ratio<1600,48699>::den R U  �X呩std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Dx2 T U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx2 T U  �X呩std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gx O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gy � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >::_Minimum_asan_allocation_alignment   �   &   抏   _Mtx_try  抏   _Mtx_recursive @ �   std::_General_precision_tables_2<float>::_Max_special_P 8 �  ' std::_General_precision_tables_2<float>::_Max_P $ U  �std::ratio<400,146097>::num ( U  ��: std::ratio<400,146097>::den A �   std::_General_precision_tables_2<double>::_Max_special_P 9 �  5std::_General_precision_tables_2<double>::_Max_P  蔱   std::_INVALID_ARGUMENT  蔱   std::_NO_SUCH_PROCESS & 蔱   std::_OPERATION_NOT_PERMITTED , 蔱   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蔱   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN % �    _Atomic_memory_order_relaxed % �   _Atomic_memory_order_consume % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst   �   �  � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible Z _   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable 2 <  �����std::shared_timed_mutex::_Max_readers   �   �  \ _   std::allocator<donut::engine::SkinnedMeshJoint>::_Minimum_asan_allocation_alignment D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable   �   �   H :    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified - <  `std::_Big_integer_flt::_Maximum_bits - <    std::_Big_integer_flt::_Element_bits . <  s std::_Big_integer_flt::_Element_count �   G  j _   std::allocator<std::shared_ptr<donut::engine::MeshInstance> >::_Minimum_asan_allocation_alignment B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity  �     a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val Z _   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size � _   std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable :    std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi :   std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos � _   std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment  蠚    Json::nullValue ' <�   Json::numberOfCommentPlacement   M�    Json::significantDigits q _   std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >::_Minimum_asan_allocation_alignment $ �   std::_Locbase<int>::collate " �   std::_Locbase<int>::ctype % �   std::_Locbase<int>::monetary $ �   std::_Locbase<int>::numeric P U  �r ( std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Nx2 ! �   std::_Locbase<int>::time L U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Dx2 % �    std::_Locbase<int>::messages   �  ? std::_Locbase<int>::all ! �    std::_Locbase<int>::none + U  	�       �Json::Value::minLargestInt + U  
��������Json::Value::maxLargestInt N U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx1 % _   ��Json::Value::maxLargestUInt N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx1   �  �   �Json::Value::minInt N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx2   �  ����Json::Value::maxInt R U  �r ( std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx2 ! <  �����Json::Value::maxUInt M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gx & U  	�       �Json::Value::minInt64 M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gy & U  
��������Json::Value::maxInt64   _   ��Json::Value::maxUInt64 * <   Json::Value::defaultRealPrecision / s  
�      餋Json::Value::maxUInt64AsDouble   U  std::ratio<3600,1>::num   U   std::ratio<3600,1>::den 3 U  � std::integral_constant<__int64,200>::value c _   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment $ U  � std::ratio<200,146097>::num ( U  ��: std::ratio<200,146097>::den J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 x _   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy Q U  �X呩std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Dx2 O U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx2 S U  �X呩std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx2 N U  H std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gx N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gy    �   �  2 U  2 std::integral_constant<__int64,50>::value ':   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable : U  �� std::integral_constant<__int64,438291>::value  U   std::ratio<24,1>::num  U   std::ratio<24,1>::den I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx1 I U  std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gy # U  2 std::ratio<50,438291>::num ' U  �� std::ratio<50,438291>::den 9 U  ��Q std::integral_constant<__int64,86400>::value /_   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size /_   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets ):    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi N U  ��Q std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Dx2 1 U   std::integral_constant<__int64,1>::value L U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx2 P U  ��Q std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gx K U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gy D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment 2 U   std::integral_constant<__int64,24>::value % U  ��Q std::ratio<86400,1>::num ! U   std::ratio<86400,1>::den 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable + �!        nvrhi::rt::c_IdentityTransform q _   std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >::_Minimum_asan_allocation_alignment  U   std::ratio<1,24>::num  U   std::ratio<1,24>::den   �    � N U  �r ( std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Dx2 L U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx2 P U  �r ( std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gx K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gy  U   std::ratio<7,1>::num  U   std::ratio<7,1>::den Z _   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx1 M U  ��Q std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx2 2 U  
 std::integral_constant<__int64,10>::value I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gy B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE  �1   std::_Consume_header  �1   std::_Generate_header D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity : U  ��:	 std::integral_constant<__int64,604800>::value . :   std::integral_constant<bool,1>::value # U  
 std::ratio<10,438291>::num ' U  �� std::ratio<10,438291>::den a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity  �  _CatchableType  �  EnvironmentLight " �  _s__RTTIBaseClassDescriptor    SampleSettings ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  '0  __std_tzdb_error & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  O�  OpacityMicroMapUIData ( !�  OpacityMicroMapUIData::BuildState  �0  _Ctypevec   y�  MaterialShadingProperties & �  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  齚  _Thrd_result  #   rsize_t  �>  __std_fs_find_data &   $_TypeDescriptor$_extraBytes_23 - cZ  $_s__CatchableTypeArray$_extraBytes_32 # )D  __std_fs_reparse_data_buffer Z _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^  _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> `   __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �>  __std_fs_dir_handle  �/  __std_access_rights    _TypeDescriptor & NZ  $_TypeDescriptor$_extraBytes_34  渇  _Stl_critical_section 	 I  tm % �  _s__RTTICompleteObjectLocator2  q�  LightConstants & kZ  $_TypeDescriptor$_extraBytes_30 A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 焃  $_TypeDescriptor$_extraBytes_46  �  _s__CatchableType  j�  SubInstanceData & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t  片  PTMaterial 9   __vcrt_va_list_is_reference<wchar_t const * const>  銩  __std_fs_filetime E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & �  $_TypeDescriptor$_extraBytes_20  p  va_list  垺  PerspectiveCameraEx - �  $_s__CatchableTypeArray$_extraBytes_16   SD  __std_fs_copy_file_result  �5  __std_code_page  联  MeshGeometryDebugData d 讔  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > T %�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<PTMaterial> > > G 啀  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > c 煾  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > a 顛  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> � 5�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > ] 鍗  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ f�  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � U�  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > [ 賺  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > ^ I�  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1> � <�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � 粛  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 閶  std::_Default_allocator_traits<std::allocator<float> > ^ @�  std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1> C 獚  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � 爫  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> � M�  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,void> 3 敻  std::allocator<std::shared_ptr<PTMaterial> > �   std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > 8 k�  std::_Ptr_base<donut::engine::FramebufferFactory> � 笇  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 槏  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 悕  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 亶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 1�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > � 7�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > K '�  std::allocator_traits<std::allocator<std::shared_ptr<PTMaterial> > > 6 �  std::allocator<donut::engine::SkinnedMeshJoint> M J�  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L 垗  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 儘  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > � 痈  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > | �  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > T r�  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > � �  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,float,1>,1> F 叽  std::_Default_allocator_traits<std::allocator<PTMaterialData> > U h�  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � �  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > > > � �  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > : 榝  std::_Vector_val<std::_Simple_types<unsigned int> > D L�  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � 虼  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > > � >�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � .�  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> ~  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > >,1> e h�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > U �  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > � 蟾  std::_Compressed_pair<std::default_delete<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> >,std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> *,1> � O]  std::_Non_trivial_copy<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > J 敫  std::_Vector_val<std::_Simple_types<std::shared_ptr<PTMaterial> > > "p�  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > W 嫺  std::_Default_allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > � 岣  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > l 父  std::_Default_allocator_traits<std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > > U 鐘  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > w 崭  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > y 聘  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > � !�  std::_Default_allocator_traits<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> > > c 焊  std::allocator_traits<std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > > 4 鄬  std::allocator<donut::math::vector<float,2> > M   std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> > = �  std::allocator<donut::math::vector<unsigned short,4> > K 賹  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p 巉  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U ﹪  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > ^ 灥  std::default_delete<std::vector<GeometryDebugData,std::allocator<GeometryDebugData> > > M k^  std::_Normal_allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > F脤  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> � 皩  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> h 「  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > � 湼  std::_Compressed_pair<std::allocator<std::shared_ptr<PTMaterial> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<PTMaterial> > >,1> N 嵏  std::allocator_traits<std::allocator<donut::engine::SkinnedMeshJoint> > 2 罕  std::_Ptr_base<donut::engine::GltfImporter> ' 煻  std::less<Json::Value::CZString> { ▽  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > l e�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � �  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > , <�  std::allocator<nvrhi::BindingSetItem> K 5�  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > � s�  std::_Compressed_pair<std::hash<std::shared_ptr<donut::engine::Material> >,std::_Compressed_pair<std::equal_to<std::shared_ptr<donut::engine::Material> >,float,1>,1> . 綶  std::_Ptr_base<donut::vfs::IFileSystem> �i�  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > > >,1> � D�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > > > � 岱  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > � �  std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >,std::_Iterator_base0> � a�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > > J g�  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > H   std::_Optional_destruct_base<OpacityMicroMapUIData::BuildState,1> � �  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � 匽  std::_Non_trivial_move_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > �  �  std::_Uhash_choose_transparency<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,void> � 醣  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > g 莱  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > L �  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  m�  std::allocator<float> � 	�  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> o J�  std::default_delete<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> > 鷭  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> 8 z  std::_Ptr_base<donut::engine::CommonRenderPasses> � 髬  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1> � F�  std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> > � 夭  std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Strategy � 刹  std::_Tree<std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> >::_Redbl k ,]  std::_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0> 4 雼  std::allocator_traits<std::allocator<float> > c �  std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> > [ 輯  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > � a]  std::_Non_trivial_move<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � F�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > > > l ~�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > w 懗  std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > � 3�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > 6 	�  std::_Ptr_base<donut::engine::SceneTypeFactory> � �  std::_Tree_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > > > ; #�  std::hash<std::shared_ptr<donut::engine::MeshInfo> > � �  std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >,1> W�  std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> X   std::_Default_allocator_traits<std::allocator<donut::engine::SceneImportResult> > � Q�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > H 氤  std::_Arg_types<std::shared_ptr<donut::engine::MeshInfo> const &> [ s�  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' {I  std::default_delete<wchar_t [0]> < 耿  std::_Vector_val<std::_Simple_types<PTMaterialData> > . /  std::_Conditionally_enabled_hash<int,1> A C  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> � 	�  std::_Compressed_pair<std::allocator<donut::engine::SkinnedMeshJoint>,std::_Vector_val<std::_Simple_types<donut::engine::SkinnedMeshJoint> >,1> N �  std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &> X   std::_Func_class<void,std::shared_ptr<donut::engine::MeshInfo> const &>::_Storage ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> > ' @�  std::_Ref_count_obj2<MaterialEx> . 鴒  std::integer_sequence<unsigned __int64>  �  std::_Lockit T �  std::function<bool __cdecl(std::shared_ptr<donut::engine::Material> const &)>  坃  std::_Stop_callback_base � 醴  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > >  b  std::timed_mutex � @�  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > � �  std::vector<std::shared_ptr<donut::engine::SkinnedMeshInstance>,std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >::_Reallocation_policy D �;  std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty / A�  std::shared_ptr<donut::engine::Material> - �?  std::reverse_iterator<wchar_t const *> 6 刕  std::allocator_traits<std::allocator<char8_t> > 5 m�  std::shared_ptr<donut::engine::SceneGraphNode> 9 >�  std::shared_ptr<donut::engine::animation::Sampler> " 媂  std::_Char_traits<char,int> � 矸  std::_Compressed_pair<std::allocator<donut::engine::SceneImportResult>,std::_Vector_val<std::_Simple_types<donut::engine::SceneImportResult> >,1>  0  std::_Fs_file  �=  std::optional<int> : 宸  std::optional<int>::_AllowUnwrappingAssignment<int> � ,�  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  N  std::_Num_base & $/  std::hash<std::error_condition> K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 5 $0  std::_Tzdb_deleter<__std_tzdb_time_zones_info> R 9�  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > � 惴  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > = 色 std::basic_ostream<wchar_t,std::char_traits<wchar_t> > # �  std::numeric_limits<char8_t>  /,  std::_Big_uint128    std::condition_variable  �=  std::optional<__int64> / �  std::weak_ptr<donut::engine::SceneGraph> E m^  std::allocator_traits<std::_Crt_allocator<std::chrono::tzdb> >   std::unordered_map<std::shared_ptr<donut::engine::Material>,unsigned int,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > G ^^  std::allocator_traits<std::allocator<std::chrono::leap_second> > + 　  std::_Optional_construct_base<float> ) 怷  std::_Narrow_char_traits<char,int> i 魥  std::initializer_list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > ] 苑  std::vector<std::shared_ptr<PTMaterial>,std::allocator<std::shared_ptr<PTMaterial> > > s ７  std::vector<std::shared_ptr<PTMaterial>,std::allocator<std::shared_ptr<PTMaterial> > >::_Reallocation_policy L 閵  std::allocator_traits<std::allocator<donut::math::vector<float,2> > > 瓠  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >  j  std::hash<float> E D~  std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc> ! �4  std::__floating_decimal_64 S 蘕  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone_link> > c -�  std::stack<unsigned __int64,std::deque<unsigned __int64,std::allocator<unsigned __int64> > > � g�  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,std::_Iterator_base0> k �  std::pointer_traits<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> const *> } #�  std::_Default_allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > " 唂  std::_Align_type<double,64>  �5  std::less<void>  /  std::hash<int> G �  std::_Optional_construct_base<OpacityMicroMapUIData::BuildState>  P  std::_Num_int_base  i3  std::ctype<wchar_t> " �/  std::_System_error_category � _�  std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > > / 揚  std::_Conditionally_enabled_hash<bool,1> Wf�  std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> 2 蹔  std::shared_ptr<donut::engine::BufferGroup> 2 @�  std::_Ptr_base<donut::engine::MeshGeometry> � 瘖  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > + 琠  std::_Atomic_storage<unsigned int,4> ' 薛  std::_Ref_count_obj2<MeshInfoEx>  �5  std::_Format_arg_index  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > / y2  std::codecvt<char32_t,char8_t,_Mbstatet> & 嚘  std::_Wrap<PerspectiveCameraEx> 4 鑹  std::shared_ptr<donut::engine::LoadedTexture> k =]  std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > f �;  std::pair<std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >,bool> 碶  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! n  std::piecewise_construct_t ! 蓅  std::_Ptr_base<std::mutex> �}  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> u 訵  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > � I�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >,1> . �  std::_Ptr_base<donut::engine::MeshInfo> 6 餧  std::allocator_traits<std::allocator<wchar_t> >  t  std::shared_timed_mutex � A�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >,1> & ;~  std::equal_to<unsigned __int64> � s]  std::_Non_trivial_copy_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  `  std::bad_cast B 厭  std::enable_shared_from_this<donut::engine::SceneGraphNode>  JR  std::equal_to<void> 4 D�  std::allocator<donut::math::vector<float,4> > ; 2�  std::hash<std::shared_ptr<donut::engine::Material> > � P�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > 3 僄  std::_Ptr_base<donut::engine::ShaderFactory> � 粿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > } .�  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > � 鼦  std::vector<std::shared_ptr<donut::engine::SceneCamera>,std::allocator<std::shared_ptr<donut::engine::SceneCamera> > >::_Reallocation_policy q 綁  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 媺  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy ~.�  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > >,1> o n�  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> ( 偀  std::shared_ptr<EnvironmentLight> � 摔  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >     std::_Compare_t " u  std::numeric_limits<double> $ d  std::atomic<unsigned __int64>  �  std::__non_rtti_object < 鰒  std::_Ptr_base<donut::engine::DescriptorTableManager> , K2  std::_Codecvt_guard<char8_t,char16_t> ( @  std::_Basic_container_proxy_ptr12 � D�  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 2 #L  std::allocator<std::chrono::time_zone_link> 4 F�  std::allocator<donut::math::vector<float,3> > � 瞌  std::_List_node_emplace_op2<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > � 悡  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > � _�  std::vector<std::shared_ptr<donut::engine::SceneGraphNode>,std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > >::_Reallocation_policy = 6`  std::_Atomic_pointer<std::_Stop_callback_base const *> > 刦  std::vector<unsigned int,std::allocator<unsigned int> > T Tf  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy   �5  std::_Decode_result<char> t   std::_Compressed_pair<std::allocator<PTMaterialData>,std::_Vector_val<std::_Simple_types<PTMaterialData> >,1> { #�  std::allocator_traits<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> > > 1    std::array<nvrhi::FramebufferAttachment,8> T �  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > > � j�  std::list<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >  q  std::_Num_float_base %  std::pointer_traits<wchar_t *> � �  std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >  抈  std::stop_token  �-  std::logic_error O   std::allocator_traits<std::allocator<donut::engine::SceneImportResult> > 3 噾  std::weak_ptr<donut::engine::SceneGraphNode> � �  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > � 歙  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,std::_Iterator_base0> _ 鸲  std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety / 頚  std::allocator<std::chrono::leap_second> P �  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 鈭  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! 頬  std::char_traits<char32_t> � ﹫  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  Q1  std::locale  �1  std::locale::_Locimp  b1  std::locale::facet   k1  std::locale::_Facet_guard  1  std::locale::id [ 飓 std::basic_ostringstream<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > ? 槎  std::allocator_traits<std::allocator<unsigned __int64> > : 魬  std::shared_ptr<donut::engine::SkinnedMeshInstance> - VL  std::allocator<std::chrono::time_zone> ] 叅  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > > 2 盛  std::default_delete<Json::CharReader::Impl>  �  std::_Compare_ncmp s 竂  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > Z 缍  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >   R  std::numeric_limits<bool> � 荻  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > > # *Y  std::_WChar_traits<char16_t> _ 殘  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u i�  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy � 榷  std::unique_ptr<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3>,std::default_delete<std::array<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,3> > > ǘ  std::_Compressed_pair<std::less<Json::Value::CZString>,std::_Compressed_pair<std::allocator<std::_Tree_node<std::pair<Json::Value::CZString const ,Json::Value>,void *> >,std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >,1>,1> P 獻  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord . �  std::_Ptr_base<donut::engine::Material> * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> Z W  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> >  綻  std::stop_source R �9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > h {9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >::_Reallocation_policy M i?  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  K  std::ostream � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1> " *�  std::shared_ptr<MeshInfoEx>  F.  std::overflow_error d 毝  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> > z h�  std::vector<donut::engine::SkinnedMeshJoint,std::allocator<donut::engine::SkinnedMeshJoint> >::_Reallocation_policy " x5  std::_Basic_format_arg_type , b2  std::_Codecvt_guard<char16_t,char8_t> % L  std::_One_then_variadic_args_t R 棲  std::_Compressed_pair<std::default_delete<MeshDebugData>,MeshDebugData *,1> W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * jb  std::_Vb_val<std::allocator<bool> > E 鞢  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j 靅  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  骸  std::optional<float> > #�  std::optional<float>::_AllowUnwrappingAssignment<float>   闿  std::char_traits<wchar_t> \ :  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > r �9  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> >::_Reallocation_policy  pa  std::recursive_mutex   �  std::pmr::memory_resource  	>  std::pair<int,int> ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> � 鵷  std::unordered_map<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 7 "�  std::shared_ptr<donut::engine::SceneTypeFactory> � 鐕  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> � 醯  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::IShadowMap> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > >,1> n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy ' �  std::allocator<unsigned __int64>  砞  std::false_type � 淼  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > > >  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � �:  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > H }�  std::_Default_allocator_traits<std::allocator<unsigned __int64> > \ 櫑  std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 愠  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture>,void *> > > ! a  std::hash<std::thread::id> $ 
6  std::_Fmt_fixed_buffer_traits  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > c 蠶  std::_Alloc_construct_ptr<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  扟  std::fpos<_Mbstatet> b gQ  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone> > > , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> + 拧  std::_Optional_destruct_base<bool,1> �   std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  俛  std::adopt_lock_t o 奓  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_current_zone_info>,__std_tzdb_current_zone_info *,1>  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 6 [�  std::_Ptr_base<donut::engine::Scene::Resources> 7 讎  std::shared_ptr<donut::engine::DescriptorHandle> , n  std::numeric_limits<unsigned __int64> � 盏  std::_Compressed_pair<std::allocator<donut::engine::animation::Keyframe>,std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> >,1> F L  std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >  �0  std::_Locinfo 6 B  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L 珖  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > 9 枤  std::shared_ptr<donut::engine::FramebufferFactory> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 4 婯  std::_Atomic_padded<std::chrono::tzdb_list *> s 奤  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > N 諼  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone> > H 谞  std::_Arg_types<std::shared_ptr<donut::engine::Material> const &> 9 DJ  std::basic_streambuf<char,std::char_traits<char> > $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16> � 车  std::_Default_allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > e 偷  std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > > m 赖  std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > >::_Redbl f ;�  std::_Simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > = 媷  std::shared_ptr<donut::engine::DescriptorTableManager>  �  std::string_view  �  std::wstring_view % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound � 彣  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >  Qa  std::_Mutex_base � {�  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > b u�  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  Z5  std::money_base  縘  std::money_base::pattern s   std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >  y0  std::_Timevec 5降  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > >,1> D f�  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > w 档  std::allocator_traits<std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >  胉  std::nostopstate_t " 笭  std::shared_ptr<MaterialEx>  f  std::defer_lock_t   �.  std::_Init_once_completer  絔  std::endian j sC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � BC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �a  std::scoped_lock<> + �2  std::codecvt<wchar_t,char,_Mbstatet> h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> d xK  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::leap_second> > > Q 籡  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>    std::_Iterator_base12 j 飶  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> > � 綇  std::vector<donut::engine::animation::Keyframe,std::allocator<donut::engine::animation::Keyframe> >::_Reallocation_policy  mN  std::_Pocma_values . B0  std::_Tzdb_deleter<__std_tzdb_sys_info> 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> � Φ  std::_Compressed_pair<std::default_delete<std::vector<GeometryDebugData,std::allocator<GeometryDebugData> > >,std::vector<GeometryDebugData,std::allocator<GeometryDebugData> > *,1> ! /  std::hash<std::error_code> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > N 	@  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > ( 筣  std::ratio<1,1000000000000000000> @ nY  std::_Default_allocator_traits<std::allocator<char32_t> > ( d  std::_Atomic_padded<unsigned int> � 敧  std::vector<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > > � b�  std::vector<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > >::_Reallocation_policy  �?  std::allocator<char32_t> ? !D  std::unique_ptr<char [0],std::default_delete<char [0]> > � 毜  std::unique_ptr<std::vector<GeometryDebugData,std::allocator<GeometryDebugData> >,std::default_delete<std::vector<GeometryDebugData,std::allocator<GeometryDebugData> > > > � }  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > ? y�  std::equal_to<std::shared_ptr<donut::engine::Material> > � 定  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > $ �  std::_Atomic_integral<long,4> � 1�  std::list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > +   std::_Ref_count_obj2<MeshGeometryEx>  L<  std::_Flist_unchecked_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,std::_Iterator_base0> � r�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > >,1> R =K  std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >  桺  std::hash<bool> � K�  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >     std::streamsize 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty � �;  std::_Compressed_pair<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >,std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,1> ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> \ j�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > ��  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > > 頃  std::enable_shared_from_this<donut::engine::SceneGraph> � �:  std::tuple<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > )   std::_Ref_count_obj2<GameSettings> M [�  std::_Arg_types<std::basic_string_view<char,std::char_traits<char> > > " b  std::lock_guard<std::mutex> 0 x�  std::_Ref_count_obj2<PerspectiveCameraEx> N 蔝  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone> > K I�  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > x X�  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > ] �:  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > dQ�  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::MeshInfo>,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> >,0> > W \� std::basic_stringbuf<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > S 礥  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone_link> > % V�  std::_Ptr_base<SampleSettings> k =�  std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �  std::hash<long double> � 侤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � Q@  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy [ �<  std::_Flist_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > >   �  std::_Comparison_category / 8�  std::shared_ptr<donut::engine::MeshInfo> X}  std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>  f  std::try_to_lock_t _ P�  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > > U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ �5  std::_Decode_result<char32_t> # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0  w  std::hash<double> 5 箵  std::shared_ptr<donut::engine::SceneGraphLeaf> O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> , �  std::allocator<std::_Container_proxy> n �:  std::_Tuple_val<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > >  臁  std::optional<bool> < N�  std::optional<bool>::_AllowUnwrappingAssignment<bool> " 猘  std::_Align_type<double,72> G �;  std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >  �5  std::_Lazy_locale / Y  std::_Char_traits<char32_t,unsigned int>  >/  std::_System_error < 朿  std::_Atomic_padded<std::_Stop_callback_base const *> ( �)  std::hash<nvrhi::FramebufferInfo>  螸  std::_Fmt_buffer<char> 9 桰  std::allocator<std::filesystem::_Find_file_handle> b 衶  std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > + 顭  std::shared_ptr<PerspectiveCameraEx>  �.  std::error_condition % 砞  std::integral_constant<bool,0>  �  std::bad_exception & 肐  std::_Zero_then_variadic_args_t K L�  std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > �D�  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >,0> d @�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > _ >�  std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > / 甝  std::_General_precision_tables_2<double> � 6�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > > > ? $`  std::_Atomic_storage<std::_Stop_callback_base const *,8> � ]L  std::_Compressed_pair<std::allocator<std::chrono::time_zone>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >,1>  �  std::u32string -�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > N {J  std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>  �-  std::invalid_argument   玗  std::char_traits<char8_t> w   std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > - R�  std::_Ref_count_obj2<EnvironmentLight> [   std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > . ]  std::_General_precision_tables_2<float> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > { )�  std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<Json::Value::CZString const ,Json::Value> > > > 1 臺  std::_Atomic_integral_facade<unsigned int>  瀉  std::cv_status S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 s�  std::_Vector_val<std::_Simple_types<float> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > b L:  std::unique_ptr<__std_tzdb_time_zones_info,std::_Tzdb_deleter<__std_tzdb_time_zones_info> > A i�  std::allocator_traits<std::allocator<nvrhi::BufferRange> > + rD  std::pair<enum __std_win_error,bool> S  ?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char8_t> > >  �5  std::_Fmt_codec_base<0> d 尧  std::_Compressed_pair<std::default_delete<Json::CharReader::Impl>,Json::CharReader::Impl *,1> J ]  std::allocator_traits<std::allocator<std::chrono::time_zone_link> > � 舸  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> > >  鑐  std::thread  a  std::thread::id S Y?  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �-  std::length_error \ [�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > # 姍  std::_Ptr_base<GameSettings>  3a  std::jthread F TU  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 烮  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! s  std::numeric_limits<float>  �4  std::time_base   }4  std::time_base::dateorder � 愦  std::_Tmap_traits<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> >,0> ) �  std::_Atomic_integral_facade<long> = 岽  std::allocator_traits<std::allocator<PTMaterialData> > i 鐊  std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >  絘  std::mutex 8 '�  std::_Ptr_base<donut::engine::animation::Sampler> Q �  std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > % 盤  std::hash<enum nvrhi::BlendOp> � 哟  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> > > c g�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > B Y�  std::allocator<std::shared_ptr<donut::engine::IShadowMap> > � R�  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> > > c &�  std::_Simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >  �  std::_Ref_count_base " VP  std::hash<unsigned __int64> f 9  std::unique_ptr<__std_tzdb_current_zone_info,std::_Tzdb_deleter<__std_tzdb_current_zone_info> > 1 癒  std::_Flist_node<std::chrono::tzdb,void *>  ]  std::ratio<60,1> k 軰  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_time_zones_info>,__std_tzdb_time_zones_info *,1> S x�  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > > ] 弗  std::_Func_class<void,std::basic_string_view<char,std::char_traits<char> > >::_Storage : +6  std::_String_view_iterator<std::char_traits<char> > 3 覊  std::_Ptr_base<donut::engine::LoadedTexture>  '  std::exception_ptr  �  std::strong_ordering  ]  std::ratio<1,1000000> [ B�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneCamera> > > % �  std::_Itraits_pointer_strategy � 将  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > >,1> C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > )   std::hash<enum nvrhi::BlendFactor>  棣  std::_Wrap<MeshInfoEx> 讪  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > > M �  std::_Default_allocator_traits<std::allocator<std::_Container_proxy> > * 谉  std::_Ptr_base<PerspectiveCameraEx> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t>  �.  std::once_flag  �.  std::error_code T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy ' �4  std::pair<char *,enum std::errc> * �=  std::_Optional_destruct_base<int,1> ! �4  std::__floating_decimal_32 J :w  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < �\  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  �3  std::_Iosb<int>   �3  std::_Iosb<int>::_Seekdir ! �3  std::_Iosb<int>::_Openmode   �3  std::_Iosb<int>::_Iostate ! �3  std::_Iosb<int>::_Fmtflags # �3  std::_Iosb<int>::_Dummy_enum 7 齖  std::allocator_traits<std::allocator<char32_t> >  ~[  std::nano I 7L  std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> | =�  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> � 猝  std::_Alloc_construct_ptr<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > U 4�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > 0璃  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> M G�  std::_Func_base<bool,std::shared_ptr<donut::engine::Material> const &> M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 Y  std::_Char_traits<char16_t,unsigned short> $ 睓  std::shared_ptr<GameSettings> 6 緡  std::_Ptr_base<donut::engine::DescriptorHandle> a _<  std::_Flist_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > $ �)  std::hash<nvrhi::BufferRange> V ^;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> > l -;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >::_Reallocation_policy T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  "1  std::_Locbase<int> " 鈙  std::shared_ptr<std::mutex> i 恾  std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> 9 $z  std::shared_ptr<donut::engine::CommonRenderPasses> ) 彨  std::default_delete<MeshDebugData> S bX  std::forward_list<std::chrono::tzdb,std::_Crt_allocator<std::chrono::tzdb> > ! 郳  std::char_traits<char16_t> 栓  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > >,1> � 瞾  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > [ 2�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshInstance> > >  �  std::tuple<> 5 �<  std::_Atomic_pointer<std::chrono::tzdb_list *> P 燯  std::_Uninitialized_backout_al<std::allocator<std::chrono::leap_second> >  �  std::_Container_base12 W 轡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  t  std::shared_mutex - #�  std::weak_ptr<donut::engine::Material>  	/  std::io_errc  4  std::ios_base  )4  std::ios_base::_Fnarray  #4  std::ios_base::_Iosarray  �3  std::ios_base::Init  �3  std::ios_base::failure  �3  std::ios_base::event h (�  std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > 0 黲  std::integer_sequence<unsigned __int64,0> ) X  std::numeric_limits<unsigned char> * 印  std::_Optional_construct_base<bool> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type   d  std::numeric_limits<long> " 衆  std::initializer_list<char> N 懓  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &> X #�  std::_Func_class<void,std::shared_ptr<donut::engine::Material> const &>::_Storage  �  std::_Invoker_strategy  鯟  std::nothrow_t � L�  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1>  檁  std::_Stop_state 荺  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > T  �  std::function<void __cdecl(std::shared_ptr<donut::engine::MeshInfo> const &)> $ �  std::_Default_allocate_traits � x�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphNode> > >,1>   璍  std::_Fmt_buffer<wchar_t> � 绯  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > M 0?  std::_String_iterator<std::_String_val<std::_Simple_types<char8_t> > > N �?  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 瞈  std::allocator_traits<std::allocator<char> > 0 A�  std::_Ptr_base<donut::engine::IShadowMap> ! `  std::numeric_limits<short>  u   std::_Vbase . S  std::allocator<nvrhi::rt::GeometryDesc> # 醓  std::unique_lock<std::mutex> ! ⅵ  std::_Wrap<SampleSettings>  痋  std::ratio<2629746,1> ( 苭  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > � 宄  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture>,void *> > > c 疾  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > ! �3  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > + �5  std::_Grapheme_Break_property_values � 猿  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > >,1> < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 8 
�  std::weak_ptr<donut::engine::SkinnedMeshInstance> , d�  std::shared_ptr<donut::engine::Light> 2 化  std::_Ptr_base<donut::engine::TextureCache> 9莪  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > % m�  std::_Ptr_base<MeshGeometryEx> � 坛  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::SceneCamera> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneCamera> > >,1> ^ 某  std::is_nothrow_move_constructible<`donut::vfs::enumerate_to_vector'::`2'::<lambda_1> > ^ 鲁  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> > > O 镻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock Y 闯  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::IShadowMap> > > y 毳  std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > � 鯧  std::_Compressed_pair<std::allocator<std::chrono::leap_second>,std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >,1>  �  std::bad_alloc  ^.  std::underflow_error B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> ) 礚  std::_Fmt_counting_buffer<wchar_t> c 蔏  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_leap_info [0]>,__std_tzdb_leap_info *,1>  �  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � �  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy % ア  std::allocator<PTMaterialData> _ �  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &> J 侷  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D   std::allocator<std::shared_ptr<donut::engine::MeshInstance> > D oI  std::_Compressed_pair<std::default_delete<char [0]>,char *,1> � A}  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0>   騙  std::atomic<unsigned int>  L5  std::messages_base  m�  std::_Wrap<MaterialEx> - 遊  std::_Crt_allocator<std::chrono::tzdb> d担  std::_Hash<std::_Umap_traits<std::shared_ptr<donut::engine::Material>,unsigned int,std::_Uhash_compare<std::shared_ptr<donut::engine::Material>,std::hash<std::shared_ptr<donut::engine::Material> >,std::equal_to<std::shared_ptr<donut::engine::Material> > >,std::allocator<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> >,0> > g 癥  std::_Normal_allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  .  std::out_of_range # f  std::numeric_limits<__int64> _ 苿  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 晞  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i 鮅  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > " 骵  std::initializer_list<bool> b ３  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > 櫝  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > >,1>  +3  std::ctype<char> @ W�  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > s 劝  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > N 姵  std::_Vector_val<std::_Simple_types<donut::engine::SceneImportResult> > d �:  std::_Tuple_val<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > > ) ,�  std::default_delete<OmmBuildQueue> P ��  std::_Vector_val<std::_Simple_types<donut::engine::animation::Keyframe> > ? v�  std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >  h  std::memory_order � q�  std::map<Json::Value::CZString,Json::Value,std::less<Json::Value::CZString>,std::allocator<std::pair<Json::Value::CZString const ,Json::Value> > > Z 静  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � 安  std::list<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int>,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ! (b  std::recursive_timed_mutex  �4  std::chars_format � ¨  std::_Compressed_pair<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Vector_val<std::_Simple_types<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,1> q   std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > � s�  std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::_Reallocation_policy " 磗  std::condition_variable_any 6 鮯  std::condition_variable_any::_Cv_any_notify_all  T4  std::nullopt_t  V4  std::nullopt_t::_Tag  賉  std::ratio<3600,1> # �  std::_Atomic_storage<long,4> , �<  std::atomic<std::chrono::tzdb_list *> # *  std::hash<nvrhi::BlendState> / 譡  std::shared_ptr<donut::vfs::IFileSystem>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  U  std::in_place_t K D�  std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > z f}  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > D jL  std::_Vector_val<std::_Simple_types<std::chrono::time_zone> > 3 R�  std::shared_ptr<donut::engine::MeshGeometry>  a/  std::system_error < {X  std::_Default_allocator_traits<std::allocator<char> > � =�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > > > > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 括  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > > N 鬆  std::_Func_class<bool,std::shared_ptr<donut::engine::Material> const &> X "�  std::_Func_class<bool,std::shared_ptr<donut::engine::Material> const &>::_Storage g 楺  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> > >    std::ratio<1,1> & 劆  std::shared_ptr<MeshGeometryEx> ^ �;  std::unique_ptr<__std_tzdb_leap_info [0],std::_Tzdb_deleter<__std_tzdb_leap_info [0]> > R 4�  std::_Compressed_pair<std::default_delete<OmmBuildQueue>,OmmBuildQueue *,1> 7 (�  std::optional<OpacityMicroMapUIData::BuildState> 2 楠 std::_Measure_string_prefix_iterator_legacy ) �=  std::_Optional_construct_base<int>   D[  std::forward_iterator_tag  ..  std::runtime_error � 鞅  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > � 璞  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture>,void *> >   
  std::bad_array_new_length B 1�  std::vector<PTMaterialData,std::allocator<PTMaterialData> > X 驖  std::vector<PTMaterialData,std::allocator<PTMaterialData> >::_Reallocation_policy E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > 9 岜  std::allocator<donut::engine::animation::Keyframe> / 綾  std::_Atomic_storage<unsigned __int64,8> K 诒  std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > !   std::_Ptr_base<MaterialEx>  舄 std::_Fmt_codec<char,1>  �0  std::_Yarn<char> N 帰  std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z WY  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  鑕  std::allocator<bool> � 激  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *>  �  std::u16string _ 	�  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 貎  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy � 骚  std::_Hash_find_last_result<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *>  ^  std::nested_exception  �  std::_Distance_unknown ) 殐  std::allocator<nvrhi::BufferRange> H jX  std::pointer_traits<std::_Flist_node<std::chrono::tzdb,void *> *> 3 颖  std::shared_ptr<donut::engine::GltfImporter> ( j  std::numeric_limits<unsigned int> 7 K  std::basic_ostream<char,std::char_traits<char> > < 7U  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> & o�  std::shared_ptr<SampleSettings> � ,}  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > E   std::allocator_traits<std::allocator<std::chrono::time_zone> > , 2  std::codecvt<char32_t,char,_Mbstatet> 1 聤  std::_Ptr_base<donut::engine::BufferGroup> | �}  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 1 Z�  std::shared_ptr<donut::engine::IShadowMap> C �  std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> �諡  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,PTTexture,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,PTTexture> >,0> > ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy F q�  std::allocator<std::shared_ptr<donut::engine::SceneGraphNode> >     std::streamoff 0 h�  std::vector<float,std::allocator<float> > F 6�  std::vector<float,std::allocator<float> >::_Reallocation_policy 0 袚  std::_Ptr_base<donut::engine::SceneGraph> # a�  std::_Wrap<EnvironmentLight>  �  std::atomic<long> & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> , �2  std::_Codecvt_guard<char8_t,char32_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' w  std::numeric_limits<long double>  /  std::errc ^ �  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::Light> > > " hX  std::pointer_traits<char *> } +�  std::_Compressed_pair<std::allocator<unsigned __int64>,std::_Deque_val<std::_Deque_simple_types<unsigned __int64> >,1> J 喊  std::_Default_allocator_traits<std::allocator<unsigned __int64 *> >  覗  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > > � 爾  std::vector<std::shared_ptr<donut::engine::MeshInstance>,std::allocator<std::shared_ptr<donut::engine::MeshInstance> > >::_Reallocation_policy � �  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::Light> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::Light> > >,1> , 凷  std::default_delete<std::_Facet_base> 9 惈 std::basic_ios<wchar_t,std::char_traits<wchar_t> > e Y�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >  v.  std::range_error  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �4  std::to_chars_result  颽  std::_UInt_is_zero  �  std::_Compare_eq y 調  std::_Uhash_choose_transparency<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,void>  ~[  std::ratio<1,1000000000>  �?  std::allocator<char16_t> $ hI  std::default_delete<char [0]> C ケ  std::allocator<std::shared_ptr<donut::engine::SceneCamera> > . 糱  std::vector<bool,std::allocator<bool> > J 鷤  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 蓚  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  |[  std::ratio<1,1000> h ;�  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > > i 灡  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >  z[  std::ratio<1,10000000>  沪  std::_Wrap<GameSettings> 敱  std::unordered_map<std::shared_ptr<donut::engine::MeshInfo>,unsigned int,std::hash<std::shared_ptr<donut::engine::MeshInfo> >,std::equal_to<std::shared_ptr<donut::engine::MeshInfo> >,std::allocator<std::pair<std::shared_ptr<donut::engine::MeshInfo> const ,unsigned int> > > ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> d 戡  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const &>  $1  std::_Crt_new_delete % �/  std::_Iostream_error_category2 * 糛  std::_String_constructor_concat_tag j 拾  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > A 及  std::allocator_traits<std::allocator<unsigned __int64 *> > 4 c`  std::atomic<std::_Stop_callback_base const *>  �8  std::allocator<char> 秚  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> > T   std::function<void __cdecl(std::shared_ptr<donut::engine::Material> const &)> 7 t�  std::shared_ptr<donut::engine::Scene::Resources>  愍 std::_Fmt_codec_base<1> ) g�  std::allocator<unsigned __int64 *>    std::nullptr_t =葠  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> > L嚛  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Range_eraser K!�  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >,0> >::_Clear_guard . 鏧  std::_Char_traits<char8_t,unsigned int> ' x[  std::_Floating_to_chars_overload & H[  std::random_access_iterator_tag 4 汫  std::shared_ptr<donut::engine::ShaderFactory> ; 梷  std::_Conditionally_enabled_hash<unsigned __int64,1> R 酙  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > � ��  std::_Func_impl_no_alloc<`donut::vfs::enumerate_to_vector'::`2'::<lambda_1>,void,std::basic_string_view<char,std::char_traits<char> > >  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> 3 P0  std::_Tzdb_deleter<__std_tzdb_leap_info [0]> @ 闎  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> # 鄀  std::allocator<unsigned int>  �0  std::_Yarn<wchar_t> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> " *�  std::shared_ptr<PTMaterial> 7 G�  std::allocator<donut::engine::SceneImportResult>  b  std::wstring } U�  std::_Uninitialized_backout_al<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > 5 譪  std::_Atomic_integral_facade<unsigned __int64> z F�  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ' V  std::numeric_limits<signed char> � 獲  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > � 鳕  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > >  �-  std::domain_error  �  std::u32string_view �   std::pair<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> *,bool>  �  std::_Container_base 1 钃  std::shared_ptr<donut::engine::SceneGraph> [ 2�  std::unique_ptr<Json::CharReader::Impl,std::default_delete<Json::CharReader::Impl> >  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  5  std::_Big_integer_flt { @�  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > > � �  std::vector<std::shared_ptr<donut::engine::IShadowMap>,std::allocator<std::shared_ptr<donut::engine::IShadowMap> > >::_Reallocation_policy I 藩  std::unique_ptr<MeshDebugData,std::default_delete<MeshDebugData> > Z 澂  std::_Default_allocator_traits<std::allocator<donut::engine::animation::Keyframe> >  �  std::_Literal_zero ; w  std::weak_ptr<donut::engine::DescriptorTableManager> $ cP  std::hash<nvrhi::IResource *> 3 爷  std::shared_ptr<donut::engine::TextureCache> 4   std::_Ptr_base<donut::engine::SceneGraphLeaf>  �4  std::from_chars_result � *L  std::_Compressed_pair<std::allocator<std::chrono::time_zone_link>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >,1> " w4  std::_Nontrivial_dummy_type , 挕  std::_Optional_destruct_base<float,1> � 猑  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char> D 譭  std::_Default_allocator_traits<std::allocator<unsigned int> > 8 �6  std::chrono::duration<int,std::ratio<2629746,1> > i u;  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<3600,1> > > & 28  std::chrono::year_month_weekday  u6  std::chrono::day 9 D-  std::chrono::duration<__int64,std::ratio<1,1000> > j %7  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<86400,1> > > n :=  std::chrono::time_point<std::chrono::gps_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  7  std::chrono::weekday  �,  std::chrono::nanoseconds  �6  std::chrono::year ' 8  std::chrono::year_month_day_last  -  std::chrono::minutes  �6  std::chrono::days y N=  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �,  std::chrono::duration<__int64,std::ratio<1,1000000000> > # L7  std::chrono::weekday_indexed 9 �6  std::chrono::duration<int,std::ratio<31556952,1> >  �6  std::chrono::years , fX  std::chrono::duration_values<__int64>  v,  std::chrono::seconds " �8  std::chrono::time_zone_link 6 �6  std::chrono::duration<int,std::ratio<86400,1> > 3 -  std::chrono::duration<int,std::ratio<60,1> > 6 v,  std::chrono::duration<__int64,std::ratio<1,1> > s �,  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   q[  std::chrono::steady_clock   X  std::chrono::system_clock 6 *-  std::chrono::duration<double,std::ratio<60,1> > + X8  std::chrono::year_month_weekday_last  <  std::chrono::tzdb_list + l<  std::chrono::tzdb_list::_Unique_lock + *<  std::chrono::tzdb_list::_Shared_lock ; �-  std::chrono::duration<double,std::ratio<1,1000000> > > �-  std::chrono::duration<double,std::ratio<1,1000000000> > = \,  std::chrono::duration<__int64,std::ratio<1,10000000> > " �7  std::chrono::month_day_last  %7  std::chrono::sys_days n =  std::chrono::time_point<std::chrono::utc_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  �8  std::chrono::time_zone q L,  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >   `7  std::chrono::weekday_last ! �7  std::chrono::month_weekday 5 �,  std::chrono::duration<int,std::ratio<3600,1> >  i7  std::chrono::last_spec & �7  std::chrono::month_weekday_last  :7  std::chrono::local_days j �8  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,1> > > N SK  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > > Z .>  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::<lambda_1>  �8  std::chrono::sys_seconds  �7  std::chrono::year_month 8 Z-  std::chrono::duration<double,std::ratio<1,1000> >  s7  std::chrono::month_day " �7  std::chrono::year_month_day  �,  std::chrono::hours < q-  std::chrono::duration<__int64,std::ratio<1,1000000> > @ m[  std::chrono::zoned_traits<std::chrono::time_zone const *> 5 �,  std::chrono::duration<double,std::ratio<1,1> >  �6  std::chrono::months  89  std::chrono::tzdb e :7  std::chrono::time_point<std::chrono::local_t,std::chrono::duration<int,std::ratio<86400,1> > >  @Y  std::chrono::gps_clock  =Y  std::chrono::tai_clock  �6  std::chrono::month n "=  std::chrono::time_point<std::chrono::tai_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  dX  std::chrono::utc_clock  h[  std::chrono::local_t & �=  std::chrono::_Time_parse_fields 8 y=  std::chrono::_Time_parse_fields::_Leap_second_rep = w=  std::chrono::_Time_parse_fields::_Parse_tp_or_duration 3 Y=  std::chrono::_Time_parse_fields::_FieldFlags H g[  std::chrono::duration<__int64,std::ratio<1,1000000000000000000> >  �8  std::chrono::leap_second 8 �,  std::chrono::duration<double,std::ratio<3600,1> >  �2  std::ctype_base  EA  std::filesystem::perms ' 繟  std::filesystem::directory_entry $ JA  std::filesystem::copy_options ( 5A  std::filesystem::filesystem_error 7 rR  std::filesystem::_Path_iterator<wchar_t const *> ) �>  std::filesystem::_Find_file_handle & y>  std::filesystem::_Is_slash_oper . 螧  std::filesystem::_Should_recurse_result $   std::filesystem::perm_options 4 滳  std::filesystem::recursive_directory_iterator . �A  std::filesystem::_File_status_and_error & 9B  std::filesystem::_Dir_enum_impl 0 KB  std::filesystem::_Dir_enum_impl::_Creator @ QB  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! TA  std::filesystem::file_type . nB  std::filesystem::_Directory_entry_proxy " 揇  std::filesystem::space_info * 桞  std::filesystem::directory_iterator & N=  std::filesystem::file_time_type 0 虰  std::filesystem::_Recursive_dir_enum_impl ) 鞟  std::filesystem::directory_options # gA  std::filesystem::file_status u 霡  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( U[  std::filesystem::_File_time_clock  ?  std::filesystem::path $ �>  std::filesystem::path::format * ?R  std::filesystem::_Normal_conversion ? &� std::basic_streambuf<wchar_t,std::char_traits<wchar_t> > < gU  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �1  std::codecvt<char16_t,char,_Mbstatet> , �2  std::_Codecvt_guard<char32_t,char8_t> � u  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  R[  std::char_traits<char> � 镮  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �.  std::error_category ) �.  std::error_category::_Addr_storage  \~  std::_Wrap<std::mutex> � 窏  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > > � 厳  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimation>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimation> > >::_Reallocation_policy i o:  std::tuple<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! �/  std::_System_error_message  �  std::_Unused_parameter =   std::allocator<std::shared_ptr<donut::engine::Light> > h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  l4  std::bad_optional_access A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > , 確  std::_Atomic_integral<unsigned int,4> 7 !B  std::shared_ptr<std::filesystem::_Dir_enum_impl> G 但  std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > ! �  std::_Ptr_base<MeshInfoEx> + 摝  std::_Ref_count_obj2<SampleSettings> f 3�  std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> " DT  std::_Floating_point_string = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> 2 �5  std::_Extended_Pictographic_property_values  �1  std::_Codecvt_mode  A   std::max_align_t @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > z 爙  std::_Compressed_pair<std::hash<unsigned __int64>,std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1>,1> ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn $ q?  std::ranges::_Iter_move::_Cpo ) o?  std::ranges::_Iter_move::_Cpo::_St % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn � く  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > ! 1�  std::_Wrap<MeshGeometryEx>    std::_Exact_args_t  P[  std::ratio<86400,1>  V6  std::default_sentinel_t � A  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > >  N[  std::ratio<31556952,1> Q 煰  std::allocator_traits<std::allocator<donut::engine::animation::Keyframe> > 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8>  =K  std::ostringstream � 懐  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > >  6  std::_Fmt_buffer_traits ' i�  std::_Ptr_base<EnvironmentLight> \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  g0  std::_Facet_base b 姱  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::SceneGraphAnimation> > > ' �)  std::hash<nvrhi::BindingSetItem> + Q�  std::_Ptr_base<donut::engine::Light> " 甔  std::_WChar_traits<wchar_t> 2 �2  std::codecvt<unsigned short,char,_Mbstatet> . �=  std::_Optional_destruct_base<__int64,1> - �=  std::_Optional_construct_base<__int64> c ��  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::SkinnedMeshInstance> > > z 韃  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> # �/  std::_Generic_error_category  扟  std::streampos  B[  std::input_iterator_tag 2 JU  std::_Wrap<std::filesystem::_Dir_enum_impl> � 褧  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > > � 煏  std::vector<std::shared_ptr<donut::engine::SceneGraphAnimationChannel>,std::allocator<std::shared_ptr<donut::engine::SceneGraphAnimationChannel> > >::_Reallocation_policy X 孲  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> ��  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::animation::Sampler>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > 0 芻  std::_Atomic_integral<unsigned __int64,8> , 塪  std::_Atomic_padded<unsigned __int64> ' 綪  std::hash<enum nvrhi::ColorMask> / ,2  std::codecvt<char16_t,char8_t,_Mbstatet> 5 
`  std::_Locked_pointer<std::_Stop_callback_base> 3 豃  std::basic_ios<char,std::char_traits<char> >  �1  std::codecvt_base t r�  std::allocator_traits<std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > & 譒  std::_Fmt_counting_buffer<char> �   std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *> I p�  std::unique_ptr<OmmBuildQueue,std::default_delete<OmmBuildQueue> >  錎  std::bad_function_call O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag 9 輵  std::_Ptr_base<donut::engine::SkinnedMeshInstance> ' P~  std::_Ref_count_obj2<std::mutex> � P�  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > �  �  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Reallocation_policy ; 賓  std::allocator_traits<std::allocator<unsigned int> > X 憸  std::function<void __cdecl(std::basic_string_view<char,std::char_traits<char> >)> ' 蜠  std::hash<std::filesystem::path> 	r}  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> � L}  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > R W�  std::_Func_base<void,std::basic_string_view<char,std::char_traits<char> > > [ b�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > f 憵  std::vector<donut::engine::SceneImportResult,std::allocator<donut::engine::SceneImportResult> > | 1�  std::vector<donut::engine::SceneImportResult,std::allocator<donut::engine::SceneImportResult> >::_Reallocation_policy _ ?[  std::allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > > 7 �<  std::_Atomic_storage<std::chrono::tzdb_list *,8>  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > 7 40  std::_Tzdb_deleter<__std_tzdb_current_zone_info> " �  std::_Asan_aligned_pointers 4 U�  std::_Ptr_base<donut::engine::SceneGraphNode> � 姝  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> > > F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  q5  std::format_error  �  std::partial_ordering � |�  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > > > *> # |5  std::_Decode_result<wchar_t> ! �  std::_Ptr_base<PTMaterial> ^ 色  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > > . <#  std::array<nvrhi::BindingLayoutItem,16>  9[  std::_Default_sentinel P 耎  std::_Default_allocator_traits<std::allocator<std::chrono::leap_second> > $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int> �仺  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::animation::Sampler> > > >,1> E   std::deque<unsigned __int64,std::allocator<unsigned __int64> > O 嚁  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Is_bidi U 啍  std::deque<unsigned __int64,std::allocator<unsigned __int64> >::_Pop_direction   �5  std::_GB11_LeftHand_regex * �5  std::_GB11_LeftHand_regex::_State_t  �  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::shared_ptr<donut::engine::Material> const ,unsigned int> > >,1> 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access 
 !   wint_t  K�  VolumePTConstants & 庐  $_TypeDescriptor$_extraBytes_40  萫  nvrhi::IShaderLibrary # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling $ �(  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets 0 緀  nvrhi::RefCountPtr<nvrhi::IShaderLibrary>  &  nvrhi::GraphicsState / W  nvrhi::static_vector<nvrhi::Viewport,16>  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray ) 艷  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! 緀  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc 2 馟  nvrhi::RefCountPtr<nvrhi::IComputePipeline>  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice 6 �  nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray . 鮞  nvrhi::RefCountPtr<nvrhi::ICommandList>  艷  nvrhi::DeviceHandle   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture ! 蒪  nvrhi::utils::ScopedMarker $ =b  nvrhi::utils::BitSetAllocator . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  �(  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # {w  nvrhi::DescriptorTableHandle  �(  nvrhi::TimerQueryHandle 2 {w  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �(  nvrhi::HeapHandle # 馟  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - H  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  �(  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  賧  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  lH  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 賧  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  H  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 繸  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   �(  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # 齺  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  鮞  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable * lH  nvrhi::RefCountPtr<nvrhi::ITexture>  �&  nvrhi::ComputeState 2 齺  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery  G�  PTTexture  �/  __std_win_error  S0  __std_tzdb_leap_info  �0  lconv   �  __RTTIBaseClassDescriptor & 局  $_TypeDescriptor$_extraBytes_72 
    _off_t    stat  t   int32_t  Z  timespec & JZ  $_TypeDescriptor$_extraBytes_37  cD  __std_fs_file_id 
 !   _ino_t 
 沘  _Cnd_t # 70  __std_tzdb_current_zone_info ' ID  __std_fs_create_directory_result    PTMaterialBase  !   uint16_t  �/  __std_fs_stats    MaterialsBaker * 菓  donut::engine::SkinnedMeshReference ! �  donut::engine::SceneCamera $ 緬  donut::engine::SceneGraphNode 0 噿  donut::engine::SceneGraphNode::DirtyFlags " 洃  donut::engine::MeshInstance ) 簯  donut::engine::SkinnedMeshInstance / ��  donut::engine::MeshGeometryPrimitiveType   &�  donut::engine::SceneGraph > [�  donut::engine::ResourceTracker<donut::engine::MeshInfo> ( 袛  donut::engine::AnimationAttribute $ B�  donut::engine::SceneGraphLeaf ! 焪  donut::engine::BufferGroup  9�  donut::engine::Material * 幀  donut::engine::Material::HairParams 0 埇  donut::engine::Material::SubsurfaceParams  鼧  donut::engine::Scene ' ~�  donut::engine::PerspectiveCamera  7�  donut::engine::Light ' 2�  donut::engine::SceneContentFlags  鈝  donut::engine::MeshInfo & I�  donut::engine::DirectionalLight & 翑  donut::engine::SceneGraphWalker ' 6�  donut::engine::SceneLoadingStats &   donut::engine::SceneTypeFactory ( {�  donut::engine::animation::Sampler ) 2�  donut::engine::animation::Keyframe ) �  donut::engine::animation::Sequence  褀  donut::engine::MeshType  Z�  donut::engine::SpotLight " +t  donut::engine::BindingCache # 劕  donut::engine::LoadedTexture & 輛  donut::engine::DescriptorHandle , Ow  donut::engine::DescriptorTableManager B w  donut::engine::DescriptorTableManager::BindingSetItemsEqual B w  donut::engine::DescriptorTableManager::BindingSetItemHasher % 墂  donut::engine::VertexAttribute 0 饠  donut::engine::SceneGraphAnimationChannel " 
�  donut::engine::MeshGeometry % t   donut::engine::DescriptorIndex > A�  donut::engine::ResourceTracker<donut::engine::Material> M 覡  donut::engine::ResourceTracker<donut::engine::Material>::ConstIterator   k�  donut::engine::PointLight ) _�  donut::engine::SceneGraphAnimation $ $�  donut::engine::MaterialDomain ' /�  donut::engine::SceneImportResult ' ~�  donut::vfs::enumerate_callback_t % 蹨  donut::vfs::RelativeFileSystem    donut::vfs::IBlob  脺  donut::vfs::IFileSystem  WI  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3  EG  donut::math::float2  鈷  donut::math::dquat # 錏  donut::math::vector<float,3>  $�  donut::math::int4  u   donut::math::uint  /F  donut::math::plane ! $�  donut::math::vector<int,4>  �  donut::math::daffine3  �  donut::math::double3 # F  donut::math::vector<float,4> $ �  donut::math::vector<double,3>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes $ 鳟  donut::math::vector<double,4>  F  donut::math::float4 & _�  donut::math::matrix<double,3,3> % WI  donut::math::matrix<float,4,4> # 揈  donut::math::affine<float,3>   4I  donut::math::box<float,3> " *E  donut::math::vector<bool,2>  4I  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> $ �  donut::math::affine<double,3> & 鈷  donut::math::quaternion<double> M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const> ' 十  $_TypeDescriptor$_extraBytes_367    _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ! )0  __std_tzdb_time_zones_info     intmax_t    terminate_handler  �  _s__RTTIBaseClassArray & VZ  $_TypeDescriptor$_extraBytes_52 
 Y  ldiv_t  蕈  MeshInfoEx  �/  __std_fs_file_flags  �0  _Cvtvec & 飘  $_TypeDescriptor$_extraBytes_48  u   _Thrd_id_t - �  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray ' C�  $_TypeDescriptor$_extraBytes_290  �/  __std_fs_copy_options     ptrdiff_t  
  _stat64i32  赢  LocalConfig  E0  __std_tzdb_sys_info  �  _PMD      uint8_t & 衼  $_TypeDescriptor$_extraBytes_41  ,  type_info ' �  _s__RTTIClassHierarchyDescriptor  t   errno_t  �/  __std_fs_reparse_tag  \  _lldiv_t  C  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  K  Json::OStream  蠚  Json::ValueType   +�  Json::StreamWriterBuilder    Json::ValueIteratorBase  禌  Json::ValueConstIterator  井  Json::StreamWriter " �  Json::StreamWriter::Factory  <�  Json::CommentPlacement  #   Json::LargestUInt     Json::LargestInt  u   Json::UInt  u   Json::ArrayIndex  �  Json::CharReader  猗  Json::CharReader::Impl     Json::CharReader::Factory ( 煯  Json::CharReader::StructuredError     Json::Int64  倹  Json::PathArgument  z�  Json::PathArgument::Kind  \�  Json::Value  嵁  Json::Value::Comments ( d�  Json::Value::<unnamed-type-bits_>  t�  Json::Value::ValueHolder  k�  Json::Value::CZString + o�  Json::Value::CZString::StringStorage / O�  Json::Value::CZString::DuplicationPolicy  M�  Json::PrecisionType    Json::StaticString  �  Json::String  t   Json::Int  虥  Json::ValueIterator  #   Json::UInt64  G  _s__ThrowInfo  c6  __std_fs_convert_result  �/  __std_fs_stats_flags  W�  MaterialEx  �  __RTTIBaseClassArray  �  __crt_locale_data_public - �  $_s__CatchableTypeArray$_extraBytes_24  Ya  _Mtx_internal_imp_t & 4Z  $_TypeDescriptor$_extraBytes_25 % �  __RTTIClassHierarchyDescriptor  �0  _Collvec   艭  __std_fs_volume_name_kind 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t 
    fpos_t  �  FILE & ?�  $_TypeDescriptor$_extraBytes_17  A�  ExtendedScene 
 Ma  _Mtx_t 3 �  __vcrt_va_list_is_reference<wchar_t const *>    mbstate_t  ?  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray  DD  __std_fs_remove_result  篃  ExtendedSceneTypeFactory  F�  OmmBaker  靈  _Thrd_t - �  $_s__RTTIBaseClassArray$_extraBytes_16 , �  $_s__RTTIBaseClassArray$_extraBytes_8 - /Z  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  �/  __std_fs_file_attr  !�  GameSettings  �  __std_exception_data 
 u   _dev_t  �  MeshGeometryEx  紺  __std_ulong_and_error  竣  PTMaterialData  A   __std_tzdb_epoch_milli  \  lldiv_t  Y  _ldiv_t  [  _timespec64     intptr_t  u   uint32_t  c  ComputePass 
 �  _iobuf  ┇  MeshDebugData  �  __crt_locale_pointers �   p      揾配饬`vM|�%
犕�哝煹懿鏈椸  A    藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  �    觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �    
9信�;cGHR{匞U鐉�gPW$Y(厊\^s     仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  D   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   郖�Χ葦'S詍7,U若眤�M进`  �   +4[(広
倬禼�溞K^洞齹誇*f�5  4    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  r   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   Q雍憻Z]Ra芽浬膼鋹hwpC躶Q1�4�  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  &   l籴靈LN~噾2u�< 嵓9z0iv&jザ  x   +FK茂c�G1灈�7ほ��F�鳺彷餃�  �   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   v峞M� {�:稚�闙蛂龣 �]<��  ,   チ畴�
�&u?�#寷K�資 +限^塌>�j  `   v{犵帊螀轴5藢6褶帎w鵥錅g   �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  
   穫農�.伆l'h��37x,��
fO��  G   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   -;壱�#困C塟琕�6劎?"碄0;� 櫔轔  �   �7頔碠<晔@岙�撁k4統N絠熙鶳 �     G�膢刉^O郀�/耦��萁n!鮋W VS  R   憒峦锴摦懣苍劇o刦澬z�/s▄![�  �   o�椨�4梠"愜��
}z�$ )鰭荅珽X  �   v-�+鑟臻U裦@驍�0屽锯
砝簠@     ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  N   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  >   �	玮媔=zY沚�c簐P`尚足,\�>:O     E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   嵮楖"qa�$棛獧矇oPc续忴2#
  E	   �0�*е彗9釗獳+U叅[4椪 P"��  �	   )�Q�Ａ9氫�&煝]R埬�楜� 磛庋p�  �	   �)D舼PS橼鈝{#2{r�#獷欲3x(  
   �=蔑藏鄌�
艼�(YWg懀猊	*)  Y
   J8/�枭加�/[鳗V�3潴�5<s�$  v
   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �
   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�     觑v�#je<d鼋^r
u��闑鯙珢�  ,   渒�?^v)f启n?鶚鯼Y|縟痵5恰�]�  ^   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   ����lS窳艻BC唖�n�5鷂;需  �   扝	_u赂-墉MmJ鋉�-;騂钟@  #   龀缬@唶�鱮�瓗禽鐑�^]>Xz豹tN  g   cS�<蹪�1竰z舣?�[)Gwr �动  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   缱S炦噄�<^敾R}肸(3倁説�  .
   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �
   经H臣8v;注诶�#��
夔A17�	迒�#k_  �
   存*?\��-矪q7o責覃:},p穿奵�  �
   c�#�'�縌殹龇D兺f�$x�;]糺z�  L   o藾錚\F鄦泭|嚎醖b&惰�_槮  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�     穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  W   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �   Y愬鈖d>gQ�畵駺譼翂 M倷遑,h/�.  �   猯�諽!~�:gn菾�]騈购����'     鏀q�N�&}
;霂�#�0ncP抝  ?   dhl12� 蒑�3L� q酺試\垉R^{i�  ~   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �   副謐�斦=犻媨铩0
龉�3曃譹5D   	   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  I   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇     瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  G   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  &   交�,�;+愱`�3p炛秓ee td�	^,  g   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   eSO僌rM騮纚坵*L犁�L宵�)魐�  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  7   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  k   曀"�H枩U传嫘�"繹q�>窃�8  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  3   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  }   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   鹴y�	宯N卮洗袾uG6E灊搠d�  	   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  Y   _O縋[HU-銌�鼪根�鲋薺篮�j��  �   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  3   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  q   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  
   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  K   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   �X�& 嗗�鹄-53腱mN�<杴媽1魫  �   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  5   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  �   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<     x)憂^恛閶{籮>鐼末BA<颭~儽&'%;-g�  Z   .QVBL籺'Z鬖b濁蟂�8w,/kr彨�$擠�  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  6   *u\{┞稦�3壅阱\繺ěk�6U�  t   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-     z�0叐i�%`戉3猂|Ei韍訋�#Q@�  D   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮     河gz%2邫鴡L篰妣�X
=v蹵�闏�  2    d蜯�:＠T邱�"猊`�?d�B�#G騋  n   ZP偽玶;婫-%r铈鋳噟�*隙  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   僴>⊙絥躦懂�&t@缫菊茺轹魍朠O塨  2   =J�(o�'k螓4o奇缃�
黓睆=呄k_  n   煋�	y鋵@$5х葑愔*濋>�( 懪銳  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  )   鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  h   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  �   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �    狾闘�	C縟�&9N�┲蘻c蟝2  .    6衍Q擃D 紛~t加 唒4,憪剙綂嗈l�  X    �'稌� 变邯D)\欅)	@'1:A:熾/�  �    �"睱建Bi圀対隤v��cB�'窘�n  �    彲 ㄋ牐媝e哟�<N7%�Yn�52�峟�  /!   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  l!   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �!   ii(祍P%舌+;�'縿E╪嫏脈3L籾
�  �!   繃S,;fi@`騂廩k叉c.2狇x佚�  2"   W簅r�hc鷟;9攫�.�6Y柦阁��}.J╗  |"   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �"   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  #   苶T$k俥獛觐扗諨攱;懤{訳氀�#+詴4  G#   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �#   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �#   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  $   �*o驑瓂a�(施眗9歐湬

�  X$   襡桇�霏約�萇圱IV!枖jR�6頝椬  �$    I嘛襨签.濟;剕��7啧�)煇9触�.  �$   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  %   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  e%   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �%   メデ/纝z?�顉鮝驱�.Fg,c  �%   �咹怓%旗t暐GL慚ヌ��\T鳃�  &   Pフ+
V
*戋zydD躩=壠�jA�N盪{u5  K&   �9俅�(� .]嬝暧烵啤�秾*~w�=[@�  �&   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �&   <AE#(�/y鍹籲贚痶蹒-	蒸|邨VWTR  '   妇舠幸佦郒]泙茸餈u)	�位剎  X'   �嵪=�2}Qコk捑8噣酻:JY?�`  �'   靋!揕�H|}��婡欏B箜围紑^@�銵  �'   zY{���睃R焤�0聃
扨-瘜}  (   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  I(   f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  �(   �颠喲津,嗆y�%\峤'找_廔�Z+�  �(   蜅�萷l�/费�	廵崹
T,W�&連芿  )   v�%啧4壽/�.A腔$矜!洎\,Jr敎  X)   5�\營	6}朖晧�-w氌rJ籠騳榈  �)   t�j噾捴忊��
敟秊�
渷lH�#  �)   D���0�郋鬔G5啚髡J竆)俻w��  -*   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  l*   豊+�丟uJo6粑'@棚荶v�g毩笨C  �*   5啿g赬耢x ;`"郠oa!}榨k|{q�.)?X�  �*   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  
+   A縏 �;面褡8歸�-構�壋馵�2�-R癕  I+   匐衏�$=�"�3�a旬SY�
乢�骣�  �+   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �+   悯R痱v 瓩愿碀"禰J5�>xF痧  ,   �
bH<j峪w�/&d[荨?躹耯=�  ],   矨�陘�2{WV�y紥*f�u龘��  �,   +椬恡�
	#G許�/G候Mc�蜀煟-  �,   齝D屜u�偫[篔聤>橷�6酀嘧0稈  "-   a�傌�抣?�g]}拃洘銌刬H-髛&╟  `-   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �-   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �-   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  S.      �.   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �.   � 罟)M�:J榊?纸i�6R�CS�7膧俇  D/   仫減f╋萩}_遫R衵9弧0Up*q蹩鎍,  �/   f扥�,攇(�
}2�祛浧&Y�6橵�  �/   �芮�>5�+鮆"�>fw瘛h�=^���  0   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  T0   [届T藎秏1潴�藠?鄧j穊亘^a  �0   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �0   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  1   nA雙�鯯リM硔－'�鑔�,頓辒T  M1   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �1   �5︷騿嵃a\瞒蚶壩陠钤帋澾蛷;k�  �1   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  2   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  \2   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �2   饵嶝{郀�穮炗
AD2峵濝k鴖N  �2   L�9[皫zS�6;厝�楿绷]!��t  3   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  T3   4[剒2dnK 鲦�$龃馁=6f烛*銆^�  �3   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  �3   天e�1濎夑Y%� 褡\�Tā�%&閜�  �   l
      �  X  B   �  X  H   �  X  Y   �  X  �   b  h  �  r  x  U   s  x  �   �  h  �  �  h  �  �  h  x  �  �  K   �  �  f   I  �   N  J  �   B  R  �   �	  �  0  r   �  �  P   %  �   �  &  �   �  -  �   �  /  �   �  0  �   �  3  x  �  >  �   �  ?  �   0   �  �   �  �  �   D
  �  �   �  �  �   O   �  x  �  A  x  �  c  x  �   e  �  �   �  �   �  �  �   �  �  x  �    �   s    �   �  �  �   )
  (  x  �   +  �      {  �   x  9   x  �   �'  @  �   �'  @  �   (  @  �   �+     >   �+     ;   �+     8  �+     9  �+  �  �   ,  h  �  ,  h  Z  ,     �  ,     �  ,  h  �   ,  h  �  !,  h  �  ",  h  "  #,  h  5  (,  h  �  ),  h  �  *,  h  Z  +,  h  5  ,,  h  �  -,  h  �  .,  h  Z  /,  h  5  3,  8  �  5,     �  8,  h  5  ;,  h  �  <,  h  Z  ?,  h  "  @,  h  5  A,  8  l  B,  8  S  I,  h  �  J,  h  �  K,  h  5  Q,  h  �  S,  0  �   T,  h  �  U,  h  Z  \,  h  5  ],  h  5  a,  h  5  b,  h  t  f,     �  g,     �  h,  �	  �   i,  �	  �   j,  h  t  k,  h  t  l,  h  t  m,  h  t  n,  h  t  o,  h  t  s,  h  �  t,  h  t  u,  h  �  v,  h  t  w,  8  �  y,  `	  /  �,  h  t  �,  h  t  �,  h  �  �,  h  t  �,  h  t  �,  �   9
  �,  �	  �   �,  �	  �   �,  h  z  �,  h  z  �,  x  �  �,  `	    �,  `	    �,  �	  L  �,  �	  D  �,  �	  %   �,  h  �  �,    \   �,    \   �,    \   �,    \   �,  �   �  �,  h  �
  �,  h  i  �,  h  i  �,  h  i  �,  h  i  �,  h  �
  �,  h  i  �,  h  �
  �,  h  i  �,  h  �
  �,  h  �  �,  h  w  �,  h  �  �,  h  �  �,  h  w  �,  h  �  �,    c   �,    c   �,    c   �,  h  D  �,  h  D  �,  h  D  �,  h  D  �,  h  D  -  x  >  -  �   �  -  �  �   -  �  �   -  h     -  h  5  -  h     -  h     -  h  5  -  h  5  -  h  5  !-  h  5  :-  h  n  H-  h  n  K-  h  n  M-      �   R-  h  n  T-      �   W-      �   a-  h  n  d-  �   �  m-      M   n-      M   p-      M   �-  h  &  �-  h  9  �-  h  O  �-  h  &  �-  h  9  �-  h  &  �-  h  9  �-  h  &  �-  h  9  �-  h  :  �-  h  :  �-  h  :  �-  h  :  �-  h  &  �-  h     �-  h  5  �-  h     �-  h  9  �-  h  :  �-  h  &  �-  h     �-  h  9  �-  h  :  �-  h  &  �-  h  9  �-  h  U  �-  h  O  �-  h  O  �-  h  O  �-      �   �-      �   �-      �   �-  x  �  .  x  /  .  x  /  .  �  >   .  p  �   .  x  /  	.  �  I   .  x  /  
.  x  /  .  x  /  .  x  /  .  h  :  .  h  :  .  h  :  .  h  :  .  h  :  .  h  :  .  h  :  >.  h  �  @.  8  `  A.  h  �  I.  h  �  N.  @  �   O.  @  �   Q.  8  `  R.  h  �  T.  h  �  W.  h  `  X.  8  �  Y.  h  t  _.  h  t  b.  @  �   c.  8  �  d.  h  t  e.  h  t  i.  x  �  l.  x  �  m.  h  �  p.  h  t  q.  h  �  z.  �  �   {.  h  M  |.  �  �   }.  �  �   ~.  �  �   .  �  �   �.  �  �   �.  �  �   �.  h  V  �.  h  M  �.  h  M  �.  h  M  �.  �	  �   �.  �	  �   �.  �	  �   �.  x  9  �.  x  �  �.  x  �  �.  x  >  �.  x  >  �.  h     �.  h  5  �.  h     �.  h  5  �.  h     �.  h  5  �.  h  n  �.  8  �  �.  h  n  �.  8  �  �.      R   �.      R   �.      R   �.  x  �  �.  x  �  �.  x  �  �.  x  9  �.  x  9  �.  @  �   �.  @  �   �   d4   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stack D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\Rtxpt\OpacityMicroMap\OmmBaker.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\reader.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\Rtxpt\LocalConfig.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Config.h D:\RTXPT\External\Donut\include\donut\core\json.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\value.h D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\forwards.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\forward_list D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\allocator.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtree C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\RTXPT\Rtxpt\ExtendedScene.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\RTXPT\External\Donut\include\donut\core\math\quat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\include\donut\shaders\light_cb.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\charconv D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\RTXPT\Rtxpt\ExtendedScene.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h D:\RTXPT\External\Donut\include\donut\engine\Scene.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\RTXPT\External\Donut\include\donut\engine\SceneGraph.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream D:\RTXPT\External\Donut\include\donut\core\math\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\RTXPT\Rtxpt\Materials\MaterialsBaker.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\External\Donut\include\donut\engine\BindingCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\shared_mutex C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\condition_variable D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\version.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\writer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Donut\include\donut\core\math\color.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\RTXPT\Rtxpt\Shaders\SubInstanceData.h D:\RTXPT\External\Donut\include\donut\core\vfs\VFS.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_tzdb.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stop_token D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_print.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\misc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\RTXPT\Rtxpt\ComputePass.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\predefined C++ types (compiler internal) D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\format D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_format_ucd_tables.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\json.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\json\json_features.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bit D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Materials\MaterialPT.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\RTXPT\External\Donut\include\donut\engine\KeyframeAnimation.h D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\ExtendedScene.obj �       Lk  湴  �   牥  �  
 虐      砂     
 彰  �   倜  �  
 枰  �   煲  �  
 �%     �%    
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁裥砓>Y7?樰�=      �?                  �?                  �?      �?  �?  �?@SH冹 H嬟�    �H兡 [�
   g      �   �   5 G                     �,        �operator>><int> 
 >7�   node  AJ         
 >M   dest  AI  	       AK        	 
 Z   ?/                         H  0   7�  Onode  8   M  Odest  O   �   0                   $       \  �	   ]  �   ^  �,   �   0   �  
 Z   �   ^   �  
 y   �   }   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H嬟H孂�    劺t艱$D H婦$@H�H媆$0H兡 _肏峊$@荄$@    H嬒�    塂$@艱$DH婦$@H�H媆$0H兡 _�   d   B   g      �   �  5 G            b   
   W   �,        �operator>><int> 
 >7�   node  AJ          AM       Q    
 >�=   dest  AI  
     O    AK        
  M        �-  J M        �.  J M        �.  J N N N M        W-   M        p-   N N Z   /  ?/                         H " h   �+  W-  X-  p-  �-  �.  �.   0   7�  Onode  8   �=  Odest  O�   H           b        <       c  �   d  �   f  �   k  �1   j  �O   k  �,   �   0   �  
 Z   �   ^   �  
 j   �   n   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 @SH冹 H嬟�    �H兡 [�
   i      �   �   7 G                     �,        �operator>><float> 
 >7�   node  AJ         
 >	   dest  AI  	       AK        	 
 Z   @/                         H  0   7�  Onode  8   	  Odest  O �   0                   $       \  �	   ]  �   ^  �,   �   0   �  
 \   �   `   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H嬟H孂�    劺t艱$D H婦$@H�H媆$0H兡 _肏峊$@荄$@    H嬒�    �D$@艱$DH婦$@H�H媆$0H兡 _�   d   B   i      �   �  7 G            d   
   Y   �,        �operator>><float> 
 >7�   node  AJ          AM       S    
 >Α   dest  AI  
     Q    AK        
  M        �-  L M        �.  L M        �.  L N N N M        T-   M        n-   N N Z   /  @/                         H " h   �+  T-  U-  n-  �-  �.  �.   0   7�  Onode  8   Α  Odest  O  �   H           d        <       c  �   d  �   f  �   k  �1   j  �Q   k  �,   �   0   �  
 \   �   `   �  
 l   �   p   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 @SH冹0H嬟L嬄H嬔H峀$ �    � �婡塁H兡0[�   j      �   �   O G            -      '   �,        �operator>><donut::math::vector<float,3> > 
 >7�   node  AJ         
 >窫   dest  AI  	     #  AK        	 
 Z   A/   0                     H  @   7�  Onode  H   窫  Odest  O �   0           -        $       \  �	   ]  �'   ^  �,      0     
 t      x     
 �      �     
 �      �     
          
 @SH冹PH�    H3腍塂$@H嬟L嬄H嬔H峀$ �    H嬓H嬎�    H婽$8H凓v.H婰$ H�翲嬃H侜   rH婭鳫兟'H+罤兝鳫凐w�    H婰$@H3惕    H兡P[描    �	   �   $   f   /      h   �   u   �   �   �      �   	  w G            �      �   �,        �operator>><std::basic_string<char,std::char_traits<char>,std::allocator<char> > > 
 >7�   node  AJ        # 
 >�   dest  AI       m f   AK          M        J  93L M        %  3.A M        �  	3 N M        �  .>A M        3  >&9 M        c  I)
 Z   �  
 >   _Ptr  AH  I       AJ  C       AH g       >#    _Bytes  AK  F     > &   M        s  
R
"
 Z   �   >_    _Ptr_container  AH  ]       AJ  V       N N N N N N Z   B/  [,   P                     I > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c  
 :@   O        $LN40  `   7�  Onode  h   �  Odest  O   �   8           �        ,       \  �   ]  �l   ^  �   ]  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 0  �   4  �  
 @  �   D  �  
 �     �    
    �   $  �  
 H塡$WH冹 H嬟H孂�    劺t艱$A 稤$@f�H媆$0H兡 _肏峊$@艱$@ H嬒�    圖$@艱$A稤$@f�H媆$0H兡 _�   d   ?   h      �   �  6 G            _   
   T   �,        �operator>><bool> 
 >7�   node  AJ          AM       N    
 >亍   dest  AI  
     L    AK        
  M        �-  G M        �.  G M        �.  G N N N M        M-   M        m-   N N Z   /  >/                         H " h   �+  M-  N-  m-  �-  �.  �.   0   7�  Onode  8   亍  Odest  O   �   H           _        <       c  �   d  �   f  �   k  �1   j  �L   k  �,   �   0   �  
 [   �   _   �  
 k   �   o   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H�9 斃�   �   �   C G                      �,        �std::operator==<GameSettings>  >敊   _Left  AJ          >   __formal  AK          D                           H� 
 h   I-      敊  O_Left       O__formal  O   �   0              h     $       w �    x �   y �,   �   0   �  
 i   �   m   �  
 �   �   �   �  
    �     �  
 H�9 斃�   �   �   E G                      �,        �std::operator==<SampleSettings>  >]�   _Left  AJ          >   __formal  AK          D                           H� 
 h   L-      ]�  O_Left       O__formal  O �   0              h     $       w �    x �   y �,   �   0   �  
 k   �   o   �  
 �   �   �   �  
    �     �  
 H�9 斃�   �   �   T G                      �,        �std::operator==<donut::engine::SceneGraphLeaf>  >♁   _Left  AJ          >   __formal  AK          D                           H� 
 h   Q-      ♁  O_Left       O__formal  O  �   0              h     $       w �    x �   y �,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
   �     �  
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5         �   �  k G            :      :   9         �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >_   _Bytes  AJ        9  $  >_    _Block_size  AH       1 
   >_    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        r  
 Z      N Z   �  q   (                      H 
 h   r         $LN14  0   _  O_Bytes  O   �   h           :   x  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 s  %   w  %  
 �  �   �  �  
 H塡$WH冹0H孃H嬞荄$     L�
    L�    3襀��    H�    H荂    H吚tH婳H吷t�AH�H婫H塁H嬅H媆$@H兡0_�   s   "   p   ,   �      �   �  f G            j   
   _   �,        �std::dynamic_pointer_cast<EnvironmentLight,donut::engine::Light>  >8�   _Other  AK        
  AM  
     \ 
 >�    _Ptr  AH  0     (  AH \       M        �-  D M        �.  DM M        :-  D	 M        �  M N N N N 0                    H� " h   �  #,  -  :-  ;-  �-  �.   H   8�  O_Other  O   �   @           j   h     4       � �   � �?   � �D   � �\   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$WH冹0H孃H嬞荄$     L�
    L�    3襀��    H�    H荂    H吚tH婳H吷t�AH�H婫H塁H嬅H媆$@H兡0_�   m   "   g   ,   �      �   �  k G            j   
   _   �,        �std::dynamic_pointer_cast<GameSettings,donut::engine::SceneGraphLeaf>  >♁   _Other  AK        
  AM  
     \ 
 >�    _Ptr  AH  0     (  AH \       M        �-  D M        �.  DM M        �.  D	 M        �  M N N N N 0                    H� " h   �  +,  -  Q-  �-  �.  �.   H   ♁  O_Other  O  �   @           j   h     4       � �   � �?   � �D   � �\   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$WH冹0H孃H嬞荄$     L�
    L�    3襀��    H�    H荂    H吚tH婳H吷t�AH�H婫H塁H嬅H媆$@H兡0_�   j   "   g   ,   �      �   �  m G            j   
   _   �,        �std::dynamic_pointer_cast<SampleSettings,donut::engine::SceneGraphLeaf>  >♁   _Other  AK        
  AM  
     \ 
 >簟    _Ptr  AH  0     (  AH \       M        �-  D M        �.  DM M        �.  D	 M        �  M N N N N 0                    H� " h   �  /,  -  Q-  �-  �.  �.   H   ♁  O_Other  O�   @           j   h     4       � �   � �?   � �D   � �\   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$WH冹0H嬞3�箑   �    H塂$@H嬓H吚劍   W� 茾   茾   H�    H��   BB B0B@BPBpH墇�    H墇 H墇(H墇0�B<W缐BDH�    H塀荁8����荁H  �?荁L  �?荁P  �?荁T����墇XB`H墇pH荁x   @坺`�H嬜H岯H塖H�H嬅H媆$HH兡0_�   �   A   R   J   �   n   �   �   �      �   �  H G            �   
   �   �,        �std::make_shared<EnvironmentLight>  >L�    _Rx  AK  !     �  B@        �  M        �-  �� N M        �-  -"*) M        �  	0 N M        z.  "H*) M        R  �� M        A  �� M        �  �� M          �� N N N N M        �  �� N M        �.  H. M        �.  �
H. N N M        �+  f M        J,  f M        K,  �f N N N N N
 Z     ' S錏  4  donut::math::colors::white  C      N     B  0                     @ r h   �  �  v  �  �  R  S  &  �  �  A  �    �+  #,  J,  K,  -  �-  �-  �-  z.  �.  �.  �.  �.  �.   @   U�  O_Ret  ^      K�   O �   @           �   h     4       �
 �   �
 ��   �
 ��   �
 ��   �
 �,   |   0   |  
 l   |   p   |  
 |   |   �   |  
 J  |   N  |  
 �  |   �  |  
   |     |  
 H塡$WH冹0H嬞3�峅h�    H塂$@H嬓H吚tuW� 茾   茾   H�    H�3�BB B0B@BPH塀`H墇H墇 荁(  �?荁,  �?圔4圔<H�    H塀@坺A@坺H@坺P@坺X@坺`�H嬜H岯H塖H�H嬅H媆$HH兡0_�   �   ;   U   {   �      �   �  K G            �   
   �   �,        �std::make_shared<PerspectiveCameraEx>  >r�    _Rx  AK       �  B@        �  M        �-  �� N M        �-  ' M        �  	* N M        |.  B N N
 Z      0                     @ b h   �  �  �  �+  �+  J,  K,  -  -  M-  N-  T-  U-  m-  n-  �-  �-  �-  �-  �-  �-  |.  �.   @   聼  O_Ret  ^      q�   O �   @           �   h     4       �
 �   �
 ��   �
 ��   �
 ��   �
 �,   �   0   �  
 o   �   s   �  
    �   �   �  
 �  �   �  �  
 �  �   �  �  
 H塡$WH冹0H嬞3�峅`�    H塂$@H嬓H吚toW� 茾   茾   H�    H�H�    BB B0B@BPH墇H墇 H塀@坺)@坺+@坺-@坺/@坺4@坺<@坺D@坺L@坺T@坺\�H嬜H岯H塖H�H嬅H媆$HH兡0_�   �   ;   X   E   �      �   �  F G            �   
   �   �,        �std::make_shared<SampleSettings>  >崷    _Rx  AK       �  B@        �  M        �-  �� N M        �-  '" M        �  	* N M        }.  I N N
 Z      0                     @ f h   �  �  �  �+  �+  /,  J,  K,  -  M-  N-  T-  U-  W-  X-  m-  n-  p-  �-  �-  �-  }.  �.  �.   @   p�  O_Ret  ^      對   O �   @           �   h     4       �
 �   �
 ��   �
 ��   �
 ��   �
 �,   �   0   �  
 j   �   n   �  
 z   �   ~   �  
 �  �   �  �  
 �  �   �  �  
 H�H�H婤H堿3繦�H塀H嬃�   �     b G                      �,        �std::static_pointer_cast<donut::engine::Material,MaterialEx>  >墵   _Other  AK          M        �-    M        �.  @&H N N                        H�  h   8,  =-  �-  �.      墵  O_Other  O�   0              h     $       � �    � �   � �,   �   0   �  
 �   �   �   �  
    �   $  �  
 L�3繦�H堿H婤H吚t�@H婤H堿H嬃L��   �   �  n G            (       '   �,        �std::static_pointer_cast<donut::engine::SceneGraphLeaf,EnvironmentLight>  >p�   _Other  AK        ( 
 >!�    _Ptr  AP       %  M        �-  	 M        {.  L
 M        �.  ,	 M        �   N N N M        @,  � N N                        H�  h   �  @,  C-  �-  {.  �.      p�  O_Other  O �   8           (   h     ,       � �    � �   � �'   � �,   ~   0   ~  
 �   ~   �   ~  
 �   ~   �   ~  
 �  ~   �  ~  
 H�    W繦�3�AH堿H茿    圓A(H堿8H茿@   圓(圓L茿H����莵�     �?莵�     �?H莵�     �?H墎�   H墎�   墎�   H堿PH堿XH堿`H堿hH堿pH堿xH墎�   H墎�   H墎�   H墎�   H墎�   H墎�   H墎�   H墎�   H墎�   墎�   H莵�     �?莵�     �?H莵�      ?莵    �?莵    �?莵    �?莵    �?f墎  莵     ?莵     ?莵      ?莵$     ?莵(     ?莵,     ?H莵0    �?垇8  H墎\  墎d  莵<    �?莵@    �?莵D    �?莵H     ?莵L     ?莵P    �>H莵T  殭?莵h  ff�?莵l    @@垇x  墎|  H嬃莵p  莵t   苼�  �   �      �   D  G G            �      �  �.        �donut::engine::Material::Material 
 >'�   this  AJ        � D    M        �  
乲 N M        �  亁 N M        �  �< N M        �  � N M        �  � N M        �  
e N M        �  ^ N M        �  ? N M        �'  �� N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �.  z M        �.  �z N N M        �.  r M        �.  �r N N M        R  " M        &  &$ N M        A  " M        �  " M          " N N N N M        R  MR M        &  
$ N M        A   M        �   M           N N N N                        @ F h   v  �  R  S  �  &  �  �  A  �    �'  �.  �.  �.  �.      '�  Othis  O,      0     
 l      p     
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %       ,   �      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,      0     
 d      h     
 t      x     
 �      �     
 �      �     
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %       ,   �      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,      0     
 z      ~     
 �      �     
 �      �     
 �      �     
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !   X     ,       �  �    �  �   �  �   �  �,      0     
 z      ~     
          
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %          �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   X     $       H  �   I  �)   J  �,      0     
 d      h     
 t      x     
 �      �     
 �      �     
          
 H�    H��   d      �   �   d G                   
   &-        �std::_Ref_count_obj2<MaterialEx>::~_Ref_count_obj2<MaterialEx> 
 >:�   this  AJ                                 H� 
 h   a      :�  Othis  O  �   (              h            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H��   a      �   �   l G                   
   )-        �std::_Ref_count_obj2<MeshGeometryEx>::~_Ref_count_obj2<MeshGeometryEx> 
 >颚   this  AJ                                 H� 
 h   a      颚  Othis  O  �   (              h            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H��   ^      �   �   d G                   
   ,-        �std::_Ref_count_obj2<MeshInfoEx>::~_Ref_count_obj2<MeshInfoEx> 
 >甩   this  AJ                                 H� 
 h   a      甩  Othis  O  �   (              h            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H��   R      �   �   p G                   
   8-        �std::_Ref_count_obj2<EnvironmentLight>::~_Ref_count_obj2<EnvironmentLight> 
 >L�   this  AJ                                 H� 
 h   a      L�  Othis  O  �   (              h            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H��   [      �   �   h G                   
   /-        �std::_Ref_count_obj2<GameSettings>::~_Ref_count_obj2<GameSettings> 
 >Ζ   this  AJ                                 H� 
 h   a      Ζ  Othis  O  �   (              h            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H��   U      �   �   v G                   
   5-        �std::_Ref_count_obj2<PerspectiveCameraEx>::~_Ref_count_obj2<PerspectiveCameraEx> 
 >r�   this  AJ                                 H� 
 h   a      r�  Othis  O�   (              h            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    H��   X      �   �   l G                   
   2-        �std::_Ref_count_obj2<SampleSettings>::~_Ref_count_obj2<SampleSettings> 
 >崷   this  AJ                                 H� 
 h   a      崷  Othis  O  �   (              h            2 �
   8 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  t G            K      E   T,        �std::shared_ptr<donut::engine::Material>::~shared_ptr<donut::engine::Material> 
 >/�   this  AJ        +  AJ @       M        �,  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �,   0   /�  Othis  9+       �   9=       �   O  �   0           K   h     $       � �   � �E   � �,      0     
 �      �     
 �      �     
 �           
 r     v    
 �     �    
 �     �    
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   x  f G            K      E   !,        �std::shared_ptr<EnvironmentLight>::~shared_ptr<EnvironmentLight> 
 >m�   this  AJ        +  AJ @       M        o,  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  o,   0   m�  Othis  9+       �   9=       �   O�   0           K   h     $       � �   � �E   � �,   }   0   }  
 �   }   �   }  
 �   }   �   }  
 �   }   �   }  
 d  }   h  }  
 t  }   x  }  
 �  }   �  }  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  n G            K      E   ,        �std::shared_ptr<donut::engine::Light>::~shared_ptr<donut::engine::Light> 
 >敇   this  AJ        +  AJ @       M        b,  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  b,   0   敇  Othis  9+       �   9=       �   O�   0           K   h     $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 l  �   p  �  
 |  �   �  �  
 �  �   �  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  x G            K      E   ;,        �std::shared_ptr<donut::engine::SceneGraph>::~shared_ptr<donut::engine::SceneGraph> 
 >訐   this  AJ        +  AJ @       M        �,  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �,   0   訐  Othis  9+       �   9=       �   O  �   0           K   h     $       � �   � �E   � �,   A   0   A  
 �   A   �   A  
 �   A   �   A  
    A     A  
 v  A   z  A  
 �  A   �  A  
 �  A   �  A  
 H塡$H塴$H塼$WH冹 H嬹H�H呟tkH媦3鞨;遲!fD  H�H吷t
H�+H��P怘兠H;遳錒�H媀H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐w(I嬋�    H�.H塶H塶H媆$0H媗$8H媡$@H兡 _描    蘽   �   �   �      �   k   G            �      �   ?.        �std::vector<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > >::~vector<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap>,std::allocator<nvrhi::RefCountPtr<nvrhi::rt::IOpacityMicromap> > > 
 >/�   this  AJ          AL       � �  . M        X.  I4#	 M        i.  *UJ M        c  Y)%
 Z   �  
 >   _Ptr  AJ z       >#    _Bytes  AK  R     R   -   $ M        s  bd#
(
 Z   �   >_    _Ptr_container  AP  j     :  %  AP z       >_    _Back_shift  AJ  N     V , %  AJ z       N N N M        �.  #
	 >!�   _First  AI       � u   >儶   _Last  AM  #     � {   M        �.  0 M        �.  0 M        �.  0 M        �.  0CE
 >X!    temp  AJ  3       AJ 0         N N N N N N                      0@� > h   �  s  t  c  X.  h.  i.  �.  �.  �.  �.  �.  �.  �.         $LN50  0   /�  Othis  9>       E   O �   8           �   8     ,       � �   � ��    ��   � �,   v   0   v  
 E  v   I  v  
 U  v   Y  v  
 �  v   �  v  
 
  v     v  
 p  v   t  v  
 �  v   �  v  
 �  v   �  v  
 �  v   �  v  
 
  v     v  
 .  v   2  v  
 �  v   �  v  
 �  v   �  v  
 C  
   G  
  
 g  v   k  v  
 �  v   �  v  
 H塼$WH冹 H�9H嬹H�劊   H塴$8H媔H;齮OH塡$0@ H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;齯縃媆$0H�H媀H媗$8H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媡$@H兡 _描    太   �   �   �      �   �  G            �   
   �   P.        �std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::~vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > 
 >貏   this  AJ          AL       � �  0 M        c.  
F9%	 M        l.  }*B M        c  ��)
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  }     O   2  # M        s  
��#
 
 Z   �   >_    _Ptr_container  AP  �     6    AP �       >_    _Back_shift  AJ  y     S 1   AJ �       N N N M        �.  
".8 >蕜   _First  AM  
     � �   >,�   _Last  AN  "     `  M        �.  80 M        �.  80 M        ,  80 M        l,  0/	 M        �  9/
 >�   this  AI  4     B  AI 0       M        b  P	 N N N N N N N N                       @� F h   �  b  s  t  �  c  ,  l,  c.  k.  l.  �.  �.  �.  �.  �.         $LN61  0   貏  Othis  9N       �   9e       �   O   �   H           �   8     <       � �
   � �
   � �   � ��    ��   � �,   "   0   "  
 9  "   =  "  
 I  "   M  "  
 �  "   �  "  
   "     "  
 i  "   m  "  
 }  "   �  "  
 �  "   �  "  
 �  "   �  "  
    "     "  
 $  "   (  "  
 �  "   �  "  
 �  "   �  "  
 q  �   u  �  
 �  "   �  "  
 �  "   �  "  
 �  "   �  "  
 H塼$WH冹 H�9H嬹H�劊   H塴$8H媔H;齮OH塡$0@ H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;齯縃媆$0H�H媀H媗$8H+袶冣餒侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媡$@H兡 _描    太   �   �   �      �   �  � G            �   
   �   3,        �std::vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > >::~vector<std::shared_ptr<donut::engine::Light>,std::allocator<std::shared_ptr<donut::engine::Light> > > 
 >B�   this  AJ          AL       � �  0 M        w,  
F9%	 M        �,  }*B M        c  ��)
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  }     O   2  # M        s  
��#
 
 Z   �   >_    _Ptr_container  AP  �     6    AP �       >_    _Back_shift  AJ  y     S 1   AJ �       N N N M        -  
".8 >4�   _First  AM  
     � �   >敇   _Last  AN  "     `  M        �-  80 M        �.  80 M        ,  80 M        b,  0/	 M        �  9/
 >�   this  AI  4     B  AI 0       M        b  P	 N N N N N N N N                       H� F h   �  b  s  t  �  c  ,  b,  w,  �,  �,  -  �-  �-  �.  �.         $LN61  0   B�  Othis  9N       �   9e       �   O   �   H           �   8     <       � �
   � �
   � �   � ��    ��   � �,   B   0   B  
   B   !  B  
 -  B   1  B  
 �  B   �  B  
 �  B   �  B  
 M  B   Q  B  
 a  B   e  B  
 �  B   �  B  
 �  B   �  B  
 �  B   �  B  
   B     B  
 �  B   �  B  
 �  B   �  B  
 U  �   Y  �  
 y  B   }  B  
 �  B   �  B  
 �  B   �  B  
 H塡$H塼$WH冹 H媃 H嬹����H呟t)嬊�罜凐uH�H嬎�嬊�罜凐u	H�H嬎�PH婲H吷t�羪�uH��PH媆$0H媡$8H兡 _�   �   ,  B G            r      b   .        �donut::engine::Light::~Light 
 >*�   this  AJ          AL       V  M        �+  I M        I,  I M        �,  I	 M        b  R

 >�   this  AJ  M       AJ b       N N N N M        I.  3 M        _.  )
 M        �   ,
 >�   this  AI       T  M        b  4	 N N N N                       @� " h   b  �  �+  I,  �,  I.  _.   0   *�  Othis  92       �   9F       �   9_       �   O,   =   0   =  
 g   =   k   =  
 w   =   {   =  
 �   =   �   =  
    =     =  
 ~  =   �  =  
   =     =  
   =     =  
 (  =   ,  =  
 H塡$H塼$WH冹 H嬞H媺�   H吷tH莾�       H��P怘嫵�   ����H咑t)嬊�罠凐uH�H嬑�嬊�罠凐u	H�H嬑�PH嫵�   H咑t)嬊�罠凐uH�H嬑�嬊�罠凐u	H�H嬑�PH嫵�   H咑t)嬊�罠凐uH�H嬑�嬊�罠凐u	H�H嬑�PH嫵�   H咑t)嬊�罠凐uH�H嬑�嬊�罠凐u	H�H嬑�PH媠xH咑t)嬊�罠凐uH�H嬑�嬊�罠凐u	H�H嬑�PH媠hH咑t)嬊�罠凐uH�H嬑�嬊�罠凐u	H�H嬑�PH媠XH咑t'嬊�罠凐uH�H嬑��羱�u	H�H嬑�PH婼@H凓v-H�翲婯(H侜   rH兟'L婣鳬+菻岮鳫凐wwI嬋�    H荂8    H荂@   艭( H婼 H凓v-H�翲婯H侜   rH兟'L婣鳬+菻岮鳫凐w,I嬋�    H荂    H荂    艭 H媆$0H媡$8H兡 _描    绦  �     �   D  �      �   
  H G            I     I  .        �donut::engine::Material::~Material 
 >'�   this  AI       7&  AJ          M        J  K佽[' M        %  佽
-(
 M        �  佽 N M        �  -侐Q M        3  *侕N M        c  侚))
 Z   �  
 >   _Ptr  AJ  �    &  
  >#    _Bytes  AK  �    *  AK C     % M        s  �d#
,
 Z   �   >_    _Ptr_container  AP  
      AP     .  $  >_    _Back_shift  AJ  
    
  AJ C      N N N N N N M        J  K仢" M        %  仢
-( M        �  仢 N M        �  -仹 M        3  *仾 M        c  伄)
 Z   �  
 >   _Ptr  AJ  �    &  
  >#    _Bytes  AK  �    *  AK C       M        s  伔d#
 >_    _Ptr_container  AP  �      AP �    y  o  >_    _Back_shift  AJ  �    
  AJ C      N N N N N N M        T.  0乵 M        e.  乵'	 M        �  乿,
 >�   this  AL  q    � �   M        b  亰	
 N N N N M        T.  2�; M        e.  �;)	 M        �  丏,
 >�   this  AL  ?    2  M        b  乆	 N N N N M        T.  2�	 M        e.  �	)	 M        �  �,
 >�   this  AL  
    2  M        b  �&	 N N N N M        T.  5�� M        e.  ��) M        �  ��,
 >�   this  AL  �     2  M        b  ��	 N N N N M        T.  5�� M        e.  ��) M        �  ��,
 >�   this  AL  �     5  M        b  ��	 N N N N M        T.  5j M        e.  j) M        �  v,
 >�   this  AL  q     5  M        b  ��	 N N N N M        T.  :0 M        e.  0) M        �  A,
 >�   this  AL  7     :  M        b  U	 N N N N M        �'   M        (  GE
 >�     temp  AJ        2 AJ 0     #  X  �  �  �  & X ~& �&  N N                      0@� V h   �  b  s  t  �  J  K  $  %  3  �  �  �  �  ^  c  �'  (  T.  e.         $LN151  0   '�  Othis  9,       E   9S       �   9g       �   9�       �   9�       �   9�       �   9�       �   9�       �   9      �   9$      �   98      �   9V      �   9j      �   9�      �   9�      �   O �               I  p            �  �,      0     
 m      q     
 �      �     
 [     _    
 �     �    
 �     �    
 �     �    
 �          
 &     *    
 6     :    
          
 :     >    
 J     N    
 �     �    
 �     �    
 �     �    
 �     �    
 b     f    
 �     �    
 �     �    
 %     )    
 �     �    
 L     P    
 �     �    
 X     \    
 h     l    
 	  �   	  �  
 ;	     ?	    
 K	     O	    
 [	     _	    
 k	     o	    
 {	     	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 �	     �	    
 
     
    
 
     
    
 4
     8
    
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �     P G            K      E   .        �donut::engine::MeshGeometry::~MeshGeometry 
 > �   this  AJ        +  AJ @       M        T,  : M        �,  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N N                       H�  h   b  �  T,  �,   0    �  Othis  9+       �   9=       �   O �               K   p            �  �,      0     
 u      y     
 �      �     
 �      �     
 k     o    
 {         
 �     �    
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  E G            K      E   .        �MeshGeometryEx::~MeshGeometryEx 
 >   this  AJ        +  AJ @       M        .  : M        T,  : M        �,  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N N N                       H�  h   b  �  T,  �,  .   0     Othis  9+       �   9=       �   O �               K   �            >  �,   r   0   r  
 j   r   n   r  
 z   r   ~   r  
 �   r   �   r  
 {  r     r  
 �  r   �  r  
 �  r   �  r  
 H塡$H塼$WH冹 H嬞H媺�   H吷tH莾�       H��P怘岾P�    H媨H����H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH媨8H�t'嬈�罣凐uH�H嬒��羨凗u	H�H嬒�PH婼 H凓v-H�翲婯H侜   rH兟'L婣鳬+菻岮鳫凐w,I嬋�    H荂    H荂    艭 H媆$0H媡$8H兡 _描    �5   "   �   �   �   �      �   �  H G                   
.        �donut::engine::MeshInfo::~MeshInfo 
 >誻   this  AI       � �   AJ          M        J  K��[' M        %  ��
-(
 M        �  �� N M        �  -��Q M        3  *��N M        c  ��))
 Z   �  
 >   _Ptr  AJ  �     &  
  >#    _Bytes  AK  �     S * $ % M        s  ��d#
,
 Z   �   >_    _Ptr_container  AP  �     >  )  AP �       >_    _Back_shift  AJ  �     ; 
 )  N N N N N N M        R.  0p M        d.  p'	 M        �  y,
 >�   this  AM  t     � �   M        b  ��	
 N N N N M        Q,  79 M        �,  9) M        �  G,
 >�   this  AM  =     7  M        b  [	 N N N N M        N.   M        b.  GE
 >�!    temp  AJ         AJ 0       N N
 Z   P.                        0@� ^ h   �  b  s  t  �  J  K  $  %  3  �  �  �  �  ^  c  Q,  �,  N.  R.  b.  d.         $LN64  0   誻  Othis  9,       E   9Y       �   9m       �   9�       �   9�       �   O�                 p             �,   #   0   #  
 m   #   q   #  
 �   #   �   #  
 [  #   _  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
   #     #  
 �  #   �  #  
 4  #   8  #  
 �  #   �  #  
 �  #   �  #  
 \  �   `  �  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 �  #   �  #  
 @SH冹 H嬞H媺�   H吷t
�   �    H崑�   �    怘媼�   H吷tH莾�       H��P怘嬎H兡 [�       q   '   v   S   #      �   �  = G            W      M   	.        �MeshInfoEx::~MeshInfoEx 
 >驭   this  AI  	     I  AJ        	  M        N.  , M        b.  ,GE
 >�!    temp  AJ  3       AJ J       N N M        >.  )
 M        W.  

 Z   f.   N N Z   ?.  
.                        0H�  h   >.  N.  V.  W.  b.   0   驭  Othis  9F       E   O�               W   �            I  �,   w   0   w  
 b   w   f   w  
 r   w   v   w  
 �   w   �   w  
 �   w   �   w  
 �  w   �  w  
 �  w   �  w  
 H婭H吷t�����罙凐uH�H�`�   �   <  T G                       �+        �donut::engine::SceneGraphLeaf::~SceneGraphLeaf 
 >!�   this  AJ          M        I,    M        �,   	 M        b  )
 >�   this  AJ         N N N                        H�  h   b  I,  �,      !�  Othis  9       �   O�                                >  �,   '   0   '  
 y   '   }   '  
 �   '   �   '  
 8  '   <  '  
 P  '   T  '  
 �     �   �   L G                       a        �std::_Ref_count_base::~_Ref_count_base 
 >�   this  AJ          D                           H�     �  Othis  O  �                  h            ~ �,      0     
 q      u     
 �      �     
 H�    H�H兞�       �            �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,   
   0   
  
 {   
      
  
 H�    H�H兞�       �            �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   8                          H�     �  Othis  O  �   (              X            Y  �
   Z  �,      0     
 e      i     
 �      �     
 H塡$WH冹 H孃H嬞H;蕋bH婹H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐wBI嬋�    H荂   3繦塁�OKH塆H荊   �H嬅H媆$0H兡 _描    蘂   �   �   �      �   ]  u G            �   
   �   [,        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator= 
 >�   this  AI       { o   AJ          >�   _Right  AK        
  AM  
     ~ w   M        -  0[ M        �  [ N N$ M        %  
,
, M        �   N M        �  ,f M        3  &` M        c  %)?
 Z   �  
 >   _Ptr  AJ  "     )  
  >#    _Bytes  AK  %     e & : " M        s  
.#
B
 Z   �   >_    _Ptr_container  AP  2     X  ?  AP F       >_    _Back_shift  AJ  9     Q 
 ?  N N N N N                       @� ^ h   �  s  t  $  %  &  -  3  ?  �  �  �  �  �  �  ^  b  c  k  l   -  -         $LN62  0   �  Othis  8   �  O_Right  O   �   P           �   �      D       � �   � �   � �[   � �i   � �w   � ��   � �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �     �    
 �     �    
 T     X    
 h     l    
 �     �    
 0  �   4  �  
 t     x    
 @SH冹 H�    H嬞H�雎t
酣  �    H嬅H兡 [�	   d      �      �   �   d G            +      %   e-        �std::_Ref_count_obj2<MaterialEx>::`scalar deleting destructor' 
 >:�   this  AI         AJ                                @� 
 h   &-   0   :�  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
簒   �    H嬅H兡 [�	   a      �      �   �   h G            +      %   f-        �std::_Ref_count_obj2<MeshGeometryEx>::`scalar deleting destructor' 
 >颚   this  AI         AJ                                @� 
 h   )-   0   颚  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
鸿   �    H嬅H兡 [�	   ^      �      �   �   d G            +      %   g-        �std::_Ref_count_obj2<MeshInfoEx>::`scalar deleting destructor' 
 >甩   this  AI         AJ                                @� 
 h   ,-   0   甩  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
簚   �    H嬅H兡 [�	   R      �      �   �   j G            +      %   k-        �std::_Ref_count_obj2<EnvironmentLight>::`scalar deleting destructor' 
 >L�   this  AI         AJ                                @� 
 h   8-   0   L�  Othis  O,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
篐   �    H嬅H兡 [�	   [      �      �   �   f G            +      %   h-        �std::_Ref_count_obj2<GameSettings>::`scalar deleting destructor' 
 >Ζ   this  AI         AJ                                @� 
 h   /-   0   Ζ  Othis  O,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
篽   �    H嬅H兡 [�	   U      �      �   �   m G            +      %   j-        �std::_Ref_count_obj2<PerspectiveCameraEx>::`scalar deleting destructor' 
 >r�   this  AI         AJ                                @� 
 h   5-   0   r�  Othis  O ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H�    H嬞H�雎t
篳   �    H嬅H兡 [�	   X      �      �   �   h G            +      %   i-        �std::_Ref_count_obj2<SampleSettings>::`scalar deleting destructor' 
 >崷   this  AI         AJ                                @� 
 h   2-   0   崷  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 孃H嬞H婹hH凓v-H婭PH�翲侜   rL婣鳫兟'I+菻岮鳫凐wEI嬋�    H嬎H荂`    H荂h   艭P �    @銮t
簆   H嬎�    H嬅H媆$0H兡 _描    藼   �   ^   =   q   �   �   �      �   �  T G            �   
   �   �.        �EnvironmentLight::`scalar deleting destructor' 
 >�   this  AI       z n   AJ          M        J  7::% M        %  o-
0(
* M        �   N M        �  -j M        3  &c M        c   )B
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK        h & = " M        s  
)#
E
 Z   �   >_    _Ptr_container  AP  -     [  B  AP A       >_    _Back_shift  AJ  4     T 
 B  N N N N N N                       @� B h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c  .         $LN45  0   �  Othis  O,   J   0   J  
 y   J   }   J  
 �   J   �   J  
 f  J   j  J  
 �  J   �  J  
 �  J   �  J  
 �  J      J  
 "  J   &  J  
 �  �   �  �  
 H塡$WH冹 孃H嬞H婹0H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w[I嬋�    H荂(    H荂0   艭 H婯H吷t�����罙凐uH��P@銮t
�8   H嬎�    H嬅H媆$0H兡 _描    藼   �   �   �   �   �      �   {  P G            �   
   �   �.        �GameSettings::`scalar deleting destructor' 
 >�   this  AI       � �   AJ          M        �+  Z M        I,  Z M        �,  Z	 M        b  c
 >�   this  AJ  ^       AJ x     !    N N N N M        J  K��$ M        %  o
-(
C M        �   N M        �  -�� M        3  &y M        c   )X
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK        ~ & S " M        s  
)#
[
 Z   �   >_    _Ptr_container  AP  -     q  X  AP A       >_    _Back_shift  AJ  4     j 
 X  N N N N N N                       @� R h   �  b  s  t  J  K  $  %  3  �  �  �  �  ^  c  �+  I,  �,  .         $LN58  0   �  Othis  9u       �   O ,   Z   0   Z  
 u   Z   y   Z  
 �   Z   �   Z  
   Z     Z  
   Z     Z  
 �  Z     Z  
 "  Z   &  Z  
   Z   �  Z  
 �  Z   �  Z  
 �  Z   �  Z  
 S  �   W  �  
 w  Z   {  Z  
 H塡$WH冹 嬟H孂�    雒t
�8   H嬒�    H媆$0H嬊H兡 _�   =   "   �      �   �   X G            4   
   &   �.        �donut::engine::Light::`scalar deleting destructor' 
 >*�   this  AJ          AM       $                        @�  0   *�  Othis  O  ,   >   0   >  
 }   >   �   >  
 �   >   �   >  
 H塡$WH冹 嬟H孂�    雒t
簣  H嬒�    H媆$0H嬊H兡 _�      "   �      �   �   [ G            4   
   &   �.        �donut::engine::Material::`scalar deleting destructor' 
 >'�   this  AJ          AM       $                        @�  0   '�  Othis  O   ,      0     
 �      �     
 �      �     
 @WH冹 H塡$0H孂H嫏�  H塴$8嬯H呟t6H塼$@����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH媡$@H嬒�    H媆$0@雠H媗$8t
簶  H嬒�    H嬊H兡 _肹      x   �      �   �  N G            �         �.        �MaterialEx::`scalar deleting destructor' 
 >C�   this  AJ          AM       v  M        A.  6 M        Y.  ,
 M        �  &
 >�   this  AI       O  M        b  ?	
 N N N N                       @�  h   b  �  .  A.  Y.   0   C�  Othis  9=       �   9O       �   O ,   n   0   n  
 s   n   w   n  
 �   n   �   n  
 �   n   �   n  
 {  n     n  
 �  n   �  n  
 H塡$H塴$H塼$WH冹 嬯H嬞H婭@3�H吷tH墈@H��P怘婯8H吷tH墈8H��P怘婯0H吷tH墈0H��P怘媨(����H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH媨H�t)嬈�罣凐uH�H嬒�嬈�罣凐u	H�H嬒�PH媨H�t'嬈�罣凐uH�H嬒��羨凗u	H�H嬒�P@雠t
篐   H嬎�    H嬅H媆$0H媗$8H媡$@H兡 _�   �      �   q  Q G                   f.        �MeshDebugData::`scalar deleting destructor' 
 >洬   this  AI       �  AJ          M        m.  0�� M        p.  ��'	 M        �  ��,
 >�   this  AM  �     V  M        b  ��	
 N N N N M        m.  2�� M        p.  ��)	 M        �  ��,
 >�   this  AM  �     2  M        b  ��	 N N N N M        m.  7W M        p.  W) M        �  e,
 >�   this  AM  [     7  M        b  y	 N N N N M        �'  C M        (  CDE
 >�     temp  AJ  G       AJ W     �    R  �  �   N N M        �'  / M        (  /DE
 >�     temp  AJ  3       AJ C       N N M        �'   M        (  DG
 >�     temp  AJ         AJ /       N N                      0@� " h   b  �  �'  (  g.  m.  p.   0   洬  Othis  9+       E   9?       E   9S       E   9w       �   9�       �   9�       �   9�       �   9�       �   9�       �   O   ,   q   0   q  
 v   q   z   q  
 �   q   �   q  
 �   q   �   q  
 �  q   �  q  
   q     q  
 �  q   �  q  
 �  q   �  q  
   q     q  
   q     q  
 t  q   x  q  
 �  q   �  q  
 �  q   �  q  
 �  q     q  
 
  q     q  
   q   !  q  
 -  q   1  q  
 =  q   A  q  
 M  q   Q  q  
 ]  q   a  q  
 m  q   q  q  
 @WH冹 H塡$0H孂H媃H塴$8嬯H呟t6H塼$@����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH媡$@H媆$0@雠H媗$8t
篐   H嬒�    H嬊H兡 _胢   �      �   �  _ G            z      t   0.        �donut::engine::MeshGeometry::`scalar deleting destructor' 
 > �   this  AJ          AM       k  M        .  	6 M        T,  	6 M        �,  	,
 M        �  #
 >�   this  AI       G  M        b  <	
 N N N N N                       @�  h   b  �  T,  �,  .   0    �  Othis  9:       �   9L       �   O   ,      0     
 �      �     
 �      �     
 #     '    
 �     �    
 �     �    
 @WH冹 H塡$0H孂H媃H塴$8嬯H呟t6H塼$@����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH媡$@H媆$0@雠H媗$8t
篽   H嬒�    H嬊H兡 _胢   �      �   �  R G            z      t   +.        �MeshGeometryEx::`scalar deleting destructor' 
 >   this  AJ          AM       k  M        .  	6 M        .  	6 M        T,  	6 M        �,  	,
 M        �  #
 >�   this  AI       G  M        b  <	
 N N N N N N                       @�  h   b  �  T,  �,  .  .   0     Othis  9:       �   9L       �   O   ,   s   0   s  
 w   s   {   s  
 �   s   �   s  
 /  s   3  s  
 �  s   �  s  
 �  s   �  s  
 H塡$WH冹 嬟H孂�    雒t
酣   H嬒�    H媆$0H嬊H兡 _�   #   "   �      �   �   [ G            4   
   &   -.        �donut::engine::MeshInfo::`scalar deleting destructor' 
 >誻   this  AJ          AM       $                        @�  0   誻  Othis  O   ,   $   0   $  
 �   $   �   $  
 �   $   �   $  
 H塡$WH冹 孃H嬞H媺�   H吷t
�   �    H崑�   �    怘媼�   H吷tH莾�       H��P怘嬎�    @銮t
贺   H嬎�    H嬅H媆$0H兡 _�!   q   -   v   T   #   g   �      �   �  N G            y   
   n   /.        �MeshInfoEx::`scalar deleting destructor' 
 >驭   this  AI       d  AJ          M        	.  "# Z   ?.  
.   M        N.  2 M        b.  2GE
 >�!    temp  AJ  9       AJ P       N N M        >.  /
 M        W.  

 Z   f.   N N N                      0@�  h   	.  >.  N.  V.  W.  b.   0   驭  Othis  9L       E   O  ,   x   0   x  
 s   x   w   x  
 �   x   �   x  
 �   x     x  
 
  x     x  
 �  x   �  x  
 H塡$WH冹 H嬞孃H婭H吷t�����罙凐uH��P@銮t
�0   H嬎�    H嬅H媆$0H兡 _�<   �      �   �  d G            N   
   C   ,.        �donut::engine::PerspectiveCamera::`scalar deleting destructor' 
 >u�   this  AI  
     ;  AJ        
  M        �+   M        I,   M        �,  /	 M        b  
 >�   this  AJ         AJ -     !    N N N N                       @�  h   b  �+  I,  �,  .  .   0   u�  Othis  9*       �   O   ,   6   0   6  
 �   6   �   6  
 �   6   �   6  
   6     6  
    6   $  6  
 �  6   �  6  
 H塡$WH冹 H嬞孃H婭H吷t�����罙凐uH��P@銮t
篨   H嬎�    H嬅H媆$0H兡 _�<   �      �   �  W G            N   
   C   *.        �PerspectiveCameraEx::`scalar deleting destructor' 
 >r�   this  AI  
     ;  AJ        
  M        �+   M        I,   M        �,  /	 M        b  
 >�   this  AJ         AJ -     !    N N N N                       @� " h   b  �+  I,  �,  .  .  .   0   r�  Othis  9*       �   O,   P   0   P  
 |   P   �   P  
 �   P   �   P  
   P     P  
   P     P  
 �  P   �  P  
 H塡$WH冹 H嬞孃H婭H吷t�����罙凐uH��P@銮t
篜   H嬎�    H嬅H媆$0H兡 _�<   �      �     R G            N   
   C   �.        �SampleSettings::`scalar deleting destructor' 
 >簟   this  AI  
     ;  AJ        
  M        �+   M        I,   M        �,  /	 M        b  
 >�   this  AJ         AJ -     !    N N N N                       @�  h   b  �+  I,  �,  .   0   簟  Othis  9*       �   O ,   U   0   U  
 w   U   {   U  
 �   U   �   U  
 �   U     U  
   U     U  
 {  U     U  
 H塡$WH冹 H嬞孃H婭H吷t�����罙凐uH��P@銮t
�   H嬎�    H嬅H媆$0H兡 _�<   �      �   �  ^ G            N   
   C   ..        �donut::engine::SceneCamera::`scalar deleting destructor' 
 >�   this  AI  
     ;  AJ        
  M        �+   M        I,   M        �,  /	 M        b  
 >�   this  AJ         AJ -     !    N N N N                       @�  h   b  �+  I,  �,  .   0   �  Othis  9*       �   O ,   0   0   0  
 �   0   �   0  
 �   0   �   0  
 
  0     0  
   0     0  
 �  0   �  0  
 H塡$WH冹 H嬞孃H婭H吷t�����罙凐uH��P@銮t
�   H嬎�    H嬅H媆$0H兡 _�<   �      �   �  a G            N   
   C   �+        �donut::engine::SceneGraphLeaf::`scalar deleting destructor' 
 >!�   this  AI  
     ;  AJ        
  M        �+   M        I,   M        �,  /	 M        b  
 >�   this  AJ         AJ -     !    N N N N                       @�  h   b  �+  I,  �,   0   !�  Othis  9*       �   O  ,   ,   0   ,  
 �   ,   �   ,  
 �   ,   �   ,  
 
  ,     ,  
   ,   !  ,  
 �  ,   �  ,  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �         0   �      �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   �  Othis  O ,   	   0   	  
 w   	   {   	  
 �   	   �   	  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �         0   �      �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �  �   0   �  Othis  O  ,      0     
 �      �     
 �      �     
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �         0   �      �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @� 
 h   �   0   �  Othis  O ,      0     
 w      {     
 �      �     
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 H塡$H塼$WH冹@H孃H嬞H峀$(�    悑C4H媡$(�C,�F,塅4婥@�C8�F8塅@婥D塅D�CH�FHH峉PH峃PH;蕋L婤H儂vH��    H�    H荊    H婰$0H吷t�AH婰$0H媡$(H�7H塐H吷t3����嬈�罙凐u"H媆$0H�H嬎��羢凗uH婰$0H��PH嬊H媆$PH媡$XH兡@_�   |   q         �   �  = G            �      �   ,        �EnvironmentLight::Clone 
 >�   this  AI       �  AJ          AI �      
 >偀   copy  CL      (     �  CI     �       CJ     �     R 5   CL     �     
  CI    �       CJ    �       D(    M        !,  8�� M        o,  ��3 M        �  ��
 M        b  ��
 N N N N M        �,  .u M        �-  u M        {.  �� M        �.  ��
 M        �  �� N N N M        @,  �u N N N M        I  ]L
 Z   )  
 >�   this  AJ  ]       AJ u       >�   _Right  AK  Y       AK p     ~ V   M        /  f >�    _Result  AK p       N N
 Z   �,   @                    @ V h   b  �  �  I  #  $  /  �  �  =  >  !,  @,  o,  �,  �,  C-  �-  {.  �.   P   �  Othis  (   偀  Ocopy  9�       �   9�       �   O   �   `           �   �	  	   T          �   !  �    "  �5   #  �E   $  �K   %  �U   &  �u   '  ��   (  ��   �   L F                                �`EnvironmentLight::Clone'::`1'::dtor$0 
 >偀    copy  EN  (                                  �  O ,   G   0   G  
 b   G   f   G  
 r   G   v   G  
 �   G   �   G  
 �   G   �   G  
 �   G   �   G  
 �   G   �   G  
 �   G   �   G  
 �   G   �   G  
 
  G     G  
 }  G   �  G  
 �  G   �  G  
 �  G   �  G  
 �  G   �  G  
 �  G     G  
 �  G   �  G  
 �  G   �  G  
 �  G   �  G  
 l  �   p  �  
 �  �   �  �  
 H崐(   �       }   @SH冹@H峀$(H嬟�    H婦$(H�H婦$0H荂    H塁H嬅H兡@[�   �      �   J  9 G            5      /   ,        �GameSettings::Clone 
 >�   this  AJ          DP   
 >o�    copy  D(    M        �,  = M        �-  
 N M        @,  �
  N N
 Z   �,   @                     @ " h   b  �  -,  @,  v,  �,  �-   P   �  Othis  (   o�  Ocopy  O  �   8           5   �	     ,       �  �   �  �   �  �/   �  �,   X   0   X  
 ^   X   b   X  
 `  X   d  X  
 H塡$WH冹@H嬞H孃H峀$(�    婥L婦$(H荊    L�A堾H婥 I堾 婥A堾H婥(I堾(稢0fA堾0H婥4I堾4H婥<I堾<H婥DI堾DH婥LH媆$PI堾LH婦$0H塆H嬊H兡@_�   �      �   n  @ G            �   
   m   
,        �PerspectiveCameraEx::Clone 
 >r�   this  AI  
     e  AJ        
 
 >顭   copy  CP      "     f  D(    M        �,  "	L M        �-  *	L N M        @,  �
" N N
 Z   �,   @                     @ & h   b  �   ,  @,  n,  �,  �,  �-   P   r�  Othis  (   顭  Ocopy  O  �   �           �   �	     �       �  �   �  �   �  �"   �  �-   �  �1   �  �9   �  �@   �  �H   �  �Q   �  �Y   �  �a   �  �i   �  �m   �  �v   �  ��   �  �,   M   0   M  
 e   M   i   M  
 u   M   y   M  
 �   M   �   M  
 �  M   �  M  
 @SH冹@H峀$(H嬟�    H婦$(H�H婦$0H荂    H塁H嬅H兡@[�   �      �   L  ; G            5      /   ,        �SampleSettings::Clone 
 >簟   this  AJ          DP   
 >o�    copy  D(    M        �,  = M        �-  
 N M        @,  �
  N N
 Z   �,   @                     @ " h   b  �  -,  @,  v,  �,  �-   P   簟  Othis  (   o�  Ocopy  O�   8           5   �	     ,       �  �   �  �   �  �/   �  �,   S   0   S  
 `   S   d   S  
 `  S   d  S  
 H塡$H塴$H塼$ WAVAWH冹`I媓E3�I媝L嬹I孁H嬟I嬋H凖vI�H凗uCL嬈H�    �    吚u0H峀$(�    L�;L墈H�H�H婬H塊L�8L墄H媡$0�   H嬒H凖vH�H凗uL嬈H�    �    吚t%H嬒H凖vH�H凗uCL嬈H�    �    吚u0H峀$8�    L�;L墈H�H�H婬H塊L�8L墄H媡$@閰   H嬒H凖vH�H凗
uL嬈H�    �    吚uL�;L墈閃  H嬒H凖vH�H凗厐   L嬈H�    �    吚umH峀$H�    L�;L墈H�H�H婬H塊L�8L墄H媡$PH咑匉   ����嬊�罠凐呭   H�H嬑��羱�呄   H�H嬑�P榱   H嬒H凖vH�H凗厺   L嬈H�    �    吚厗   峃<�    H墑$�   H嬋H吚t^W� 茾   茾   H�    H�H�    AA A0H堿H岮L墆L墆 A(L墆8H茿@   D坹(H�H塊�I嬒H塊H岮H��L嬊H嬘I嬑�    L峔$`H嬅I媅 I媖(I媠8I嬨A_A^_肈      I   �   W   |   �      �   �   �      �   �   �   �          �   H     M  �   [  �   �  "   �  �   �  �     [   (  �     C      �   V  J G            �     �  ,        �ExtendedSceneTypeFactory::CreateLeaf 
 >憻   this  AJ        &  AV  &     x
 >�   type  AM  )     Ze4  AP        )  AM �      M        �,  %
 M        d-  %
 M        +  8 M        {  > N N M        /  # >�    _Result  AJ  /       AJ �       M        �   N N N N M        !,  w M        o,  w N N M        �,  [ M        �-  b&H N M        @,  �[ N N M        �,  %�� M        d-  %�� M        +  �� M        {  �� N N M        /  ��# >�    _Result  AJ  �       AJ �       M        �  �� N N N N M        �,  %�� M        d-  %�� M        +  �� M        {  �� N N M        /  ��# >�    _Result  AJ  �       AJ �       M        �  �� N N N N M         ,  
�� M        n,  �� M        �  ��
 >�   this  AL  �       AL �    B �  M        b  �� N N N N M        �,  �� M        �-  ��&H N M        @,  ��� N N M        �,  %�� M        d-  %�� M        +  � M        {  �
 N N M        /  ��# >�    _Result  AJ  �       AJ ,      M        �  �� N N N N M        ?,  �  M        @,  ��  N N M        �,  |)�� M        d-  |)�� M        +  �8 M        {  丅 N N M        /  |��# >�    _Result  AJ  /    "  AJ �      M        �  �/ N N N N M        -,  亄 M        v,  亄 N N M        �,  乢 M        �-  乫&H N M        @,  �乢 N N M        �,  -伮 M        d-  -伮 M        +  !佄 M        {  佖 N N M        /  伮# >�    _Result  AJ  �    "  AJ u    	  M        �  伵 N N N N M        �,  俓  M        �-  俓	 N N+ M        �,  侊"%
 Z      >Ζ    _Rx  AJ      s  AJ �      B�   �    � v   M        �-  �
" M        �  	�
 N M        ~.  �, M        R  侶 M        A  侶 M        �  侶 M          侶 N N N N M        �+  侤 M        J,  侤 M        K,  �侤 N N N N N N Z   �,  �,  �,  F/   `                     @ � h4   �  �  b  v  �  �  R  S  &  /  >  �  �  �  �  A  �    +  {  �+   ,  !,  ),  +,  -,  ?,  @,  J,  K,  n,  o,  t,  v,  �,  �,  �,  �,  �,  �,  -  d-  �-  �-  �-  �-  �-  �-  �-  ~.  �.  �.   �   憻  Othis  �   �  Otype  9�      �   9�      �   ^�     ウ   O  �   �           �  �	     �       2  �   3  �   2  �   3  �#   2  �,   3  �Q   5  �|   @  ��   7  ��   9  ��   ;  �   >  �,  @  �U  B  ��  9  ��  D  ��  F  �u  H  ��  I  �,   ]   0   ]  
 o   ]   s   ]  
    ]   �   ]  
 �   ]   �   ]  
 �   ]   �   ]  
 �   ]   �   ]  
 l  ]   p  ]  
 |  ]   �  ]  
 �  ]   �  ]  
 �  ]   �  ]  
 �  ]   �  ]  
 �  ]   �  ]  
 =  ]   A  ]  
 M  ]   Q  ]  
 r  ]   v  ]  
 �  ]   �  ]  
 �  ]   �  ]  
 �  ]   �  ]  
 �  ]   �  ]  
 �  ]     ]  
 �  ]   �  ]  
 �  ]   �  ]  
 �  ]   �  ]  
 2  ]   6  ]  
 B  ]   F  ]  
 R  ]   V  ]  
 l  ]   p  ]  
 H塴$H塼$ WH冹0龚  H嬺3龛    H塂$HH孁H吚t]W繦塡$@ 茾   H峅茾   3襀�    A笜  H��    H峅�    H�    H塆H壇�  H壇�  H媆$@�H孆H媗$PH岹H�H嬈H墌H媡$XH兡0_�   �   M   d   [   �   d      k   �      �   b  N G            �      �   ,        �ExtendedSceneTypeFactory::CreateMaterial 
 >憻   this  AJ          D@    M        �,  �� M        �-  �� M        �.  �� N N N M        �,  
]b Z     �.   >:�    _Rx  AJ  c       AM  &     � = %  BH   #     � - M        �-  3
 M        �  	6 N M        �.  =		 N N N 0                     @ b h   �  �  b  �  �  ,  8,  j,  �,  �,   -  !-  =-  �-  �-  �-  �-  �.  �.  �.  �.  �.  �.   @   憻  Othis  ^      9�   O  �   P           �   �	     D       V  �   W  �   V  �   W  ��   X  ��   W  ��   X  �,   ^   0   ^  
 s   ^   w   ^  
 "  ^   &  ^  
 2  ^   6  ^  
 F  ^   J  ^  
 ^  ^   b  ^  
 x  ^   |  ^  
 H塴$H塼$ WH冹0硅   H嬺3龛    H塂$HH孁H吚勑   W繦塡$@ 茾   H峅茾   3襀�    A肛   H��    W繦�    GH塷(H荊0   @坥@坥8H塷@H塷HH塷PH塷XH塷`H塷hH塷pH壇�   H壇�   壇�   @埊�   H壇�   @埊�   H塆H壇�   H壇�   H壇�   H壇�   H壇�   茋�   H媆$@�H孆H媗$PH岹H�H嬈H墌H媡$XH兡0_�   �   Q   ^   _   �   i         �   �  J G                    ,        �ExtendedSceneTypeFactory::CreateMesh 
 >憻   this  AJ          D@    M        �,  � M        �-  � N N# M        �,  
1��4��
 Z      >甩    _Rx  AM  &     �  BH   #     � 0 M        �-  7
 M        �  	: N M        .  A		 M        R  m M        A  m M        �  m M          m N N N N N N N 0                     @ � h,   �  �  b  v  �  �  R  S  &  �  �  A  �    ,  a,  m,  �,  �,  -  -  �-  �-  �-  �-  �-  �-  �-  �-  �-  @.  O.  Q.  S.  q.  .  �.  �.  �.  �.  �.  �.  �.  �.   @   憻  Othis  ^      师   O�   P              �	     D       L  �   M  �   L  �   M  �  N  �  M  �  N  �,   _   0   _  
 o   _   s   _  
   _     _  
   _     _  
 �  _   �  _  
 �  _   �  _  
 H塡$WH冹03�H嬟峅x�    H塂$HH嬋H吚tvW� 茾   茾   H�    H�3�AA A0A@APA`H墆H墆 H墆@H墆H墆P圓TH�    H堿H茿X����茿`����艫dH墆hH墆p�H嬒H岮H塊H�H嬅H媆$@H兡0_�   �   ;   a   u         �     R G            �   
   �   ,        �ExtendedSceneTypeFactory::CreateMeshGeometry 
 >憻   this  AJ          D@    M        �,  �� M        �-  �� N N M        �,  v
 Z      >颚    _Rx  AJ       �  BH        �  M        �-  ' M        �  	* N M        �.  B N N N 0                     @ f h   �  �  b  �  �  ,  8,  k,  �,  �,  -  -  -  �-  �-  �-  �-  �-  �-  �-  �-  S.  �.  �.   @   憻  Othis  ^      瘭   O �   0           �   �	     $       Q  �   R  ��   S  �,   `   0   `  
 w   `   {   `  
 �   `   �   `  
 	  `   
  `  
   `     `  
   `      `  
 @SH冹 H嬟�    3繦塁(H塁 H兡 [�
   :      �     J G                     ,        �EnvironmentLight::FillLightConstants 
 >�   this  AJ          >&�   lightConstants  AI  	       AK        	 
 Z   J/                         @ 
 h   �   0   �  Othis  8   &�  OlightConstants  O  �   @              �	     4       �  �	   �  �   �  �   �  �   �  �,   H   0   H  
 o   H   s   H  
 �   H   �   H  
 �   H   �   H  
 $  H   (  H  
 H塡$H塴$H塼$ H塗$WATAUAVAWH冹PH嬺L孂E3鞨�:L媌I;黷rH婫H吚t�@L�7L塼$8H媉H嬰H塡$@I�I嬑�P0=�  匎   H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦I;黸嶮�/M塷H�>H�剭   H媙H;齮AH媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;齯縃�H媀H+袶冣餒侜   rH兟'L婣鳬+菻岮鳫凐�  I嬋�    L�.L塶L塶I嬊L峔$PI媅0I媖@I媠HI嬨A_A^A]A\_肈塴$ L�
    L�    3襂嬑�    H吚tM�/M塷H呟t	�CH媗$@I�I塷�W繟M�/M塷H呟t0�����罜凐u!H�H嬎������罜凐u
H�H嬎�P怘�>H�凱���H媙H;�� ���H媉H呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH兦H;齯块湖���    �9  �   q  s   x  p   �  �   L  �      �   �	  : G            Q  !   Q  ,        �FindEnvironmentLight  >=�   lights  AK        $  AL  $     -8
  D�    >4�    <begin>$L0  AM  -     �� �  >4�    <end>$L0  AT  1      6  >d�   light  CV      F       CN     R     W I CN    H    �  _  D8    M        ,  !6 M        �,  6M M        :-  6	 M        �  ? N N N N M        3,  V��仠 M        w,  ��J亹 M        �,  侹 M        c  侹
 >   _Ptr  AJ 8      >#    _Bytes  AK      D  1  M        s  &侹
 Z   �   >_    _Ptr_container  AP  $    ,  AP 8      >_    _Back_shift  AJ      H0  AJ 8      N N N M        -  ��%	8 >4�   _First  AM  �     S  AM     Lc �  >敇   _Last  AN  �     F  AN     LS �  M        �-  8�� M        �.  8�� M        ,  8�� M        b,  ��/	 M        �  ��/
 >�   this  AI  �     =  AI �     � = � �  M        b  ��	 N N N N N N N N N M        ",  �� M        #,  ��� N N M        ,  1n M        b,  n/ M        �  p/ M        b  ��	 N N N N M        3,  Z侅 M        w,  侅N M        -  侟)	8 >4�   _First  AM  �    \  AM     Lc �  >敇   _Last  AN  �    O  AN     LS �  M        �-  8� M        �.  8� M        ,  8� M        b,  �/	 M        �  �/
 >�   this  AI  	    B  AI     LO � B  M        b  �%	 N N N N N N N N N M        ,  1伜 M        b,  伜/ M        �  伡/ M        b  佊	 N N N N' M        �,  乮!��
 >�    _Ptr  AH  �    ;  AH     F& 
 8  F � *  M        -  伆 M        #,  �伆 N N M        �-  亱
��  M        �.  仌K�� M        :-  仌	�� M        �  	仐�� N N N M        #,  �亱 N N N
 Z   �   P           (         @ � h    �  b  s  t  �  �  c  ,  ,  ",  #,  1,  2,  3,  \,  b,  w,  �,  �,  �,  �,  �,  -  -  :-  ;-  �-  �-  �-  �.  �.  �.         $LN180  �   =�  Olights  8   d�  Olight  9]       $�   9�       �   9�       �   9�       �   9�       �   9�      �   9�      �   9#      �   9:      �   O �   `           Q  �	  	   T       �  �*   �  �W   �  �k   �  ��   �  ��   �  �K  �  �i  �  �K  �  ��   �   I F                                �`FindEnvironmentLight'::`1'::dtor$0  >=�   lights  EN  �           >d�    light  EN  8                                  �  O  �   �   I F                                �`FindEnvironmentLight'::`1'::dtor$1  >=�   lights  EN  �           >d�    light  EN  8                                  �  O  ,   c   0   c  
 a   c   e   c  
 q   c   u   c  
 �   c   �   c  
 �   c   �   c  
 �   c   �   c  
   c   	  c  
   c   !  c  
   c      c  
 =  c   A  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
 '  c   +  c  
 7  c   ;  c  
 [  c   _  c  
 k  c   o  c  
   c   
  c  
   c     c  
 s  c   w  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
 R  c   V  c  
 b  c   f  c  
 f  c   j  c  
 v  c   z  c  
 $	     (	    
 _	  c   c	  c  
 o	  c   s	  c  
 	  c   �	  c  
 �	  c   �	  c  
 �	  c   �	  c  
 �	  c   �	  c  
 �	  c   �	  c  
 �	  c   �	  c  
 �	  c   �	  c  
 �	  c   �	  c  
 �
  �   �
  �  
 �
  �   �
  �  
 �
  �   �
  �  
 @  �   D  �  
 �  �   �  �  
 �  �   �  �  
 H媻�   �       B   H崐8   �       �   �   �   �   �   K G                      �+        �donut::engine::Light::GetContentFlags 
 >!�   this  AJ          D                           @     !�  Othis  O   �                               �  �,   9   0   9  
 p   9   t   9  
 �   9   �   9  
 �   �   �   �   Q G                      �+        �donut::engine::SceneCamera::GetContentFlags 
 >�   this  AJ          D                           @     �  Othis  O �                               �  �,   /   0   /  
 v   /   z   /  
 �   /   �   /  
 3烂   �   �   T G                      �+        �donut::engine::SceneGraphLeaf::GetContentFlags 
 >�   this  AJ          D                           @     �  Othis  O  �                               D  �,   )   0   )  
 y   )   }   )  
 �   )   �   )  
 歌  �   �   �   D G                      �+        �EnvironmentLight::GetLightType 
 >�   this  AJ          D                           @     �  Othis  O  �                  �              �,   F   0   F  
 i   F   m   F  
 �   F   �   F  
 �    H嬄�    (�买(�抿B�Z�R�   �      �      �   3  X G            3       2   �+        �donut::engine::SceneGraphLeaf::GetLocalBoundingBox 
 >!�   this  AJ        3  D    M        S,   ' M        �   N M        �    N M        �   N N                        @  h   5  o  �  �  S,      !�  Othis  O �               3                B  �,   (   0   (  
 }   (   �   (  
 H  (   L  (  
 H塡$H塼$ WH冹`H�    H3腍塂$PH嬺H孂H嬑H�    �    L岹8H嬓H峀$ �    H�    H嬑� �G8婡塆@�    H峎DH嬋�    H�    塆DH嬑�    H峎HH嬋�    H�    �GHH嬑�    L岹PH嬓H峀$0�    H嬓H峅P�    H婽$HH凓v.H婰$0H�翲嬃H侜   rH婭鳫兟'H+罤兝鳫凐w$�    H婰$PH3惕    L峔$`I媅 I媠(I嬨_描    �   �   *      /   e   @   j   G   
   ^   e   j   g   q   
   |   e   �   i   �      �   e   �   f   �      �   �   �   �     �      �   #  < G                   ,        �EnvironmentLight::Load 
 >�   this  AJ        $  AM  $     � �  
 >7�   node  AK        !  AL  !     � �   M        �,  V��u Z   B/  [,  
 >7�   node  AH  �       M        J  9��X M        %  ��.M M        �  	�� N M        �  .��M M        3  ��&E M        c  ��)$
 Z   �  
 >   _Ptr  AH  �       AJ  �       AH �       >#    _Bytes  AK  �     J &   M        s  
��
.
 Z   �   >_    _Ptr_container  AH  �       AJ  �       N N N N N N N M        �,  ��
 Z   @/  
 >7�   node  AH  �       N M        �,  b
 Z   ?/  
 >7�   node  AH  b       N M        �,  3
 Z   A/  
 >7�   node  AH  3       N Z   /  /  /  /   `                     A N h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c  �,  �,  �,  �,  
 :P   O        $LN49  p   �  Othis  x   7�  Onode  O �   �             �	  
   t       +  �$   ,  �D   -  �N   ,  �]   -  �n   .  �u   -  �x   .  ��   /  ��   .  ��   /  ��   0  �  /  �,   E   0   E  
 a   E   e   E  
 q   E   u   E  
 �   E   �   E  
 �   E   �   E  
 �   E   �   E  
 �  E   �  E  
 �  E   �  E  
 �  E   �  E  
   E     E  
 X  E   \  E  
 h  E   l  E  
 �  E   �  E  
   E     E  
 V  E   Z  E  
 �  �   �  �  
 8  E   <  E  
 H塡$WH侅�   H�    H3腍塂$pH嬟H孂H峀$ �    怢嬅H峊$ H峀$P�    H峅H嬓�    H婽$hH凓v/H�翲婰$PH嬃H侜   rH兟'H婭鳫+罤兝鳫凐w.�    怘峀$ �    H婰$pH3惕    H嫓$�   H伳�   _描    �   �   (   l   ;   k   G      �   �   �   m   �   �   �   �      �   �  8 G            �      �   ,        �GameSettings::Load 
 >�   this  AJ        "  AM  "     � �  
 >7�   node  AI       � � 	  AK          >+�    writer  D     M        J  9Kb M        %  K.W M        �  .VW M        3  +YT M        c  a).
 Z   �  
 >   _Ptr  AH  a       AJ  ^       AH        >#    _Bytes  AK  Y     Y + )  M        s  jd
8
 Z   �   >_    _Ptr_container  AH  u       AJ  r       N N N N N N Z   ./  /  [,  //   �                    A > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c  
 :p   O        $LN38  �   �  Othis  �   7�  Onode      +�  Owriter  O  �   @           �   �	     4       �  �"   �  �-   �  ��   �  ��   �  ��   �   G F                                �`GameSettings::Load'::`1'::dtor$0  >+�    writer  EN                                     �  O,   Y   0   Y  
 ]   Y   a   Y  
 m   Y   q   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 l  Y   p  Y  
 |  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
   Y     Y  
   Y     Y  
 �  �   �  �  
   Y     Y  
 x  �   |  �  
 �  �   �  �  
 H崐    �       m   H塡$H塼$WH冹 H孃H嬞H嬒H�    �    H嬋H嬸�    劺t艱$1 �H峊$0艱$0 H嬑�    圖$0艱$1稤$0H�    H嬒f塁0�    H嬋H嬸�    劺t艱$4 � H峊$0荄$0    H嬑�    �D$0艱$4H婦$0H�    H嬒H塁4�    H嬋H嬸�    劺t艱$4 � H峊$0荄$0    H嬑�    �D$0艱$4H婦$0H�    H嬒H塁<�    H嬋H嬸�    劺t艱$4 � H峊$0荄$0    H嬑�    �D$0艱$4H婦$0H�    H嬒H塁D�    H嬋H嬸�    劺t艱$4 � H峊$0荄$0    H嬑�    �D$0艱$4H婦$0H嬜H嬎H塁LH媆$8H媡$@H兡 _�       %       e   +   d   H   h   ]   (   i   e   t   d   �   i   �   +   �   e   �   d   �   i   �   .     e     d   0  i   G  1   S  e   ^  d   ~  i   �  4      �   �  ? G            �     �  ,        �PerspectiveCameraEx::Load 
 >r�   this  AI       � AJ         
 >7�   node  AK          AM       � M        �,  乄I%  Z   /  @/  
 >7�   node  AH  W      AL  ]    I  M        �-  亪 M        �.  亪 M        �.  亪 N N N M        T-  乫 M        n-  乫 N N N M        �,  �	I%  Z   /  @/  
 >7�   node  AH  	      AL      N  M        �-  �: M        �.  �: M        �.  �: N N N M        T-  � M        n-  � N N N M        �,  ��I%  Z   /  @/  
 >7�   node  AH  �       AL  �     N  M        �-  �� M        �.  �� M        �.  �� N N N M        T-  �� M        n-  �� N N N M        �,  mI%  Z   /  @/  
 >7�   node  AH  m       AL  s     N  M        �-  �� M        �.  �� M        �.  �� N N N M        T-  | M        n-  | N N N M        �,  $I% Z   /  >/  
 >7�   node  AH  $       AL  *     I  M        �-  P M        �.  P M        �.  P N N N M        M-  3 M        m-  3 N N N Z   /  /  /  /  /  N/                         @ B h   �+  �,  �,  M-  N-  T-  U-  m-  n-  �-  �-  �.  �.  �.  �.   0   r�  Othis  8   7�  Onode  O  �   `           �  �	  	   T       �  �   �  �U   �  ��   �  ��   �  �?  �  ��  �  ��  �  ��  �  �,   N   0   N  
 d   N   h   N  
 t   N   x   N  
 �   N   �   N  
 �   N   �   N  
 �   N   �   N  
   N     N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 _  N   c  N  
 o  N   s  N  
 �  N   �  N  
 H塡$H塼$UWAVH嬱H冹 H孃H嬞H嬒H�    �    H嬋H嬸�    劺t艵! �H峌 艵  H嬑�    圗 艵!稥 H�    H嬒f塁�    H嬋H嬸�    劺t艵! �H峌 艵  H嬑�    圗 艵!稥 H�    H嬒f塁�    H嬋H嬸�    劺t艵! �H峌 艵  H嬑�    圗 艵!稥 H�    H嬒f塁�    H嬋H嬸�    劺t艵! �H峌 艵  H嬑�    圗 艵!稥 H�    H嬒f塁�    H嬋H嬸�    E3鰟纓D坲$�H峌 D塽 H嬑�    塃 艵$H婨 H�    H嬒H塁 �    H嬋H嬸�    劺tD坲$�H峌 D塽 H嬑�    �E 艵$H婨 H�    H嬒H塁(�    H嬋H嬸�    劺tD坲$�H峌 D塽 H嬑�    塃 艵$H婨 H�    H嬒H塁0�    H嬋H嬸�    劺tD坲$�H峌 D塽 H嬑�    塃 艵$H婨 H�    H嬒H塁8�    H嬋H嬸�    劺tD坲$�H峌 D塽 H嬑�    塃 艵$H婨 H�    H嬒H塁@�    H嬋H孁�    劺tD坲$�H峌 D塽 H嬒�    �E 艵$H婨 H媡$PH塁HH媆$HH兡 A^_]�!   4   &   e   1   d   K   h   ]   7   i   e   t   d   �   h   �   :   �   e   �   d   �   h   �   =   �   e   �   d     h   &  @   2  e   =  d   Z  g   l  C   x  e   �  d   �  i   �  F   �  e   �  d   �  g   �  I      e     d   %  g   7  L   C  e   N  d   h  g   z  O   �  e   �  d   �  i      �   
  : G            �     �  ,        �SampleSettings::Load 
 >簟   this  AI       � AJ         
 >7�   node  AK          AM       x M        �,  倞I$ Z   /  @/  
 >7�   node  AH  �      AM  �    A  M        �-  偞 M        �.  偞 M        �.  偞 N N N M        T-  倷 M        n-  倷 N N N M        �,  侴I$ Z   /  ?/  
 >7�   node  AH  G      AL  M    t  M        �-  俹 M        �.  俹 M        �.  俹 N N N M        W-  俈 M        p-  俈 N N N M        �,  �I$ Z   /  ?/  
 >7�   node  AH        AL  
    C  M        �-  �, M        �.  �, M        �.  �, N N N M        W-  � M        p-  � N N N M        �,  伭I$ Z   /  ?/  
 >7�   node  AH  �      AL  �    C  M        �-  侀 M        �.  侀 M        �.  侀 N N N M        W-  佇 M        p-  佇 N N N M        �,  亅I$ Z   /  @/  
 >7�   node  AH  |      AL  �    E  M        �-  仸 M        �.  仸 M        �.  仸 N N N M        T-  亱 M        n-  亱 N N N M        �,  �6L$ Z   /  ?/  
 >7�   node  AH  6      AL  <    F  M        �-  乤 M        �.  乤 M        �.  乤 N N N M        W-  丠 M        p-  丠 N N N M        �,  ��I$ Z   /  >/  
 >7�   node  AH  �       AL  �     C  M        �-  � M        �.  � M        �.  � N N N M        M-  � M        m-  � N N N M        �,  ��I$ Z   /  >/  
 >7�   node  AH  �       AL  �     C  M        �-  �� M        �.  �� M        �.  �� N N N M        M-  �� M        m-  �� N N N M        �,  mI$ Z   /  >/  
 >7�   node  AH  m       AL  s     C  M        �-  �� M        �.  �� M        �.  �� N N N M        M-  | M        m-  | N N N M        �,  *I$ Z   /  >/  
 >7�   node  AH  *       AL  0     C  M        �-  R M        �.  R M        �.  R N N N M        M-  9 M        m-  9 N N N. Z
   /  /  /  /  /  /  /  /  /  /                         @ ^ h   �+  �,  �,  �,  M-  N-  T-  U-  W-  X-  m-  n-  p-  �-  �-  �-  �.  �.  �.  �.  �.  �.   @   簟  Othis  H   7�  Onode  O   �   x           �  �	     l       �  �   �  �V   �  ��   �  ��   �  �  �  �e  �  ��  �  ��  �  �0  �  �s  �  ��  �  �,   T   0   T  
 _   T   c   T  
 o   T   s   T  
 �   T   �   T  
 �   T   �   T  
 �   T   �   T  
 �   T     T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 ^  T   b  T  
 n  T   r  T  
 :  T   >  T  
 J  T   N  T  
   T     T  
 &  T   *  T  
 �  T   �  T  
   T     T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 
  T   
  T  
 �     �   �   I G                       �+        �donut::engine::SceneGraphLeaf::Load 
 >!�   this  AJ          D   
 >7�   node  AK          D                           @     !�  Othis     7�  Onode  O   �                               G  �,   *   0   *  
 n   *   r   *  
 �   *   �   *  
 �   *   �   *  
 H塡$H塼$WH冹@H孂�    劺uH媆$PH媡$XH兡@_肏婫PH吚t�@H媁HH塗$ H媉PH塡$(H婻H嬒�    怘呟t/�����罜凐u H�H嬎������罜凐u	H�H嬎�PH婫HH媝0H�H;辴fD  H婥H吚t�@H婯H塋$0H媨H墊$8�    怘�t/�����罣凐u H�H嬒������罣凐u	H�H嬒�PH�H;辵煱H媆$PH媡$XH兡@_�   D   R   b   �   {      �   �  E G                   
,        �ExtendedScene::LoadWithExecutor 
 >)�   this  AJ          AM       �    AM �     r  G  >�>   jsonFileName  AK          >覙   executor  AP          >覡    <begin>$L0  AI  �     r  O  AI �     O ' %  B0   8     �  >A�   it  CJ      �       D0    M        ;,  1Z M        �,  Z/ M        �  \/ M        b  s	 N N N N M        �+  + M        <,  + M        �,  +M		 M        R-  +	 M        �  4 N N N N N M        5,  �� M        y,  �� M        �,  �� M        �,  �� M        �,  �� N N N N N M        ,  �� M        h,  �� N N M        ,  ��f M        f,  �� M        �,  
�� N N N M        U,  �� M        �,  ��M		 M        a-  ��	 M        �  �� N N N N M        T,  1�� M        �,  ��/ M        �  ��/ M        b  ��	 N N N N Z   Q/  ,  4/   @                    @ � h+   b  w  �  �  �+  �+  �+  ,  ,  ,  4,  5,  8,  ;,  <,  L,  T,  U,  ],  f,  g,  h,  i,  x,  y,  �,  �,  �,  �,  �,  �,  �,  �,  �,  �,  �,  �,  �,  �,  �,  -  R-  a-   P   )�  Othis  X   �>  OjsonFileName  `   覙  Oexecutor  0   A�  Oit  9q       �   9�       �   9�       �   9�       �   O  �   h             �	  
   \       �  �   �  �   �  �+   �  ��   �  ��   �  ��   �  ��   �  �  �  �  �  ��   �   T F                                �`ExtendedScene::LoadWithExecutor'::`1'::dtor$0  >A�    it  EN  0                                  �  O   �   �   T F                                �`ExtendedScene::LoadWithExecutor'::`1'::dtor$1  >A�    it  EN  0                                  �  O   ,   a   0   a  
 j   a   n   a  
 z   a   ~   a  
 �   a   �   a  
 �   a   �   a  
 �   a   �   a  
   a     a  
   a     a  
 )  a   -  a  
 J  a   N  a  
 v  a   z  a  
 �  a   �  a  
 �  a   �  a  
 �  a   �  a  
 �  a   �  a  
 P  �   T  �  
 �  �   �  �  
 �  �   �  �  
 C  �   G  �  
 H崐    �       A   H崐0   �          H塡$H塴$H塼$WAVAWH冹0H嬺L孂H婮@H吷剉  荄$     L�
    L�    3诣    H吚tH婲HH吷t�AH媬H�3�����H吚tMH�t�GI墖�   I嫙�   I壙�   H呟t*嬇�罜凐uH�H嬎�嬇�罜凐u
H�H嬎�P惽D$     L�
    L�    3襀婲@�    H吚tH婲HH吷t�AH媈H�3跦吚tNH呟t�CI墖�   M嫹�   I墴�   M咑t+嬇餉罠凐uI�I嬑�嬇餉罠凐u	I�I嬑�PH呟t)嬇�罜凐uH�H嬎�嬇�罜凐u	H�H嬎�PH�t'嬇�罣凐uH�H嬒��羙凖u	H�H嬒�PH婩0H+F(H柳冭Hc豿9H孄H羚@ H媀(H婩0H+翲柳H;豷H��3襂嬒�    H冿H冸y襀媆$PH媗$XH媡$`H兡0A_A^_�6   j   =   g   D   �   �   m   �   g   �   �   �  b      �   ~	  J G                 �  ,        �ExtendedScene::ProcessNodesRecursive 
 >)�   this  AJ          AW       �
 >*�   node  AK          AL       � >o�   sampleSettings  CM     ^     C   CM    �    e  6  >睓   gameSettings  CI     �     �    CI    �     
 >t     i  A   �      A  �      M        �,   N M        �,  +%
 >簟    _Ptr  AH  H     I  AH �     !  M        -  ` M        /,  �
` N N M        �-  M M        �.  M
 M        �.  M	 M        �  V N N N N N M        �,  ��!%
 >�    _Ptr  AH  �     D  AH G    ^  ' 5 %  M        -  �� M        +,  �
�� N N M        �-  �� M        �.  ��
 M        �.  ��	 M        �  �� N N N N N M        ,,  Ll M        -,  +�� M        v,  ��) M        �  ��, M        b  ��	 N N N N M        u,  u M        �,  u M        e  |
 N M        -  u N N N M        .,  	l M        �,  	l M        K-  l M        �  q N N N N N M        �,  �� N M        -,  ,乽 M        v,  乽' M        �  亃, M        b  亷	
 N N N N M        ),  .丟 M        t,  丟) M        �  丩, M        b  乣	 N N N N M        (,  N�� M        ),  -� M        t,  �+ M        �  �- M        b  �1	
 N N N N M        s,  � M        �,  � M        e  �	
 N M        -  � N N N M        *,  	�� M        �,  	�� M        H-  �� M        �  �� N N N N N M        �+  伬 M        B,  伬 N N M        �+  仭 M        B,  仭 N N
 Z   ,   0                    @ � h-   b  �  �  e  �+  �+  �+  (,  ),  *,  +,  ,,  -,  .,  /,  A,  B,  L,  s,  t,  u,  v,  �,  �,  �,  �,  �,  �,  �,  �,  �,  -  -  -  -  H-  I-  K-  L-  Q-  �-  �-  �.  �.  �.   P   )�  Othis  X   *�  Onode = 臓  ExtendedScene::ProcessNodesRecursive::__l2::<lambda_1>  9�       �   9�       �   9/      �   9D      �   9^      �   9r      �   9�      �   9�      �   O  �   �             �	  
   t       [  �   k  �+   �  �b   �  �l   �  ��   �  ��   �  ��   �  �G  �  ��  �  ��  �  ��  �  ��  �  �,   b   0   b  
 o   b   s   b  
    b   �   b  
 �   b   �   b  
 �   b   �   b  
 �   b   �   b  
 �   b   �   b  
 "  b   &  b  
 :  b   >  b  
 V  b   Z  b  
 f  b   j  b  
 �  b   �  b  
 �  b   �  b  
 �  b   �  b  
 �  b   �  b  
 
	  b   	  b  
 	  b   	  b  
 *	  b   .	  b  
 :	  b   >	  b  
 J	  b   N	  b  
 Z	  b   ^	  b  
 j	  b   n	  b  
 z	  b   ~	  b  
 �	  b   �	  b  
 2烂   �     C G                      �+        �EnvironmentLight::SetProperty 
 >�   this  AJ          D   
 >�   name  AK          D    >袳   value  AP          D                           @     �  Othis     �  Oname     袳  Ovalue  O �                  �              �,   I   0   I  
 h   I   l   I  
 �   I   �   I  
 �   I   �   I  
 ,  I   0  I  
 �       5      �     F G                       ,        �PerspectiveCameraEx::SetProperty 
 >r�   this  AJ         
 >�   name  AK          >袳   value  AP         
 Z   O/                          @     r�  Othis     �  Oname     袳  Ovalue  O  �   (              �	            �  �    �  �,   O   0   O  
 k   O   o   O  
 �   O   �   O  
 �   O   �   O  
 $  O   (  O  
 2烂   �   $  P G                      �+        �donut::engine::SceneGraphLeaf::SetProperty 
 >!�   this  AJ          D   
 >�   name  AK          D    >袳   value  AP          D                           @     !�  Othis     �  Oname     袳  Ovalue  O�                               H  �,   +   0   +  
 u   +   y   +  
 �   +   �   +  
 �   +   �   +  
 8  +   <  +  
 �     �   �   A G                       �+        �donut::engine::Light::Store 
 >!�   this  AJ          D   
 >s�   node  AK          D                           @     !�  Othis     s�  Onode  O   �                               �  �,   ;   0   ;  
 f   ;   j   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 H吷tH��   H�`�   �   �   T G                      $-        �std::_Ref_count_obj2<MaterialEx>::_Delete_this 
 >:�   this  AJ                                 @�     :�  Othis  9
       >�   O  �   0              h     $       C �    D �   E �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   X G                      '-        �std::_Ref_count_obj2<MeshGeometryEx>::_Delete_this 
 >颚   this  AJ                                 @�     颚  Othis  9
       靓   O  �   0              h     $       C �    D �   E �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   T G                      *-        �std::_Ref_count_obj2<MeshInfoEx>::_Delete_this 
 >甩   this  AJ                                 @�     甩  Othis  9
       夕   O  �   0              h     $       C �    D �   E �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   Z G                      6-        �std::_Ref_count_obj2<EnvironmentLight>::_Delete_this 
 >L�   this  AJ                                 @�     L�  Othis  9
       P�   O�   0              h     $       C �    D �   E �,   �   0   �  
    �   �   �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   V G                      --        �std::_Ref_count_obj2<GameSettings>::_Delete_this 
 >Ζ   this  AJ                                 @�     Ζ  Othis  9
          O�   0              h     $       C �    D �   E �,   �   0   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   ] G                      3-        �std::_Ref_count_obj2<PerspectiveCameraEx>::_Delete_this 
 >r�   this  AJ                                 @�     r�  Othis  9
       v�   O �   0              h     $       C �    D �   E �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H吷tH��   H�`�   �   �   X G                      0-        �std::_Ref_count_obj2<SampleSettings>::_Delete_this 
 >崷   this  AJ                                 @�     崷  Othis  9
       懄   O  �   0              h     $       C �    D �   E �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H婣H兞3襀�    �   �   P G            
       
   %-        �std::_Ref_count_obj2<MaterialEx>::_Destroy 
 >:�   this  AJ          M        .   
 >P�   _Obj  AJ         N                        @� 
 h   .      :�  Othis  9
       U�   O   �   (           
   h            ? �    @ �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H婣H兞3襀�    �     T G            
       
   (-        �std::_Ref_count_obj2<MeshGeometryEx>::_Destroy 
 >颚   this  AJ          M        .   
 >�   _Obj  AJ         N                        @� 
 h   .      颚  Othis  9
       �   O   �   (           
   h            ? �    @ �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �   �     �  
   �     �  
 H婣H兞3襀�    �   �   P G            
       
   +-        �std::_Ref_count_obj2<MeshInfoEx>::_Destroy 
 >甩   this  AJ          M        .   
 >擀   _Obj  AJ         N                        @� 
 h   .      甩  Othis  9
       堞   O   �   (           
   h            ? �    @ �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H婣H兞3襀�    �     V G            
       
   7-        �std::_Ref_count_obj2<EnvironmentLight>::_Destroy 
 >L�   this  AJ          M        .   
 >W�   _Obj  AJ         N                        @� 
 h   .      L�  Othis  9
       �   O �   (           
   h            ? �    @ �,   �   0   �  
 {   �      �  
 �   �   �   �  
 �   �     �  
   �     �  
 H婣H兞3襀�    �   �   R G            
       
   .-        �std::_Ref_count_obj2<GameSettings>::_Destroy 
 >Ζ   this  AJ          M        .   
 >宝   _Obj  AJ         N                        @� 
 h   .      Ζ  Othis  9
       �   O �   (           
   h            ? �    @ �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H婣H兞3襀�    �     Y G            
       
   4-        �std::_Ref_count_obj2<PerspectiveCameraEx>::_Destroy 
 >r�   this  AJ          M        .   
 >}�   _Obj  AJ         N                        @� 
 h   .      r�  Othis  9
       啞   O  �   (           
   h            ? �    @ �,   �   0   �  
 ~   �   �   �  
 �   �   �   �  
   �     �  
   �      �  
 H婣H兞3襀�    �     T G            
       
   1-        �std::_Ref_count_obj2<SampleSettings>::_Destroy 
 >崷   this  AJ          M        
.   
 >槮   _Obj  AJ         N                        @� 
 h   
.      崷  Othis  9
          O   �   (           
   h            ? �    @ �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
 �   �     �  
   �     �  
 3烂   �   �   H G                      c        �std::_Ref_count_base::_Get_deleter 
 >�   this  AJ          D    >   __formal  AK          D                           @�     �  Othis       O__formal  O�   0              h     $       � �    � �   � �,      0     
 m      q     
 �      �     
 �           
 H冹HH峀$ �    H�    H峀$ �    �
         �      �      �   �   F G                       q        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               @            J �   K �,      0     
 �   �   �   �  
 �      �     
 H冹(H�
    �    �   �      �      �   w   7 G                     ~        坰td::_Xlen_string 
 Z   �!   (                      @        $LN3  O �   (              �             		 �   
	 �,      0     
 s   �   w   �  
 �      �     
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   �   �   �   �   �   �   �   ,  �   O     U     [  �      �     r G            `     `  )        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >�   _Ptr  AK          AW       D/  >_   _Count  AL       G4  AP         B M        -  E
(?SD3$--K
 Z   ~   >#     _New_capacity  AH  �     �  * N  V r  AJ  �       AM  O     =  ^ �  AH �     G  ,  AJ �       M        �,  �� M        ?   �� N N M        �  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        �  ��?�� M        �  ��?��* M        (  ��

*%
u- M        9   ��	)
��
 Z   q   >_    _Block_size  AJ  �     �  �  AJ �       >_    _Ptr_container  AH  �       AH �     }  b 
 >�    _Ptr  AV  �       AV �     ~ V "  M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  X(  M          X' >_    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M        �  
~ N N N M        �  -�W M        3  �&P M        c  �
)/
 Z   �  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        s  
�#
2
 Z   �   >_    _Ptr_container  AP        AP +    4  *  >_    _Back_shift  AJ      
  AJ Z      N N N N N M        �  L4 N M        0  $# >p    _Result  AM  '       AM 8      M        �  ' N N                       @ z h   �  �  r  s  t  �  $  0  3  ?  �  �  �  �  �  �  c  �  �  �    �  �  '  (  /   9   �,  -         $LN144  @   �  Othis  H   �  O_Ptr  P   _  O_Count e 潵  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_1>  O�   h           `  �   
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 �          
          
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 m     q    
 }     �    
 H     L    
 \     `    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 F     J    
 k     o    
 {         
 �     �    
 �     �    
          
      #    
 �     �    
 �     �    
 \  �   `  �  
          
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              X     $       ^  �    _  �   `  �,      0     
 _      c     
 �      �     
  d T 4 2p    H           �      �      )    20    2           �      �      /   
 
4 
2p    B           �      �      5    20    <           �      �      ;   
 
4 
2p    B           �      �      A    20    <           �      �      G   
 
4 
2p    B           �      �      M    �                  �      �      S    B                 �      �      Y   
 
4 
2p    �           �      �      _    T
 4	 2�p`    [           �      �      e   ! �     [          �      �      e   [   8          �      �      k   !       [          �      �      e   8  T          �      �      q   !   �     [          �      �      e   T  `          �      �      w    d 4 2p           �      �       I          �      �      }   h           �      �          �   XC W x � � � � � (FZx�
 
4 
2p    4           �      �      �    20               �      �      �   ! t               �      �      �      E           �      �      �   !                 �      �      �   E   K           �      �      �   - 20               �      �      �   ! t               �      �      �      E           �      �      �   !                 �      �      �   E   K           �      �      �   - 2p               �      �      �   ! T 4               �      �      �                 �      �      �   ! d              �      �      �      T           �      �      �   !                �      �      �   T   d           �      �      �   !                 �      �      �   d   z           �      �      �   *<
 
d 
2p               �      �      �   ! T               �      �      �      '           �      �      �   ! 4    '          �      �      �   '   v           �      �      �   !      '          �      �      �   v   �           �      �      �   !                 �      �      �   �   �           �      �      �   >U d 4 2p           �                       �      �         h           
      
          �   XI]{�
 
4 
2p    4           �      �         
 
4 
2p    N           �      �         
 
4 
2p    N           �      �      %   
 
4 
2p    N           �      �      .    d 4 2p    r           �      �      7   "6R
 
4 
2p    4           �      �      @    20               �      �      F   ! t               �      �      F      E           �      �      L   !                 �      �      F   E   K           �      �      R   -
 
d 
2p               �      �      [   ! T               �      �      [      '           �      �      a   ! 4    '          �      �      a   '   v           �      �      g   !      '          �      �      a   v   �           �      �      m   !                 �      �      [   �   �           �      �      s   >U d 4 �p    P      �                 �      �      |    d 4
 rp           �      �       �           �      �      �   (           �      �   
    P   }   4 �幆� 20               �      �      �   
 
4 
2p    �           �      �      �   
 
4
 
rp    �           �      �      �    d 4 2p    �          �      �      �   
 
4 
2p    N           �      �      �    r0    5           �      �      �    d
 4	 2�pP    �          �      �      �   
 
4 
2p    N           �      �      �    r0    5           �      �      �    
4 
�p        r      �      �       �           �      �      �   (           �      �   
    @   m   N &� 
 
4 
2p    �           �      �      �   h
 d T 4 ���p    �          �      �      �   �� d T
 Rp    .           �      �      �   ! 4     .          �      �      �   .   �           �      �      �   !       .          �      �      �   �   �           �      �      �    d T
 Rp    2                            ! 4     2                           2   �                         	   !       2                           �                               
 
4 
Rp    �                           d 4
 rp           �      !                                (           $      '   
    @:    `   A         $ ~�4ax┫�
 d T 4
 R��p           �      3                             -   (           6      9          �   �� V q � � � � 3Nb|�! !d !T !4 !�����p           �       E       Q                      ?   (           H      K       !:    p6       B   	   �      �   ��? u � � � ���* 2p                           Q   ! T 4                           Q      !                       W   ! d    !                      W   !   W                       ]   !      !                      W   W   o                       c   !                             Q   o   �                       i   -? d T 4 2p           �      x                             r   h           {      ~          �   VX g{櫗溯 20                           �   ! t                           �      E                       �   !                             �   E   K                       �   - 2p               	      	      �   ! T 4               	      	      �                 	      	      �   ! d              	      	      �      T           	      	      �   !                	      	      �   T   d           	      	      �   !                 	      	      �   d   z           	      	      �   *< d T 4 2p           �      �       �                       �   h           �      �          �   | 20           �      �       W                       �   h           �      �          �   �
 
4 
2p           �      �       y           
      
      �   h           �      �          �   �
 
4	 
Rp    �                       �    20                           �   ! t                           �      E                       �   !                             �   E   K                       �   - R0    -                           20                               20                               �0    @      �       �                          
 
4	 
Rp    �                          
 
4	 
Rp    �                       #   
 
4 
Rp           �      /       j                       )   `       2   V M
 
4 
Rp           �      >       j                       8   `       A   V M 20                           G   ! t                           G      E                       M   !                             G   E   K                       S   -
 
4 
Rp           �      b       j                       \   `       e   V M
 
4 
2p    _                       k   
 
4 
2p    d                       q   
 
4 
2p    b                       w    20    +                       }    20    +                       �    20    +                         �    20    +           !      !      �    20    +           "      "      �    20    +           #      #      �    20    +           $      $      �    B      :           &      &      �                               v               Unknown exception                             �      
                                     �               bad array new length                                
      �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �                         .?AVbad_alloc@std@@     �              ����                      �                         .?AVexception@std@@     �               ����                      �         string too long     ����    ����        ��������                    �                             �                              �      %                                                               �      -      (      �       )   (   *   0   +                                                               �      1      (      �       /   (   *   0   +                                                               �      7      (      3       /   (   4   0   5                                                                                       �      ?      (      �       9   (   *   0   <   8   �   @   :   H   ;                                                                                       	      K      (      G       9   (   E   0   I   8   F   @   H   H   ;                                                                     Q      (      M       /   (   N   0   O                                                               $      V      (      S       )   (   T   0   +                                                               0      [      (      X       )   (   Y   0   +                       <      o                       K      t                       Z      y   radianceScale textureIndex rotation path EnvironmentLight PerspectiveCamera PerspectiveCameraEx MaterialPatch SampleSettings GameSettings enableAutoExposure exposureCompensation exposureValue exposureValueMin exposureValueMax realtimeMode enableAnimations enableReSTIRDI enableReSTIRGI startingCamera realtimeFireflyFilter maxBounces realtimeMaxDiffuseBounces referenceMaxDiffuseBounces textureMIPBias                                             i      �      �      �                                                      x      �      �      �                                                      �      �      �      �                                                      �      �      �      �                                                      �      �      �      �                                                      �      �      �      �                                                      �      �      �      �                          .?AVSceneGraphLeaf@engine@donut@@     �                   .?AVSampleSettings@@     �                   .?AVGameSettings@@     �                   .?AVLight@engine@donut@@     �                   .?AVEnvironmentLight@@     �                                         �      y      v                         |                                  ����    @                   �      y                                         �      �      �                         �                           �                    ����    @                   �      �                                         �      �      �                         �                                   �      �                    ����    @                   �      �                   .?AV_Ref_count_base@std@@     �                         �                   �               ����    @                   �      �                                         �      �      �                   .?AUMaterial@engine@donut@@     �                         �                   �               ����    @                   �      �                                         �      �      �                   .?AUMeshGeometry@engine@donut@@     �                         �                   �               ����    @                   �      �                                         �      �      �                   .?AUMeshInfo@engine@donut@@     �                         �                   �               ����    @                   �      �                                         g      �      �                         �                   �               ����    @                   g      �                                         �      �      �                   .?AVSceneCamera@engine@donut@@     �                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AVPerspectiveCamera@engine@donut@@     �                         �                                   �      �      �              ����    @                   �      �                                         p             �                                                          �              ����    @                   p                                                s            	                                                                        �              ����    @                   s                                                                              .?AVPerspectiveCameraEx@@     �                                                                    !      �      �      �              ����    @                                                                  j      '      $                         *                           -      �              ����    @                   j      '                                         m      3      0                         6                           9      �              ����    @                   m      3                                         ?      B      <                   .?AUMaterialEx@@     �                         E                           H      �              ����    @                   ?      B                                         N      Q      K                   .?AUMeshGeometryEx@@     �                         T                           W      �              ����    @                   N      Q                                         ]      `      Z                   .?AUMeshInfoEx@@     �                         c                           f      �              ����    @                   ]      `                                         l      o      i                   .?AV?$_Ref_count_obj2@VEnvironmentLight@@@std@@     �                         r                           u      �              ����    @                   l      o                                         {      ~      x                   .?AV?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@     �                         �                           �      �              ����    @                   {      ~                                         �      �      �                   .?AV?$_Ref_count_obj2@VSampleSettings@@@std@@     �                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@VGameSettings@@@std@@     �                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@UMeshInfoEx@@@std@@     �                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@UMeshGeometryEx@@@std@@     �                         �                           �      �              ����    @                   �      �                                         �      �      �                   .?AV?$_Ref_count_obj2@UMaterialEx@@@std@@     �                         �                           �      �              ����    @                   �      �   �����   �   .   , 
D�        donut::engine::Light::`vftable'      �      �  
    �   $   " 
�        MaterialEx::`vftable'    �      �  
    �   5   3 
�        donut::engine::MeshGeometry::`vftable'       �      �  
    �   1   / 
�        donut::engine::MeshInfo::`vftable'       �      �  
    �   (   & 
�        std::exception::`vftable'    �      �  
    �   (   & 
�        MeshGeometryEx::`vftable'            
    �   (   & 
�        std::bad_alloc::`vftable'    �      �  
    �   3   1 
�        std::bad_array_new_length::`vftable'     �      �  
    �   $   " 
�        MeshInfoEx::`vftable'            
    �   *   ( 
D�        EnvironmentLight::`vftable'      �      �  
    �   -   + 
0Z        PerspectiveCameraEx::`vftable'       �      �  
    �   (   & 
0Z        SampleSettings::`vftable'    �      �  
    �   &   $ 
0Z        GameSettings::`vftable'      �      �  
    �   1   / 
�        donut::engine::Material::`vftable'       �      �  
    �   7   5 
0Z        donut::engine::SceneGraphLeaf::`vftable'     �      �  
    �   @   > 
�        std::_Ref_count_obj2<EnvironmentLight>::`vftable'    R      R  
    �   C   A 
�        std::_Ref_count_obj2<PerspectiveCameraEx>::`vftable'     U      U  
    �   >   < 
�        std::_Ref_count_obj2<SampleSettings>::`vftable'      X      X  
    �   <   : 
�        std::_Ref_count_obj2<GameSettings>::`vftable'    [      [  
    �   :   8 
�        std::_Ref_count_obj2<MeshInfoEx>::`vftable'      ^      ^  
    �   >   < 
�        std::_Ref_count_obj2<MeshGeometryEx>::`vftable'      a      a  
    �   :   8 
�        std::_Ref_count_obj2<MaterialEx>::`vftable'      d      d  
    �   4   2 
0Z        donut::engine::SceneCamera::`vftable'    �      �  
    �   :   8 
0Z        donut::engine::PerspectiveCamera::`vftable'      �      �  
 郝�C�(0%�'5�堈�溞矺\�<@垯僙�*lG渋坱螚伔贕郴扷亘暗霌{竿�% �腤\�8膤M*[6E費詥*�锹坼簫碶渴墿k 4陫"A�讘㈦檴祆墶U&蚵�
淊�8棬骊捝趉'$鍖{屌鏥a\T簬$鍖{屌琨P8淁艈{$鍖{屌鏸,N韉![h�K蜌�(進U篆土jo'茛惙阋�Oゲ江鑟'茛惙阋>
ao'茛惙阋甸�3EE]^Xj�
螐(M5
韄� Nt谑攮迪-K��柘倻饼HI,珻亟r�2滲猇Ro:#粤�z	G�l��?跗�kOG;嫰M\聡臂ep禭�?As贛嚤踖p禭飓D堦縵�6萪O�H焟cq擪霵婬(Φ�夵劫Fk{�粦到貴k{獚圳瑙榻貴k{� |弗Z沤劫Fk{K孯�/〗貴k{鍐橃箾娊貴k{r蛟&�\劫Fk{�u觱2茎$愜w獛啯W炷F遤�$愜w獛啯蟝�G<$愜w獛啯被�聡吩$愜w獛啯3�� =^存U
(B�飢费pmdD預!Z騵戥峌評ZdD預!Z驐�1:4&7峞誝�)�櫊u牗殽搳螽`L江竅�9熵1韢廙eQT7跞hWI傌1韢廙eQ烅鯝v4�憷挕箘謄倠y�羀]!5韒�Hv顰</`"kD园缷+慥}	琏端祆癜~t芊=�?i$	嶀預棊膬/S;圾j硘嶀預棊膬З�?y9%龠Z�$|��龤z/yI窑,捑瘑帤{茟y縷<��<:�#�~氣JZ%玢漷;�垾輡6a=0劯魗l恖d��0槥)虜g颦婦�蕑胗1弝膞#� 饨z藖�=厦;Z弅朏鸕�#GI�1�'翊M 彅}�壁G`_~嚩侍���<欹鯂遰1鷊Cl[�/馤'�:肌褄YY蛈m蕰馈衿鴷暜駜畎忭庳lY濏|�3袣�6�5�闇�ｅ鎏鼼�#<濌2皻<覈�$"牫�=轇�:Pm�驥E谓Sso�
h畔鵇\媭e#gC盦Lm}璔既牘J%uo5琭Y扒R蝳_u~2畨]蚞m �2冴K絚�鰋)=往�,K絚�鰋-驹見Ck箩邆5>(H7и篼}箩邆5>螬�0|⒙徇�5>;ㄓ$7� �蹰kL�*胾�,臕f贲+抺詴痲陥進♁恗昷嵁	/tk>C绀e��2�*$�9钖飝lo嵁	/嬿 �
篙9腼蔳V� 箂腎�O赈嚎雖@g�*w蜮R�+� �+媫"�0茨�鳍~g早{rL�艫d矺.|zKm|唔ezZ%�/柳靪吔竆赑;筲跍�*[故3飠�+ 埚巌;�帅be2梩<騄鱱�3�>Y�%!毠虗Nx�$蟊惺魪挄聋_侄-�&z輆遯軎w縫?輈潁瓠
斗夲翩*]琵>Nて瑽赠�镄顷�衔X
鼯
U訧)#hv瓯樍3轏熟莀了"齠�4鍉�=]ーE端祆癜~tT钬U黢哨煠��	�*tNτ簟*訧)#hv瓯�昸鳐3�!�8v=jQ�B~k宪辁薌衳磩	耋Bx胴'洋m|驸UQR7�7薌衳磩橼甛�煻遂祚皛te�"#鄂�7颦硣KH傾~e+�>躔7颦硣Kw?�	~裃条�7颦硣K:齯e?筿躔7颦硣K�8�c挣�7颦硣K薆j笁楑�7颦硣K�t抨�0桴�7颦硣K潼�柸�瘊衚癙漊�:处瘊衚癙�鐧z,瘊衚癙z�6S欹旔k癙w袘�
播旔k癙殯獱*bu旔k癙M蜀念	>;瘊衚癙ㄢ琽 鮡�$蟊惺籼鴷y�%I栶賑?T秝鋊蛙�
f]{謑p臫圄w�膜\丽Υ賊沄�3騋nN鵘J庳)�dyF{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这�9E\$L釉尴 蛮l�(証橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 売g楟迺�%'敼_m^G咞taR�,F_棢杻#Q媮飆�"�X[囶<�9E\$L釉轎4u�=雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛繌�bx斣*k狍裛偲囝z`cv"氎�2b+	MFwN間龍,6壞痝�+�莱Fwq∟�
V�9�I鄘5灺yX话妗灐杔Z�9.楞ч鷃兟+d+眔Aom�昈d婸潤昬*楏W�1嚲\僎&|篇4胎�1� {楚�旲呚�!斈wvu顈騼7RN砛觛楟迺�%rx�i葏鹴aR�,F_棢杻#Q媮飆�"E+	�9�9E\$L釉轎4u�=M{>�0
蟨9E\$L釉轞�6	褔諴��9E\$L釉轞�6	褔諴��9E\$L釉轞�6	褔諴��c闲�
墸g丢遧�6藢<c覂m9E\$L釉轎4u�=雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛Z�9.楞ч鷃兟+d+眔Aom�昈d婸潤昬*楏W�1嚲\僎&|篇4胎�1� {楚�旲呚�!斈wvu顈騼7RN砛x捎+薇7-腨!櫾*骫A焅褌�?耯�mdd�a�:2槔j謠鐶\Pzjλ�4A晭j�>雵J-WV8o�摮Dk.,9E\$L釉轤艮�:_�rлn}�=杉s鶷z氈\6	朌釉�鐛9E\$L釉轞�6	褔諴��6e<F\飏俼�5vx凾2l?~J�-dX搎�9E\$L釉轞�6	褔諴��6e<F\飏俼�5v+U箾睞)毺��dd�a�:画k湻J處�1�*�9E\$L釉�鍉巚K朩@&d瘠攔僰�5G披x3腠訢縝赚Y拆v爁Q軣
,騤K躈 mP}2K:�+厨3礩僠僶藧�*�|覆饁爁Q�3,�4q胭#婎挨]陘�#0纼i�G�6'j8f鈔�蒏暋m-u"釔[�9x*骫A焅淹p窴桖|�dd�a�:啽冺��舣鼔燹1O�#M瞓◇潫歝忰獡绝-航dd�a�:_棢杻#Q@�pH~錦sl=聑鐨/"�1铲,墛T鄤E嵒dd�a�:� �F娀D瘈忼牸y}�#"z�V繌�bx斣*k狍裛�7�D奊�6联鲼$� 楁邸翫j椵E<�縱,s侣�V�9�I鄽j祉�"ⅰ$朸閴.�?覡7-腨!櫾咞taR�,F_棢杻#Q6f�1B颲╀md雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛繌�bx斣*k狍裛偲囝z`cv"氎�2b+	MFwN間龍,6壞痝�+�莱Fwq∟�
V�9�I鄘5灺yX话妗灐杔.�?覡coq�	a%咞taR�,F_棢杻#Q=泳脸餿焉S�8萀D莲Tラ~�&咞taR�,F_棢杻#Q怯!偵疙﨏帲晗D'Q鶘2m46咞taR�,F_棢杻#Q暰b.d萧�埨囈=v6雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛/谚泹嵴d繅鬮朏菜{.嘕-WV8o0]Z�9�$雵J-WV8o曋赛鍠��$<`{塣d\蜩kU萧�埨圐裏Ｋ.W糁埨噄＄燤�'G%钉�譑iv褍| H挎驻趀钚梆阇�/騫坾镚橤%钉�譑iv褍| H挎驻趀钚梆阇�/騫坾镚雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛橤%钉�譑iv褍| H挎驻趀钚梆阇�/騫坾镚9E\$L釉�1&y萉Be9E\$L釉��&9�=NB9E\$L釉�>%'剌i雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)雵J-WV8o额	hQ�)-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H7G�鱭澱7G�鱭澱7G�鱭澱詠zv(缭亃v(缭亃v(鏪o祹a鶪Y[o祹a鶪Y詠zv(缭亃v(缭亃v(�7G�鱭澱7G�鱭澱7G�鱭澱徨蝝�T瀃鮬#蓐哋�<�i:K��/媚�'契喰�:�;憐�逗g&�h珍\貾猱孨T? �	�+訬8zMp{x鏦y纘鴥�*嬱
禃�%荋w O{陮�-h錫撄g芭添q7臸肁|1k�慀
廗�
湢o檻b硽^}v	Q��6a�3笼;�	2�%@9�$ 矤伞鄵れ}鋤�'呲,4��;儗,4��;儗,4��;儗,4��;儗,4��;儗,4��;儗,4��;儗$l_	-~lec,鱀姵掉拓户︳嶕��\ㄆz��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2�鐕�C]q"�:邍A愦靮鸬2�>料C5��\&2渇�+浰=4�"�:邍A愦靮鸬2�>料C5��\&2渘餣毰�"�:邍A愦靮鸬2�>料C5��\&2�"�:邍A愦靮鸬2�>料C5��\&2湉鯺寓�
%ZZ�$为赞G刹~赣 "^惋砤��\&2�!oC劏轧鼑#qM�5<A:蓰咨难栨熸� �\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �\&2湱汽M顷莚黋lL�^鴐禵諢覸鰛B	挿;�\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2溿N=﹢�%ZZ�$为赞G刹~赣 "^惋砤��\&2溦�p2揽�+%ZZ�$为赞G刹~赣 "^惋砤��\&2湜~+�5%ZZ�$为赞G刹~赣 "^惋砤��\&2滮/獫�	7%ZZ�$为赞G刹~赣 "^惋砤��\&2湉<冮V!摢%ZZ�$为赞G刹~赣 "^惋砤��\&2湅F狶gj�%ZZ�$为赞G刹~赣 "^惋砤��\&2湬欷創�	�%ZZ�$为赞G刹~赣 "^惋砤��\&2湆ン^vX%ZZ�$为赞G刹~赣 "^惋砤��\&2湺潤h祙俹%ZZ�$为赞G刹~赣 "^惋砤��\&2�	J�E掳
%ZZ�$为赞G刹~赣 "^惋砤萡H#^�?�<溺钼�&�遜�<泙7@乭障Ass4,灞� W�1�5�;� タY烜烰^�1GE脌祦�
監�賅鉤Kt:C膖xG� 剡痑p�8玚� ;绖釃n�#S鷰栃9	麑殷o'砲>骷�Thj>粳�蛄皴p琙CCo鲽�'i]劍6�k衔墥g贫銫�-X cB逮獤6?儹嗋勼ブ)        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       P                .debug$S       8a 
             .debug$T       l                 .rdata         L       4G蹾                         7          .text$mn             ~皯Y     .debug$S          
           .text$mn       b      圤娐     .debug$S       �             .text$mn    	         D悱�     .debug$S    
      
       	    .text$mn       d      L屮     .debug$S       �             .text$mn    
   -      
首     .debug$S       8  
       
    .text$mn       �      訫奝     .debug$S       X             .text$mn       _      鎝     .debug$S       �             .text$mn              l;     .debug$S       0             .text$mn              l;     .debug$S       0             .text$mn              l;     .debug$S       @             .text$mn       :      眡�     .debug$S                    .text$mn       j      屇淬     .debug$S       �             .text$mn       j      屇淬     .debug$S       �             .text$mn       j      屇淬     .debug$S        �             .text$mn    !   �      |o6     .debug$S    "   T         !    .text$mn    #   �      浈僔     .debug$S    $   �  
       #    .text$mn    %   �      镎�     .debug$S    &   �  
       %    .text$mn    '          B
忣     .debug$S    (   P         '    .text$mn    )   (       芌     .debug$S    *   �         )    .text$mn    +   �     E憔     .debug$S    ,   P         +    .text$mn    -   <      .ズ     .debug$S    .   0  
       -    .text$mn    /   <      .ズ     .debug$S    0   L  
       /    .text$mn    1   !      :著�     .debug$S    2   <         1    .text$mn    3   2      X于     .debug$S    4   <         3    .text$mn    5         峦諡     .debug$S    6            5    .text$mn    7         峦諡     .debug$S    8            7    .text$mn    9         峦諡     .debug$S    :            9    .text$mn    ;         峦諡     .debug$S    <            ;    .text$mn    =         峦諡     .debug$S    >            =    .text$mn    ?         峦諡     .debug$S    @            ?    .text$mn    A         峦諡     .debug$S    B            A    .text$mn    C   K       }'     .debug$S    D   �         C    .text$mn    E   K       }'     .debug$S    F   �         E    .text$mn    G   K       }'     .debug$S    H   �         G    .text$mn    I   K       }'     .debug$S    J   �         I    .text$mn    K   �      吖     .debug$S    L   �          K    .text$mn    M   �      u`     .debug$S    N     "       M    .text$mn    O   �      u`     .debug$S    P   �  "       O    .text$mn    Q   r       陨W2     .debug$S    R   8         Q    .text$mn    S   I     鳚鲹     .debug$S    T   T
  V       S    .text$mn    U   K       稫@     .debug$S    V   �         U    .text$mn    W   K       稫@     .debug$S    X   �         W    .text$mn    Y        舎飧     .debug$S    Z   �  &       Y    .text$mn    [   W      d醡�     .debug$S    \   �         [    .text$mn    ]           !(;�     .debug$S    ^   p  
       ]    .text$mn    _          .B+�     .debug$S    `   �          _    .text$mn    a         ��#     .debug$S    b   �          a    .text$mn    c         ��#     .debug$S    d   �          c    .text$mn    e   �      	�     .debug$S    f   �         e    .text$mn    g   +      �     .debug$S    h   �          g    .text$mn    i   +      趡�     .debug$S    j   �          i    .text$mn    k   +      廯�#     .debug$S    l   �          k    .text$mn    m   +      �,�     .debug$S    n   �          m    .text$mn    o   +      >>浶     .debug$S    p   �          o    .text$mn    q   +      嗇�     .debug$S    r   �          q    .text$mn    s   +      (寶     .debug$S    t   �          s    .text$mn    u   �      ��     .debug$S    v   �         u    .text$mn    w   �      喪�      .debug$S    x   �         w    .text$mn    y   4      縑妁     .debug$S    z   �          y    .text$mn    {   4      �,+     .debug$S    |   �          {    .text$mn    }   �      G�:�     .debug$S    ~   �         }    .text$mn            �#�     .debug$S    �   �  *           .text$mn    �   z      f:l,     .debug$S    �   �         �    .text$mn    �   z      �せ     .debug$S    �   �         �    .text$mn    �   4      琅r     .debug$S    �   �          �    .text$mn    �   y      袙v�     .debug$S    �   �         �    .text$mn    �   N      Z敶     .debug$S    �   �         �    .text$mn    �   N      tn�     .debug$S    �   �         �    .text$mn    �   N      菴籗     .debug$S    �   �         �    .text$mn    �   N      W	H�     .debug$S    �   �         �    .text$mn    �   N      W	H�     .debug$S    �   �         �    .text$mn    �   B      贘S     .debug$S    �             �    .text$mn    �   B      贘S     .debug$S    �            �    .text$mn    �   B      贘S     .debug$S    �   �          �    .text$mn    �   H       襶.      .debug$S    �   �         �    .text$mn    �   �      �q�     .debug$S    �   �  (       �    .text$x     �         Kバg�    .text$mn    �   5      >C�     .debug$S    �   �         �    .text$mn    �   �      \f     .debug$S    �     
       �    .text$mn    �   5      >C�     .debug$S    �   �         �    .text$mn    �   �     妹�      .debug$S    �     6       �    .text$mn    �   �      T癛p     .debug$S    �   �         �    .text$mn    �         X�(     .debug$S    �   H         �    .text$mn    �   �      含毥     .debug$S    �   L         �    .text$mn    �         Qs匓     .debug$S    �   d  
       �    .text$mn    �   Q     譎鷽     .debug$S    �   �  Z       �    .text$x     �         漤羕�    .text$x     �         :�水    .text$mn    �          覫勀     .debug$S    �   �          �    .text$mn    �          悤�     .debug$S    �   �          �    .text$mn    �          �猴     .debug$S    �   �          �    .text$mn    �          �!妣     .debug$S    �   �          �    .text$mn    �   3      圎     .debug$S    �   h         �    .text$mn    �        uY#�     .debug$S    �   �  "       �    .text$mn    �   �      !C叁     .debug$S    �   �         �    .text$x     �         S芫    .text$mn    �   �     xl     .debug$S    �             �    .text$mn    �   �  (   �     .debug$S    �   �
  4       �    .text$mn    �          .B+�     .debug$S    �            �    .text$mn    �        Y�X     .debug$S    �   p  &       �    .text$x     �         S芮    .text$x     �         "E萷�    .text$mn    �        埶�
     .debug$S    �   
  0       �    .text$mn    �          簎x�     .debug$S    �   L  
       �    .text$mn    �         �%     .debug$S    �   L  
       �    .text$mn    �          簎x�     .debug$S    �   X  
       �    .text$mn    �          .B+�     .debug$S    �            �    .text$mn    �          c淖�     .debug$S    �             �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �          c淖�     .debug$S    �             �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �          c淖�     .debug$S    �             �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �          c淖�     .debug$S    �            �    .text$mn    �   
       肷瞰     .debug$S    �   <  
       �    .text$mn    �   
       肷瞰     .debug$S    �   @  
       �    .text$mn    �   
       肷瞰     .debug$S    �   <  
       �    .text$mn    �   
       肷瞰     .debug$S    �   @  
       �    .text$mn    �   
       肷瞰     .debug$S    �   <  
       �    .text$mn    �   
       肷瞰     .debug$S    �   D  
       �    .text$mn    �   
       肷瞰     .debug$S    �   @  
       �    .text$mn    �          �猴     .debug$S    �   ,         �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �   `     匮�5     .debug$S    �   �  B       �    .text$mn    �         崪覩     .debug$S    �   �          �        \       �        x                �                �                �                �                �                �                                      3        A      c        [      �        {      �        �          i                   �      -        �      �        �          i	                         1        =      a        b      /        �      �        �          i                   �      �        
      �        #      e        z      �        �      _        �      �        0      S        R      +        t      {        �          i                   �      C        �      U              �        I          i                   t      M        �      Y               �        G          i$                   n      ]        �      �        �      �        2      �        m      �        �      �        '          i,                   T      �        �      �        �          i0                   �               U	               �	               #
      �        S
          i6                   �
      �        �
                     �        ?               �      Q        �      y                  i>                   *      I        b      O        �               �
               �
      �              �        3      �        �      �        �      �        P      u        r          iJ                   �      �        �      �              �        �      �        �          iP                   �      �        D      �        r      �        �          iU                   �      �              �        .      w        L          iZ                   j      �        	      �        h      �        �      �        *      �        ~      �        �      �        �               �               �               n               �               �                              f               �               
               /      }        K          in                   g              �      W        �      �        �          is                   �      K        t      [        �      �        �          ix                   �                     !        Y      E        �      )        5      
        �              �      	        �              k      #        �      %              '        �                            �                            �              �      G        $               �               �               (!              \!      ;        �!      �        �!      �        "      m        K"          i�                   �"      ?        �"      �        �"      �        C#      q        �#          i�                   �#      A        �#      �        .$      �        m$      s        �$          i�                   �$      =        %      �        J%      �        �%      o        �%          i�                   �%      9        %&      �        \&      �        �&      k        �&          i�                   '      7        5'      �        p'      �        �'      i        �'          i�                   !(      5        Q(      �        �(      �        �(      g        �(          i�                   -)              �)      �        �)      �        �*      �        �*      �        F+      �        	,      �        l,               ,               �,               �,               �,               �,           memcmp           memcpy           memmove          memset           $LN13       �    $LN5        3    $LN10       �    $LN7        -    $LN13       �    $LN10       /    $LN16       �    $LN3        �    $LN4        �    $LN3       �    $LN4        �    $LN62   �   e    $LN66       e    $LN144  `  �    $LN151      �    $LN151  I  S    $LN154      S    $LN8        {    $LN18       C    $LN20       U    $LN26       �    $LN61   �   M    $LN64       M    $LN64     Y    $LN67       Y    $LN8        �    $LN21       �    $LN24       �    $LN27       �    $LN32       Q    $LN8        y    $LN18       I    $LN61   �   O    $LN64       O    $LN49     �    $LN53       �    $LN44       �    $LN6        �    $LN45   �   u    $LN48       u    $LN25       �    $LN90       �    $LN30       �    $LN25       �    $LN175      �    $LN24       �    $LN25       �    $LN38   �   �    $LN42       �    $LN58   �   w    $LN61       w    $LN311      �    $LN64       �    $LN139      �    $LN69       �    $LN105      �    $LN162      �    $LN180  Q  �    $LN183      �    $LN26       }    $LN61           $LN23       W    $LN29       �    $LN50   �   K    $LN53       K    $LN18       [    $LN23       �    $LN89       !    $LN18       E    $LN4        
    $LN4            $LN4        	    $LN40   �       $LN44           $LN143      #    $LN167      %    $LN21           $LN21           $LN18       G    $LN21           $LN20           $LN20           $LN20           $LN8        m    $LN8        q    $LN8        s    $LN8        o    $LN8        k    $LN8        i    $LN8        g    $LN14   :       $LN17           .xdata      �          F┑@�        �,      �    .pdata      �         X賦鷻        -      �    .xdata      �          （亵3        '-      �    .pdata      �          T枨3        P-      �    .xdata      �          %蚘%�        x-      �    .pdata               惻竗�        �-          .xdata               （亵-        �-         .pdata              2Fb�-        �-         .xdata               %蚘%�        .         .pdata              惻竗�        =.         .xdata               （亵/        c.         .pdata              2Fb�/        �.         .xdata               %蚘%�        �.         .pdata              惻竗�        �.         .xdata      	         懐j烍        -/      	   .pdata      
        Vbv        ]/      
   .xdata               �9��        �/         .pdata              �1磅        �/         .xdata      
         %蚘%e        �/      
   .pdata              寵Qe        ,0         .xdata               蔜-鬻        �0         .pdata              愶L�        �0         .xdata              �qL凎        K1         .pdata              ~蕉谨        �1         .xdata              |摈        2         .pdata              瞚挨�        q2         .xdata              S!熐�        �2         .pdata              �o堶        53         .xdata              圇�
S        �3         .pdata              锶M        �3         .xdata        	      �#荤S        �3         .xdata              jS        4         .xdata               b锜S        H4         .voltbl              陬S    _volmd         .xdata               %蚘%{        t4         .pdata              嘳�{        �4         .xdata               （亵C        �4         .pdata               � 貱        5          .xdata      !        范^揅        L5      !   .pdata      "        鳶�C        �5      "   .xdata      #        @鴚`C        �5      #   .pdata      $        [7蹸        	6      $   .voltbl     %         飾殪C    _volmd      %   .xdata      &         （亵U        H6      &   .pdata      '        � 賃        v6      '   .xdata      (        范^揢        �6      (   .pdata      )        鳶�U        �6      )   .xdata      *        @鴚`U        7      *   .pdata      +        [7躑        07      +   .voltbl     ,         飾殪U    _volmd      ,   .xdata      -         3�倎        _7      -   .pdata      .        �	o苼        �7      .   .xdata      /        �裩�        �7      /   .pdata      0        鶈唩        �7      0   .xdata      1        -鑻C�        ,8      1   .pdata      2        廾E4�        `8      2   .xdata      3        l[?�        �8      3   .pdata      4        2宪k�        �8      4   .xdata      5        k�8�        �8      5   .pdata      6        /谀�        09      6   .voltbl     7         @�    _volmd      7   .xdata      8         釫�<M        d9      8   .pdata      9         *鬰M        �9      9   .xdata      :        驫@<M        �:      :   .pdata      ;        "�bM        ;      ;   .xdata      <        Dc団M        �;      <   .pdata      =        `獤M        @<      =   .xdata      >        蜼U跰        �<      >   .pdata      ?        槜姚M        f=      ?   .xdata      @        炖ＺM        �=      @   .pdata      A        eNGaM        �>      A   .voltbl     B         灸ZM    _volmd      B   .xdata      C        圇�
Y        ?      C   .pdata      D        *輏Y        I?      D   .xdata      E  	      �#荤Y        r?      E   .xdata      F        jY        �?      F   .xdata      G         b锜Y        �?      G   .voltbl     H         趱狠Y    _volmd      H   .xdata      I         %蚘%�        �?      I   .pdata      J        嘳��        +@      J   .voltbl     K         -哥]    _volmd      K   .xdata      L         %蚘%�        Y@      L   .pdata      M        咝<�        嶡      M   .voltbl     N         賚c�    _volmd      N   .xdata      O         %蚘%�        翤      O   .pdata      P        咝<�        鬇      P   .voltbl     Q         賚c�    _volmd      Q   .xdata      R         %蚘%�        %A      R   .pdata      S        咝<�        ]A      S   .voltbl     T         賚c�    _volmd      T   .xdata      U         O鞶        擜      U   .pdata      V        頄u頠        籄      V   .voltbl     W         #3�7Q    _volmd      W   .xdata      X         %蚘%y        酇      X   .pdata      Y        嘳�y        
B      Y   .xdata      Z         （亵I        8B      Z   .pdata      [        � 買        xB      [   .xdata      \        范^揑        稡      \   .pdata      ]        鳶�I        鳥      ]   .xdata      ^        @鴚`I        9C      ^   .pdata      _        [7躀        zC      _   .voltbl     `         飾殪I    _volmd      `   .xdata      a         釫�<O        籆      a   .pdata      b         *鬰O        ?D      b   .xdata      c        驫@<O        翫      c   .pdata      d        "�bO        GE      d   .xdata      e        Dc団O        蘀      e   .pdata      f        `獤O        QF      f   .xdata      g        蜼U跲        諪      g   .pdata      h        槜姚O        [G      h   .xdata      i        炖ＺO        郍      i   .pdata      j        eNGaO        eH      j   .voltbl     k         灸ZO    _volmd      k   .xdata      l        e6嚂�        闔      l   .pdata      m        �&�        "I      m   .xdata      n        �:�?�        YI      n   .pdata      o        S7Z枦        礗      o   .xdata      p  	      � )9�        J      p   .xdata      q        籧o]�        nJ      q   .xdata      r         �[uf�        襃      r   .voltbl     s         )�3皼    _volmd      s   .xdata      t         （亵�        0K      t   .pdata      u        #1i�        zK      u   .xdata      v         %蚘%u        肒      v   .pdata      w        駷tLu        鞬      w   .xdata      x         瀫"�        L      x   .pdata      y        o炥��        uL      y   .xdata      z         �	琢        覮      z   .pdata      {        輥p�        M      {   .xdata      |         %蚘%�        HM      |   .pdata      }        咝<�        uM      }   .voltbl     ~         賚c�    _volmd      ~   .xdata               c%C劉                 .pdata      �        ]-廷        鸐      �   .xdata      �         澚)γ        TN      �   .pdata      �        謄
�        奛      �   .xdata      �         %蚘%�        縉      �   .pdata      �        咝<�        鏝      �   .voltbl     �         賚c�    _volmd      �   .xdata      �         c%C劄        O      �   .pdata      �        ]-蜑        fO      �   .xdata      �        铓朤�        絆      �   .pdata      �        D褃X�        馩      �   .xdata      �  	      � )9�        $P      �   .xdata      �        遱谸�        ZP      �   .xdata      �         �<u尉        朠      �   .xdata      �         %蚘%w        蘌      �   .pdata      �        ]鰓        騊      �   .voltbl     �         j閕Cw    _volmd      �   .xdata      �         扯r�        Q      �   .pdata      �        ヶ�=�        綫      �   .voltbl     �         j犡3�    _volmd      �   .xdata      �         �&蠧�        dR      �   .pdata      �        dp�        薘      �   .xdata      �        傶猝�        1S      �   .pdata      �        墫辯�        橲      �   .xdata      �        垰玌�        T      �   .pdata      �        瑰捎�        iT      �   .xdata      �         �&蠧�        裈      �   .pdata      �         T枨�        4U      �   .xdata      �        孓鰧�        朥      �   .pdata      �        }甕蕤        鶸      �   .xdata      �        炀縹�        ^V      �   .pdata      �        �i�        耉      �   .xdata      �         ug刉�        &W      �   .pdata      �         邫�        昗      �   .xdata      �        �:�?�        X      �   .pdata      �        |=CT�        _X      �   .xdata      �  	      � )9�        篨      �   .xdata      �  
      仓喦        Y      �   .xdata      �         �?i]�        |Y      �   .voltbl     �         y怓�    _volmd      �   .xdata      �         皖�        赮      �   .pdata      �        0Z澦        1Z      �   .xdata      �  	      � )9�        嘮      �   .xdata      �        j�        郱      �   .xdata      �         嗼l        ?[      �   .voltbl     �         �1#    _volmd      �   .xdata      �  $      
砊        榌      �   .pdata      �        q��        T\      �   .xdata      �  	      � )9�        ]      �   .xdata      �        �-p飚        蚞      �   .xdata      �         wZ?�        慯      �   .voltbl     �         d蹒�    _volmd      �   .xdata      �         3�倉        O_      �   .pdata      �        �	o苶        s_      �   .xdata      �        箱t貆        朹      �   .pdata      �        (轻趠        籣      �   .xdata      �        f}        郷      �   .pdata      �        Hmw軁        `      �   .xdata      �        �-禼}        *`      �   .pdata      �        �(A}        O`      �   .xdata      �        k�8}        t`      �   .pdata      �        莤A}        檂      �   .voltbl     �         襴L杴    _volmd      �   .xdata      �        vQ9	        綻      �   .pdata      �        �&        錪      �   .xdata      �  	      �#荤        a      �   .xdata      �        j        4a      �   .xdata      �         岝 j        ca      �   .voltbl     �         �.wZ    _volmd      �   .xdata      �         （亵W        宎      �   .pdata      �        � 賅        痑      �   .xdata      �        范^揥        補      �   .pdata      �        鳶�W        鮝      �   .xdata      �        @鴚`W        b      �   .pdata      �        [7躓        =b      �   .voltbl     �         飾殪W    _volmd      �   .xdata      �         3�們        ab      �   .pdata      �        �	o苾        塨      �   .xdata      �        �裩�        癰      �   .pdata      �        鶈唭        賐      �   .xdata      �        -鑻C�        c      �   .pdata      �        廾E4�        +c      �   .xdata      �        l[?�        Tc      �   .pdata      �        2宪k�        }c      �   .xdata      �        k�8�              �   .pdata      �        /谀�        蟘      �   .voltbl     �         @�    _volmd      �   .xdata      �        vQ9	K        鴆      �   .pdata      �        栝K        揹      �   .xdata      �  	      �#荤K        -e      �   .xdata      �        jK        蔱      �   .xdata      �         竷 nK        mf      �   .xdata      �        蚲7M[        
g      �   .pdata      �        啁鉥[        )g      �   .xdata      �  	      �#荤[        Gg      �   .xdata      �        j[        hg      �   .xdata      �         b紌[        廹      �   .xdata      �        �酑�        癵      �   .pdata      �        粖硣        詆      �   .xdata      �  	      �#荤�        鱣      �   .xdata      �        j�        h      �   .xdata      �         P�T�        Ih      �   .xdata      �         写販!        oh      �   .pdata      �        .0�!        蚳      �   .xdata      �         （亵E        *i      �   .pdata      �        � 貳        ci      �   .xdata      �        范^揈        沬      �   .pdata      �        鳶�E        読      �   .xdata      �        @鴚`E        j      �   .pdata      �        [7蹺        Ij      �   .voltbl     �         飾殪E    _volmd      �   .voltbl     �         脘輒)    _volmd      �   .xdata      �         僣�
        僯      �   .pdata      �        噖sb
        醞      �   .xdata      �         （亵        >k      �   .pdata      �        �8院        hk      �   .xdata      �         （亵	        慿      �   .pdata      �        �*^�	        籯      �   .xdata      �        巔Ar        鋕      �   .pdata      �        緥�        坙      �   .xdata      �         写販#        +m      �   .pdata      �        秘�#        弇      �   .xdata      �         写販%        騧      �   .pdata      �         鮩s%        Ln      �   .xdata      �        债�              �   .pdata      �        s�+A        Uo      �   .xdata      �        Mw2�        p      �   .xdata      �         �.e�        秔      �   .voltbl     �         -=m    _volmd      �   .xdata               债�        hq          .pdata              s�+A        r         .xdata              Mw2�        縭         .xdata               �.e�        ms         .voltbl              -=m    _volmd         .xdata               （亵G        t         .pdata              � 貵        Vt         .xdata              范^揋        恡         .pdata              鳶�G        蘴         .xdata      	        @鴚`G        u      	   .pdata      
        [7蹽        Du      
   .voltbl              飾殪G    _volmd         .xdata              债�        �u         .pdata      
        s�+A        "v      
   .xdata              Mw2�        胿         .xdata               �.e�        gw         .voltbl              -=m    _volmd         .xdata               %蚘%        x         .pdata              j��        Ix         .xdata               %蚘%        唜         .pdata              AT        聏         .xdata               %蚘%        齲         .pdata              僻螔        9y         .xdata               （亵m        ty         .pdata               ~        穣         .xdata               （亵q        鵼         .pdata               ~        ?z         .xdata               （亵s        剒         .pdata               ~        舲         .xdata               （亵o        {         .pdata               ~        D{         .xdata               （亵k        倇         .pdata                ~        縶          .xdata      !         （亵i        鹻      !   .pdata      "         ~        <|      "   .xdata      #         （亵g        ||      #   .pdata      $         ~        箌      $   .xdata      %         �9�        鮸      %   .pdata      &        礝
        R}      &   .rdata      '                     畗     '   .rdata      (         �;�         舽      (   .rdata      )                     靰     )   .rdata      *                     ~     *   .rdata      +         �)         %~      +   .xdata$x    ,                     Q~      ,   .xdata$x    -        虼�)         s~      -   .data$r     .  /      嶼�         杶      .   .xdata$x    /  $      4��         粇      /   .data$r     0  $      鎊=               0   .xdata$x    1  $      銸E�         *      1   .data$r     2  $      騏糡         i      2   .xdata$x    3  $      4��         �      3       �               �  @       .rdata      4         燺渾         �      4   .data       5          烀�          *�      5       ^�     5   .rdata      6                     厐     6   .rdata      7                          7   .rdata      8                     莯     8   .rdata      9  8                   鎬     9   .rdata      :  8                   �     :   .rdata      ;  8                   -�     ;   .rdata      <  P   
                U�     <   .rdata      =  P   
                q�     =   .rdata      >  8                   媮     >   .rdata      ?  8                   ▉     ?   .rdata      @  8                   纴     @   .rdata      A                     謥     A   .rdata      B                     陙     B   .rdata      C                     �     C   .rdata      D         �0抺         �      D   .rdata      E  
       傕埏         7�      E   .rdata      F  	       1E橚         W�      F   .rdata      G         奞攧         r�      G   .rdata      H         �0繙         墏      H   .rdata      I         �.膔         畟      I   .rdata      J         20         詡      J   .rdata      K         y鲒         鼈      K   .rdata      L         灿         �      L   .rdata      M  
       �〇�         ?�      M   .rdata      N         W|         _�      N   .rdata      O         +赻�         唭      O   .rdata      P         �(涄         瘍      P   .rdata      Q         nXXS         袃      Q   .rdata      R         加A         鮾      R   .rdata      S  
       鏱t         �      S   .rdata      T         +蜟         :�      T   .rdata      U         鉏眔         _�      U   .rdata      V         瑚鱩         亜      V   .rdata      W         j岒"               W   .rdata      X         譢彅         膭      X   .rdata      Y         m(�         顒      Y   .rdata      Z         嶬舅         �      Z   .rdata      [         w#Z         :�      [   .rdata      \         躞Gn         i�      \   .rdata      ]  (                   媴     ]   .rdata      ^  (                   緟     ^   .rdata      _  (                   魠     _   .rdata      `  (                   %�     `   .rdata      a  (                   T�     a   .rdata      b  (                   亞     b   .rdata      c  (                   矄     c   .data$r     d  2      詂刑         邌      d   .data$r     e  %      瞐5�         �      e   .data$r     f  #      §!b         "�      f   .data$r     g  )      渞H�         ;�      g   .data$r     h  '      樆O         Z�      h   .rdata$r    i  $      'e%�         w�      i   .rdata$r    j        �          弴      j   .rdata$r    k                           k   .rdata$r    l  $      Gv�:         粐      l   .rdata$r    m  $      'e%�         趪      m   .rdata$r    n        }%B         驀      n   .rdata$r    o                     �      o   .rdata$r    p  $      `         �      p   .rdata$r    q  $      'e%�         =�      q   .rdata$r    r        �弾         `�      r   .rdata$r    s                     亪      s   .rdata$r    t  $      H衡�               t   .data$rs    u  *      8V綊         虉      u   .rdata$r    v        �          靾      v   .rdata$r    w                     �      w   .rdata$r    x  $      Gv�:         $�      x   .rdata$r    y  $      'e%�         I�      y   .data$rs    z  ,      ‰ �         i�      z   .rdata$r    {        �          媺      {   .rdata$r    |                           |   .rdata$r    }  $      Gv�:         菈      }   .rdata$r    ~  $      'e%�         顗      ~   .data$rs      0      4
輢         �         .rdata$r    �        �          8�      �   .rdata$r    �                     Z�      �   .rdata$r    �  $      Gv�:         |�      �   .rdata$r    �  $      'e%�               �   .data$rs    �  ,      蹳朣         菉      �   .rdata$r    �        �          閵      �   .rdata$r    �                     �      �   .rdata$r    �  $      Gv�:         %�      �   .rdata$r    �  $      'e%�         L�      �   .rdata$r    �        �          r�      �   .rdata$r    �                     枊      �   .rdata$r    �  $      Gv�:         簨      �   .rdata$r    �  $      'e%�         鐙      �   .data$rs    �  /      %�)         
�      �   .rdata$r    �        }%B         /�      �   .rdata$r    �                     P�      �   .rdata$r    �  $      `         q�      �   .rdata$r    �  $      'e%�         泴      �   .data$rs    �  5      &覜_         膶      �   .rdata$r    �        �弾         飳      �   .rdata$r    �                     �      �   .rdata$r    �  $      H衡�         =�      �   .rdata$r    �  $      'e%�         m�      �   .rdata$r    �        }%B         妽      �   .rdata$r    �                           �   .rdata$r    �  $      `         缻      �   .rdata$r    �  $      'e%�         鋶      �   .rdata$r    �        �弾         ��      �   .rdata$r    �                     �      �   .rdata$r    �  $      H衡�         1�      �   .rdata$r    �  $      'e%�         S�      �   .data$rs    �  *      倎侁         q�      �   .rdata$r    �        �J�         憥      �   .rdata$r    �  $                   瓗      �   .rdata$r    �  $      o咔b         蓭      �   .rdata$r    �  $      'e%�         顜      �   .rdata$r    �        }%B         �      �   .rdata$r    �                     �      �   .rdata$r    �  $      `         5�      �   .rdata$r    �  $      'e%�         U�      �   .rdata$r    �        }%B         l�      �   .rdata$r    �                     亸      �   .rdata$r    �  $      `         枏      �   .rdata$r    �  $      'e%�         磸      �   .data$rs    �  !      椕
         蓮      �   .rdata$r    �        }%B         鄰      �   .rdata$r    �                     髲      �   .rdata$r    �  $      `         �      �   .rdata$r    �  $      'e%�         "�      �   .data$rs    �  %      �         ;�      �   .rdata$r    �        }%B         V�      �   .rdata$r    �                     m�      �   .rdata$r    �  $      `         剱      �   .rdata$r    �  $      'e%�               �   .data$rs    �  !      a)�7         箰      �   .rdata$r    �        }%B         袗      �   .rdata$r    �                     銗      �   .rdata$r    �  $      `         鰫      �   .rdata$r    �  $      'e%�         �      �   .data$rs    �  @      泞�         F�      �   .rdata$r    �        }%B         |�      �   .rdata$r    �                     畱      �   .rdata$r    �  $      `         鄳      �   .rdata$r    �  $      'e%�         �      �   .data$rs    �  C      猻菎         R�      �   .rdata$r    �        }%B         嫆      �   .rdata$r    �                     罀      �   .rdata$r    �  $      `         鯍      �   .rdata$r    �  $      'e%�         3�      �   .data$rs    �  >      5G         e�      �   .rdata$r    �        }%B         檽      �   .rdata$r    �                     蓳      �   .rdata$r    �  $      `         鶕      �   .rdata$r    �  $      'e%�         2�      �   .data$rs    �  <      �$�         b�      �   .rdata$r    �        }%B         敂      �   .rdata$r    �                     聰      �   .rdata$r    �  $      `         饠      �   .rdata$r    �  $      'e%�         '�      �   .data$rs    �  :      l谙         U�      �   .rdata$r    �        }%B         厱      �   .rdata$r    �                     睍      �   .rdata$r    �  $      `         輹      �   .rdata$r    �  $      'e%�         �      �   .data$rs    �  >      +0 �         D�      �   .rdata$r    �        }%B         x�      �   .rdata$r    �                           �   .rdata$r    �  $      `         貣      �   .rdata$r    �  $      'e%�         �      �   .data$rs    �  :      ?庂�         ?�      �   .rdata$r    �        }%B         o�      �   .rdata$r    �                     洍      �   .rdata$r    �  $      `         菞      �   .rdata      �         �;�         鼦      �   .rdata      �         ǜ8�         �      �       �           _fltused         .debug$S    �  <          <   .debug$S    �  0          A   .debug$S    �  D          7   .debug$S    �  @          8   .debug$S    �  4          '   .debug$S    �  4          B   .debug$S    �  4          )   .debug$S    �  @          *   .debug$S    �  0          C   .debug$S    �  8          =   .debug$S    �  <          >   .debug$S    �  4          ?   .debug$S    �  4          @   .debug$S    �  @          6   .debug$S    �  D          9   .debug$S    �  L          ]   .debug$S    �  P          ^   .debug$S    �  L          _   .debug$S    �  H          `   .debug$S    �  H          a   .debug$S    �  L          b   .debug$S    �  H          c   .debug$S    �  @          :   .debug$S    �  H          ;   .chks64     �  �                .�  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z _purecall ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@$$QEAV01@@Z ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ??1Material@engine@donut@@UEAA@XZ ??0Material@engine@donut@@QEAA@XZ ??_GMaterial@engine@donut@@UEAAPEAXI@Z ??_EMaterial@engine@donut@@UEAAPEAXI@Z ??1?$shared_ptr@UMaterial@engine@donut@@@std@@QEAA@XZ ??1MeshGeometry@engine@donut@@UEAA@XZ ??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z ??_EMeshGeometry@engine@donut@@UEAAPEAXI@Z ??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ ??1MeshInfo@engine@donut@@UEAA@XZ ??_GMeshInfo@engine@donut@@UEAAPEAXI@Z ??_EMeshInfo@engine@donut@@UEAAPEAXI@Z ??1SceneGraphLeaf@engine@donut@@UEAA@XZ ?GetLocalBoundingBox@SceneGraphLeaf@engine@donut@@UEAA?AU?$box@M$02@math@3@XZ ?GetContentFlags@SceneGraphLeaf@engine@donut@@UEBA?AW4SceneContentFlags@23@XZ ?Load@SceneGraphLeaf@engine@donut@@UEAAXAEBVValue@Json@@@Z ?SetProperty@SceneGraphLeaf@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ??_GSceneGraphLeaf@engine@donut@@UEAAPEAXI@Z ??_ESceneGraphLeaf@engine@donut@@UEAAPEAXI@Z ?GetContentFlags@SceneCamera@engine@donut@@UEBA?AW4SceneContentFlags@23@XZ ??_GSceneCamera@engine@donut@@UEAAPEAXI@Z ??_ESceneCamera@engine@donut@@UEAAPEAXI@Z ?Clone@PerspectiveCamera@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?Load@PerspectiveCamera@engine@donut@@UEAAXAEBVValue@Json@@@Z ?SetProperty@PerspectiveCamera@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ??_GPerspectiveCamera@engine@donut@@UEAAPEAXI@Z ??_EPerspectiveCamera@engine@donut@@UEAAPEAXI@Z ?GetContentFlags@Light@engine@donut@@UEBA?AW4SceneContentFlags@23@XZ ?FillLightConstants@Light@engine@donut@@UEBAXAEAULightConstants@@@Z ?Store@Light@engine@donut@@UEBAXAEAVValue@Json@@@Z ?SetProperty@Light@engine@donut@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@3@@Z ??1Light@engine@donut@@UEAA@XZ ??_GLight@engine@donut@@UEAAPEAXI@Z ??_ELight@engine@donut@@UEAAPEAXI@Z ??1?$shared_ptr@VSceneGraph@engine@donut@@@std@@QEAA@XZ ??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ ?CreateLeaf@SceneTypeFactory@engine@donut@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@5@@Z ?LoadWithExecutor@Scene@engine@donut@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z ?Load@EnvironmentLight@@UEAAXAEBVValue@Json@@@Z ?GetLightType@EnvironmentLight@@UEBAHXZ ?Clone@EnvironmentLight@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?FillLightConstants@EnvironmentLight@@UEBAXAEAULightConstants@@@Z ?SetProperty@EnvironmentLight@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@donut@@@Z ??_GEnvironmentLight@@UEAAPEAXI@Z ??_EEnvironmentLight@@UEAAPEAXI@Z ?Clone@PerspectiveCameraEx@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?Load@PerspectiveCameraEx@@UEAAXAEBVValue@Json@@@Z ?SetProperty@PerspectiveCameraEx@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBU?$vector@M$03@math@donut@@@Z ??_GPerspectiveCameraEx@@UEAAPEAXI@Z ??_EPerspectiveCameraEx@@UEAAPEAXI@Z ?Clone@SampleSettings@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?Load@SampleSettings@@UEAAXAEBVValue@Json@@@Z ??_GSampleSettings@@UEAAPEAXI@Z ??_ESampleSettings@@UEAAPEAXI@Z ?Clone@GameSettings@@EEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ ?Load@GameSettings@@EEAAXAEBVValue@Json@@@Z ??_GGameSettings@@UEAAPEAXI@Z ??_EGameSettings@@UEAAPEAXI@Z ?CreateLeaf@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@@Z ?CreateMaterial@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMaterial@engine@donut@@@std@@XZ ?CreateMesh@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshInfo@engine@donut@@@std@@XZ ?CreateMeshGeometry@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshGeometry@engine@donut@@@std@@XZ ?LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z ?ProcessNodesRecursive@ExtendedScene@@AEAAXPEAVSceneGraphNode@engine@donut@@@Z ?FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z ?isNull@Value@Json@@QEBA_NXZ ??AValue@Json@@QEBAAEBV01@PEBD@Z ??$Read@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@json@donut@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVValue@Json@@AEBV23@@Z ??$Read@H@json@donut@@YAHAEBVValue@Json@@AEBH@Z ??$Read@_N@json@donut@@YA_NAEBVValue@Json@@AEB_N@Z ??$Read@M@json@donut@@YAMAEBVValue@Json@@AEBM@Z ??$Read@U?$vector@M$02@math@donut@@@json@donut@@YA?AU?$vector@M$02@math@1@AEBVValue@Json@@AEBU231@@Z ?writeString@Json@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBVFactory@StreamWriter@1@AEBVValue@1@@Z ??0StreamWriterBuilder@Json@@QEAA@XZ ??1StreamWriterBuilder@Json@@UEAA@XZ ??_GMaterialEx@@UEAAPEAXI@Z ??_EMaterialEx@@UEAAPEAXI@Z ??_GMeshDebugData@@QEAAPEAXI@Z ??1MeshGeometryEx@@UEAA@XZ ??_GMeshGeometryEx@@UEAAPEAXI@Z ??_EMeshGeometryEx@@UEAAPEAXI@Z ??1?$vector@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ ??1MeshInfoEx@@UEAA@XZ ??_GMeshInfoEx@@UEAAPEAXI@Z ??_EMeshInfoEx@@UEAAPEAXI@Z ?PostMaterialLoad@LocalConfig@@SAXAEAUMaterial@engine@donut@@@Z ??$make_shared@VEnvironmentLight@@$$V@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@XZ ??1?$shared_ptr@VEnvironmentLight@@@std@@QEAA@XZ ??$static_pointer_cast@VSceneGraphLeaf@engine@donut@@VEnvironmentLight@@@std@@YA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@AEBV?$shared_ptr@VEnvironmentLight@@@0@@Z ??$?5U?$vector@M$02@math@donut@@@@YAXAEBVValue@Json@@AEAU?$vector@M$02@math@donut@@@Z ??$?5H@@YAXAEBVValue@Json@@AEAH@Z ??$?5M@@YAXAEBVValue@Json@@AEAM@Z ??$?5V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@@YAXAEBVValue@Json@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ??$make_shared@VPerspectiveCameraEx@@$$V@std@@YA?AV?$shared_ptr@VPerspectiveCameraEx@@@0@XZ ??$make_shared@VSampleSettings@@$$V@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@XZ ??$static_pointer_cast@UMaterial@engine@donut@@UMaterialEx@@@std@@YA?AV?$shared_ptr@UMaterial@engine@donut@@@0@$$QEAV?$shared_ptr@UMaterialEx@@@0@@Z ??$?8VSceneGraphLeaf@engine@donut@@@std@@YA_NAEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@$$T@Z ??$dynamic_pointer_cast@VSampleSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z ??$?8VSampleSettings@@@std@@YA_NAEBV?$shared_ptr@VSampleSettings@@@0@$$T@Z ??$dynamic_pointer_cast@VGameSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VGameSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z ??$?8VGameSettings@@@std@@YA_NAEBV?$shared_ptr@VGameSettings@@@0@$$T@Z ??1?$shared_ptr@VLight@engine@donut@@@std@@QEAA@XZ ??$dynamic_pointer_cast@VEnvironmentLight@@VLight@engine@donut@@@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@AEBV?$shared_ptr@VLight@engine@donut@@@0@@Z ??$?5_N@@YAXAEBVValue@Json@@AEAV?$optional@_N@std@@@Z ??$?5M@@YAXAEBVValue@Json@@AEAV?$optional@M@std@@@Z ??$?5H@@YAXAEBVValue@Json@@AEAV?$optional@H@std@@@Z ??1?$_Ref_count_obj2@VEnvironmentLight@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VEnvironmentLight@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VEnvironmentLight@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VEnvironmentLight@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VEnvironmentLight@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@VSampleSettings@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VSampleSettings@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VSampleSettings@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VSampleSettings@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VSampleSettings@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@VGameSettings@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VGameSettings@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VGameSettings@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VGameSettings@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VGameSettings@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@UMeshInfoEx@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@UMeshInfoEx@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@UMeshInfoEx@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@UMeshInfoEx@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@UMeshInfoEx@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@UMeshGeometryEx@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@UMeshGeometryEx@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@UMeshGeometryEx@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@UMeshGeometryEx@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@UMeshGeometryEx@@@std@@UEAAPEAXI@Z ??1?$_Ref_count_obj2@UMaterialEx@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@UMaterialEx@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@UMaterialEx@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@UMaterialEx@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@UMaterialEx@@@std@@UEAAPEAXI@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?dtor$0@?0??Clone@EnvironmentLight@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ@4HA ?dtor$0@?0??FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z@4HA ?dtor$0@?0??Load@GameSettings@@EEAAXAEBVValue@Json@@@Z@4HA ?dtor$0@?0??LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z@4HA ?dtor$1@?0??FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z@4HA ?dtor$1@?0??LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __RTDynamicCast __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@$$QEAV01@@Z $pdata$??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@$$QEAV01@@Z $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??1Material@engine@donut@@UEAA@XZ $pdata$??1Material@engine@donut@@UEAA@XZ $cppxdata$??1Material@engine@donut@@UEAA@XZ $stateUnwindMap$??1Material@engine@donut@@UEAA@XZ $ip2state$??1Material@engine@donut@@UEAA@XZ $unwind$??_GMaterial@engine@donut@@UEAAPEAXI@Z $pdata$??_GMaterial@engine@donut@@UEAAPEAXI@Z $unwind$??1?$shared_ptr@UMaterial@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@UMaterial@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@UMaterial@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@UMaterial@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@UMaterial@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@UMaterial@engine@donut@@@std@@QEAA@XZ $unwind$??1MeshGeometry@engine@donut@@UEAA@XZ $pdata$??1MeshGeometry@engine@donut@@UEAA@XZ $chain$0$??1MeshGeometry@engine@donut@@UEAA@XZ $pdata$0$??1MeshGeometry@engine@donut@@UEAA@XZ $chain$1$??1MeshGeometry@engine@donut@@UEAA@XZ $pdata$1$??1MeshGeometry@engine@donut@@UEAA@XZ $unwind$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $pdata$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $chain$1$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $pdata$1$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $chain$2$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $pdata$2$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $chain$3$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $pdata$3$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $chain$4$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $pdata$4$??_GMeshGeometry@engine@donut@@UEAAPEAXI@Z $unwind$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$0$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$0$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$1$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$1$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$2$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$2$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$3$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$3$??1?$vector@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@V?$allocator@V?$shared_ptr@UMeshGeometry@engine@donut@@@std@@@2@@std@@QEAA@XZ $unwind$??1MeshInfo@engine@donut@@UEAA@XZ $pdata$??1MeshInfo@engine@donut@@UEAA@XZ $cppxdata$??1MeshInfo@engine@donut@@UEAA@XZ $stateUnwindMap$??1MeshInfo@engine@donut@@UEAA@XZ $ip2state$??1MeshInfo@engine@donut@@UEAA@XZ $unwind$??_GMeshInfo@engine@donut@@UEAAPEAXI@Z $pdata$??_GMeshInfo@engine@donut@@UEAAPEAXI@Z $unwind$??_GSceneGraphLeaf@engine@donut@@UEAAPEAXI@Z $pdata$??_GSceneGraphLeaf@engine@donut@@UEAAPEAXI@Z $unwind$??_GSceneCamera@engine@donut@@UEAAPEAXI@Z $pdata$??_GSceneCamera@engine@donut@@UEAAPEAXI@Z $unwind$??_GPerspectiveCamera@engine@donut@@UEAAPEAXI@Z $pdata$??_GPerspectiveCamera@engine@donut@@UEAAPEAXI@Z $unwind$??1Light@engine@donut@@UEAA@XZ $pdata$??1Light@engine@donut@@UEAA@XZ $unwind$??_GLight@engine@donut@@UEAAPEAXI@Z $pdata$??_GLight@engine@donut@@UEAAPEAXI@Z $unwind$??1?$shared_ptr@VSceneGraph@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VSceneGraph@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VSceneGraph@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VSceneGraph@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VSceneGraph@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VSceneGraph@engine@donut@@@std@@QEAA@XZ $unwind$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$0$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$0$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$1$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$1$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$2$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$2$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $chain$3$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $pdata$3$??1?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@std@@QEAA@XZ $unwind$?Load@EnvironmentLight@@UEAAXAEBVValue@Json@@@Z $pdata$?Load@EnvironmentLight@@UEAAXAEBVValue@Json@@@Z $unwind$?Clone@EnvironmentLight@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $pdata$?Clone@EnvironmentLight@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $cppxdata$?Clone@EnvironmentLight@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $stateUnwindMap$?Clone@EnvironmentLight@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $ip2state$?Clone@EnvironmentLight@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $unwind$?FillLightConstants@EnvironmentLight@@UEBAXAEAULightConstants@@@Z $pdata$?FillLightConstants@EnvironmentLight@@UEBAXAEAULightConstants@@@Z $unwind$??_GEnvironmentLight@@UEAAPEAXI@Z $pdata$??_GEnvironmentLight@@UEAAPEAXI@Z $unwind$?Clone@PerspectiveCameraEx@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $pdata$?Clone@PerspectiveCameraEx@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $unwind$?Load@PerspectiveCameraEx@@UEAAXAEBVValue@Json@@@Z $pdata$?Load@PerspectiveCameraEx@@UEAAXAEBVValue@Json@@@Z $unwind$??_GPerspectiveCameraEx@@UEAAPEAXI@Z $pdata$??_GPerspectiveCameraEx@@UEAAPEAXI@Z $unwind$?Clone@SampleSettings@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $pdata$?Clone@SampleSettings@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $unwind$?Load@SampleSettings@@UEAAXAEBVValue@Json@@@Z $pdata$?Load@SampleSettings@@UEAAXAEBVValue@Json@@@Z $unwind$??_GSampleSettings@@UEAAPEAXI@Z $pdata$??_GSampleSettings@@UEAAPEAXI@Z $unwind$?Clone@GameSettings@@EEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $pdata$?Clone@GameSettings@@EEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@XZ $unwind$?Load@GameSettings@@EEAAXAEBVValue@Json@@@Z $pdata$?Load@GameSettings@@EEAAXAEBVValue@Json@@@Z $cppxdata$?Load@GameSettings@@EEAAXAEBVValue@Json@@@Z $stateUnwindMap$?Load@GameSettings@@EEAAXAEBVValue@Json@@@Z $ip2state$?Load@GameSettings@@EEAAXAEBVValue@Json@@@Z $unwind$??_GGameSettings@@UEAAPEAXI@Z $pdata$??_GGameSettings@@UEAAPEAXI@Z $unwind$?CreateLeaf@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@@Z $pdata$?CreateLeaf@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@@Z $unwind$?CreateMaterial@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMaterial@engine@donut@@@std@@XZ $pdata$?CreateMaterial@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMaterial@engine@donut@@@std@@XZ $chain$0$?CreateMaterial@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMaterial@engine@donut@@@std@@XZ $pdata$0$?CreateMaterial@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMaterial@engine@donut@@@std@@XZ $chain$1$?CreateMaterial@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMaterial@engine@donut@@@std@@XZ $pdata$1$?CreateMaterial@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMaterial@engine@donut@@@std@@XZ $unwind$?CreateMesh@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshInfo@engine@donut@@@std@@XZ $pdata$?CreateMesh@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshInfo@engine@donut@@@std@@XZ $chain$0$?CreateMesh@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshInfo@engine@donut@@@std@@XZ $pdata$0$?CreateMesh@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshInfo@engine@donut@@@std@@XZ $chain$1$?CreateMesh@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshInfo@engine@donut@@@std@@XZ $pdata$1$?CreateMesh@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshInfo@engine@donut@@@std@@XZ $unwind$?CreateMeshGeometry@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshGeometry@engine@donut@@@std@@XZ $pdata$?CreateMeshGeometry@ExtendedSceneTypeFactory@@UEAA?AV?$shared_ptr@UMeshGeometry@engine@donut@@@std@@XZ $unwind$?LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z $pdata$?LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z $cppxdata$?LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z $stateUnwindMap$?LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z $ip2state$?LoadWithExecutor@ExtendedScene@@UEAA_NAEBVpath@filesystem@std@@PEAVExecutor@tf@@@Z $unwind$?ProcessNodesRecursive@ExtendedScene@@AEAAXPEAVSceneGraphNode@engine@donut@@@Z $pdata$?ProcessNodesRecursive@ExtendedScene@@AEAAXPEAVSceneGraphNode@engine@donut@@@Z $cppxdata$?ProcessNodesRecursive@ExtendedScene@@AEAAXPEAVSceneGraphNode@engine@donut@@@Z $stateUnwindMap$?ProcessNodesRecursive@ExtendedScene@@AEAAXPEAVSceneGraphNode@engine@donut@@@Z $ip2state$?ProcessNodesRecursive@ExtendedScene@@AEAAXPEAVSceneGraphNode@engine@donut@@@Z $unwind$?FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z $pdata$?FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z $cppxdata$?FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z $stateUnwindMap$?FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z $ip2state$?FindEnvironmentLight@@YA?AV?$shared_ptr@VEnvironmentLight@@@std@@V?$vector@V?$shared_ptr@VLight@engine@donut@@@std@@V?$allocator@V?$shared_ptr@VLight@engine@donut@@@std@@@2@@2@@Z $unwind$??_GMaterialEx@@UEAAPEAXI@Z $pdata$??_GMaterialEx@@UEAAPEAXI@Z $chain$1$??_GMaterialEx@@UEAAPEAXI@Z $pdata$1$??_GMaterialEx@@UEAAPEAXI@Z $chain$2$??_GMaterialEx@@UEAAPEAXI@Z $pdata$2$??_GMaterialEx@@UEAAPEAXI@Z $chain$3$??_GMaterialEx@@UEAAPEAXI@Z $pdata$3$??_GMaterialEx@@UEAAPEAXI@Z $chain$4$??_GMaterialEx@@UEAAPEAXI@Z $pdata$4$??_GMaterialEx@@UEAAPEAXI@Z $unwind$??_GMeshDebugData@@QEAAPEAXI@Z $pdata$??_GMeshDebugData@@QEAAPEAXI@Z $cppxdata$??_GMeshDebugData@@QEAAPEAXI@Z $stateUnwindMap$??_GMeshDebugData@@QEAAPEAXI@Z $ip2state$??_GMeshDebugData@@QEAAPEAXI@Z $unwind$??1MeshGeometryEx@@UEAA@XZ $pdata$??1MeshGeometryEx@@UEAA@XZ $chain$0$??1MeshGeometryEx@@UEAA@XZ $pdata$0$??1MeshGeometryEx@@UEAA@XZ $chain$1$??1MeshGeometryEx@@UEAA@XZ $pdata$1$??1MeshGeometryEx@@UEAA@XZ $unwind$??_GMeshGeometryEx@@UEAAPEAXI@Z $pdata$??_GMeshGeometryEx@@UEAAPEAXI@Z $chain$1$??_GMeshGeometryEx@@UEAAPEAXI@Z $pdata$1$??_GMeshGeometryEx@@UEAAPEAXI@Z $chain$2$??_GMeshGeometryEx@@UEAAPEAXI@Z $pdata$2$??_GMeshGeometryEx@@UEAAPEAXI@Z $chain$3$??_GMeshGeometryEx@@UEAAPEAXI@Z $pdata$3$??_GMeshGeometryEx@@UEAAPEAXI@Z $chain$4$??_GMeshGeometryEx@@UEAAPEAXI@Z $pdata$4$??_GMeshGeometryEx@@UEAAPEAXI@Z $unwind$??1?$vector@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $cppxdata$??1?$vector@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$vector@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $ip2state$??1?$vector@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VIOpacityMicromap@rt@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $unwind$??1MeshInfoEx@@UEAA@XZ $pdata$??1MeshInfoEx@@UEAA@XZ $cppxdata$??1MeshInfoEx@@UEAA@XZ $stateUnwindMap$??1MeshInfoEx@@UEAA@XZ $ip2state$??1MeshInfoEx@@UEAA@XZ $unwind$??_GMeshInfoEx@@UEAAPEAXI@Z $pdata$??_GMeshInfoEx@@UEAAPEAXI@Z $cppxdata$??_GMeshInfoEx@@UEAAPEAXI@Z $stateUnwindMap$??_GMeshInfoEx@@UEAAPEAXI@Z $ip2state$??_GMeshInfoEx@@UEAAPEAXI@Z $unwind$??$make_shared@VEnvironmentLight@@$$V@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@XZ $pdata$??$make_shared@VEnvironmentLight@@$$V@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@XZ $unwind$??1?$shared_ptr@VEnvironmentLight@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VEnvironmentLight@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VEnvironmentLight@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VEnvironmentLight@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VEnvironmentLight@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VEnvironmentLight@@@std@@QEAA@XZ $unwind$??$?5U?$vector@M$02@math@donut@@@@YAXAEBVValue@Json@@AEAU?$vector@M$02@math@donut@@@Z $pdata$??$?5U?$vector@M$02@math@donut@@@@YAXAEBVValue@Json@@AEAU?$vector@M$02@math@donut@@@Z $unwind$??$?5H@@YAXAEBVValue@Json@@AEAH@Z $pdata$??$?5H@@YAXAEBVValue@Json@@AEAH@Z $unwind$??$?5M@@YAXAEBVValue@Json@@AEAM@Z $pdata$??$?5M@@YAXAEBVValue@Json@@AEAM@Z $unwind$??$?5V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@@YAXAEBVValue@Json@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z $pdata$??$?5V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@@YAXAEBVValue@Json@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z $unwind$??$make_shared@VPerspectiveCameraEx@@$$V@std@@YA?AV?$shared_ptr@VPerspectiveCameraEx@@@0@XZ $pdata$??$make_shared@VPerspectiveCameraEx@@$$V@std@@YA?AV?$shared_ptr@VPerspectiveCameraEx@@@0@XZ $unwind$??$make_shared@VSampleSettings@@$$V@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@XZ $pdata$??$make_shared@VSampleSettings@@$$V@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@XZ $unwind$??$dynamic_pointer_cast@VSampleSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $pdata$??$dynamic_pointer_cast@VSampleSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $cppxdata$??$dynamic_pointer_cast@VSampleSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $ip2state$??$dynamic_pointer_cast@VSampleSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VSampleSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $unwind$??$dynamic_pointer_cast@VGameSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VGameSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $pdata$??$dynamic_pointer_cast@VGameSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VGameSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $cppxdata$??$dynamic_pointer_cast@VGameSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VGameSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $ip2state$??$dynamic_pointer_cast@VGameSettings@@VSceneGraphLeaf@engine@donut@@@std@@YA?AV?$shared_ptr@VGameSettings@@@0@AEBV?$shared_ptr@VSceneGraphLeaf@engine@donut@@@0@@Z $unwind$??1?$shared_ptr@VLight@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VLight@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VLight@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VLight@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VLight@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VLight@engine@donut@@@std@@QEAA@XZ $unwind$??$dynamic_pointer_cast@VEnvironmentLight@@VLight@engine@donut@@@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@AEBV?$shared_ptr@VLight@engine@donut@@@0@@Z $pdata$??$dynamic_pointer_cast@VEnvironmentLight@@VLight@engine@donut@@@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@AEBV?$shared_ptr@VLight@engine@donut@@@0@@Z $cppxdata$??$dynamic_pointer_cast@VEnvironmentLight@@VLight@engine@donut@@@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@AEBV?$shared_ptr@VLight@engine@donut@@@0@@Z $ip2state$??$dynamic_pointer_cast@VEnvironmentLight@@VLight@engine@donut@@@std@@YA?AV?$shared_ptr@VEnvironmentLight@@@0@AEBV?$shared_ptr@VLight@engine@donut@@@0@@Z $unwind$??$?5_N@@YAXAEBVValue@Json@@AEAV?$optional@_N@std@@@Z $pdata$??$?5_N@@YAXAEBVValue@Json@@AEAV?$optional@_N@std@@@Z $unwind$??$?5M@@YAXAEBVValue@Json@@AEAV?$optional@M@std@@@Z $pdata$??$?5M@@YAXAEBVValue@Json@@AEAV?$optional@M@std@@@Z $unwind$??$?5H@@YAXAEBVValue@Json@@AEAV?$optional@H@std@@@Z $pdata$??$?5H@@YAXAEBVValue@Json@@AEAV?$optional@H@std@@@Z $unwind$??_G?$_Ref_count_obj2@VEnvironmentLight@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VEnvironmentLight@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@VSampleSettings@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VSampleSettings@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@VGameSettings@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VGameSettings@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@UMeshInfoEx@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@UMeshInfoEx@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@UMeshGeometryEx@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@UMeshGeometryEx@@@std@@UEAAPEAXI@Z $unwind$??_G?$_Ref_count_obj2@UMaterialEx@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@UMaterialEx@@@std@@UEAAPEAXI@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ?white@colors@math@donut@@3U?$vector@M$02@23@B ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7Material@engine@donut@@6B@ ??_7MeshGeometry@engine@donut@@6B@ ??_7MeshInfo@engine@donut@@6B@ ??_7SceneGraphLeaf@engine@donut@@6B@ ??_7SceneCamera@engine@donut@@6B@ ??_7PerspectiveCamera@engine@donut@@6B@ ??_7Light@engine@donut@@6B@ ??_7EnvironmentLight@@6B@ ??_7PerspectiveCameraEx@@6B@ ??_7SampleSettings@@6B@ ??_7GameSettings@@6B@ ??_7MaterialEx@@6B@ ??_7MeshGeometryEx@@6B@ ??_7MeshInfoEx@@6B@ ??_C@_0O@FFNGLGKH@radianceScale@ ??_C@_0N@BPFAFJPP@textureIndex@ ??_C@_08OHGOKOGA@rotation@ ??_C@_04LNEJFJGI@path@ ??_C@_0BB@KKKPDOMI@EnvironmentLight@ ??_C@_0BC@OKCABOFC@PerspectiveCamera@ ??_C@_0BE@ODDFFEEA@PerspectiveCameraEx@ ??_C@_0O@PELCJDEB@MaterialPatch@ ??_C@_0P@PLJOOGAM@SampleSettings@ ??_C@_0N@HCBNBAPN@GameSettings@ ??_C@_0BD@CKPDEKMH@enableAutoExposure@ ??_C@_0BF@FHJLOAFP@exposureCompensation@ ??_C@_0O@PENPKOME@exposureValue@ ??_C@_0BB@GFEIFGCM@exposureValueMin@ ??_C@_0BB@HHMDLCED@exposureValueMax@ ??_C@_0N@IEJCNLJK@realtimeMode@ ??_C@_0BB@HFNOCFFH@enableAnimations@ ??_C@_0P@EHJNLFPL@enableReSTIRDI@ ??_C@_0P@EFNLALKC@enableReSTIRGI@ ??_C@_0P@KNOHAHC@startingCamera@ ??_C@_0BG@MDFJBCMI@realtimeFireflyFilter@ ??_C@_0L@HCFAEMHO@maxBounces@ ??_C@_0BK@JLKNAKED@realtimeMaxDiffuseBounces@ ??_C@_0BL@LPKGNKFG@referenceMaxDiffuseBounces@ ??_C@_0P@EGGLAPON@textureMIPBias@ ??_7?$_Ref_count_obj2@VEnvironmentLight@@@std@@6B@ ??_7?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@6B@ ??_7?$_Ref_count_obj2@VSampleSettings@@@std@@6B@ ??_7?$_Ref_count_obj2@VGameSettings@@@std@@6B@ ??_7?$_Ref_count_obj2@UMeshInfoEx@@@std@@6B@ ??_7?$_Ref_count_obj2@UMeshGeometryEx@@@std@@6B@ ??_7?$_Ref_count_obj2@UMaterialEx@@@std@@6B@ ??_R0?AVSceneGraphLeaf@engine@donut@@@8 ??_R0?AVSampleSettings@@@8 ??_R0?AVGameSettings@@@8 ??_R0?AVLight@engine@donut@@@8 ??_R0?AVEnvironmentLight@@@8 ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4Material@engine@donut@@6B@ ??_R0?AUMaterial@engine@donut@@@8 ??_R3Material@engine@donut@@8 ??_R2Material@engine@donut@@8 ??_R1A@?0A@EA@Material@engine@donut@@8 ??_R4MeshGeometry@engine@donut@@6B@ ??_R0?AUMeshGeometry@engine@donut@@@8 ??_R3MeshGeometry@engine@donut@@8 ??_R2MeshGeometry@engine@donut@@8 ??_R1A@?0A@EA@MeshGeometry@engine@donut@@8 ??_R4MeshInfo@engine@donut@@6B@ ??_R0?AUMeshInfo@engine@donut@@@8 ??_R3MeshInfo@engine@donut@@8 ??_R2MeshInfo@engine@donut@@8 ??_R1A@?0A@EA@MeshInfo@engine@donut@@8 ??_R4SceneGraphLeaf@engine@donut@@6B@ ??_R3SceneGraphLeaf@engine@donut@@8 ??_R2SceneGraphLeaf@engine@donut@@8 ??_R1A@?0A@EA@SceneGraphLeaf@engine@donut@@8 ??_R4SceneCamera@engine@donut@@6B@ ??_R0?AVSceneCamera@engine@donut@@@8 ??_R3SceneCamera@engine@donut@@8 ??_R2SceneCamera@engine@donut@@8 ??_R1A@?0A@EA@SceneCamera@engine@donut@@8 ??_R4PerspectiveCamera@engine@donut@@6B@ ??_R0?AVPerspectiveCamera@engine@donut@@@8 ??_R3PerspectiveCamera@engine@donut@@8 ??_R2PerspectiveCamera@engine@donut@@8 ??_R1A@?0A@EA@PerspectiveCamera@engine@donut@@8 ??_R4Light@engine@donut@@6B@ ??_R3Light@engine@donut@@8 ??_R2Light@engine@donut@@8 ??_R1A@?0A@EA@Light@engine@donut@@8 ??_R4EnvironmentLight@@6B@ ??_R3EnvironmentLight@@8 ??_R2EnvironmentLight@@8 ??_R1A@?0A@EA@EnvironmentLight@@8 ??_R4PerspectiveCameraEx@@6B@ ??_R0?AVPerspectiveCameraEx@@@8 ??_R3PerspectiveCameraEx@@8 ??_R2PerspectiveCameraEx@@8 ??_R1A@?0A@EA@PerspectiveCameraEx@@8 ??_R4SampleSettings@@6B@ ??_R3SampleSettings@@8 ??_R2SampleSettings@@8 ??_R1A@?0A@EA@SampleSettings@@8 ??_R4GameSettings@@6B@ ??_R3GameSettings@@8 ??_R2GameSettings@@8 ??_R1A@?0A@EA@GameSettings@@8 ??_R4MaterialEx@@6B@ ??_R0?AUMaterialEx@@@8 ??_R3MaterialEx@@8 ??_R2MaterialEx@@8 ??_R1A@?0A@EA@MaterialEx@@8 ??_R4MeshGeometryEx@@6B@ ??_R0?AUMeshGeometryEx@@@8 ??_R3MeshGeometryEx@@8 ??_R2MeshGeometryEx@@8 ??_R1A@?0A@EA@MeshGeometryEx@@8 ??_R4MeshInfoEx@@6B@ ??_R0?AUMeshInfoEx@@@8 ??_R3MeshInfoEx@@8 ??_R2MeshInfoEx@@8 ??_R1A@?0A@EA@MeshInfoEx@@8 ??_R4?$_Ref_count_obj2@VEnvironmentLight@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VEnvironmentLight@@@std@@@8 ??_R3?$_Ref_count_obj2@VEnvironmentLight@@@std@@8 ??_R2?$_Ref_count_obj2@VEnvironmentLight@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VEnvironmentLight@@@std@@8 ??_R4?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@@8 ??_R3?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@8 ??_R2?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VPerspectiveCameraEx@@@std@@8 ??_R4?$_Ref_count_obj2@VSampleSettings@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VSampleSettings@@@std@@@8 ??_R3?$_Ref_count_obj2@VSampleSettings@@@std@@8 ??_R2?$_Ref_count_obj2@VSampleSettings@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VSampleSettings@@@std@@8 ??_R4?$_Ref_count_obj2@VGameSettings@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VGameSettings@@@std@@@8 ??_R3?$_Ref_count_obj2@VGameSettings@@@std@@8 ??_R2?$_Ref_count_obj2@VGameSettings@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VGameSettings@@@std@@8 ??_R4?$_Ref_count_obj2@UMeshInfoEx@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@UMeshInfoEx@@@std@@@8 ??_R3?$_Ref_count_obj2@UMeshInfoEx@@@std@@8 ??_R2?$_Ref_count_obj2@UMeshInfoEx@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@UMeshInfoEx@@@std@@8 ??_R4?$_Ref_count_obj2@UMeshGeometryEx@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@UMeshGeometryEx@@@std@@@8 ??_R3?$_Ref_count_obj2@UMeshGeometryEx@@@std@@8 ??_R2?$_Ref_count_obj2@UMeshGeometryEx@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@UMeshGeometryEx@@@std@@8 ??_R4?$_Ref_count_obj2@UMaterialEx@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@UMaterialEx@@@std@@@8 ??_R3?$_Ref_count_obj2@UMaterialEx@@@std@@8 ??_R2?$_Ref_count_obj2@UMaterialEx@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@UMaterialEx@@@std@@8 __real@7f7fffff __real@ff7fffff __security_cookie 