d唜 駡w� t      .drectve        P  �               
 .debug$S        � $           @ B.debug$T        l   p             @ B.rdata          @   �             @ @@.text$mn                         P`.debug$S          ' 7        @B.text$mn        #  s �          P`.debug$S        �  �  �7     �   @B.text$x            �? �?         P`.text$x            �? �?         P`.text$x            �? �?         P`.text$x            �? �?         P`.text$x            @ @         P`.text$x            @ ,@         P`.text$x            6@ F@         P`.text$x            P@ `@         P`.text$x            j@ z@         P`.text$mn           凘              P`.debug$S        �   園 `A        @B.text$mn        "   淎              P`.debug$S        �  続 VC        @B.text$mn        "   鯟              P`.debug$S        �  D 珽        @B.text$mn        "   LF              P`.debug$S        �  nF 
H        @B.text$mn        "   狧              P`.debug$S        �  蘃 XJ        @B.text$mn        "   鳭              P`.debug$S        �  K         @B.text$mn        "   FM              P`.debug$S        �  hM 鬘        @B.text$mn        "   擮              P`.debug$S        �  禣 BQ        @B.text$mn        K   釷              P`.debug$S        �  -R T        @B.text$mn        ?   峊 蘐         P`.debug$S        \  郥 <V        @B.text$mn        H   碫              P`.debug$S        �  黇 繶        @B.text$mn        �  豗 籟         P`.debug$S        �  韀 礲     2   @B.text$mn        �  ヾ Ug     
    P`.debug$S        8  譯 s     Z   @B.text$x            搗 焩         P`.text$mn        G  ﹙ 饃         P`.debug$S        �  y �     :   @B.xdata             <�             @0@.pdata             P� \�        @0@.xdata             z� 妭        @0@.pdata             瀭 獋        @0@.xdata          	   葌 褌        @@.xdata             鍌 雮        @@.xdata             鮽             @@.xdata             鴤 �        @0@.pdata             � (�        @0@.xdata          	   F� O�        @@.xdata             c� i�        @@.xdata             s�             @@.xdata             v� 唭        @0@.pdata             殐         @0@.xdata          	   膬 蛢        @@.xdata             醿 鐑        @@.xdata             駜             @@.xdata              鰞 �        @0@.pdata             *� 6�        @0@.xdata          	   T� ]�        @@.xdata          :   q� 珓        @@.xdata             �             @@.voltbl            '�                .xdata          (   -� U�        @0@.pdata             i� u�        @0@.xdata          	   搮 渽        @@.xdata             皡 羺        @@.xdata          '   邊             @@.xdata          $   � *�        @0@.pdata             >� J�        @0@.xdata          	   h� q�        @@.xdata             厗 媶        @@.xdata             晢             @@.xdata          $    艈        @0@.pdata             蠁 蹎        @0@.xdata             鶈 	�        @0@.pdata             � )�        @0@.xdata          	   G� P�        @@.xdata             d� j�        @@.xdata             t�             @@.xdata             w� 噰        @0@.pdata             泧         @0@.xdata          	   艊 螄        @@.xdata             鈬 鑷        @@.xdata             驀             @@.xdata             鯂 �        @0@.pdata             � %�        @0@.xdata          	   C� L�        @@.xdata             `� f�        @@.xdata             p�             @@.xdata             s� 儓        @0@.pdata             棃         @0@.xdata          	   翀 蕡        @@.xdata             迗 鋱        @@.xdata             顖             @@.xdata             駡 �        @0@.pdata             � !�        @0@.xdata          	   ?� H�        @@.xdata             \� b�        @@.xdata             l�             @@.xdata             o�             @0@.pdata             w� 儔        @0@.xdata              祲        @0@.pdata             訅 邏        @0@.xdata             龎 
�        @0@.pdata             +� 7�        @0@.voltbl            U�               .data               W�             @ @�.rdata             w�             @0@.rdata             |�             @@@.rdata          
   枈             @@@.rdata                          @0@.rdata                          @P@.chks64         �  穵              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   S  ^     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\AccumulationPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Ensure_adl  $_Weak_order  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $placeholders  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $vfs  $math 	 $colors  $log 	 $stdext  �   嗟 M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gx M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gy Q U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx1  U   std::ratio<1,12>::num  U   std::ratio<1,12>::den 3 U  � std::integral_constant<__int64,200>::value M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx2 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gx L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gy _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment ; U  �r ( std::integral_constant<__int64,2629746>::value $ U  � std::ratio<200,146097>::num ( U  ��: std::ratio<200,146097>::den Q U  �X呩std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Dx2 O U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx2 S U  �X呩std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx2 N U  H std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gx N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gy 2 U  2 std::integral_constant<__int64,50>::value - :    std::chrono::system_clock::is_steady : U  �� std::integral_constant<__int64,438291>::value $ U   std::ratio<1,10000000>::num ( U  ��枠 std::ratio<1,10000000>::den # U  2 std::ratio<50,438291>::num ' U  �� std::ratio<50,438291>::den N U  ��Q std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Dx2 L U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx2 P U  ��Q std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gx K U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gy ; :   std::atomic<unsigned __int64>::is_always_lock_free 2 U   std::integral_constant<__int64,24>::value E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment  U   std::ratio<1,24>::num  U   std::ratio<1,24>::den ) <   nvrhi::ObjectTypes::SharedHandle - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + <  �  nvrhi::ObjectTypes::VK_Sampler N U  �r ( std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Nx2 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule . <  �
  nvrhi::ObjectTypes::VK_RenderPass J U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Dx2 / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo L U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx2 P U  �r ( std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx2 6 :   std::_Iterator_base0::_Unwrap_when_unverified K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gx K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gy  U   std::ratio<1,1>::num � :   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified  U   std::ratio<1,1>::den 2 U  
 std::integral_constant<__int64,10>::value C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity 7 :   std::_Iterator_base12::_Unwrap_when_unverified # U  
 std::ratio<10,438291>::num ' U  �� std::ratio<10,438291>::den � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Same_size_and_compatible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_assignable d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N U  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 J U  std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 O U  �X呩std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Nx2 ( U  ��枠 std::ratio<10000000,1>::num K U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Dx2 P U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx1 $ U   std::ratio<10000000,1>::den L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx1   U   std::ratio<1,3600>::num   U  std::ratio<1,3600>::den P U  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy M U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx2 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx1 L U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx2 K U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gx Q U  �X呩std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gy L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gx L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gy < U  ��枠 std::integral_constant<__int64,10000000>::value 1 U   std::integral_constant<__int64,5>::value ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos # U   std::ratio<5,2629746>::num ' U  �r ( std::ratio<5,2629746>::den F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 - :   std::chrono::steady_clock::is_steady F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 T U  �r ( std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Dx2  ;  ��I@donut::math::PI_f " s  
�-DT�!	@donut::math::PI_d H U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx1 ! ;  ��7�5donut::math::epsilon " ;  �  �donut::math::infinity & U   std::ratio<1,1000000000>::num H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx1  ;  �  �donut::math::NaN H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 * U  � 蕷;std::ratio<1,1000000000>::den R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 V U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx1 G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gx R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx2 G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gy V U  �r ( std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx2 Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gx L U  ��Q std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Nx2 Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gy H U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Dx2 4 U  std::integral_constant<__int64,3600>::value J U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx1 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx1 F U  
� 泌�  std::integral_constant<__int64,26297460000000>::value J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx2 N U  ��Q std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx2 8 :   std::atomic<unsigned long>::is_always_lock_free I U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gx I U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gy 4 U  �std::integral_constant<__int64,1440>::value * U   std::ratio<1,26297460000000>::num 2 U  
� 泌�  std::ratio<1,26297460000000>::den   U   std::ratio<1,1440>::num U U  �X呩std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Nx2   U  �std::ratio<1,1440>::den Q U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Dx2 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx1 W U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx1 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx2 W U  �X呩std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx2 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gx R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gy / <  � nvrhi::rt::cluster::kClasByteAlignment . <   nvrhi::rt::cluster::kClasMaxTriangles - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex G U  
� <$A std::integral_constant<__int64,315569520000000>::value C :   std::atomic<std::chrono::tzdb_list *>::is_always_lock_free . :    std::integral_constant<bool,0>::value O U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Nx2 S U  � 蕷;std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Dx2 + U   std::ratio<1,315569520000000>::num 3 U  
� <$A std::ratio<1,315569520000000>::den Q U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx1 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx1 * U  � 蕷;std::ratio<1000000000,1>::num & U   std::ratio<1000000000,1>::den U U  � 蕷;std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx2 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx2 R U  ��Q std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Nx2 P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gx N U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Dx2 P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gy P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx1 T U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx1 E U  
� 牳0F  std::integral_constant<__int64,3600000000000>::value P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gy D U  
� 纈*�   std::integral_constant<__int64,864000000000>::value 1 U  
� 牳0F  std::ratio<3600000000000,1>::num ) U   std::ratio<3600000000000,1>::den ( U   std::ratio<1,864000000000>::num 0 U  
� 纈*�   std::ratio<1,864000000000>::den . :   std::integral_constant<bool,1>::value ( �    std::chrono::local_info::unique - �   std::chrono::local_info::nonexistent + �   std::chrono::local_info::ambiguous % _   std::ctype<char>::table_size E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 M U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gy C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gy d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size > U  � 蕷;std::integral_constant<__int64,1000000000>::value F U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Nx2 F U  �std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Dx2 ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos * :    std::chrono::utc_clock::is_steady H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx1   U  �std::ratio<1000,1>::num   U   std::ratio<1000,1>::den H U  �std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gy 4 U  �std::integral_constant<__int64,1000>::value * :    std::chrono::tai_clock::is_steady 6 v,  �奮�std::chrono::tai_clock::_Tai_epoch_adjust L U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Nx2 P U  � 蕷;std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gx ) <   donut::math::vector<bool,2>::DIM M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gy ) <   donut::math::vector<bool,3>::DIM * :    std::chrono::gps_clock::is_steady 6 v,  �w�*韘td::chrono::gps_clock::_Gps_epoch_adjust ) <   donut::math::vector<bool,4>::DIM   �   std::_Iosb<int>::skipws ! �   std::_Iosb<int>::unitbuf # �   std::_Iosb<int>::uppercase " �   std::_Iosb<int>::showbase # �   std::_Iosb<int>::showpoint ! �    std::_Iosb<int>::showpos  �  @ std::_Iosb<int>::left  �  � std::_Iosb<int>::right 5 :    std::filesystem::_File_time_clock::is_steady " �   std::_Iosb<int>::internal  �   std::_Iosb<int>::dec  �   std::_Iosb<int>::oct  �   std::_Iosb<int>::hex $ �   std::_Iosb<int>::scientific  �    std::_Iosb<int>::fixed " �   0std::_Iosb<int>::hexfloat # �   @std::_Iosb<int>::boolalpha " �  � �std::_Iosb<int>::_Stdio % �  �std::_Iosb<int>::adjustfield # �   std::_Iosb<int>::basefield $ �   0std::_Iosb<int>::floatfield ! �    std::_Iosb<int>::goodbit   �   std::_Iosb<int>::eofbit ! �   std::_Iosb<int>::failbit   �   std::_Iosb<int>::badbit  �   std::_Iosb<int>::in  �   std::_Iosb<int>::out  �   std::_Iosb<int>::ate  �   std::_Iosb<int>::app  �   std::_Iosb<int>::trunc # �  @ std::_Iosb<int>::_Nocreate $ �  � std::_Iosb<int>::_Noreplace   �    std::_Iosb<int>::binary  �    std::_Iosb<int>::beg  �   std::_Iosb<int>::cur  �   std::_Iosb<int>::end , �  @ std::_Iosb<int>::_Default_open_prot  U  < std::ratio<60,1>::num  U   std::ratio<60,1>::den M U  �r ( std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Nx2 I U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx2 O U  �r ( std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx2 J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gx J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gy N U  �X呩std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx2 P U  �X呩std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gy _ _   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment K U  ��Q std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Dx2 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx2 M U  ��Q std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gx � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Same_size_and_compatible H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gy � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_assignable   U   std::ratio<1,1000>::num   U  �std::ratio<1,1000>::den � :   std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_constructible # U   std::ratio<1,1000000>::num ' U  �@B std::ratio<1,1000000>::den � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_assignable D _   ��std::basic_string_view<char,std::char_traits<char> >::npos � :   std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_assignable : _    std::integral_constant<unsigned __int64,0>::value Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment 9 �   std::chrono::_Time_parse_fields::_Era_begin_wday J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos S _   std::allocator<std::chrono::time_zone>::_Minimum_asan_allocation_alignment R :    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified / U   std::ratio<1,1000000000000000000>::num 7 U  
�  dС多
std::ratio<1,1000000000000000000>::den \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos - �   std::_Invoker_pmf_pointer::_Strategy * <   donut::math::vector<float,3>::DIM , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy U :   std::_String_view_iterator<std::char_traits<char> >::_Unwrap_when_unverified 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable X _   std::allocator<std::chrono::time_zone_link>::_Minimum_asan_allocation_alignment L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos * <   donut::math::vector<float,4>::DIM * 錏        donut::math::lumaCoefficients L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos * <   donut::math::vector<float,2>::DIM ':   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable U _   std::allocator<std::chrono::leap_second>::_Minimum_asan_allocation_alignment  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ) <   donut::math::frustum::numCorners ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; _  �std::_Floating_type_traits<double>::_Exponent_mask J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ' U  �r ( std::ratio<2629746,1>::num # U   std::ratio<2629746,1>::den b <    std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::fractional_width 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices # �        nvrhi::AllSubresources $ �   ��std::strong_ordering::less c _   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment $ �    std::strong_ordering::equal & �   std::strong_ordering::greater ' �  ��std::chrono::year::_Year_min % �  �std::chrono::year::_Year_max J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong           nvrhi::EntireBuffer : _   std::integral_constant<unsigned __int64,1>::value . �   donut::math::box<float,3>::numCorners A _   std::allocator<char>::_Minimum_asan_allocation_alignment ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity T U  �r ( std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Dx2 X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size V U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx1 ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx1 C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 # U   std::ratio<1,2629746>::num a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset ' U  �r ( std::ratio<1,2629746>::den C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gy E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gx D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gy 2 U   std::integral_constant<__int64,12>::value : _   std::integral_constant<unsigned __int64,2>::value R U  ��Q std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Dx2 T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos T U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx1 ! U   std::ratio<1,86400>::num % U  ��Q std::ratio<1,86400>::den : _   std::integral_constant<unsigned __int64,3>::value P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gy : U  ��: std::integral_constant<__int64,146097>::value  H    std::denorm_absent  H   std::denorm_present  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity 3 U  �std::integral_constant<__int64,400>::value & :    std::_Num_base::has_quiet_NaN * :    std::_Num_base::has_signaling_NaN # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent - �    std::integral_constant<int,0>::value ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix ) H   std::_Num_float_base::has_denorm + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed - :   std::_Num_float_base::is_specialized 4 _  @ _Mtx_internal_imp_t::_Critical_section_size * K   std::_Num_float_base::round_style 5 _   _Mtx_internal_imp_t::_Critical_section_align $ �   std::_Num_float_base::radix T U  �X呩std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Dx2 + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits * �   std::numeric_limits<bool>::digits + :    std::_Aligned<64,8,short,0>::_Fits V U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx1 ) :   std::_Aligned<64,8,int,0>::_Fits $ U   std::ratio<1,31556952>::num ( U  �X呩std::ratio<1,31556952>::den - :   std::numeric_limits<char>::is_signed R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx2 - :    std::numeric_limits<char>::is_modulo 3 �  \ std::filesystem::path::preferred_separator * �   std::numeric_limits<char>::digits V U  �X呩std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gx , �   std::numeric_limits<char>::digits10 Q U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gy 4 :   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 :   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 :   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 :   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits % �    _Atomic_memory_order_relaxed 0 �  	 std::numeric_limits<char32_t>::digits10 % �   _Atomic_memory_order_consume % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 1 <   donut::math::vector<unsigned int,2>::DIM , :   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 # <   kPolymorphicLightTypeShift " <   kPolymorphicLightTypeMask . <  �   kPolymorphicLightShapingEnableBit 1 <  �    kPolymorphicLightIesProfileEnableBit - ;  �   羕PolymorphicLightMinLog2Radiance - ;  �   BkPolymorphicLightMaxLog2Radiance D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 :   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 � :   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable ( <   donut::math::vector<int,2>::DIM ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos Q U  �r ( std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Dx2 S U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx2 S U  �r ( std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx2 N U  6 std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gx N U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gy 4 U  @std::integral_constant<__int64,1600>::value 7 U  �;緎td::integral_constant<__int64,48699>::value N :   std::_Trivial_cat<char,char,char &,char &>::_Same_size_and_compatible K :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_constructible H :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_assignable $ U  @std::ratio<1600,48699>::num H :    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified & U  �;緎td::ratio<1600,48699>::den a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible R U  �X呩std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Nx2 � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible N U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Dx2 � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable T U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx2 T U  �X呩std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gx O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gy @ �   std::_General_precision_tables_2<float>::_Max_special_P $ U  �std::ratio<400,146097>::num ( U  ��: std::ratio<400,146097>::den 8 �  ' std::_General_precision_tables_2<float>::_Max_P A �   std::_General_precision_tables_2<double>::_Max_special_P 9 �  5std::_General_precision_tables_2<double>::_Max_P � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask $ �   std::_Locbase<int>::collate " �   std::_Locbase<int>::ctype % �   std::_Locbase<int>::monetary $ �   std::_Locbase<int>::numeric ! �   std::_Locbase<int>::time % �    std::_Locbase<int>::messages   �  ? std::_Locbase<int>::all ! �    std::_Locbase<int>::none :    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi :   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard   U  std::ratio<3600,1>::num   U   std::ratio<3600,1>::den � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment  U   std::ratio<24,1>::num  U   std::ratio<24,1>::den I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx1 I U  std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gy + �!        nvrhi::rt::c_IdentityTransform 9 U  ��Q std::integral_constant<__int64,86400>::value � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible - <  `std::_Big_integer_flt::_Maximum_bits - <    std::_Big_integer_flt::_Element_bits � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable . <  s std::_Big_integer_flt::_Element_count 1 U   std::integral_constant<__int64,1>::value % U  ��Q std::ratio<86400,1>::num ! U   std::ratio<86400,1>::den B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size x _   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size  U   std::ratio<7,1>::num  U   std::ratio<7,1>::den I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx1 M U  ��Q std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gy  �1   std::_Consume_header  �1   std::_Generate_header : U  ��:	 std::integral_constant<__int64,604800>::value ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos ( U  ��: std::ratio<146097,400>::num $ U  �std::ratio<146097,400>::den T U  ��: std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx1 P U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx1 T U  ��Q std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx2 P U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gx -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size O U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gy -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets ':    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi < U  �X呩std::integral_constant<__int64,31556952>::value ( U  �X呩std::ratio<31556952,1>::num $ U   std::ratio<31556952,1>::den / :   std::atomic<long>::is_always_lock_free P U  �r ( std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Dx2  U   std::ratio<12,1>::num  U   std::ratio<12,1>::den K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Dx2 N U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx2 T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment R U  �r ( std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx2  �  _CatchableType " �  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  '0  __std_tzdb_error & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �0  _Ctypevec  酕  PolymorphicLightInfo & �  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  #   rsize_t  �>  __std_fs_find_data &   $_TypeDescriptor$_extraBytes_23 - cZ  $_s__CatchableTypeArray$_extraBytes_32 # )D  __std_fs_reparse_data_buffer Z _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^  _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> `   __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �>  __std_fs_dir_handle  �/  __std_access_rights    _TypeDescriptor & NZ  $_TypeDescriptor$_extraBytes_34  G  PathTracerCameraData 	 I  tm % �  _s__RTTICompleteObjectLocator2 & kZ  $_TypeDescriptor$_extraBytes_30 A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 焃  $_TypeDescriptor$_extraBytes_46  �  _s__CatchableType & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const>  銩  __std_fs_filetime E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & �  $_TypeDescriptor$_extraBytes_20  貴  LightingControlData  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16   SD  __std_fs_copy_file_result  �5  __std_code_page � 薧  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � 赹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > z 韃  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> i鋇  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � 躛  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 淾  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > � O]  std::_Non_trivial_copy<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > � 蚟  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > M k^  std::_Normal_allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > � 萟  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > . 綶  std::_Ptr_base<donut::vfs::IFileSystem> �糬  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � 匽  std::_Non_trivial_move_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 盺  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > � 猑  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> k ,]  std::_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0> � a]  std::_Non_trivial_move<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 瀆  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > 廭  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> g\  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > [ 坁  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' {I  std::default_delete<wchar_t [0]> . /  std::_Conditionally_enabled_hash<int,1> A C  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit D �;  std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty - �?  std::reverse_iterator<wchar_t const *> 6 刕  std::allocator_traits<std::allocator<char8_t> > " 媂  std::_Char_traits<char,int>  0  std::_Fs_file  �=  std::optional<int> � 俕  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >  "   std::_Atomic_counter_t  N  std::_Num_base & $/  std::hash<std::error_condition> K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 5 $0  std::_Tzdb_deleter<__std_tzdb_time_zones_info> � z^  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > # �  std::numeric_limits<char8_t>  /,  std::_Big_uint128  �=  std::optional<__int64> E m^  std::allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > G ^^  std::allocator_traits<std::allocator<std::chrono::leap_second> > ) 怷  std::_Narrow_char_traits<char,int>  j  std::hash<float> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc> ! �4  std::__floating_decimal_64 S 蘕  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone_link> > \^  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >  �5  std::less<void>  /  std::hash<int>  P  std::_Num_int_base  i3  std::ctype<wchar_t> " �/  std::_System_error_category / 揚  std::_Conditionally_enabled_hash<bool,1>  �5  std::_Format_arg_index  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > / y2  std::codecvt<char32_t,char8_t,_Mbstatet> k =]  std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > f �;  std::pair<std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >,bool> 碶  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> u 訵  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 餧  std::allocator_traits<std::allocator<wchar_t> > � s]  std::_Non_trivial_copy_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  `  std::bad_cast  JR  std::equal_to<void> 3 僄  std::_Ptr_base<donut::engine::ShaderFactory> � 粿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > o 踈  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1> 6 ~H  std::initializer_list<nvrhi::BindingLayoutItem>     std::_Compare_t " u  std::numeric_limits<double>  �  std::__non_rtti_object , K2  std::_Codecvt_guard<char8_t,char16_t> ( @  std::_Basic_container_proxy_ptr12 2 #L  std::allocator<std::chrono::time_zone_link>   �5  std::_Decode_result<char> 1    std::array<nvrhi::FramebufferAttachment,8>  q  std::_Num_float_base  �-  std::logic_error 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety / 頚  std::allocator<std::chrono::leap_second> ! 頬  std::char_traits<char32_t>  Q1  std::locale  �1  std::locale::_Locimp  b1  std::locale::facet   k1  std::locale::_Facet_guard  1  std::locale::id - VL  std::allocator<std::chrono::time_zone>  �  std::_Compare_ncmp s 竂  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   R  std::numeric_limits<bool>  S6  std::_Fmt_codec<char,0> # *Y  std::_WChar_traits<char16_t> P 獻  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> Z W  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> > R �9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > h {9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >::_Reallocation_policy M i?  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  F.  std::overflow_error " x5  std::_Basic_format_arg_type , b2  std::_Codecvt_guard<char16_t,char8_t> % L  std::_One_then_variadic_args_t W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > E 鞢  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j 靅  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   闿  std::char_traits<wchar_t> \ :  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > r �9  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> >::_Reallocation_policy   �  std::pmr::memory_resource  	>  std::pair<int,int> ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> � 鑍  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  砞  std::false_type  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � �:  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > $ 
6  std::_Fmt_fixed_buffer_traits  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > c 蠶  std::_Alloc_construct_ptr<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  扟  std::fpos<_Mbstatet> b gQ  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone> > > , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> o 奓  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_current_zone_info>,__std_tzdb_current_zone_info *,1>  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , n  std::numeric_limits<unsigned __int64> F L  std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >  �0  std::_Locinfo 6 B  std::_Ptr_base<std::filesystem::_Dir_enum_impl> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 4 婯  std::_Atomic_padded<std::chrono::tzdb_list *> s 奤  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > N 諼  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone> > � 萞  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > 9 DJ  std::basic_streambuf<char,std::char_traits<char> > $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16>  �  std::string_view  �  std::wstring_view % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound  Z5  std::money_base  縘  std::money_base::pattern  y0  std::_Timevec   �.  std::_Init_once_completer  絔  std::endian j sC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � BC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy + �2  std::codecvt<wchar_t,char,_Mbstatet> h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> d xK  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::leap_second> > > Q 籡  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16>    std::_Iterator_base12 � 颸  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  mN  std::_Pocma_values . B0  std::_Tzdb_deleter<__std_tzdb_sys_info> 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> ! /  std::hash<std::error_code> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > N 	@  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > ( 筣  std::ratio<1,1000000000000000000> @ nY  std::_Default_allocator_traits<std::allocator<char32_t> >  �?  std::allocator<char32_t> ? !D  std::unique_ptr<char [0],std::default_delete<char [0]> > $ �  std::_Atomic_integral<long,4>  L<  std::_Flist_unchecked_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,std::_Iterator_base0> R =K  std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >  桺  std::hash<bool>     std::streamsize 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty � �;  std::_Compressed_pair<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >,std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,1> ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> 2 鼿  std::initializer_list<nvrhi::IBindingSet *> � �:  std::tuple<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > N 蔝  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone> > ] �:  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > S 礥  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone_link> >  �  std::hash<long double> � 侤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � Q@  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy [ �<  std::_Flist_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > >   �  std::_Comparison_category H 蠬  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ �5  std::_Decode_result<char32_t> # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0  w  std::hash<double> O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> n �:  std::_Tuple_val<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > G �;  std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >  �5  std::_Lazy_locale / Y  std::_Char_traits<char32_t,unsigned int>  >/  std::_System_error ( �)  std::hash<nvrhi::FramebufferInfo>  螸  std::_Fmt_buffer<char> 9 桰  std::allocator<std::filesystem::_Find_file_handle>  �.  std::error_condition % 砞  std::integral_constant<bool,0>  �  std::bad_exception & 肐  std::_Zero_then_variadic_args_t / 甝  std::_General_precision_tables_2<double> � ]L  std::_Compressed_pair<std::allocator<std::chrono::time_zone>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >,1>  �  std::u32string N {J  std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>  �-  std::invalid_argument   玗  std::char_traits<char8_t> [   std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > . ]  std::_General_precision_tables_2<float> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > b L:  std::unique_ptr<__std_tzdb_time_zones_info,std::_Tzdb_deleter<__std_tzdb_time_zones_info> > + rD  std::pair<enum __std_win_error,bool> S  ?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char8_t> > >  �5  std::_Fmt_codec_base<0> J ]  std::allocator_traits<std::allocator<std::chrono::time_zone_link> > S Y?  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �-  std::length_error F TU  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 烮  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! s  std::numeric_limits<float>  �4  std::time_base   }4  std::time_base::dateorder ) �  std::_Atomic_integral_facade<long> % 盤  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " VP  std::hash<unsigned __int64> f 9  std::unique_ptr<__std_tzdb_current_zone_info,std::_Tzdb_deleter<__std_tzdb_current_zone_info> > 1 癒  std::_Flist_node<std::chrono::tzdb,void *>  ]  std::ratio<60,1> k 軰  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_time_zones_info>,__std_tzdb_time_zones_info *,1> : +6  std::_String_view_iterator<std::char_traits<char> >  '  std::exception_ptr  �  std::strong_ordering  ]  std::ratio<1,1000000> % �  std::_Itraits_pointer_strategy C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > )   std::hash<enum nvrhi::BlendFactor> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t>  �.  std::once_flag  �.  std::error_code T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy ' �4  std::pair<char *,enum std::errc> * �=  std::_Optional_destruct_base<int,1> ! �4  std::__floating_decimal_32  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < �\  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  �3  std::_Iosb<int>   �3  std::_Iosb<int>::_Seekdir ! �3  std::_Iosb<int>::_Openmode   �3  std::_Iosb<int>::_Iostate ! �3  std::_Iosb<int>::_Fmtflags # �3  std::_Iosb<int>::_Dummy_enum 7 齖  std::allocator_traits<std::allocator<char32_t> >  ~[  std::nano I 7L  std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > � 馶  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 Y  std::_Char_traits<char16_t,unsigned short> a _<  std::_Flist_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > $ �)  std::hash<nvrhi::BufferRange> V ^;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> > l -;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >::_Reallocation_policy T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  "1  std::_Locbase<int> S bX  std::forward_list<std::chrono::tzdb,std::_Crt_allocator<std::chrono::tzdb> > ! 郳  std::char_traits<char16_t>  �  std::tuple<> 5 �<  std::_Atomic_pointer<std::chrono::tzdb_list *> P 燯  std::_Uninitialized_backout_al<std::allocator<std::chrono::leap_second> >  �  std::_Container_base12 W 轡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  	/  std::io_errc  4  std::ios_base  )4  std::ios_base::_Fnarray  #4  std::ios_base::_Iosarray  �3  std::ios_base::Init  �3  std::ios_base::failure  �3  std::ios_base::event ) X  std::numeric_limits<unsigned char> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type   d  std::numeric_limits<long> " 衆  std::initializer_list<char>  �  std::_Invoker_strategy  鯟  std::nothrow_t 荺  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �  std::_Default_allocate_traits   璍  std::_Fmt_buffer<wchar_t> M 0?  std::_String_iterator<std::_String_val<std::_Simple_types<char8_t> > > N �?  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 瞈  std::allocator_traits<std::allocator<char> > ! `  std::numeric_limits<short> . S  std::allocator<nvrhi::rt::GeometryDesc>  痋  std::ratio<2629746,1> ; �  std::basic_string_view<char,std::char_traits<char> > �璡  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! �3  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > + �5  std::_Grapheme_Break_property_values < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty O 镻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock � 鯧  std::_Compressed_pair<std::allocator<std::chrono::leap_second>,std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >,1>  �  std::bad_alloc  ^.  std::underflow_error B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> ) 礚  std::_Fmt_counting_buffer<wchar_t> c 蔏  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_leap_info [0]>,__std_tzdb_leap_info *,1> J 侷  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D oI  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>  L5  std::messages_base - 遊  std::_Crt_allocator<std::chrono::tzdb> g 癥  std::_Normal_allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  .  std::out_of_range # f  std::numeric_limits<__int64> i 鮅  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >  +3  std::ctype<char> d �:  std::_Tuple_val<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > >  h  std::memory_order  �4  std::chars_format  T4  std::nullopt_t  V4  std::nullopt_t::_Tag  賉  std::ratio<3600,1> # �  std::_Atomic_storage<long,4> , �<  std::atomic<std::chrono::tzdb_list *> # *  std::hash<nvrhi::BlendState> / 譡  std::shared_ptr<donut::vfs::IFileSystem>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  U  std::in_place_t D jL  std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >  a/  std::system_error < {X  std::_Default_allocator_traits<std::allocator<char> > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > g 楺  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> > >    std::ratio<1,1> ^ �;  std::unique_ptr<__std_tzdb_leap_info [0],std::_Tzdb_deleter<__std_tzdb_leap_info [0]> > 2 F6  std::_Measure_string_prefix_iterator_legacy ) �=  std::_Optional_construct_base<int> 3 闔  std::initializer_list<nvrhi::BindingSetItem>   D[  std::forward_iterator_tag  ..  std::runtime_error   
  std::bad_array_new_length E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >  �0  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z WY  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  �  std::u16string  ^  std::nested_exception  �  std::_Distance_unknown H jX  std::pointer_traits<std::_Flist_node<std::chrono::tzdb,void *> *> 7 K  std::basic_ostream<char,std::char_traits<char> > ( j  std::numeric_limits<unsigned int> < 7U  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> E   std::allocator_traits<std::allocator<std::chrono::time_zone> > , 2  std::codecvt<char32_t,char,_Mbstatet>  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> , �2  std::_Codecvt_guard<char8_t,char32_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' w  std::numeric_limits<long double>  /  std::errc " hX  std::pointer_traits<char *> , 凷  std::default_delete<std::_Facet_base>  v.  std::range_error  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �4  std::to_chars_result  �  std::_Compare_eq  ~[  std::ratio<1,1000000000>  �?  std::allocator<char16_t> $ hI  std::default_delete<char [0]> ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  |[  std::ratio<1,1000>  z[  std::ratio<1,10000000> ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  $1  std::_Crt_new_delete % �/  std::_Iostream_error_category2 * 糛  std::_String_constructor_concat_tag  �8  std::allocator<char>    std::nullptr_t . 鏧  std::_Char_traits<char8_t,unsigned int> ' x[  std::_Floating_to_chars_overload & H[  std::random_access_iterator_tag 4 汫  std::shared_ptr<donut::engine::ShaderFactory> ; RP  std::_Conditionally_enabled_hash<unsigned __int64,1> R 酙  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> 3 P0  std::_Tzdb_deleter<__std_tzdb_leap_info [0]> @ 闎  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl>  �0  std::_Yarn<wchar_t> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  b  std::wstring ' V  std::numeric_limits<signed char> � 獲  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �-  std::domain_error  �  std::u32string_view  �  std::_Container_base  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  5  std::_Big_integer_flt  �  std::_Literal_zero $ cP  std::hash<nvrhi::IResource *>  �4  std::from_chars_result � *L  std::_Compressed_pair<std::allocator<std::chrono::time_zone_link>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >,1> " w4  std::_Nontrivial_dummy_type 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char> 8 �6  std::chrono::duration<int,std::ratio<2629746,1> > i u;  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<3600,1> > > & 28  std::chrono::year_month_weekday  u6  std::chrono::day 9 D-  std::chrono::duration<__int64,std::ratio<1,1000> > j %7  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<86400,1> > > n :=  std::chrono::time_point<std::chrono::gps_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  7  std::chrono::weekday  �,  std::chrono::nanoseconds  �6  std::chrono::year ' 8  std::chrono::year_month_day_last  -  std::chrono::minutes  �6  std::chrono::days y N=  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �,  std::chrono::duration<__int64,std::ratio<1,1000000000> > # L7  std::chrono::weekday_indexed 9 �6  std::chrono::duration<int,std::ratio<31556952,1> >  �6  std::chrono::years , fX  std::chrono::duration_values<__int64>  v,  std::chrono::seconds " �8  std::chrono::time_zone_link 6 �6  std::chrono::duration<int,std::ratio<86400,1> > 3 -  std::chrono::duration<int,std::ratio<60,1> > 6 v,  std::chrono::duration<__int64,std::ratio<1,1> > s �,  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   q[  std::chrono::steady_clock   X  std::chrono::system_clock 6 *-  std::chrono::duration<double,std::ratio<60,1> > + X8  std::chrono::year_month_weekday_last  <  std::chrono::tzdb_list + l<  std::chrono::tzdb_list::_Unique_lock + *<  std::chrono::tzdb_list::_Shared_lock ; �-  std::chrono::duration<double,std::ratio<1,1000000> > > �-  std::chrono::duration<double,std::ratio<1,1000000000> > = \,  std::chrono::duration<__int64,std::ratio<1,10000000> > " �7  std::chrono::month_day_last  %7  std::chrono::sys_days n =  std::chrono::time_point<std::chrono::utc_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  �8  std::chrono::time_zone q L,  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >   `7  std::chrono::weekday_last ! �7  std::chrono::month_weekday 5 �,  std::chrono::duration<int,std::ratio<3600,1> >  i7  std::chrono::last_spec & �7  std::chrono::month_weekday_last  :7  std::chrono::local_days j �8  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,1> > > N SK  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > > Z .>  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::<lambda_1>  �8  std::chrono::sys_seconds  �7  std::chrono::year_month 8 Z-  std::chrono::duration<double,std::ratio<1,1000> >  s7  std::chrono::month_day " �7  std::chrono::year_month_day  �,  std::chrono::hours < q-  std::chrono::duration<__int64,std::ratio<1,1000000> > @ m[  std::chrono::zoned_traits<std::chrono::time_zone const *> 5 �,  std::chrono::duration<double,std::ratio<1,1> >  �6  std::chrono::months  89  std::chrono::tzdb e :7  std::chrono::time_point<std::chrono::local_t,std::chrono::duration<int,std::ratio<86400,1> > >  @Y  std::chrono::gps_clock  =Y  std::chrono::tai_clock  �6  std::chrono::month n "=  std::chrono::time_point<std::chrono::tai_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  dX  std::chrono::utc_clock  h[  std::chrono::local_t & �=  std::chrono::_Time_parse_fields 8 y=  std::chrono::_Time_parse_fields::_Leap_second_rep = w=  std::chrono::_Time_parse_fields::_Parse_tp_or_duration 3 Y=  std::chrono::_Time_parse_fields::_FieldFlags H g[  std::chrono::duration<__int64,std::ratio<1,1000000000000000000> >  �8  std::chrono::leap_second 8 �,  std::chrono::duration<double,std::ratio<3600,1> >  �2  std::ctype_base  EA  std::filesystem::perms ' 繟  std::filesystem::directory_entry $ JA  std::filesystem::copy_options ( 5A  std::filesystem::filesystem_error 7 rR  std::filesystem::_Path_iterator<wchar_t const *> ) �>  std::filesystem::_Find_file_handle & y>  std::filesystem::_Is_slash_oper . 螧  std::filesystem::_Should_recurse_result $   std::filesystem::perm_options 4 滳  std::filesystem::recursive_directory_iterator . �A  std::filesystem::_File_status_and_error & 9B  std::filesystem::_Dir_enum_impl 0 KB  std::filesystem::_Dir_enum_impl::_Creator @ QB  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! TA  std::filesystem::file_type . nB  std::filesystem::_Directory_entry_proxy " 揇  std::filesystem::space_info * 桞  std::filesystem::directory_iterator & N=  std::filesystem::file_time_type 0 虰  std::filesystem::_Recursive_dir_enum_impl ) 鞟  std::filesystem::directory_options # gA  std::filesystem::file_status u 霡  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( U[  std::filesystem::_File_time_clock  ?  std::filesystem::path $ �>  std::filesystem::path::format * ?R  std::filesystem::_Normal_conversion < gU  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �1  std::codecvt<char16_t,char,_Mbstatet> , �2  std::_Codecvt_guard<char32_t,char8_t>  R[  std::char_traits<char> � 镮  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �.  std::error_category ) �.  std::error_category::_Addr_storage i o:  std::tuple<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! �/  std::_System_error_message  �  std::_Unused_parameter h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  l4  std::bad_optional_access A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > 7 !B  std::shared_ptr<std::filesystem::_Dir_enum_impl> " DT  std::_Floating_point_string = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> 2 �5  std::_Extended_Pictographic_property_values  �1  std::_Codecvt_mode @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn $ q?  std::ranges::_Iter_move::_Cpo ) o?  std::ranges::_Iter_move::_Cpo::_St % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn    std::_Exact_args_t  P[  std::ratio<86400,1>  V6  std::default_sentinel_t � A  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > >  N[  std::ratio<31556952,1> 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8>  =K  std::ostringstream  6  std::_Fmt_buffer_traits \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  g0  std::_Facet_base ' �)  std::hash<nvrhi::BindingSetItem> " 甔  std::_WChar_traits<wchar_t> 2 �2  std::codecvt<unsigned short,char,_Mbstatet> . �=  std::_Optional_destruct_base<__int64,1> - �=  std::_Optional_construct_base<__int64> # �/  std::_Generic_error_category  扟  std::streampos  B[  std::input_iterator_tag 2 JU  std::_Wrap<std::filesystem::_Dir_enum_impl> X 孲  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> ' 綪  std::hash<enum nvrhi::ColorMask> / ,2  std::codecvt<char16_t,char8_t,_Mbstatet> 3 豃  std::basic_ios<char,std::char_traits<char> >  �1  std::codecvt_base & 譒  std::_Fmt_counting_buffer<char>  錎  std::bad_function_call O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag ' 蜠  std::hash<std::filesystem::path> _ ?[  std::allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > > 7 �<  std::_Atomic_storage<std::chrono::tzdb_list *,8>  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > 7 40  std::_Tzdb_deleter<__std_tzdb_current_zone_info> " �  std::_Asan_aligned_pointers F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  q5  std::format_error  �  std::partial_ordering # |5  std::_Decode_result<wchar_t> . <#  std::array<nvrhi::BindingLayoutItem,16>  9[  std::_Default_sentinel P 耎  std::_Default_allocator_traits<std::allocator<std::chrono::leap_second> > $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int>   �5  std::_GB11_LeftHand_regex * �5  std::_GB11_LeftHand_regex::_State_t 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access 
 !   wint_t # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling " �  nvrhi::SamplerReductionType $ �(  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets  &  nvrhi::GraphicsState * DH  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16>  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray ) 艷  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! �(  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc 2 馟  nvrhi::RefCountPtr<nvrhi::IComputePipeline>  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray  艷  nvrhi::DeviceHandle   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  DH  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # �(  nvrhi::DescriptorTableHandle     nvrhi::ShaderType  �(  nvrhi::TimerQueryHandle   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �(  nvrhi::HeapHandle # 馟  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - H  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  �(  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  �(  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  lH  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  H  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 繸  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   �(  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �(  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # �(  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  )  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable * lH  nvrhi::RefCountPtr<nvrhi::ITexture>  �&  nvrhi::ComputeState  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery  �/  __std_win_error  S0  __std_tzdb_leap_info  �0  lconv   �  __RTTIBaseClassDescriptor 
    _off_t  I  AccumulationConstants    stat  t   int32_t  Z  timespec & JZ  $_TypeDescriptor$_extraBytes_37  cD  __std_fs_file_id 
 !   _ino_t # 70  __std_tzdb_current_zone_info ' ID  __std_fs_create_directory_result  !   uint16_t  �/  __std_fs_stats $ 僃  donut::engine::ICompositeView    donut::engine::IView   蜦  donut::engine::PlanarView ! 﨑  donut::engine::ShaderMacro # 紿  donut::engine::ShaderFactory " 沍  donut::engine::StaticShader  WI  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3  EG  donut::math::float2 # 錏  donut::math::vector<float,3>  u   donut::math::uint  /F  donut::math::plane # F  donut::math::vector<float,4>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes % 慫  donut::math::matrix<float,3,4>  (G  donut::math::uint2  F  donut::math::float4 % WI  donut::math::matrix<float,4,4> # 揈  donut::math::affine<float,3>   4I  donut::math::box<float,3> " *E  donut::math::vector<bool,2>  4I  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> * (G  donut::math::vector<unsigned int,2> M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>    _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ! )0  __std_tzdb_time_zones_info     intmax_t    terminate_handler  �  _s__RTTIBaseClassArray  gZ  PlanarViewConstants & VZ  $_TypeDescriptor$_extraBytes_52 
 Y  ldiv_t  �/  __std_fs_file_flags  �0  _Cvtvec - �  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray  �/  __std_fs_copy_options     ptrdiff_t  
  _stat64i32  E0  __std_tzdb_sys_info  �  _PMD      uint8_t  ,  type_info ' �  _s__RTTIClassHierarchyDescriptor  t   errno_t  �/  __std_fs_reparse_tag  \  _lldiv_t  C  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  G  _s__ThrowInfo  c6  __std_fs_convert_result  �/  __std_fs_stats_flags  �  __RTTIBaseClassArray  �  __crt_locale_data_public - �  $_s__CatchableTypeArray$_extraBytes_24 & 4Z  $_TypeDescriptor$_extraBytes_25 % �  __RTTIClassHierarchyDescriptor  �0  _Collvec   艭  __std_fs_volume_name_kind 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t 
    fpos_t  鏔  PolymorphicLightInfoEx  �  FILE 3 �  __vcrt_va_list_is_reference<wchar_t const *>    mbstate_t  ?  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray  DD  __std_fs_remove_result  騀  PolymorphicLightInfoFull - �  $_s__RTTIBaseClassArray$_extraBytes_16 - /Z  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  �/  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  紺  __std_ulong_and_error  A   __std_tzdb_epoch_milli  \  lldiv_t  lG  AccumulationPass  Y  _ldiv_t  [  _timespec64     intptr_t  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers �   �      G�膢刉^O郀�/耦��萁n!鮋W VS  @    o�椨�4梠"愜��
}z�$ )鰭荅珽X  �    黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �    ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L     +FK茂c�G1灈�7ほ��F�鳺彷餃�  B   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  �   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   o忍x:筞e飴刌ed'�g%X鶩赴5�n�     傊P棼r铞
w爉筫y;H+(皈LL��7縮  i    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  4   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  s   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �   +4[(広
倬禼�溞K^洞齹誇*f�5  O   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  
   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  V   x)憂^恛閶{籮>鐼末BA<颭~儽&'%;-g�  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �   .QVBL籺'Z鬖b濁蟂�8w,/kr彨�$擠�  +   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  z   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  #   歚W%虴�[�,莶CKF�AZⅰq恶�4�  b   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   鏀q�N�&}
;霂�#�0ncP抝     鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  [   �"睱建Bi圀対隤v��cB�'窘�n  �   曀"�H枩U传嫘�"繹q�>窃�8  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  6	   夈殱IjA� 2睆��:)w篈鱎W壩&徾^  Z	   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �	   D���0�郋鬔G5啚髡J竆)俻w��  �	   $^IXV嫓進OI蔁
�;T6T@佮m琦�  -
   蜅�萷l�/费�	廵崹
T,W�&連芿  j
   c�#�'�縌殹龇D兺f�$x�;]糺z�  �
   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�     圽Q&4Y3巷B:C �_%aP縀懮��,褻G  I   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �    z�漼嶜q镛�F苜�:壗Wア燤PEx�  �   +YE擋%1r+套捑@鸋MT61' p廝 飨�  .   ,┭0甗�+天没2骟Bw蛁�%"艠E�  f   交�,�;+愱`�3p炛秓ee td�	^,  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  ?
    狾闘�	C縟�&9N�┲蘻c蟝2  }
   �'稌� 变邯D)\欅)	@'1:A:熾/�  �
   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^     *u\{┞稦�3壅阱\繺ěk�6U�  A   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�     <AE#(�/y鍹籲贚痶蹒-	蒸|邨VWTR  W   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   険L韱#�簀O闚样�4莿Y丳堟3捜狰  (   o藾錚\F鄦泭|嚎醖b&惰�_槮  g   揾配饬`vM|�%
犕�哝煹懿鏈椸  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  6   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  |   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`     猯�諽!~�:gn菾�]騈购����'  G   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  $   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  Z   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  "   Pフ+
V
*戋zydD躩=壠�jA�N盪{u5  i   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   仫減f╋萩}_遫R衵9弧0Up*q蹩鎍,  �   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  -   f扥�,攇(�
}2�祛浧&Y�6橵�  k   �芮�>5�+鮆"�>fw瘛h�=^���  �   h伫{�,疑x萰�籴�?%m遊k泯蒛�3�:�  �   S仄�p�'�/2H��g'浗o$鏊^藵捯�     [届T藎秏1潴�藠?鄧j穊亘^a  U   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   觑v�#je<d鼋^r
u��闑鯙珢�  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  2   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  |   Fp{�悗鉟壍Au4DV�`t9���&*I  �   V� c鯐鄥杕me綻呥EG磷扂浝W)     穫農�.伆l'h��37x,��
fO��  @   R冈悚3Y	�1P#��(勁灱�涰n跲
  w   5�\營	6}朖晧�-w氌rJ籠騳榈  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  =   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  z   饵嶝{郀�穮炗
AD2峵濝k鴖N  �   �*o驑瓂a�(施眗9歐湬

�  �   V8追i顚�^�k细�;>牧惺扴	�\s  ;   �0�*е彗9釗獳+U叅[4椪 P"��  v    I嘛襨签.濟;剕��7啧�)煇9触�.  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  /   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  q   妇舠幸佦郒]泙茸餈u)	�位剎  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   靋!揕�H|}��婡欏B箜围紑^@�銵  <   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   �颠喲津,嗆y�%\峤'找_廔�Z+�     _O縋[HU-銌�鼪根�鲋薺篮�j��  \   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   t�j噾捴忊��
敟秊�
渷lH�#  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  -   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  k   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  �   � 罟)M�:J榊?纸i�6R�CS�7膧俇  A   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�       a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  >    L�9[皫zS�6;厝�楿绷]!��t  |    �	玮媔=zY沚�c簐P`尚足,\�>:O  �    繃S,;fi@`騂廩k叉c.2狇x佚�  !   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  G!   zY{���睃R焤�0聃
扨-瘜}  �!   嵮楖"qa�$棛獧矇oPc续忴2#
  �!   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  "   `k�"�1�^�`�d�.	*貎e挖芺
脑�  J"   )�Q�Ａ9氫�&煝]R埬�楜� 磛庋p�  �"   �)D舼PS橼鈝{#2{r�#獷欲3x(  �"   譫鰿3鳪v鐇�6瘻x侃�h�3&�   #   �
bH<j峪w�/&d[荨?躹耯=�  _#   j轲P[塵5m榤g摏癭 鋍1O骺�*�  �#   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �#   +椬恡�
	#G許�/G候Mc�蜀煟-  :$   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �$   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �$   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  %   鹴y�	宯N卮洗袾uG6E灊搠d�  Z%   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �%   副謐�斦=犻媨铩0
龉�3曃譹5D   �%   J�(S�	皓&r悖,@椎�輮� ��*{�  #&   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  c&   �暊M茀嚆{�嬦0亊2�;i[C�/a\  �&   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �&   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  '   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  S'   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �'   豊+�丟uJo6粑'@棚荶v�g毩笨C  �'   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  (   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  p(   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  �(   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �(   憒峦锴摦懣苍劇o刦澬z�/s▄![�  ()   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  g)   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��  �)   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �)   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s   *   匐衏�$=�"�3�a旬SY�
乢�骣�  j*      �*   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  �*   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  ;+   郖�Χ葦'S詍7,U若眤�M进`  �+   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �+   )鎋]5岽B鑯 �誽|寋獸辪牚  ,   檒Gq$�#嗲RR�錨账��K諻刮g�   H,   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �,   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �,   dhl12� 蒑�3L� q酺試\垉R^{i�  -   龀缬@唶�鱮�瓗禽鐑�^]>Xz豹tN  \-   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �-   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �-   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   �      b  H  �  �  H
  \   �  H
  |   �  H
  }   �  H
  �   �  H
  �   �  H
  �  �  H
  n  �  H
  o  �  H
  x  �  H
  }  �  H
    �  H
  %  �  H
  [  �  H
  �  �  H  �  �  H  �  �  `  m   �  H  �  �  H  Z  �  �  �   �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �
  q   �  �
  @   �  �
  5   �  �
  @   �  �
  5   �  �    �  �  �   �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �  �   �  �
  q   �  �
  @   �  �
  5   �  �
  q   �  �
  @   �  �
  5   m  H  5  �  H  t  �  �    �  �  �   �  �    �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �  �   �  �
  q   �  �    �  �  �   �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �  �     `    j  �  �   k  �  �   �  �  �   �  �  �   
  H  D  �  H  n  �  �  �   �  �  �   �   .   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\ReGIR\ReGIRParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\charconv C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\RTXPT\Rtxpt\AccumulationPass.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\RTXPT\Rtxpt\RTXDI\ShaderParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\RTXPT\External\Donut\include\donut\shaders\view_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_print.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_tzdb.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\format D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_format_ucd_tables.hpp D:\RTXPT\Rtxpt\AccumulationPass.h D:\RTXPT\Rtxpt\Shaders\PathTracer\PathTracerShared.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Config.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\LightingTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\shaders\sky_cb.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bit D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\GI\ReSTIRGIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\RTXPT\Rtxpt\Shaders\Bindings\BindingDataTypes.hlsli D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\RTXPT\External\Donut\include\donut\engine\View.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime D:\RTXPT\External\Donut\include\donut\core\math\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\DI\ReSTIRDIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\predefined C++ types (compiler internal) D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\PolymorphicLight.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\RTXPT\External\Rtxdi\Include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\forward_list D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp   �       L]}  vj      zj     
 納  _   纚  _  
 !�  `   %�  `  
 F�      J�     
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁裥砓>Y7?樰�=      �?                  �?                  �?    H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AJ                                 H�     b%  Othis  O   �   0              �     $       �  �    �  �   �  �,   g    0   g   
 �   g    �   g   
 �   g    �   g   
 H塡$UVWH崿$瘅��H侅	  H�    H3腍墔   I孁H嬞H塋$XL塂$`H�H呉t
H�H嬍�P�3鯤塻H塻H塻H塻 H塻(H塻0H塻8H塻@H婫H吚t�@H�H塁8H婫H塁@塽钠E� 3褹�   H峂需    壍�  菂�  �   菂�     菂�  �  �    f塃繦塼$ 艱$$H婦$ H塃楬塼$ 艱$$H婦$ H塃燞塼$ 荄$    艱$$H婦$ H塃℉塼$ 艱$$H婦$ H塃癏塼$ 艱$$
�$   f塂$&H婦$ H塃窰嬑H墠�  H峌�@ H�H墑宛  H媿�  H�罤墠�  H兟H岴繦;衭譎峂蠬崊�  �   �     HI@ A H0I0@@A@HPIP@`A`H崏�   HpI餒崁�   H冴u瓾� H�H�H�L岴繦峊$(�怭  H嬛H峀$hH;萾H�H�0H婯H塖H吷tH��P怘婰$(H吷tH塼$(H��P恌荄$V  (    T$8H荄$H  �?荄$Q  艱$U 艱$PT$xD$HE圚�H�L岲$xH峊$0�惾   H嬛H峀$pH;萾H�H�0H婯(H塖(H吷tH��P怘婰$0H吷tH塼$0H��P怘�H�t,����嬈�罣凐uH�H嬒��羨凗u	H�H嬒�RH嬅H媿   H3惕    H嫓$8	  H伳	  _^]�   m   �   �    D  p     �       �   W
  H G            #  (     �        �AccumulationPass::AccumulationPass 
 >SG   this  DX    AI  .     � AJ        .  D0	   >))   device  AK        I  AK J     G  >mH   shaderFactory  D`    AM  +     � AP        +  D@	   >�    samplerDesc  Dx    >�"    bindingLayoutDesc  D�    M        �  5偵 M        �  偵,	 M        �  傄
 >�   this  AM  �    S  M        b  傠	
 N N N N M        �  偝 M        �  偝HB
 >n#    temp  AJ  �      AJ �    ?     Bp
  y     � D0    N N M        �  &倢 M        �  偋 M        �  偋
 >n#    temp  AJ  �      AJ �      BP
  y     � N N M        �  偀 >n#    tmp  AK  �    #  AK �    p   3   N M        �  倢C
 M        �  倷 N N N M        �  俈
 N M        �  侫 N M        �  �$ M        �  �$HB
 >t$    temp  AJ  )      AJ :    ?  B
  y     � D(    N N M        �  &価 M        �  � M        �  �
 >t$    temp  AJ        AJ $      B�	  y     � N N M        �  � >t$    tmp  AK       #  AK $    b    N M        �  価C
 M        �  �
 N N N M        �  �>
(
 >�"    <begin>$L0  AK  L    =  M        �  丳 N N M        �  �!E
 >�"    result  B    &    � N M        �  
� >�"    result  B          N M        �  �� >�"    result  B    �       N M        �  
�� >�"    result  B    �       N M        �  
�� >�"    result  B    �       N M        �  ��$ N M        �  d M        
  lM M        �  l	 M        �  u N N N M        m  �d N N M        �  ` N M        �  \ N M        �  X N M        �  T N M        �  P N M        �  J N M        �  8 M        �  ;	 N N 	                   A � h,   b  �  �  �  �  �  �  �  �  �  �  �    �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  m  ~    �  �  �  �  �  �  �  �  �  
  �  �  
 : 	  O  0	  SG  Othis  8	  ))  Odevice  @	  mH  OshaderFactory  x   �  OsamplerDesc  �   �"  ObindingLayoutDesc  9F       E   9�      �(   9       E   96      E   9�      �(   9�      E   9�      E   9�      �   9�      �   O �   x           #  P     l         �.     �8     �J     �d     ��     ��     ��     ��  %  �A  '  �v  )  ��  *  ��   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$0 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$1 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$2 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$3 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$4 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$5 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$6 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$7 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O�   L  W F                                �`AccumulationPass::AccumulationPass'::`1'::dtor$8 
 >SG   this  EN  X           EN  0	          >mH   shaderFactory  EN  `           EN  @	          >�    samplerDesc  EN  x           >�"    bindingLayoutDesc  EN  �                                  �  O,   j    0   j   
 u   j    y   j   
 �   j    �   j   
 �   j    �   j   
 �   j    �   j   
 �   j    �   j   
 �   j      j   
 �  j    �  j   
 2  j    6  j   
 B  j    F  j   
 V  j    Z  j   
 �  j    �  j   
 �  j    �  j   
 �  j    �  j   
 +  j    /  j   
 ;  j    ?  j   
   j      j   
   j    #  j   
 /  j    3  j   
 �  j    �  j   
 �  j    �  j   
 �  j    �  j   
   j      j   
   j      j   
 �  j    �  j   
   j      j   
 <  j    @  j   
 u  j    y  j   
 �  j    �  j   
 �  j    �  j   
 �	  j    �	  j   
 �	  j    �	  j   
 �	  j    �	  j   
 
  j    
  j   
 
  j    
  j   
 #
  j    '
  j   
 3
  j    7
  j   
 C
  j    G
  j   
 S
  j    W
  j   
 l
  j    p
  j   
   u      u   
 `  u    d  u   
 t  u    x  u   
 �  u    �  u   
 �  u    �  u   
 �  u    �  u   
   u      u   
 `  v    d  v   
 �  v    �  v   
 �  v    �  v   
 �  v    �  v   
 
  v    
  v   
 2
  v    6
  v   
 b
  v    f
  v   
 �
  x    �
  x   
   x      x   
   x       x   
 H  x    L  x   
 \  x    `  x   
 �  x    �  x   
 �  x    �  x   
   y      y   
 \  y    `  y   
 p  y    t  y   
 �  y    �  y   
 �  y    �  y   
 �  y    �  y   
 
  y      y   
 \  z    `  z   
 �  z    �  z   
 �  z    �  z   
 �  z    �  z   
   z      z   
 .  z    2  z   
 ^  z    b  z   
 �  {    �  {   
   {      {   
   {      {   
 D  {    H  {   
 X  {    \  {   
 �  {    �  {   
 �  {    �  {   
   |      |   
 X  |    \  |   
 l  |    p  |   
 �  |    �  |   
 �  |    �  |   
 �  |    �  |   
   |    
  |   
 X  }    \  }   
 �  }    �  }   
 �  }    �  }   
 �  }    �  }   
    }      }   
 *  }    .  }   
 Z  }    ^  }   
 �  ~    �  ~   
    ~      ~   
   ~      ~   
 @  ~    D  ~   
 T  ~    X  ~   
 ~  ~    �  ~   
 �  ~    �  ~   
 H媻`   �       s    H媻X   �       n    H媻X   H兞�       f    H媻X   H兞�       o    H媻X   H兞�       h    H媻X   H兞 �       p    H媻X   H兞(�       q    H媻X   H兞0�       r    H媻X   H兞8�       s    H嬃�   �   �   K G                      �        �nvrhi::BindingSetItem::BindingSetItem 
 >d#   this  AJ                                 H     d#  Othis  O   �                  H
             �,   e    0   e   
 p   e    t   e   
 �   e    �   e   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AH         AJ          AH        M        �  GCE
 >t$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   b%  Othis  9       E   O�   0           "   �     $       �  �   �  �   �  �,   h    0   h   
 �   h    �   h   
 �   h    �   h   
 �   h    �   h   
 �   h    �   h   
   h      h   
 P  h    T  h   
 h  h    l  h   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   N  n G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingSet>::~RefCountPtr<nvrhi::IBindingSet> 
 >鱃   this  AH         AJ          AH        M        �  GCE
 >�%    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   鱃  Othis  9       E   O  �   0           "   �     $       �  �   �  �   �  �,   p    0   p   
 �   p    �   p   
 �   p    �   p   
 �   p    �   p   
 �   p    �   p   
 �   p      p   
 J  p    N  p   
 d  p    h  p   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   X  x G            "         �        �nvrhi::RefCountPtr<nvrhi::IComputePipeline>::~RefCountPtr<nvrhi::IComputePipeline> 
 >蜧   this  AH         AJ          AH        M        �  GCE
 >�&    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   蜧  Othis  9       E   O�   0           "   �     $       �  �   �  �   �  �,   o    0   o   
 �   o    �   o   
 �   o    �   o   
 �   o    �   o   
 �   o    �   o   
   o      o   
 T  o    X  o   
 l  o    p  o   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >   this  AH         AJ          AH        M        �  GCE
 >))    temp  AJ  
       AJ        N (                     0@� 
 h   �   0     Othis  9       E   O  �   0           "   �     $       �  �   �  �   �  �,   n    0   n   
 �   n    �   n   
 �   n    �   n   
 �   n    �   n   
 �   n    �   n   
 �   n    �   n   
 B  n    F  n   
 \  n    `  n   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ISampler>::~RefCountPtr<nvrhi::ISampler> 
 >H   this  AH         AJ          AH        M        �  GCE
 >n#    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   H  Othis  9       E   O�   0           "   �     $       �  �   �  �   �  �,   q    0   q   
 �   q    �   q   
 �   q    �   q   
 �   q    �   q   
 �   q    �   q   
 �   q    �   q   
 D  q    H  q   
 \  q    `  q   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�$   this  AH         AJ          AH        M        �  GCE
 >k$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �$  Othis  9       E   O  �   0           "   �     $       �  �   �  �   �  �,   f    0   f   
 �   f    �   f   
 �   f    �   f   
 �   f    �   f   
 �   f    �   f   
 �   f    �   f   
 B  f    F  f   
 \  f    `  f   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >JH   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   JH  Othis  9       E   O�   0           "   �     $       �  �   �  �   �  �,   r    0   r   
 �   r    �   r   
 �   r    �   r   
 �   r    �   r   
 �   r    �   r   
 �   r    �   r   
 D  r    H  r   
 \  r    `  r   
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   �        �std::shared_ptr<donut::engine::ShaderFactory>::~shared_ptr<donut::engine::ShaderFactory> 
 >nG   this  AJ        +  AJ @       M        �  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �   0   nG  Othis  9+       �   9=       �   O�   0           K   H     $       � �   � �E   � �,   s    0   s   
 �   s    �   s   
 �   s    �   s   
   s    
  s   
 |  s    �  s   
 �  s    �  s   
 �  s    �  s   
 @SH冹 H嬞H兞L�
    �   D岯    怘�H吷tH�    H��P怘兡 [�   h       c       �   M  V G            ?      9   �        �nvrhi::ComputePipelineDesc::~ComputePipelineDesc 
 >�%   this  AI  	     5  AJ        	  M        �  # M        �  #CE
 >k$    temp  AJ  &       AJ 9       N N                      0H�  h        �  �   0   �%  Othis  95       E   O   ,   i    0   i   
 {   i       i   
 �   i    �   i   
 �   i    �   i   
 �   i    �   i   
 I  i    M  i   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             H  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,   a    0   a   
 g   a    k   a   
 w   a    {   a   
 �   a    �   a   
 �   a    �   a   
 �   a    �   a   
 �   a    �   a   
 �   a    �   a   
 �   a    �   a   
   a      a   
 !  a    %  a   
 1  a    5  a   
 A  a    E  a   
 �  a    �  a   
 H塡$H塼$H墊$ UH崿$ ��� !  �    H+郒�    H3腍墔�  I孁H嬞茀�  3鰤t$8荄$<   H塗$0    )D$@塼$XH荄$\   L塂$PH荄$d   荄$l����荄$x   H荄$|   L塋$pH荅�   荅����H婣(塽樓E�   H塃怘塽燞塽▔u盖E�
   H塽癏塽繦荅�$   嬈H墔�  H峀$0@ H拎D�IL郒媴�  H�繦墔�  H兞 H峌蠬;蕌虯�  H峌蠬崓�  �    H�H�L婯L崊�  H峊$ �恅  H嬛H峀$(H;萾H�H�0H婯 H塖 H吷tH��P怘婰$ H吷tH塼$ H��P怘9{0t#H�t
H�H嬒�P怘婯0H墈0H吷tH��P怘媿�  H3惕    L崪$ !  I媅I媠 I媨(I嬨]�   �    (   m   Y   _   5  �    �  �       �   q  H G            �  6   �          �AccumulationPass::CreateBindingSet 
 >SG   this  AI  <     � AJ        <  >�   inputTexture  AK        �  AK �     .  >�   outputTexture  AM  9     � AP        9  >�   renderOutputTexture  AQ        9 >�#    bindingSetDesc  D�   M        �  亽 M        �  伅 M        �  伅
 >�    temp  AJ  �      AJ �      N N M        �  仹 N M        �  仒 M        �  仒#	 N N N M        �  亅 M        �  亅HB
 >�%    temp  AJ  �      AJ �    0      B�"  U    �  D     N N M        �  &乁 M        �  乸 M        �  乸
 >�%    temp  AJ  l      AJ |      N N M        �  乭 >�%    tmp  AK  X    #  AK |    g   *  >   N M        �  乁C
 M        �  乥 N N N M        �  	��
& >�#    <begin>$L0  AJ  �     H  M        �  �� N N M        �  ��#'d N! M        �  ��#'+ >n#   sampler  AH  �     2  N M        �  ��() N M        �  b$) N M        �  C&( N  !                   A z h   �  �  �  �  �  �    	  �  �  �  �  �  �  �  �  �  �  o  z  {  �  �  �  �  �  �  �  �  
 :�   O  !  SG  Othis  !  �  OinputTexture   !  �  OoutputTexture   (!  �  OrenderOutputTexture  �  �#  ObindingSetDesc  9O      �(   9x      E   9�      E   9�      E   9�      E   O   �   @           �  P     4       7  �C   :  �9  B  ��  D  ��  E  �,   l    0   l   
 m   l    q   l   
 }   l    �   l   
 �   l    �   l   
 �   l    �   l   
 �   l    �   l   
 �   l    �   l   
   l      l   
 �  l    �  l   
 �  l    �  l   
 l  l    p  l   
 |  l    �  l   
 �  l    �  l   
 	  l    
  l   
   l      l   
 Y  l    ]  l   
 i  l    m  l   
 �  l      l   
 �  l    �  l   
 -  l    1  l   
 =  l    A  l   
 M  l    Q  l   
 ]  l    a  l   
 m  l    q  l   
 �  l    �  l   
 H塡$H塼$H墊$ UAVAWH峫$笻侅�   H�    H3腍塃?H嬹f荄$(  E3鯨塼$ L�
    L�    H峌稨婭8�    A嬛H峂縃;萾H�L�0H婲H塚H吷tH��P怘婱稨吷tL塽稨��P怢塽�W�3�E�E颒塃�L�=    L墊$ L�
    峆D岪H峂哞    怢塽H婲H塎稨吷tH��P怢墊$ L�
    �   D岯鼿峂�    怚嬈H塃7H媇稨呟t
H�H嬎�PH婨7H峿H�<荋9t%H呟t
H�H嬎�P怘�H�H吷tH��P怘婨7H�繦塃7H呟t
H�H嬎�P怚嬣 I嬛H岴H肏峂荋;萾H�L�0H婰逪塗逪吷tH��P怘兠H凔(r荋婨7H塃M嬒�   D岯鼿峂�    怣嬒�   D岯鵋峂疯    H媈H婱譎;藅"H呟t
H�H嬎�PH婱譎塢譎吷tH��P怘�H�L岴譎峌��8  I嬛H峂螲;萾H�L�0H婲H塚H吷tH��P怘婱稨吷tL塽稨��P怣嬒�   D岯鼿峂哞    怘婱譎吷tL塽譎��P怘婱?H3惕    L崪$�   I媅(I媠0I媨8I嬨A_A^]�#   m   C   c   J   f   W   t    �   h    �   g    �   b    �   g       b    �  c    �  c    j  c    �  �       �   %
  F G            �  .   �  �        �AccumulationPass::CreatePipeline 
 >SG   this  AJ        1  AL  1     n >�%    pipelineDesc  DP    M        �  俹 M        �  俹GB
 >k$    temp  AJ  s      AJ �      N N M        �  侲 M        �  侲GB
 >�&    temp  AJ  I      AJ Y      B0   R    Z  Bx      �  N N M        �  %� M        �  �9 M        �  �9
 >�&    temp  AJ  5      AJ E      N N M        �  �1 >�&    tmp  AK  "    "  AK E        N M        �  �C	 M        �  �+ N N N M        �  佨!
 M        �  � M        �  �
 >k$    temp  AJ  �    &    AJ       N N M        �  侞 >k$    tmp  AI  �    �  N M        �  侀 M        k  侀#
 N N N M        �  .乸 M        �  亾 M        �  亾 N N M        �  亯 N M        �  乸C M        �  亙 N N N# M        �  �G
 >�%    i  AI      ]  M        �  乕 M        �  乕	
 N N M        �  �" M        j  �*
 >b%   this  AM  &    } M        �  丏 M        �  丏 N N M        �  �> N M        �  �/ M        �  �/#	 N N N N M        �  � M        �  �#
 N N N M        �  �� M        �  ��# N N M        �  ��2 N M        �  �� N M        �  �� M        �  ��GB
 >k$    temp  AJ  �       AJ �     3  B0   �     L  B0  [     Q N N M        �  %[ M        �  u M        �  u
 >k$    temp  AJ  q       AJ �       N N M        �  m >k$   tmp  AK  j       AK �     ?    C       ^       C      m     S   '   N M        �  [C	 M        �  g N N N Z   �!  �!   �                    A � h&   �  �           
  �  �  �  �  �  �  �  �  �  �  �  �  n  |  }  �  �  �  �  �  �  �  �  �  �  �  �  �  j  k  �  
 :�   O  �   SG  Othis  P   �%  OpipelineDesc  9}       E   9�       E   9�       E   9      E   9:      E   9L      E   9f      E   9�      E   9�      E   9      E   9      �(   9A      E   9U      E   9      E   O   �   P           �  P     D       -  �1   .  ��   0  ��   1  ��  2  �  3  �o  4  ��   �   U F                                �`AccumulationPass::CreatePipeline'::`1'::dtor$1  >�%    pipelineDesc  EN  P                                  �  O,   k    0   k   
 k   k    o   k   
 {   k       k   
 �   k    �   k   
 �   k      k   
 X  k    \  k   
 h  k    l  k   
 x  k    |  k   
 �  k    �  k   
 �  k    �  k   
   k    	  k   
 E  k    I  k   
 U  k    Y  k   
   k      k   
   k       k   
 X  k    \  k   
 �  k    �  k   
 &  k    *  k   
 �  k    �  k   
 �  k    �  k   
 �  k    �  k   
 �  k       k   
 f  k    j  k   
 v  k    z  k   
 �  k    �  k   
 �  k    �  k   
 �  k    �  k   
 �  k    �  k   
 Q	  k    U	  k   
 a	  k    e	  k   
 q	  k    u	  k   
 �	  k    �	  k   
 �	  k    �	  k   
 �	  k    �	  k   
 �	  k    �	  k   
 �	  k    �	  k   
 �	  k    �	  k   
 �	  k    �	  k   
 �	  k    �	  k   
 
  k    
  k   
 
  k    
  k   
 !
  k    %
  k   
 <
  k    @
  k   
 �
  w    �
  w   
   w      w   
 H崐P   �       i    H嬆USVWAVH崹H��H侅�  )p�)x窰�    H3腍墔`  H�L嬺H孂H�    I嬑I嬹I嬝�怭  H�H峌蠬嬎�P H峌蠬嬑�@0H��D$X�P H婳0�@8H��D$X�P �    H峌繦嬋(�莆U3荔\螇D$@(�(�破�砌\�七U�L$(�\�(鼠D$,W缷驢*缷AH嬎�\$ �^�W荔H*繦��L$0�^�(�魄��\求T$4�D$$�惱   H荄$p   � �D$8�H3荔呧  H塃窰婫 H塂$HH婫�D$@D$HH塃��L$<L$XE�D$hM�E↖�H峌�I嬑�愗   I�H峊$ A�$   I嬑�惃   I�H峌繦嬑H嫺�   H��惛   H嬑婸+P岯檭��H�聋L媯�   H峊$HA�蠥�   D嬅I嬑婸+岯檭�辛��譏�I嬑�怷  H媿`  H3惕    (�$�  (�$p  H伳�  A^_^[]�"   m   <   i   �   l   %  �       �   �  > G            G  0             �AccumulationPass::Render 
 >SG   this  AJ        9  AM  9     � > )   commandList  AK        6  AV  6      >欶   sourceView  AI  I     � AP        I  >欶   upscaledView  AL  F     � AQ        F  >@    accumulationWeight  EO  (           D�   >/   upscaledViewport  C�       �     �  BH   �     �  >I    constants  D     >/   sourceViewport  C�       k     R  BH   t       >�&    state  D�    >�    inputDesc  AJ  �     J  M        �  �  N M        �  侞
 >C   this  AH  �      N M        �  
佇
 N M        �  伿
 >C   this  AH  �      N M        �  	�(* M        �  	�(* N N M        �  丂 N M        �  ��
 N M        �  � N M        �  ��	 N M        �  �� N �          (          A V h   �  �  �  �  �          �  �  �  �  �  �  �  x  y  �    
 :`  O  �  SG  Othis  �   )  OcommandList  �  欶  OsourceView  �  欶  OupscaledView  �  @   OaccumulationWeight  H   /  OupscaledViewport      I  Oconstants  H   /  OsourceViewport  �   �&  Ostate  9I       t)   9Y       塅   9t       塅   9�       �   9"      揊   9�      U)   9�      H)   9�      扚   9�      扚   9      V)   9      *)   O   �   (          G  P  "         L  �0   M  �O   O  �\   P  �w   R  ��   V  ��   X  ��   U  ��   T  ��   U  ��   T  ��   U  ��   W  ��   U  ��   W  ��   U  ��   W  ��   V  ��   X  �  W  �  V  �  W  �"  X  �(  \  �1  X  �@  [  �B  Y  �N  \  �W  ]  ��  ^  ��  `  ��  b  �  g  �  h  �,   m    0   m   
 c   m    g   m   
 s   m    w   m   
 �   m    �   m   
 �   m    �   m   
 �   m    �   m   
 �   m    �   m   
   m    	  m   
   m      m   
 F  m    J  m   
 }  m    �  m   
 �  m    �  m   
 �  m    �  m   
 �  m    �  m   
 "  m    &  m   
 m  m    q  m   
 �  m    �  m   
 �  m      m   
 
  m      m   
   m    !  m   
 -  m    1  m   
 =  m    A  m   
 M  m    Q  m   
 ]  m    a  m   
 m  m    q  m   
 }  m    �  m   
 �  m    �  m   
 �  m    �  m   
 �  m    �  m   
  d T 4 2p    H           �       �       �     B                    �        "           �       �       �    h           �       �           d    2 B                    �        "           �       �       �    h           �       �           d    2 20                  �        ?           �       �       �    h           �       �           d    :8 ( 4'"p`P          	     �       �        #          �       �       �    (           �       �        �6    \    �6    .    .    .    .    .    .    .       s       d    
   n       x       y       z    "   {    '   |    ,   }    1   ~    6   d    ��R4�D u ��.  t  d  4   ��P          �      �        �        �          �       �       �    (           �       �        2    �6       d       i    
   d    &� N0 f4F,F<bXD$$P0T 6	 %t%%d$%4#% P          �      �       �        �          �       �       �    (           �       �           d    
: MD "0 0 x7 h8 r 	�p`0P      `     �        G          �       �       �     B                           "           �       �       �    h                           d    2 B                           "           �       �          h                           d    2 B                    #       "           �       �          h           &      )          d    2 B                    2       "           �       �       ,   h           5      8          d    2 B                    A       "           �       �       ;   h           D      G          d    2 20               �       �       J   ! t               �       �       J      E           �       �       P   !                 �       �       J   E   K           �       �       V   -    ����    ����        ��������main app/AccumulationPass.hlsl Accumulation   �?  �?  �?  �?  �?�"+�a榓n儡j�%玌刦k網叜月o"03>飖9屓産KrHq�d�*壛�4蝯忣�$phxl8s|積�/胎瀮b=迢翸�垤g"$呣-泠}犦�53Ft∠ .hJ,p柜|櫶8W肆峖=f瓵y �<�澭�'項j^觻 &0K�'項jX
P�2禸滅'項j\僨'	BsO�'項j逯@庫脚冂'項j pk�'項j瞢&Q栩v嗙'項j賄翣臡素$愜w獛啯[跼椛TD鲢�6�<#�(棙� �蹰k�%禾)課�1袺Y'�4�%+齽�+央�倌七{�>|Qe圑&茤3�;ruh糺hg&9Hz粌F{'yZ祼垩寯啦帕�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埳S�8萀D罵��$m翲咞taR�,F_棢杻#Q惕屖碋箎Gf "衅1�8�7憻赁�dd�a�:`(z?;7盎怯蠒-K砦�In皕鑨eYQh麡燛<�dd�a�:湾�蟜:企�*硳OD 燡U齡攔+3^腦黌odd�a�:_棢杻#Q鯶~fkW�>vb侊穄騅l壻了5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G堧嘕-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛K��H$樐蜆{�h偛�洁4�7(朹L�^笵A傮龌斱/x�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       P                .debug$S       �              .debug$T       l                 .rdata         @       5G碚                         7          .text$mn              �邆     .debug$S                    .text$mn       #     啣J�     .debug$S       �  �           .text$x     	         �/�    .text$x     
         莠�    .text$x              O~性    .text$x              哈U;    .text$x     
         詜�    .text$x              �/?    .text$x              y{|�    .text$x              忹�<    .text$x              鈟    .text$mn              恶Lc     .debug$S       �              .text$mn       "       坼	     .debug$S       �             .text$mn       "       坼	     .debug$S       �             .text$mn       "       坼	     .debug$S       �             .text$mn       "       坼	     .debug$S       �             .text$mn       "       坼	     .debug$S       �             .text$mn       "       坼	     .debug$S       �             .text$mn        "       坼	     .debug$S    !   �              .text$mn    "   K       }'     .debug$S    #   �         "    .text$mn    $   ?      劸惂     .debug$S    %   \         $    .text$mn    &   H       襶.      .debug$S    '   �         &    .text$mn    (   �     P     .debug$S    )   �  2       (    .text$mn    *   �  
   "剑�     .debug$S    +   8  Z       *    .text$x     ,         喣�,*    .text$mn    -   G     眡策     .debug$S    .   �  :       -        \       &        x                �                �                �               �               
              E              }      $        �                    *        .      (        p      -        �              �              /              d              �               �      "                       �      	        =      
        �      ,        �              V              �      
        6              �                            �              �               		               	           __chkstk             /	           memcpy           memset           $LN13       &    $LN10           $LN10           $LN18       $    $LN139          $LN188      *    $LN86       (    $LN52       -    $LN10           $LN10           $LN10           $LN10           $LN10            $LN18       "    .xdata      /          F┑@&        G	      /    .pdata      0         X賦�&        k	      0    .xdata      1         /
�        �	      1    .pdata      2         +eS�        �	      2    .xdata      3   	      �#荤        �	      3    .xdata      4         j        :
      4    .xdata      5          3狷         {
      5    .xdata      6         /
�        �
      6    .pdata      7         +eS�        �
      7    .xdata      8   	      �#荤        5      8    .xdata      9         j        w      9    .xdata      :          3狷         �      :    .xdata      ;         蚲7M$              ;    .pdata      <         袮韁$        /      <    .xdata      =   	      �#荤$        \      =    .xdata      >         j$        �      >    .xdata      ?          愔
~$        �      ?    .xdata      @          m
G        �      @    .pdata      A         (F        [
      A    .xdata      B   	      � )9        �
      B    .xdata      C   :      鍠�        .      C    .xdata      D          >�>�        �      D    .voltbl     E          醭P    _volmd      E    .xdata      F   (      議w�*        
      F    .pdata      G         赉}j*        <      G    .xdata      H   	      � )9*        m      H    .xdata      I         讹屠*        �      I    .xdata      J   '       殸蹤*        �      J    .xdata      K   $      逬'�(              K    .pdata      L         6)(�(        Y      L    .xdata      M   	      � )9(        �      M    .xdata      N         j(        �      N    .xdata      O          锻`�(        @      O    .xdata      P   $      s誌A-        �      P    .pdata      Q         葳谴-        �      Q    .xdata      R         /
�        C      R    .pdata      S         +eS�        |      S    .xdata      T   	      �#荤        �      T    .xdata      U         j        �      U    .xdata      V          3狷         0      V    .xdata      W         /
�        k      W    .pdata      X         +eS�        �      X    .xdata      Y   	      �#荤        �      Y    .xdata      Z         j        2      Z    .xdata      [          3狷         |      [    .xdata      \         /
�        �      \    .pdata      ]         +eS�        �      ]    .xdata      ^   	      �#荤        9      ^    .xdata      _         j        x      _    .xdata      `          3狷         �      `    .xdata      a         /
�        �      a    .pdata      b         +eS�        6      b    .xdata      c   	      �#荤        o      c    .xdata      d         j        �      d    .xdata      e          3狷         �      e    .xdata      f         /
�         )      f    .pdata      g         +eS�         c      g    .xdata      h   	      �#荤         �      h    .xdata      i         j         �      i    .xdata      j          3狷                j    .xdata      k          （亵"        V      k    .pdata      l         � �"        �      l    .xdata      m         范^�"        �      m    .pdata      n         鳶�"              n    .xdata      o         @鴚`"        c      o    .pdata      p         [7�"        �      p    .voltbl     q          飾殪"    _volmd      q    .data       r           烀�          �      r             r    .rdata      s          旲^         F      s    .rdata      t          ,鶡�         ]      t    .rdata      u   
       �*�         �      u    .rdata      v          v靛�         �      v        �           .rdata      w          _�         �      w    _fltused         .chks64     x   �                �  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z __std_terminate ??0BindingSetItem@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1ComputePipelineDesc@nvrhi@@QEAA@XZ ??0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z ?CreatePipeline@AccumulationPass@@QEAAXXZ ?CreateBindingSet@AccumulationPass@@QEAAXPEAVITexture@nvrhi@@00@Z ?Render@AccumulationPass@@QEAAXPEAVICommandList@nvrhi@@AEBVIView@engine@donut@@1M@Z ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ?CreateShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ?dtor$0@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$1@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$1@?0??CreatePipeline@AccumulationPass@@QEAAXXZ@4HA ?dtor$2@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$3@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$4@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$5@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$6@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$7@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA ?dtor$8@?0???0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z@4HA __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $pdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $unwind$??0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $pdata$??0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $cppxdata$??0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $stateUnwindMap$??0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $ip2state$??0AccumulationPass@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VShaderFactory@engine@donut@@@std@@@Z $unwind$?CreatePipeline@AccumulationPass@@QEAAXXZ $pdata$?CreatePipeline@AccumulationPass@@QEAAXXZ $cppxdata$?CreatePipeline@AccumulationPass@@QEAAXXZ $stateUnwindMap$?CreatePipeline@AccumulationPass@@QEAAXXZ $ip2state$?CreatePipeline@AccumulationPass@@QEAAXXZ $unwind$?CreateBindingSet@AccumulationPass@@QEAAXPEAVITexture@nvrhi@@00@Z $pdata$?CreateBindingSet@AccumulationPass@@QEAAXPEAVITexture@nvrhi@@00@Z $cppxdata$?CreateBindingSet@AccumulationPass@@QEAAXPEAVITexture@nvrhi@@00@Z $stateUnwindMap$?CreateBindingSet@AccumulationPass@@QEAAXPEAVITexture@nvrhi@@00@Z $ip2state$?CreateBindingSet@AccumulationPass@@QEAAXPEAVITexture@nvrhi@@00@Z $unwind$?Render@AccumulationPass@@QEAAXPEAVICommandList@nvrhi@@AEBVIView@engine@donut@@1M@Z $pdata$?Render@AccumulationPass@@QEAAXPEAVICommandList@nvrhi@@AEBVIView@engine@donut@@1M@Z $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIComputePipeline@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingSet@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VISampler@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VShaderFactory@engine@donut@@@std@@QEAA@XZ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_04GHJNJNPO@main@ ??_C@_0BK@MLIMLIOB@app?1AccumulationPass?4hlsl@ ??_C@_0N@CCKBMGJF@Accumulation@ __real@3f800000 __security_cookie __xmm@3f8000003f8000003f8000003f800000 