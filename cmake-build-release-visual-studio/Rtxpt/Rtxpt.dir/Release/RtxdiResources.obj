d喦 飯�8 w      .drectve        P  ,               
 .debug$S        D�  |   �        @ B.debug$T        l                @ B.rdata          <   |             @ @@.text$mn        :   � �         P`.debug$S                   @B.text$mn        0   � �         P`.debug$S        �  � �        @B.text$mn        C   ]         P`.debug$S          � �*     \   @B.text$x         (   ]. �.         P`.text$mn           �. �.         P`.debug$S        @  �. 0     
   @B.text$mn        N  f0 �>     A    P`.debug$S        鴖  >A 6�     �  @B.text$x            
� �         P`.text$x            $� 4�         P`.text$x            >� N�         P`.text$x            X� h�         P`.text$x            r� 傑         P`.text$x            屲 溰         P`.text$x             盾         P`.text$x            儡 熊         P`.text$x            谲 贶         P`.text$x            糗 �         P`.text$x            � �         P`.text$x            (� 4�         P`.text$x            >� J�         P`.text$x            T� `�         P`.text$x            j� v�         P`.text$x            �� 屳         P`.text$x            栞 ⑤         P`.text$x             篙         P`.text$x            螺 屋         P`.text$x            剌 漭         P`.text$x            钶          P`.text$mn        <   � @�         P`.debug$S        0  ^� 庍     
   @B.text$mn        <   蜻 .�         P`.debug$S        L  L� 樶     
   @B.text$mn        !    �         P`.debug$S        <  1� m�        @B.text$mn        2   ┿ 坫         P`.debug$S        <  镢 +�        @B.text$mn        "   ｅ              P`.debug$S        �  佩 Q�        @B.text$mn        "   耒              P`.debug$S        �  � 熼        @B.text$mn        W   ?� 栮         P`.debug$S        4   揄        @B.text$mn        `   鲱 V�         P`.debug$S        �  j� .�        @B.text$mn        `   怛 B�         P`.debug$S        �  V� �        @B.text$mn           姻 弼         P`.debug$S        �    蓣        @B.text$mn           � �         P`.debug$S        �   ,� �        @B.text$mn        B   H� 婛         P`.debug$S                    @B.text$mn        B   潸 &�         P`.debug$S          D� T�        @B.text$mn        B   慄 尹         P`.debug$S        �   瘘 忑        @B.text$mn        H   (�              P`.debug$S        �  p� 4         @B.text$mn        �   L $         P`.debug$S        (  L t     6   @B.text$x            �
 �
         P`.text$mn            �
 �
         P`.debug$S        �   �
 �        @B.text$mn           � �         P`.debug$S        �   	 �        @B.text$mn           � 

         P`.debug$S        �   
         @B.text$mn        `  B �         P`.debug$S        �  � r     B   @B.text$mn        =    C         P`.debug$S        �  W #        @B.text$mn        '   O              P`.debug$S          v �         @B.text$mn           �  �          P`.debug$S        �   �  �!        @B.xdata             �!             @0@.pdata             " "        @0@.xdata             ="             @0@.pdata             E" Q"        @0@.xdata             o"             @0@.pdata             {" �"        @0@.xdata             �"             @0@.pdata             �" �"        @0@.xdata             �"             @0@.pdata             �" �"        @0@.xdata             
#             @0@.pdata             # !#        @0@.xdata             ?#             @0@.pdata             K# W#        @0@.xdata             u#             @0@.pdata             }# �#        @0@.xdata             �#             @0@.pdata             �# �#        @0@.xdata             �#             @0@.pdata             �# �#        @0@.xdata             $ +$        @0@.pdata             I$ U$        @0@.xdata             s$ �$        @0@.pdata             �$ �$        @0@.xdata             �$ �$        @0@.pdata             �$ 	%        @0@.xdata             '%             @0@.pdata             /% ;%        @0@.xdata             Y%             @0@.pdata             a% m%        @0@.xdata             �% �%        @0@.pdata             �% �%        @0@.xdata          	   �% �%        @@.xdata             �% �%        @@.xdata             &             @@.xdata             	& &        @0@.pdata             -& 9&        @0@.xdata          	   W& `&        @@.xdata             t& z&        @@.xdata             �&             @@.xdata          $   �& �&        @0@.pdata             �& �&        @0@.xdata          	   �& �&        @@.xdata          �   ' �'        @@.xdata          S   �(             @@.xdata             ?) [)        @0@.pdata             o) {)        @0@.xdata          	   �) �)        @@.xdata             �) �)        @@.xdata             �)             @@.xdata             �)             @0@.pdata             �) �)        @0@.xdata             �)             @0@.pdata             * *        @0@.xdata             0*             @0@.pdata             8* D*        @0@.xdata             b* z*        @0@.pdata             �* �*        @0@.xdata          
   �* �*        @@.xdata             �*             @@.xdata             �* �*        @@.xdata             �* �*        @@.xdata             	+             @@.xdata             +             @0@.pdata             + $+        @0@.voltbl            B+               .xdata             C+             @0@.pdata             K+ W+        @0@.xdata             u+             @0@.pdata             �+ �+        @0@.xdata             �+             @0@.pdata             �+ �+        @0@.rdata             �+ �+        @@@.rdata             ,             @@@.rdata             %, =,        @@@.rdata             [, s,        @@@.rdata             �,             @@@.xdata$x           �, �,        @@@.xdata$x           �, �,        @@@.data$r         /   - ?-        @@�.xdata$x        $   I- m-        @@@.data$r         $   �- �-        @@�.xdata$x        $   �- �-        @@@.data$r         $   �- .        @@�.xdata$x        $   . 9.        @@@.rdata             M.             @@@.data               ].             @ @�.rdata             }.             @@@.rdata             �.             @@@.rdata          
   �.             @@@.rdata             �.             @@@.rdata             �.             @@@.rdata             �.             @@@.rdata             �.             @@@.rdata              /             @@@.rdata             /             @@@.rdata             %/             @@@.rdata             7/             @@@.rdata             E/             @@@.rdata$r        $   U/ y/        @@@.rdata$r           �/ �/        @@@.rdata$r           �/ �/        @@@.rdata$r        $   �/ �/        @@@.rdata$r        $   0 '0        @@@.rdata$r           E0 Y0        @@@.rdata$r           c0 w0        @@@.rdata$r        $   �0 �0        @@@.rdata$r        $   �0 �0        @@@.rdata$r           1 1        @@@.rdata$r           #1 ?1        @@@.rdata$r        $   ]1 �1        @@@.rdata             �1             @P@.debug$S        4   �1 �1        @B.debug$S        4   �1 !2        @B.debug$S        @   52 u2        @B.chks64         8  �2              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   �  \     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\RtxdiResources.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $rtxdi  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Weak_order  $literals  $string_literals  $string_view_literals 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $_Binary_hypot  $nvrhi  $rt 
 $cluster  $ObjectTypes  $donut  $math 	 $colors   �   慚  s :   std::_Trivial_cat<unsigned char,unsigned char,unsigned char &&,unsigned char &>::_Same_size_and_compatible p :   std::_Trivial_cat<unsigned char,unsigned char,unsigned char &&,unsigned char &>::_Bitcopy_constructible m :   std::_Trivial_cat<unsigned char,unsigned char,unsigned char &&,unsigned char &>::_Bitcopy_assignable ) <   donut::math::frustum::numCorners � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable ; :   std::atomic<unsigned __int64>::is_always_lock_free E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment ) <   nvrhi::ObjectTypes::SharedHandle - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + <  �  nvrhi::ObjectTypes::VK_Sampler 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule . <  �
  nvrhi::ObjectTypes::VK_RenderPass / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo 6 :   std::_Iterator_base0::_Unwrap_when_unverified C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable 7 :   std::_Iterator_base12::_Unwrap_when_unverified d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable 1 <   donut::math::vector<unsigned int,2>::DIM 8 :   std::atomic<unsigned long>::is_always_lock_free # <   kPolymorphicLightTypeShift " <   kPolymorphicLightTypeMask . <  �   kPolymorphicLightShapingEnableBit 1 <  �    kPolymorphicLightIesProfileEnableBit - ;  �   羕PolymorphicLightMinLog2Radiance - ;  �   BkPolymorphicLightMaxLog2Radiance / <  � nvrhi::rt::cluster::kClasByteAlignment . <   nvrhi::rt::cluster::kClasMaxTriangles - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex . :    std::integral_constant<bool,0>::value ) <   donut::math::vector<bool,2>::DIM ) <   donut::math::vector<bool,3>::DIM ) <   donut::math::vector<bool,4>::DIM . :   std::integral_constant<bool,1>::value ( <   donut::math::vector<int,2>::DIM E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos J _   std::allocator<unsigned char>::_Minimum_asan_allocation_alignment D _   ��std::basic_string_view<char,std::char_traits<char> >::npos : _    std::integral_constant<unsigned __int64,0>::value Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; _  �std::_Floating_type_traits<double>::_Exponent_mask J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices # �        nvrhi::AllSubresources . <   RtxdiResources::c_NumReservoirBuffers 0 <   RtxdiResources::c_NumGIReservoirBuffers $ �    std::strong_ordering::equal ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong           nvrhi::EntireBuffer � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable A _   std::allocator<char>::_Minimum_asan_allocation_alignment ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size : _   std::integral_constant<unsigned __int64,2>::value T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos  H    std::denorm_absent  H   std::denorm_present  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity & :    std::_Num_base::has_quiet_NaN * :    std::_Num_base::has_signaling_NaN # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix ) H   std::_Num_float_base::has_denorm + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed - :   std::_Num_float_base::is_specialized 4 _  @ _Mtx_internal_imp_t::_Critical_section_size * K   std::_Num_float_base::round_style 5 _   _Mtx_internal_imp_t::_Critical_section_align $ �   std::_Num_float_base::radix + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits * �   std::numeric_limits<bool>::digits + :    std::_Aligned<64,8,short,0>::_Fits ) :   std::_Aligned<64,8,int,0>::_Fits - :   std::numeric_limits<char>::is_signed - :    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 :   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 :   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 :   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 1 :   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits % �    _Atomic_memory_order_relaxed 0 �  	 std::numeric_limits<char32_t>::digits10 % �   _Atomic_memory_order_consume % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 , :   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 :   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos    �   2o  a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment * <   donut::math::vector<float,3>::DIM ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment + �!        nvrhi::rt::c_IdentityTransform B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity * <   donut::math::vector<float,4>::DIM a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size * 錏        donut::math::lumaCoefficients g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size * <   donut::math::vector<float,2>::DIM ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos - <   rtxdi::c_NumReSTIRDIReservoirBuffers / :   std::atomic<long>::is_always_lock_free  ;  ��I@donut::math::PI_f " s  
�-DT�!	@donut::math::PI_d ! ;  ��7�5donut::math::epsilon " ;  �  �donut::math::infinity  ;  �  �donut::math::NaN T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment  �  _CatchableType " �  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  Q�  ReSTIRDI_BufferIndices  酕  PolymorphicLightInfo & �  $_TypeDescriptor$_extraBytes_28    _Smtx_t  #   rsize_t    _TypeDescriptor  G  PathTracerCameraData 	 I  tm % �  _s__RTTICompleteObjectLocator2 A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType ' !�  rtxdi::RISBufferSegmentAllocator & N�  rtxdi::ReSTIRDIStaticParameters  H�  rtxdi::ReSTIRDIContext & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & �  $_TypeDescriptor$_extraBytes_20  貴  LightingControlData  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16 ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty 6 刕  std::allocator_traits<std::allocator<char8_t> > " 媂  std::_Char_traits<char,int>  鶐  std::_Value_init_tag  "   std::_Atomic_counter_t  N  std::_Num_base K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > # �  std::numeric_limits<char8_t> ) 怷  std::_Narrow_char_traits<char,int>  j  std::hash<float> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc>  P  std::_Num_int_base $ 諑  std::allocator<unsigned char> / 揚  std::_Conditionally_enabled_hash<bool,1>  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > 6 餧  std::allocator_traits<std::allocator<wchar_t> >  `  std::bad_cast     std::_Compare_t " u  std::numeric_limits<double>  �  std::__non_rtti_object ( @  std::_Basic_container_proxy_ptr12 1    std::array<nvrhi::FramebufferAttachment,8>  q  std::_Num_float_base 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety ! 頬  std::char_traits<char32_t>  �  std::_Compare_ncmp   R  std::numeric_limits<bool> # *Y  std::_WChar_traits<char16_t> T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1> % L  std::_One_then_variadic_args_t W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   闿  std::char_traits<wchar_t> ; 閹  std::_Vector_val<std::_Simple_types<unsigned char> > @ 菐  std::vector<unsigned char,std::allocator<unsigned char> > V 枎  std::vector<unsigned char,std::allocator<unsigned char> >::_Reallocation_policy   �  std::pmr::memory_resource ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  砞  std::false_type  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , n  std::numeric_limits<unsigned __int64> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16> % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> % 5  std::array<nvrhi::Viewport,16>    std::_Iterator_base12 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > @ nY  std::_Default_allocator_traits<std::allocator<char32_t> >  �?  std::allocator<char32_t> $ �  std::_Atomic_integral<long,4>  桺  std::hash<bool> 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>  �  std::hash<long double> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy   �  std::_Comparison_category U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0  w  std::hash<double> O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> / Y  std::_Char_traits<char32_t,unsigned int> ( �)  std::hash<nvrhi::FramebufferInfo> % 砞  std::integral_constant<bool,0>  �  std::bad_exception & 肐  std::_Zero_then_variadic_args_t  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>   玗  std::char_traits<char8_t> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > ! s  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long> % 盤  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " VP  std::hash<unsigned __int64>  '  std::exception_ptr  �  std::strong_ordering % �  std::_Itraits_pointer_strategy C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > )   std::hash<enum nvrhi::BlendFactor> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t> T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 齖  std::allocator_traits<std::allocator<char32_t> >  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 Y  std::_Char_traits<char16_t,unsigned short> $ �)  std::hash<nvrhi::BufferRange> ! 郳  std::char_traits<char16_t>  �  std::tuple<>  �  std::_Container_base12 ) X  std::numeric_limits<unsigned char> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type   d  std::numeric_limits<long> " 衆  std::initializer_list<char>  �  std::_Invoker_strategy $ �  std::_Default_allocate_traits 3 瞈  std::allocator_traits<std::allocator<char> > ! `  std::numeric_limits<short> . S  std::allocator<nvrhi::rt::GeometryDesc> ; �  std::basic_string_view<char,std::char_traits<char> > C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock  �  std::bad_alloc B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> # f  std::numeric_limits<__int64>  h  std::memory_order # �  std::_Atomic_storage<long,4> # *  std::hash<nvrhi::BlendState>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1> < {X  std::_Default_allocator_traits<std::allocator<char> > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >   D[  std::forward_iterator_tag   
  std::bad_array_new_length E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >  �  std::_Container_proxy  ^  std::nested_exception  �  std::_Distance_unknown ( j  std::numeric_limits<unsigned int>  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' w  std::numeric_limits<long double> E I�  std::_Default_allocator_traits<std::allocator<unsigned char> >  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �  std::_Compare_eq  �?  std::allocator<char16_t> ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy < O�  std::allocator_traits<std::allocator<unsigned char> > ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> * 糛  std::_String_constructor_concat_tag  �8  std::allocator<char>    std::nullptr_t . 鏧  std::_Char_traits<char8_t,unsigned int> & H[  std::random_access_iterator_tag ; RP  std::_Conditionally_enabled_hash<unsigned __int64,1>  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  b  std::wstring ' V  std::numeric_limits<signed char>  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  �  std::_Literal_zero $ cP  std::hash<nvrhi::IResource *> 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char>  R[  std::char_traits<char>  �  std::_Unused_parameter h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8> \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty ' �)  std::hash<nvrhi::BindingSetItem> " 甔  std::_WChar_traits<wchar_t> E -�  std::_Uninitialized_backout_al<std::allocator<unsigned char> >  "J  std::streampos  B[  std::input_iterator_tag r 軒  std::_Compressed_pair<std::allocator<unsigned char>,std::_Vector_val<std::_Simple_types<unsigned char> >,1> ' 綪  std::hash<enum nvrhi::ColorMask> O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  �  std::partial_ordering . <#  std::array<nvrhi::BindingLayoutItem,16> $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int> 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling $ �(  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets  s  nvrhi::ResourceStates  &  nvrhi::GraphicsState / W  nvrhi::static_vector<nvrhi::Viewport,16> ! v  nvrhi::SharedResourceFlags  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! �(  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  �(  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # �(  nvrhi::DescriptorTableHandle  �(  nvrhi::TimerQueryHandle   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �(  nvrhi::HeapHandle # �(  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  �(  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  賧  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  lH  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 賧  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  �(  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 繸  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   �(  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �(  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # �(  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  �  nvrhi::CpuAccessMode  )  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable * lH  nvrhi::RefCountPtr<nvrhi::ITexture>  �&  nvrhi::ComputeState  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery   �  __RTTIBaseClassDescriptor 
    _off_t    stat  t   int32_t  Z  timespec 
 !   _ino_t  �  RtxdiResources , �  ReSTIRDI_TemporalResamplingParameters  !   uint16_t  WI  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3  EG  donut::math::float2 # 錏  donut::math::vector<float,3>  u   donut::math::uint  /F  donut::math::plane # F  donut::math::vector<float,4>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes % 慫  donut::math::matrix<float,3,4>  (G  donut::math::uint2  F  donut::math::float4 % WI  donut::math::matrix<float,4,4> # 揈  donut::math::affine<float,3> " *E  donut::math::vector<bool,2>  GF  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> * (G  donut::math::vector<unsigned int,2> M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>    _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> + �  ReSTIRDI_SpatialResamplingParameters    terminate_handler  �  _s__RTTIBaseClassArray 
 Y  ldiv_t - �  $_s__RTTIBaseClassArray$_extraBytes_24 ) �  ReSTIRDI_InitialSamplingParameters  �  _CatchableTypeArray     ptrdiff_t  
  _stat64i32  �  _PMD      uint8_t  ,  type_info ' �  _s__RTTIClassHierarchyDescriptor  t   errno_t  \  _lldiv_t  C  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  G  _s__ThrowInfo  �  __RTTIBaseClassArray  �  __crt_locale_data_public - �  $_s__CatchableTypeArray$_extraBytes_24 ! �  ReSTIRDI_ShadingParameters % �  __RTTIClassHierarchyDescriptor & Q�  RTXDI_ReservoirBufferParameters 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  鏔  PolymorphicLightInfoEx  �  FILE 3 �  __vcrt_va_list_is_reference<wchar_t const *>    mbstate_t  ?  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray  騀  PolymorphicLightInfoFull 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  \  lldiv_t  M�  RTXDI_RuntimeParameters  Y  _ldiv_t  [  _timespec64  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers   �   �      G�膢刉^O郀�/耦��萁n!鮋W VS  @    o�椨�4梠"愜��
}z�$ )鰭荅珽X  �    黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �    ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L      z�漼嶜q镛�F苜�:壗Wア燤PEx�  9   ,┭0甗�+天没2骟Bw蛁�%"艠E�  q   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮      d蜯�:＠T邱�"猊`�?d�B�#G騋  M   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡     仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  Y   +4[(広
倬禼�溞K^洞齹誇*f�5  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   泽閇�R鯄呙+困胢p=�R刐鉍籫�8[  J    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥     �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  a   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�     绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  Y   歚W%虴�[�,莶CKF�AZⅰq恶�4�  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅     鏀q�N�&}
;霂�#�0ncP抝  E   �"睱建Bi圀対隤v��cB�'窘�n  �   R冈悚3Y	�1P#��(勁灱�涰n跲
  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎     鉹�訨)�\馡冷踹o乑�!旱�?  @   D���0�郋鬔G5啚髡J竆)俻w��  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  �   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  	   蜅�萷l�/费�	廵崹
T,W�&連芿  ?	   c�#�'�縌殹龇D兺f�$x�;]糺z�  �	   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �	   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  
   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  \
   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �
   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �
   交�,�;+愱`�3p炛秓ee td�	^,     樁*披B憱祯敛鍭�7� T癀n烬
雚臁  o   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  &   �	玮媔=zY沚�c簐P`尚足,\�>:O  g   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��  �   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  9
   o藾錚\F鄦泭|嚎醖b&惰�_槮  x
   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �
   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`     蜞憚>�/�狌b替T蕚鎸46槹n�洜9  h   S仄�p�'�/2H��g'浗o$鏊^藵捯�  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   觑v�#je<d鼋^r
u��闑鯙珢�     Fp{�悗鉟壍Au4DV�`t9���&*I  L   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  �   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �   齶`蟴妳駬嚢锟甕鎐$鲇晡b#夺炭  �   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  ;   V8追i顚�^�k细�;>牧惺扴	�\s  y   逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �   檒Gq$�#嗲RR�錨账��K諻刮g�   '   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  f   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   0筍N孭)�餂;檒狍�%"~+台@��     チ畴�
�&u?�#寷K�資 +限^塌>�j  Q   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗     V� c鯐鄥杕me綻呥EG磷扂浝W)  e   穫農�.伆l'h��37x,��
fO��  �   5�\營	6}朖晧�-w氌rJ籠騳榈  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  )   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  f   �*o驑瓂a�(施眗9歐湬

�  �   �0�*е彗9釗獳+U叅[4椪 P"��  �    I嘛襨签.濟;剕��7啧�)煇9触�.  )   �=蔑藏鄌�
艼�(YWg懀猊	*)  j   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  �   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  <   齝D屜u�偫[篔聤>橷�6酀嘧0稈  z   _O縋[HU-銌�鼪根�鲋薺篮�j��  �   蜅駠x馘Qf^��=夸餕V�G窄憫尢25     l籴靈LN~噾2u�< 嵓9z0iv&jザ  S   	{Z�范�F�m猉	痹缠!囃ZtK�T�  �   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  �   J�(S�	皓&r悖,@椎�輮� ��*{�     a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  E   繃S,;fi@`騂廩k叉c.2狇x佚�  �   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   zY{���睃R焤�0聃
扨-瘜}     `k�"�1�^�`�d�.	*貎e挖芺
脑�  J   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   �
bH<j峪w�/&d[荨?躹耯=�  �   )鎋]5岽B鑯 �誽|寋獸辪牚     j轲P[塵5m榤g摏癭 鋍1O骺�*�  N   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   +椬恡�
	#G許�/G候Mc�蜀煟-  �   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  ,   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  k   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �   鹴y�	宯N卮洗袾uG6E灊搠d�      Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  P   副謐�斦=犻媨铩0
龉�3曃譹5D   �   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  �   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�     ;o屮G蕞鍐剑辺a岿;q琂謇:謇  W   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  �   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  5   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  u   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   匐衏�$=�"�3�a旬SY�
乢�骣�  �   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  I   �疀�4�A圏,oHB瓳HJ��2�0(v/  }   *u\{┞稦�3壅阱\繺ěk�6U�  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  G    �(M↙溋�
q�2,緀!蝺屦碄F觡  �   4      �    B   �    H   �    Y   �    �   r    U   s    �   �      K   �  �  �   �  �  �   J  �  B  R  �  �	  �  �    �  �  �   %  �  �  &  �  �  )  �  +
  0  �  �  3    �  >  �  �  ?  �  0   �  �  D
  �  �  �  �  �  O   �    �  A    �  c    �   �  �  �  �  �  �  �    �    �  s    �  �  �  �  )
  (    �   9     �   �'  �    �'  �  �   �'  �  �   (  �  �  (  �    (  �  �   (  �  �   /(  �  @
  *  �
  [   *    S  
*    4  *    �  *    `  
*  �    *    �  *  �  �   *  �  9
  *    �  *    
  *    �  *  �  �  *    �  *    �  "*    �  #*    �  &*    ]  **    �  ,*    �  .*    �  1*    �  3*     �  6*    �  9*     �   :*     �   �   �    D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\RTXPT\Rtxpt\RTXDI\ShaderParameters.h D:\RTXPT\External\Donut\include\donut\shaders\view_cb.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Rtxdi\Include\rtxdi\LightSampling\RISBufferSegmentAllocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\External\Donut\include\donut\shaders\sky_cb.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\RTXPT\Rtxpt\RTXDI\RtxdiResources.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\RTXPT\External\Rtxdi\Include\Rtxdi\DI\ReSTIRDIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\RTXPT\Rtxpt\Shaders\PathTracer\PathTracerShared.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Config.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\LightingTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiUtils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\GI\ReSTIRGIParameters.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiTypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\RTXPT\External\Rtxdi\Include\Rtxdi\LightSampling\RISBufferSegmentParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\Rtxpt\RTXDI\RtxdiResources.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\core\math\color.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\ReGIR\ReGIRParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\RTXPT\Rtxpt\Shaders\Bindings\BindingDataTypes.hlsli D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\PolymorphicLight.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\RTXPT\External\Rtxdi\Include\rtxdi\DI\ReSTIRDI.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp  �       LW}  .      .     
 W/  !   [/  !  
 跼      逺     
 稶      籙     
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁�  �?                  �?                  �?    谐Y>Y7?樰�=H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   9         �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >_   _Bytes  AJ        9  $  >_    _Block_size  AH       1 
   >_    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        r  
 Z      N Z   �  q   (                      H 
 h   r         $LN14  0   _  O_Bytes  O   �   h           :     
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 s     w    
 �  �    �  �   
 H塡$WH冹 H嬟I孁H+貶嬔L嬅H嬒�    H�;H媆$0H兡 _�   �       �   X  Y G            0   
   %   3*        �std::_Copy_memmove<unsigned char *,unsigned char *>  >    _First  AJ          >    _Last  AK          >    _Dest  AM         AP          >_    _Count  AI  
                             H 
 h   /*   0      O_First  8      O_Last  @      O_Dest  O�   @           0         4       � �   � �   � �!   � �%   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 l  �    p  �   
 H塋$SVWAVAWH冹 L嬺H嬹H�������H;��  L媦L+9H婭H+H嬔H殃H嬅H+翲;葀+H塡$XH�&      ��    H吚勌   H峹'H冪郒塆AH�
I;轎B轍塡$XH侞   rH岾'H塡$XH;�啛   敕H呟t
H嬎�    H孁�3�H墊$hJ�?M嬈M+�3诣    L婩H�L+翲嬒�    怘�H吷t-H媀H+袶侜   rH兟'L婣鳬+菻岮鳫凐w'I嬋�    H�>J�7H塅H�H塅H兡 A_A^_^[描    惕    惕    蘕   �    �   �    �   �    �   �      �    2  �    8  �    >  �       �   N  � G            C     C  *        �std::vector<unsigned char,std::allocator<unsigned char> >::_Resize_reallocate<std::_Value_init_tag> 
 >X�   this  AJ          AL       -  DP    >_   _Newsize  AK          AV       0 
 >鰩   _Val  AP        B\  �  � � <  AP �       D`    >#     _Newcapacity  AI        #7  W  � �  AI W     �  A �   AJ W       BX   M     � &  �   >_    _Oldsize  AW  -     �  
  >tO    _Appended_first  AJ  �     
  >tO    _Newvec  AM  �       AM �     � z   Bh   �     ~  M        **  W,�� M        1*  W,��- M        (  W,	
%
��* M        9   W%)		��
 Z   q   >_    _Block_size  AJ  �     �  �  AJ W       >_    _Ptr_container  AH  \       AH �     �  g 
 >�    _Ptr  AM  m       AM �     � z   M        r  W
 Z      N N M        r  ��
 Z      N N N N" M        #*  0g+ >_    _Oldcapacity  AJ  4     � #  \  t 
  AJ �     	  >_    _Geometric  AI  w       M        &*  0 N N M        *  �� >#    _Count  AP  �     
  M        .*  �� N N M        ,*  �� >tO   _Last  AP  �       M        3*  ��c >�    _First_ch  AK  �       >_    _Count  AP  �       N N% M        "*  ��h-# M        *  &��E >_   _Count  AK  �     
  AK 
      M        c  ��)$
 Z   �  
 >   _Ptr  AJ 
      >#    _Bytes  AK  �     $  
  AK 
    )   % M        s  ��d#
'
 Z   �   >_    _Ptr_container  AP  �       AP 
    )    >_    _Back_shift  AJ  �     -  AJ 
    )  N N N N
 Z   !*               (         0@ � h!   �  �  r  s  t  c  '  (  9   *  *  *  *  *  *   *  "*  #*  $*  &*  **  +*  ,*  -*  .*  /*  0*  1*  3*  4*  6*  8*  9*         $LN107  P   X�  Othis  X   _  O_Newsize  `   鰩  O_Val  O  �   �           C       �       � �   � �)   � �0   � �W   � �s   � ��   � ��   � ��   � ��   � ��   	 �%  
 �1  	 �7  � �=  � ��   �  � F            (   
   (             �`std::vector<unsigned char,std::allocator<unsigned char> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::catch$0 
 >X�   this  EN  P         ( 
 >鰩   _Val  EN  `         ( 
 Z   *                        � �        __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0        $LN107  P   X�  Nthis  X   _  N_Newsize  `   鰩  N_Val  O�   0           (        $        �
    �    �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �      �   
    �    $  �   
 @  �    D  �   
 o  �    s  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 2  �    6  �   
 B  �    F  �   
 V  �    Z  �   
 !  �    %  �   
 5  �    9  �   
 ^  �    b  �   
 n  �    r  �   
 �  �    �  �   
 �  �    �  �   
 K  �    O  �   
 g  �    k  �   
 �  �    �  �   
 �  �    �  �   
 4  �    8  �   
 t  �    x  �   
 �  �    �  �   
 �  �      �   
   �      �   
 Z  �    ^  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 )  �    -  �   
 9  �    =  �   
          
 d  �    h  �   
 	  �     	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 
     
    
 �
     �
    
 �
  �    �
  �   
 H塗$UH冹 H嬯L婨XH婾hH婱P�    3�3设    �   �    #   �    @SH冹 L嬄H嬟L+�3诣    H嬅H兡 [�   �       �   �   G G                     .*        �std::_Zero_range<unsigned char *>  >tO   _First  AJ          >tO   _Last  AI         AK                                H 
 h   /*   0   tO  O_First  8   tO  O_Last  O   �   8                   ,       � �   � �   � �   � �,   �    0   �   
 n   �    r   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 @USVWATAUAVAWH崿$��H侅�  H�    H3腍墔�  M嬮L墔�  H嬺H孂H塋$(� 媴`  堿媿h  塐嫕p  塤媿x  塐E3鯨墂L墂 L墂(L墂0L墂8L墂@L墂HL墂PL墂XL墂`L墂hH菂x     W�厐  H菂�     D埖�  H菂�     D壍�  D埖�  f菂�   D壍�  肁�   A;茿B荋拎H墔p  菂�      H菂�  
   �    �厐  �   f墔�  D埖�  H�L崊p  H峊$ H嬑�悁   A嬛H峀$0H;萾H�L�0H婳H塛H吷tH��P怘婰$ H吷tL塼$ H��P怘菂(  0   W��0  L壍@  H菂H     茀0   H菂P      菂X      茀\   f菂d   D壍h  嬅凔AB荋�@H玲H墠   菂`      �    �    H嬋H菂@     H菂H          �   堿艫 H墠0  H�L崊   H峊$ H嬑�悁   I嬛H峀$8H;萾H�L�0H婳 H塛 H吷tH��P怘婰$ H吷tL塼$ H��P怢塽怢塽�W�E燣塽癏荅�   艵� H荅�    荅�    艵� D墋衒荅�  D塽豂嬐�    凐AB荋拎H塃惼E�'艵�荅�    艵�H�       �I�������L媏窱凕	r3H峕營凕HG]燞荅�	   A�	   H�    H嬎�    艭	 樨   I嬏H验I嬈H+罫;鄓	M孇H岯'�+I�A�   I;荓G鳬峅H侚   r,H岮'H;�啓
  H嬋�    H吚剮
  H峏'H冦郒塁H吷t
�    H嬝�3跦荅�	   L墋蛤    ��   圕艭	 I凕v3I峊$H婱燞嬃H侜   rH兟'H婭鳫+罤兝鳫凐�
  �    H塢犉E�H�L岴怘峊$ H嬑�悁   E3銩嬙H峀$@H;萾H�L� H婳@H塛@H吷tH��P怘婰$ H吷tL塪$ H��P怚嬐�    凐A�   AB臜拎H塃惼E�-L媫窱�r1H峕營�HG]燞荅�   E岴H�    H嬎�    艭 檗   I嬒H验I嬈H+罫;鴙-H�       �H兝'H嬋�    H吚�'	  H峏'H冦郒塁?I�A�   I;芁G餓峃H侚   rH岮'H;�嗚  氪H吷t
�    H嬝�I嬡H荅�   L塽�    �   f塁艭 I�v2I峎H婱燞嬃H侜   rH兟'H婭鳫+罤兝鳫凐噦  �    H塢燞�L岴怘峊$ H嬑�悁   I嬙H峀$HH;萾H�L� H婳HH塛HH吷tH��P怘婰$ H吷tL塪$ H��P怐嫷h  D祊  C�6H菂�   0   W�呧   H菂�      茀�    H菂      菂      茀   f菂   D墺  凐AB艐豀�@H拎H墔�   菂      H菂�      �    �呧   �   墔�   �   f墔�   �   垍�   茀�    H�L崊�   H峊$ H嬑�悁   I嬙H峀$PH;萾H�L� H婳(H塛(H吷tH��P怘婰$ H吷tL塪$ H��P怘菂�     W�厫  fo
    f崰  茀�   H菂�      菂�      茀�   f菂�   D墺�  媴x  H拎H墔�  菂�      A�   H�    H崓�  �    H�L崊�  H峊$ H嬑�悁   I嬙H峀$XH;萾H�L� H婳0H塛0H吷tH��P怘婰$ H吷tL塪$ H��P怢墺�  W�呅  fo
    f嵿  茀�   f菂�   H菂�      f菂�    f菂   D墺  H��    H墔�  茀�  菂       A�   H�    H崓�  �    茀�  H�L崊�  H峊$ H嬑�悁   I嬙H峀$`H;萾H�L� H婳8H塛8H吷tH��P怘婰$ H吷tL塪$ H��P怢墺�   L墺�   W�厫   fo
    f崰   茀�    H菂�       菂�       茀�    D壄�   f菂�     D墺�   H嫕�  H嬎�    ��	H墔�   茀�   茀�   A�   H�    H崓�   �    菂�       茀�   H�L崊�   H峊$ H嬑�悁   I嬙H峀$hH;萾H�L� H婳PH塛PH吷tH��P怘婰$ H吷tL塪$ H��P怢塭0L塭8W�E@fo
    fMP艵@ H荅`    荅h    艵l D塵pf荅t  D塭xH崟�  H嬎�    婬H�蒆拎H塃0荅8   荅p@   艵tA�   H�    H峂@�    艵aH�L岴0H峊$ H嬑�悁   I嬙H峀$pH;萾H�L� H婳XH塛XH吷tH��P怘婰$ H吷tL塪$ H��P怢塭郘塭�W�E餱o
    fM 艵� H荅    荅    艵 D塵 f荅$  D塭(H崟�  H嬎�    婬H�IH拎H塃嗲E�    荅 @   艵$A�   H�    H峂痂    艵H�L岴郒峊$ H嬑�悁   I嬙H峀$xH;萾H�L� H婳`H塛`H吷tH��P怘婰$ H吷tL塪$ H��P怐壄  D壄  D壄  D壄  D壄   H菂$     f菂,   W��0  fo
    f岪  茀0   菂P     茀T   D墺X  f菂\    )卄  茀p   D墺t  茀x   L崓   L崊  H崟  A嬑�    茀R  A�
   H�    H崓0  �    菂t      茀x  茀,  !H�L崊  H峊$ H嬑�P(I嬙H峂�H;萾H�L� H婳hH塛hH吷tH��P怘婰$ H吷tL塪$ H��P怘嫊H  H凓v4H�翲媿0  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐�  �    fo    f匑  茀0   H婾H凓v1H�翲婱餒嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚍   �    fo    fE 艵� H峂0�    怘崓�   �    怘崓�  �    怘崓�  �    怘崓�   �    怘峂愯    怘崓   �    怘崓p  �    怘嬊H媿�  H3惕    H伳�  A_A^A]A\_^[]描    愯    惕    愯    �   j     $   *  $     �    3  '   <  '   �  �    I  *   Q  �    �  �    �  �    �  *   �  *   .  �    �  �    �  -   �  �      �    \  �    w  -   �  -   �  �    �  0   �  0   �  0   �  0   Y  m   �  3   �  �    6  m   �  6   �  �    '	  m   x	  �    �	  9   �	  �    (
  m   a
  �    �
  <   �
  �      m   >  �    l  ?   u  �      m   }  �    �  B   �  �    G
  �    O
  m   �
  �    �
  m   �
  �    �
  �    �
  �    �
  �    �
  �    �
  �    �
  �      �      �    7  �    =  �    C  �    I  �       �   �6  D G            N  -   N  *        �RtxdiResources::RtxdiResources 
 >鴯   this  D(    AJ        =  AM  =     �
  D@   >))   device  AK        :  AL  :     �
  >��   context  B�  7      AI  t	    �� �  AP        F" >�   risBufferSegmentAllocator  AQ        0  AU  0     p AU <      >u    maxEmissiveMeshes  A   K     �  EO  (           D`   >u    maxEmissiveTriangles  A   T       An  "      EO  0           Dh   >u    maxPrimitiveLights  A   ]     v�/ [
 r  EO  8           Dp   >u    maxGeometryInstances  A   f     �  EO  @           Dx  ! >    neighborOffsetBufferDesc  D�   >�   localLightPdfDesc  CK  8   
    	  CK 8   K
      D  ! >    lightReservoirBufferDesc  D0   >u     lightBufferElements  A   -    Z  >u     maxLocalLights  An  )    %	  ! >    primitiveLightBufferDesc  D    >   giReservoirBufferDesc  CK  (   f
    	  CK (   �
      D�    >    lightBufferDesc  D�  * >    geometryInstanceToLightBufferDesc  D�  $ >    lightIndexMappingBufferDesc  D�   >    taskBufferDesc  Dp   >    risBufferDesc  D�    M        J  L峛��& M        %  峛
1

�� M        �  1峫�� M        3  .峯��  M        c  峷)��
 Z   �  
 >   _Ptr  AH  v
      AJ  s
      AH �
      >#    _Bytes  AK  o
    � . �  M        s  �d��
 Z   �   >_    _Ptr_container  AH  �
      AJ  �
      N N N N N N M        J  X�
�>& M        %  �

4
�� M        �  4��1 M        3  1��.  M        c  �$)�
 Z   �  
 >   _Ptr  AH  $
      AJ  !
      AH F
      >#    _Bytes  AK  
    31 �  M        s  �-d�
 Z   �   >_    _Ptr_container  AH  8
      AJ  5
      N N N N N N M        �  岕 M        �  岕HB
 >�    temp  AJ  �      AJ 

    �  * i *  B    
    K9  B�  �    �n  N N M        
*  %屛 M        �  岃 M        �  岃
 >�    temp  AJ  �      AJ �      N N M        �  屶 >�    tmp  AK  �    "  AK �        N M        *  屛C	 M        �  屭 N N N M        (  寛 M        /(  寛
 Z   )   N N M        *  孒 N M        R  �	 M        &  �
 N M        A  �	 M        �  �	 M          �	 N N N N M        �'  嫻 M        (  嫻HB
 >�     temp  AJ  �      AJ �    �  B    �    �; t  B  �    ��  N N M        �'  &嫆 M        �'  嫮 M        (  嫮
 >�     temp  AJ  �      AJ �      N N M        (  嫢 >�     tmp  AK  �    #  AK �    �    N M        (  嫆C
 M        (  嫙 N N N M        (  媍 M        /(  媍
 Z   )   N N M        R  婟 M        &  �

 N M        A  婟 M        �  婟 M          婟 N N N N M        �'  娷 M        (  娷HB
 >�     temp  AJ  �
      AJ �
    K  B    �
    c�   Q  Bx  �
    ��  N N M        �'  &姷 M        �'  娦 M        (  娦
 >�     temp  AJ  �
      AJ �
      N N M        (  娙 >�     tmp  AK  �
    #  AK �
    ^    N M        (  姷C
 M        (  娐 N N N M        (  妴 M        /(  妴
 Z   )   N N M        R  �  M        &  �$

 N M        A  �  M        �  �  M          �  N N N N M        �'  � M        (  �HB
 >�     temp  AJ  
      AJ 
    K  B    
    @�  � � .  B�  �	    vd  N N M        �'  &壺 M        �'  夡 M        (  夡
 >�     temp  AJ  �	      AJ �	      N N M        (  夒 >�     tmp  AK  �	    #  AK �	    ^    N M        (  壺C
 M        (  夊 N N N M        (  墫 M        /(  墫
 Z   )   N N M        R  � M        &  �#
 N M        A  � M        �  � M          � N N N N M        �'  堳 M        (  堳HB
 >�     temp  AJ  �      AJ 	    l " B    	    J
 � � � 8  BH  �    �n  N N M        �'  &埼 M        �'  堥 M        (  堥
 >�     temp  AJ  �      AJ �      N N M        (  堘 >�     tmp  AK  �    #  AK �    �    N M        (  埼C
 M        (  堐 N N N M        (  垨 M        /(  垨
 Z   )   N N M        R  �+ M        &  �2
 N M        A  �+ M        �  �+ M          �+ N N N N M        �'  � M        (  �HB
 >�     temp  AJ        AJ !    � & B        4�  � � � � "  B�  �    jX  N N M        �'  &囦 M        �'  � M        (  �
 >�     temp  AJ  �      AJ       N N M        (  圇 >�     tmp  AK  �    #  AK     �    N M        (  囦C
 M        (  囻 N N N M        (  嚦 M        /(  嚦
 Z   )   N N M        R  嘚 M        &  嘦
 N M        A  嘚 M        �  嘚 M          嘚 N N N N M        �'  �* M        (  �*HB
 >�     temp  AJ  /      AJ @    � * B    9    �  � � � � �   B      K9  N N M        �'  &� M        �'  � M        (  �
 >�     temp  AJ        AJ *      N N M        (  � >�     tmp  AK      #  AK *    �    N M        (  �C
 M        (  � N N N M        (  I啟 M        /(  I啟 M        )  
啟+7 M        �  L7啳 N N N N M        R  �; M        &  咮 N M        A  �; M        �  �; M          �; N N N N M        �'  � M        (  �HB
 >�     temp  AJ  
      AJ     � . B        :%  � � � � � (  B�  �    p^  N N M        �'  &呣 M        �'  咘 M        (  咘
 >�     temp  AJ  �      AJ       N N M        (  咇 >�     tmp  AK  �    #  AK     �    N M        (  呣C
 M        (  呺 N N N  M        (  ��o��堟  M        /(  ��o��堟. M        )  劙J-(Diok堟@ M        *  勲&($2埄 >#    _New_capacity  AH        AJ  >    * "   AK      
  AV  !    �#   AH 
      AJ 
    7	 [ � * � d AV �    Y  Cn      3      >_    _Old_capacity  AJ  �    M    AW  �    =  AJ 
      AW �    �b s  M        *  卼 M        ?   卼 N N$ M        �  �
	0堟 >p    _Fancy_ptr  AI  c      AI h    �!� M        �  �
4堟 M        �  �
4堟. M        (  �
4
	
%
堊+ M        9   �
()$	堮
 Z   q   >_    _Block_size  AH  K      AH 
    7	 *	 >_    _Ptr_container  AH        AH h    � �
 >�    _Ptr  AI  #      AI h    �!� M        r  �

 Z      N N M        r  匸
 Z      N N N N N M        �  勸8 M          勸- N N M        �  2厯埊 M        3  .厳埆  M        c  厼)垈
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    .  AK B      M        s  収d垚
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N M        �  L勏 N M        0  労	
 >嘚   this  AI  �    	  >p    _Result  AI  �    $  AI �    �  N N N N M        �'  剏 M        (  剏HB
 >�     temp  AJ  ~      AJ �     . B    �    �	� � � | � c @ {  B�
  O    �	 N N M        �'  )凮 M        �'  刴 M        (  刴
 >�     temp  AJ  i      AJ y      N N M        (  別 >�    tmp  AK  b      AK y        C       U    
  C      e    2   )   N M        (  凮F
 M        (  刜 N N N M        (  �%�
 M        /(  �%�
' M        )  �
-(D��* M        *  僞&%J($3 >#    _New_capacity  AH  v      AJ  �    F   ?   AW  r        AH �      AJ �    �
 - e * � �	 Co      �      >_    _Old_capacity  AJ  d    )  AT  %    ?  AJ �      AT 6      M        *  冞 M        ?   冞 N N M        �  儔F >p    _Fancy_ptr  AI  �      AI �    u
� - L
 � �� M        �  F儘 M        �  F儘  M        (  儘)
,%
" M        9   儢$	() >_    _Block_size  AH  �    	  AH �    �
 �
 >_    _Ptr_container  AH  �      AH �    t
 P

 >�    _Ptr  AI  �      AI �    u
� - L
 � �� M        r  儯
 Z      N N M        r  兦
 Z      N N N N N M        �  %僤 M          僤+	 N N M        �  3� M        3  .� M        c  �)
 Z   �  
 >   _Ptr  AH        AJ        AH -      >#    _Bytes  AK      .  AK B      M        s  �d >_    _Ptr_container  AH        AJ        N N N N N M        �  L傽 N M        0  �+	
 >嘚   this  AI  /    	  >p    _Result  AI  8    &  AI 6    
� - � 
 - 2� N N N N M        R  偖 M        &  偛$ N M        A  偖 M        �  偖 M          偖 N N N N M        �'  倣 M        (  倣HB
 >�     temp  AJ  �      AJ �    C 2 B    �    �� x � ~ h r O ,	 g
  B�  f    � N N M        �'  &俧 M        �'  倎 M        (  倎
 >�     temp  AJ  }      AJ �      N N M        (  倅 >�     tmp  AK  i    #  AK �    ^    N M        (  俧C
 M        (  俿 N N N M        (  A�
 M        /(  A�
 M        )  A�
  M        *  �

++
 M        *  �0 M        ?   �0 N N M        �  
�
 >p    _Fancy_ptr  AJ      F  M        �  
�
 M        �  
�
 M        (  
�
 M        r  
�
 Z      N N N N N N N N N M        R  仧 M        &  仸' N M        A  仧 M        �  仧 M          仧 N N N N M        �'  亄 M        (  亄HB
 >�     temp  AJ  �      AJ �    g 6 B    �    � � � � � z � a	 >
 y  B�  T    � N N M        �'  &乀 M        �'  乷 M        (  乷
 >�     temp  AJ  k      AJ {      N N M        (  乬 >�    tmp  AK  d      AK {    �    C       W    
  C      g    �   )   N M        (  乀C
 M        (  乤 N N N M        (  0� M        /(  0� M        )  
�+ M        �  L� N N N N M        R  �� M        &  �� N M        A  �� M        �  �� M          �� N N N N M        �  �� N M        �'  �� N M        �'  �� N M        �'  �� N M        �'  �� N M        �'  �� N M        �'  | N M        �'  x N M        �'  t N M        �'  p N M        �'  i N> Z   D*  D*  B*  A*  A*  C*  �'  �'  �'  �'  �'  �'  �'  �'   �          @         A h@   �  �  r  s  t  v  �  �  �  �  J  K  R  S  �  �  $  %  &  )  0  3  >  ?  �  �  �  �  �  �  �  <  A  ^  c  �  �  �  �      �  �  '  (  /   9   �'  �'  �'  �'  �'  (  (  (  (  /(  *  *  *  
*  *  *  *  
 :�  O        $LN1403  @  鴯  Othis  H  ))  Odevice  P  ��  Ocontext & X  �  OrisBufferSegmentAllocator  `  u   OmaxEmissiveMeshes ! h  u   OmaxEmissiveTriangles  p  u   OmaxPrimitiveLights ! x  u   OmaxGeometryInstances % �    OneighborOffsetBufferDesc    �  OlocalLightPdfDesc % 0    OlightReservoirBufferDesc %      OprimitiveLightBufferDesc " �     OgiReservoirBufferDesc  �    OlightBufferDesc . �    OgeometryInstanceToLightBufferDesc ( �    OlightIndexMappingBufferDesc  p    OtaskBufferDesc  �     OrisBufferDesc  9N      �(   9w      E   9�      E   9`      �(   9�      E   9�      E   9I      �(   9u      E   9�      E   9�      �(   9      E   9      E   9�      �(   9&      E   9<      E   9�      �(   9      E   9      E   9�      �(   9�      E   9	      E   9�	      �(   9�	      E   9
      E   9�
      �(   9�
      E   9�
      E   9�      �(   9�      E   9�      E   9�      �(   9�      E   9
      E   O  �              N  �            .  ��
        B     ��  \   �  E   *  �N   +  �W   ,  �`   -  �i   .  ��   1  ��   /  ��   3  ��   0  �  2  �  4  �<  6  ��  ;  ��  9  ��  =  ��  :  �  <  �
  >  �N  ?  ��  B  ��  C  ��  D  ��  E  �  F  �	  G  �
  H  �2  I  �:  J  ��  M  ��  N  ��  O  ��  P  �  S  �)  T  �-  X  �;  V  �p  Z  ��  W  ��  Y  ��  [  ��  ]  �@  b  �N  `  ��  d  ��  a  ��  c  ��  e  ��  f  �+  i  �I  k  �f  n  �v  j  ��  l  ��  m  ��  o  ��  p  ��  q  �	  t  �m	  u  ��	  v  ��	  w  ��	  x  ��	  y  ��	  z  ��	  {  � 
  ~  �V
    �t
  �  �{
  �  ��
  �  ��
  �  ��
  �  ��
  �  ��
  �  �3  �  �Q  �  �X  �  �_  �  �c  �  �y  �  �}  �  �	  �  �d  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �

  �  �<  O  �H  �  ��   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$0 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$1 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$2 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$3 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$4 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$5 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$6 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$7 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$8 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  S F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$9 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O�   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$10 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$11 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$13 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$15 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$18 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$20 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$22 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$24 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$26 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$28 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   �   �  T F                                �`RtxdiResources::RtxdiResources'::`1'::dtor$30 
 >鴯   this  EN  (           EN  @         ! >    neighborOffsetBufferDesc  EN  �          >�    localLightPdfDesc  EN           ! >    lightReservoirBufferDesc  EN  0         ! >    primitiveLightBufferDesc  EN             >    giReservoirBufferDesc  EN  �           >    lightBufferDesc  EN  �         * >    geometryInstanceToLightBufferDesc  EN  �         $ >    lightIndexMappingBufferDesc  EN  �          >    taskBufferDesc  EN  p          >    risBufferDesc  EN  �                                  �  O   ,   �    0   �   
 q   �    u   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 @  �    D  �   
 P  �    T  �   
 `  �    d  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 0  �    4  �   
 P  �    T  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
   �      �   
 s  �    w  �   
 �  �    �  �   
   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 A  �    E  �   
 Q  �    U  �   
 ,  �    0  �   
 <  �    @  �   
 L  �    P  �   
 m  �    q  �   
 �  �    �  �   
 �  �    �  �   
 A  �    E  �   
 Q  �    U  �   
 i  �    m  �   
 }  �    �  �   
 �  �    �  �   
 �  �    	  �   
 >	  �    B	  �   
 N	  �    R	  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �      �   
 n  �    r  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 -
  �    1
  �   
 =
  �    A
  �   
 M
  �    Q
  �   
 i
  �    m
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 *  �    .  �   
 :  �    >  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 J  �    N  �   
 Z  �    ^  �   
 �  �    �  �   
 �  �    �  �   
 	  �    
  �   
   �      �   
 )  �    -  �   
 M  �    Q  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �    "  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 6  �    :  �   
 F  �    J  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    	  �   
   �      �   
 A  �    E  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 `  �    d  �   
 p  �    t  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �       �   
 4  �    8  �   
 D  �    H  �   
 `  �    d  �   
 x  �    |  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 _  �    c  �   
 o  �    s  �   
 B  �    F  �   
 R  �    V  �   
   �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �       �   
 (   �    ,   �   
 8   �    <   �   
 �   �    �   �   
 �   �    �   �   
 !  �    !  �   
 &!  �    *!  �   
 6!  �    :!  �   
 �!  �    �!  �   
 �!  �    �!  �   
 �!  �    �!  �   
 �!  �    �!  �   
 V"  �    Z"  �   
 f"  �    j"  �   
 �"  �    �"  �   
 �"  �    �"  �   
 �"  �    �"  �   
 �"  �    �"  �   
 �#  �    �#  �   
 �#  �    �#  �   
 $  �    $  �   
 $  �    $  �   
 '$  �    +$  �   
 G$  �    K$  �   
 o$  �    s$  �   
 $  �    �$  �   
 �$  �    �$  �   
 �$  �    �$  �   
 %  �    %  �   
 &%  �    *%  �   
 �%  �    �%  �   
 �%  �    �%  �   
 &  �    &  �   
 &  �    &  �   
 >&  �    B&  �   
 N&  �    R&  �   
 k'  �    o'  �   
 {'  �    '  �   
 �'  �    �'  �   
 �'  �    �'  �   
 �'  �    �'  �   
 (  �    (  �   
 (  �    (  �   
 x(  �    |(  �   
 �(  �    �(  �   
 �(  �    �(  �   
 �)  �    �)  �   
 �)  �    �)  �   
 �)  �    �)  �   
 �)  �    �)  �   
 b*  �    f*  �   
 r*  �    v*  �   
 �*  �    �*  �   
 �*  �    �*  �   
 �+  �    �+  �   
 B-  �    F-  �   
 R-  �    V-  �   
 b-  �    f-  �   
 �-  �    �-  �   
 .  �    .  �   
 .  �    .  �   
 W.  �    [.  �   
 g.  �    k.  �   
 .  �    �.  �   
 �.  �    �.  �   
 h2  
   l2  
  
 �4  �    �4  �   
 �4  �    �4  �   
 �4  �    �4  �   
 5  �    5  �   
 5  �    5  �   
 "5  �    &5  �   
 25  �    65  �   
 B5  �    F5  �   
 R5  �    V5  �   
 b5  �    f5  �   
 r5  �    v5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 6  �    6  �   
 6  �    6  �   
 "6  �    &6  �   
 26  �    66  �   
 B6  �    F6  �   
 R6  �    V6  �   
 b6  �    f6  �   
 r6  �    v6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 4:  �    8:  �   
 �:  �    �:  �   
 �:  �    �:  �   
 �:  �    �:  �   
 �:  �    ;  �   
 6;  �    :;  �   
 m;  �    q;  �   
 �;  �    �;  �   
 �;  �    �;  �   
 <  �    <  �   
 I<  �    M<  �   
 v<  �    z<  �   
 �<  �    �<  �   
 �<  �    �<  �   
 D=  �    H=  �   
 X=  �    \=  �   
 �=  �    �=  �   
 �=  �    �=  �   
 �=  �    �=  �   
 ->  �    1>  �   
 a>  �    e>  �   
 �>  �    �>  �   
 �>  �    �>  �   
 	?  �    
?  �   
 6?  �    :?  �   
 b?  �    f?  �   
 �?  �    �?  �   
 @  �    @  �   
 @  �    @  �   
 O@  �    S@  �   
 @  �    傽  �   
 禓  �    篅  �   
 鞞  �    馌  �   
 !A  �    %A  �   
 OA  �    SA  �   
 廇  �    揂  �   
 葾  �    虯  �   
 鯝  �    鶤  �   
 "B  �    &B  �   
 tB  �    xB  �   
 腂  �    菳  �   
 谺  �    蹷  �   
 C  �    C  �   
 ?C  �    CC  �   
 vC  �    zC  �   
 瑿  �    盋  �   
 酑  �    錍  �   
 D  �    D  �   
 OD  �    SD  �   
 塂  �    岲  �   
 禗  �    篋  �   
 釪  �    鍰  �   
 4E  �    8E  �   
 凟  �    圗  �   
 楨  �    淓  �   
 螮  �    覧  �   
 �E  �    F  �   
 6F  �    :F  �   
 mF  �    qF  �   
   �      �   
 螰  �    覨  �   
 G  �    G  �   
 IG  �    MG  �   
 vG  �    zG  �   
   �      �   
 鬐  �    鳪  �   
 DH  �    HH  �   
 XH  �    \H  �   
 廐  �    揌  �   
 縃  �    肏  �   
 鯤  �    鶫  �   
 -I  �    1I  �   
 aI  �    eI  �   
 廔  �    揑  �   
 螴  �    覫  �   
 	J  �    
J  �   
 6J  �    :J  �   
 bJ  �    fJ  �   
 碕  �    窲  �   
 K  �    K  �   
 K  �    K  �   
 OK  �    SK  �   
 K  �    僈  �   
 禟  �    篕  �   
 鞬  �    馣  �   
 !L  �    %L  �   
 OL  �    SL  �   
 廘  �    揕  �   
 蒐  �    蚅  �   
 鯨  �    鶯  �   
 "M  �    &M  �   
 tM  �    xM  �   
 腗  �    萂  �   
 豈  �    躆  �   
 N  �    N  �   
 ?N  �    CN  �   
 vN  �    zN  �   
 璑  �    盢  �   
 酦  �    錘  �   
 O  �    O  �   
 OO  �    SO  �   
 塐  �    峅  �   
 禣  �    篛  �   
 釵  �    鍻  �   
 4P  �    8P  �   
 凱  �    圥  �   
 楶  �    淧  �   
 螾  �    覲  �   
 �P  �    Q  �   
 6Q  �    :Q  �   
 mQ  �    qQ  �   
   �      �   
 螿  �    観  �   
 R  �    R  �   
 IR  �    MR  �   
 vR  �    zR  �   
   �      �   
 鬜  �    鳵  �   
 DS  �    HS  �   
 XS  �    \S  �   
 廠  �    揝  �   
 縎  �    肧  �   
 鯯  �    鶶  �   
 -T  �    1T  �   
 aT  �    eT  �   
 廡  �    揟  �   
 蟃  �    覶  �   
 	U  �    
U  �   
 6U  �    :U  �   
 bU  �    fU  �   
 碪  �    窾  �   
 V  �    	V  �   
 V  �    V  �   
 PV  �    TV  �   
 �V  �    刅  �   
 稸  �    籚  �   
 頥  �    騐  �   
 "W  �    &W  �   
 PW  �    TW  �   
 怶  �    擶  �   
 蔠  �    蜽  �   
 鱓  �    鸚  �   
 #X  �    'X  �   
 xX  �    |X  �   
 蒟  �    蚗  �   
 軽  �    醁  �   
 Y  �    Y  �   
 DY  �    HY  �   
 {Y  �    Y  �   
 瞃  �    禮  �   
 鎅  �    闥  �   
 Z  �    Z  �   
 TZ  �    XZ  �   
 嶼  �    抁  �   
 籞  �    縕  �   
 鏩  �    隯  �   
 <[  �    @[  �   
 峓  �    慬  �   
   �      �   
 豙  �    躘  �   
 \  �    \  �   
 ?\  �    C\  �   
 v\  �    z\  �   
 猏  �    甛  �   
 豛  �    躙  �   
 ]  �    ]  �   
 R]  �    V]  �   
 ]  �    僝  �   
 玗  �    痌  �   
  ^  �    ^  �   
 Q^  �    U^  �   
 e^  �    i^  �   
 淾  �    燸  �   
 蘜  �    衈  �   
 _  �    _  �   
 :_  �    >_  �   
 n_  �    r_  �   
 淿  �    燺  �   
 躝  �    郷  �   
 `  �    `  �   
 C`  �    G`  �   
 o`  �    s`  �   
 腵  �    萡  �   
 a  �    a  �   
 )a  �    -a  �   
 `a  �    da  �   
 恆  �    攁  �   
 莂  �    薬  �   
   �    b  �   
 2b  �    6b  �   
 `b  �    db  �   
 燽  �      �   
 赽  �    辀  �   
 c  �    c  �   
 3c  �    7c  �   
 坈  �    宑  �   
 賑  �    輈  �   
 韈  �    馽  �   
 $d  �    (d  �   
 Td  �    Xd  �   
 媎  �    廳  �   
 耫  �    芼  �   
 鰀  �    鷇  �   
 $e  �    (e  �   
 de  �    he  �   
 瀍  �      �   
 薳  �    蟚  �   
 鱡  �    鹐  �   
 Lf  �    Pf  �   
 漟  �      �   
 眆  �    礷  �   
 鑖  �    靎  �   
 g  �    g  �   
 Og  �    Sg  �   
 唃  �    奼  �   
 篻  �    緂  �   
 鑗  �    靏  �   
 (h  �    ,h  �   
 bh  �    fh  �   
 廻  �    揾  �   
 籬  �    縣  �   
 i  �    i  �   
 ai  �    ei  �   
 ui  �    yi  �   
 琲  �    癷  �   
 躨  �    鄆  �   
 j  �    j  �   
 Jj  �    Nj  �   
 ~j  �    俲  �   
 琷  �    癹  �   
 靔  �    餵  �   
 &k  �    *k  �   
 Sk  �    Wk  �   
 k  �    僰  �   
 詋  �    豮  �   
 %l  �    )l  �   
 9l  �    =l  �   
 pl  �    tl  �   
 爈  �      �   
 譴  �    踠  �   
 m  �    m  �   
 Bm  �    Fm  �   
 pm  �    tm  �   
 癿  �    磎  �   
 阭  �    頼  �   
 n  �    n  �   
 Cn  �    Gn  �   
 榥  �    渘  �   
 閚  �    韓  �   
 齨  �    o  �   
 4o  �    8o  �   
 do  �    ho  �   
 沷  �    無  �   
 襬  �    謔  �   
 p  �    
p  �   
 4p  �    8p  �   
 tp  �    xp  �   
 畃  �    瞤  �   
 踦  �    遬  �   
 q  �    q  �   
 \q  �    `q  �   
 璹  �    眖  �   
 羜  �    舚  �   
 鴔  �    黴  �   
 (r  �    ,r  �   
 _r  �    cr  �   
 杛  �    歳  �   
 蕆  �    蝦  �   
 鴕  �    黵  �   
 8s  �    <s  �   
 rs  �    vs  �   
 焥  �      �   
 藄  �    蟬  �   
 H媻(   H兞�       �    H媻(   H兞 �       �    H媻(   H兞(�       �    H媻(   H兞0�       �    H媻(   H兞8�       �    H媻(   H兞@�       �    H媻(   H兞H�       �    H媻(   H兞P�       �    H媻(   H兞X�       �    H媻(   H兞`�       �    H媻(   H兞h�       �    H崐p  �       �    H崐   �       �    H崐�   �       �    H崐�  �       �    H崐�  �       �    H崐�  �       �    H崐�  �       �    H崐0  �       �    H崐�   �       �    H崐  �       �    @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   �   %   �    ,   �      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�         �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !        ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   �   %   �       �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2        $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �'        �nvrhi::RefCountPtr<nvrhi::IBuffer>::~RefCountPtr<nvrhi::IBuffer> 
 >穣   this  AH         AJ          AH        M        (  GCE
 >�     temp  AJ  
       AJ        N (                     0H� 
 h   (   0   穣  Othis  9       E   O  �   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >JH   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   JH  Othis  9       E   O�   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 D  �    H  �   
 \  �    `  �   
 @SH冹 H嬞H�	H吷t:H婼H+袶侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �:   �    R   �       �   �  � G            W      W   *        �std::vector<unsigned char,std::allocator<unsigned char> >::~vector<unsigned char,std::allocator<unsigned char> > 
 >X�   this  AI  	     N G   AJ        	 $ M        *  	h-%	
 M        *  &9 >_   _Count  AK         AK 9       M        c  )
 Z   �  
 >   _Ptr  AJ 9       >#    _Bytes  AK       A   $   AK 9      " M        s  
!#

 Z   �   >_    _Ptr_container  AP  %     1    AP 9       >_    _Back_shift  AJ       J -   AJ 9       
  N N N N                       H� & h   �  s  t  c  *  *  *  *         $LN30  0   X�  Othis  O �   8           W        ,       � �	   � �K    �Q   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 .  �    2  �   
 >  �    B  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 /  �    3  �   
 U  �    Y  �   
 i  �    m  �   
 �     �    
 �  �       �   
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �    [   �       �   �  D G            `      `   �'        �nvrhi::BufferDesc::~BufferDesc 
 >�   this  AI  
     S L   AJ        
  M        J  GM) M        %  -(

 M        �   N M        �  -G M        3  &@ M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        s  
##
"
 Z   �   >_    _Ptr_container  AP  '     8    AP ;       >_    _Back_shift  AJ  .     1 
   N N N N N N                       H� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN39  0   �  Othis  O ,   �    0   �   
 i   �    m   �   
 }   �    �   �   
 ]  �    a  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �     �    
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂0    H荂8   艭  H兡 [描    �<   �    [   �       �   �  F G            `      `   *        �nvrhi::TextureDesc::~TextureDesc 
 >y   this  AI  
     S L   AJ        
  M        J  GM) M        %  -(

 M        �   N M        �  -G M        3  &@ M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        s  
##
"
 Z   �   >_    _Ptr_container  AP  '     8    AP ;       >_    _Back_shift  AJ  .     1 
   N N N N N N                       H� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN39  0   y  Othis  O   ,   �    0   �   
 k   �    o   �   
    �    �   �   
 _  �    c  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �     �    
 H�    H�H兞�       �      �       �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       �      �       �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   8                          H�     �  Othis  O  �   (                          Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �  �   0   �  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   �      �    0   �       �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @� 
 h   �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 H塡$H塼$H墊$ AVH冹PA孁L嬺H嬹�9 厱   W荔D$03跦塡$@C� 嬓吚tL岲$`H峀$0�    H媆$@嬜H媩$0H嬒�    I�L婰$8L+螲荄$     L嬊H媀PI嬑�Px�H�t/H+逪嬊H侞   rH兠'H�鳫+荋兝鳫凐w!H嬘H嬒�    H媆$hH媡$pH媩$xH兡PA^描    蘈   �    `   �    �   �    �   �       �   
  O G            �      �   *        �RtxdiResources::InitializeNeighborOffsets 
 >鴯   this  AJ          AL       � �   > )   commandList  AK          AV       � �   >u    neighborOffsetCount  A        D  Ah          A  �       >菐    offsets  D0    M        *  4��J M        *  ��/E M        *  ,��B >_   _Count  AI  �       AI �       M        c  ��)!
 Z   �  
 >   _Ptr  AH  �       AM  \     7  AH �       AM �       >#   	 _Bytes  AI  U     � ;  \ !  AI �       C       2     #  C      U     l ; ,  M        s  ��d
+
 Z   �   >_    _Ptr_container  AH  �       AM  �       N N N N N M        *  g N M        
*  = >_   _Newsize  AK  =       AK U       M        *   =
 Z   *   N N M        *  
* M        *  
* M        *  
* N N N
 Z   @*   P                    @ � h   �  �  s  t  v  c  �'  *  	*  
*  *  *  *  *  *  *  *  *  *  *  *  *  *  *   *  -*  .*  /*  0*  6*  9*         $LN70  `   鴯  Othis  h    )  OcommandList   p   u   OneighborOffsetCount  0   菐  Ooffsets  9�       =)   O  �   X           �   �     L       �  �   �  �*   �  �7   �  �U   �  �d   �  ��   �  ��   �  ��   �   ^ F                                �`RtxdiResources::InitializeNeighborOffsets'::`1'::dtor$0  >菐    offsets  EN  0                                  �  O,   �    0   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
    �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
   �      �   
 &  �    *  �   
 G  �    K  �   
 _  �    c  �   
 s  �    w  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 T  �    X  �   
 d  �    h  �   
 �     �    
   �    
  �   
    �    $  �   
 �  �    �  �   
 �  �      �   
 H崐0   �       �    H冹HH峀$ �    H�    H峀$ �    �
   �             �       �   �   F G                       q        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �            J �   K �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H冹(H�
    �    �         �       �   w   7 G                     ~        坰td::_Xlen_string 
 Z   �!   (                      @        $LN3  O �   (              �            		 �   
	 �,   �    0   �   
 s       w      
 �   �    �   �   
 H冹(H�
    �    �   E      �       �   �   i G                     !*        坰td::vector<unsigned char,std::allocator<unsigned char> >::_Xlength 
 Z   �!   (                      @        $LN3  O   �   (                          a �   b �,   �    0   �   
 �      �     
 �   �    �   �   
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   �    �   �    �   �    �   �    ,  �    O  �    U  �    [  �       �     r G            `     `  )        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >�   _Ptr  AK          AW       D/  >_   _Count  AL       G4  AP         B M        *  E
(?SD3$--K
 Z   ~   >#     _New_capacity  AH  �     �  * N  V r  AJ  �       AM  O     =  ^ �  AH �     G  ,  AJ �       M        *  �� M        ?   �� N N M        �  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        �  ��?�� M        �  ��?��* M        (  ��

*%
u- M        9   ��	)
��
 Z   q   >_    _Block_size  AJ  �     �  �  AJ �       >_    _Ptr_container  AH  �       AH �     }  b 
 >�    _Ptr  AV  �       AV �     ~ V "  M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  X(  M          X' >_    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M        �  
~ N N N M        �  -�W M        3  �&P M        c  �
)/
 Z   �  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        s  
�#
2
 Z   �   >_    _Ptr_container  AP        AP +    4  *  >_    _Back_shift  AJ      
  AJ Z      N N N N N M        �  L4 N M        0  $# >p    _Result  AM  '       AM 8      M        �  ' N N                       H z h   �  �  r  s  t  �  $  0  3  ?  �  �  �  �  �  �  c  �  �  �    �  �  '  (  /   9   *  *         $LN144  @   �  Othis  H   �  O_Ptr  P   _  O_Count e 
�  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_1>  O�   h           `  �  
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 m  �    q  �   
 }  �    �  �   
 H  �    L  �   
 \  �    `  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 F  �    J  �   
 k  �    o  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �    #  �   
 �  �    �  �   
 �  �    �  �   
 \     `    
   �      �   
 H冹(H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �3   �    8   �       �     O G            =      =   *        �std::allocator<unsigned char>::deallocate 
 >蠋   this  AJ          AJ (       D0   
 >tO   _Ptr  AK        < +   >_   _Count  AP          AP (        M        c  )

 >   _Ptr  AH (       >#    _Bytes  AP       $    AP (      " M        s  
#

 Z   �   >_    _Ptr_container  AJ       (    AJ (       >_    _Back_shift  AH         AH (       N N (                      H  h   �  s  c         $LN20  0   蠋  Othis  8   tO  O_Ptr  @   _  O_Count  O �   8           =        ,       � �   � �.   � �2   � �,   �    0   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 !  �    %  �   
 B  �    F  �   
 V  �    Z  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 ?     C    
 �  �    �  �   
 �蓩裂�葖亮�葖亮�葖亮�葖亮��烂   �   �   5 G            '       &   *        �getNextPowerOf2 
 >u    a  A   $       A         $                         @     u   Oa  O   �   `           '   �  	   T         �      �     �     �     �     �     �$     �&   !  �,   �    0   �   
 W   �    [   �   
 g   �    k   �   
 �   �    �   �   
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0                   $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H           �       �           20    2           �       �       #   
 
4 
2p    B           �       �       )    20    <           �       �       /   
 
4 
2p    B           �       �       5    20    <           �       �       ;   
 
4 
2p    B           �       �       A    �                  �       �       G    B                             M    T
 4	 2�p`    [                       S   ! �     [                      S   [   8                      Y   !       [                      S   8  T                      _   !   �     [                      S   T  `                      e    20    `                       k    20    `                       q    B             �       }       "                       w   h           �      �          �    2 B             �       �       "           	      	      �   h           �      �          �    2-
  
��	��p`0P        �     �       �       N                      �   (           �      �   >    .    .    .    .    .    .    .    .    .    .    *    �	>    b    �>    b    A>    b    A>    b    >    b    >    b    >    b    �>    b    �>    b    A>       �       �       �       �       �       �        �    %   �    *   �    /   �    4   �    9   �    @   �    E   �    L   �    Q   �    X   �    ]   �    d   �    i   �    p   �    u   �    |   �    �   �    �   �    �   �    �   �    �   �    �   �    �   �    �   �    H9R4�D� "D  y"D �$R&4$(p*D(%,~.D,�0�2D0�4�6D4�8�:D8�<�>D8M >  t d 4
 ��           �       �       �           
      
      �   (           �      �   
    `   �    �z  B      =                       �    20    W                       �    B                             �    2�
�p`0           �       �       C                      �   8               �      �   	   �            �   �       �    � �� 
 
2P    (           �       �       �     20                           �   
 
4 
2p    0                       �    B      :                       �                               H      �       �    Unknown exception                             T      �       �                                `      �       �    bad array new length                                �                                        
                               .?AVbad_array_new_length@std@@                    ����                      
      �                    .?AVbad_alloc@std@@                   ����                            �                    .?AVexception@std@@                    ����                            �    string too long     ����    ����        ��������TaskBuffer PrimitiveLightBuffer RisBuffer RisLightDataBuffer LightDataBuffer GeometryInstanceToLightBuffer LightIndexMappingBuffer NeighborOffsets LightReservoirBuffer GIReservoirBuffer LocalLightPdf vector too long                                             K      H                         N                   Q               ����    @                         K                                               W      T                         Z                           ]      Q              ����    @                         W                                         
      c      `                         f                                   i      ]      Q              ����    @                   
      c                     �   (   & 
�        std::exception::`vftable'    �      �  
    �   (   & 
�        std::bad_alloc::`vftable'    �      �  
    �   3   1 
�        std::bad_array_new_length::`vftable'     �      �  
 �"+�a榓鹟嚦螱玌刦k網k俙:h�K蜌�(蝲�?�8鄼A"R�儿�
�-�>Dt暦靿nb筽r�?c斧�,3郧Jv�嚻址Z�瀠霊茽uY禥�-/���28ü>�*ㄑk編�5f惌+Y`@P�"�+葑諦�*讔妅C�潵6�8uDFr退0�+4a昩懹憾S�3T�1@汍R観遞.�54ZB檟.�=挄賄m$P閝+肱2火�%;飑n搩�$袌�/�洭y紬Ut��V掞儭_�\潮7蝣R荼渡#D,嚤踖p禭�?As贛嚤踖p禭飓D堦縵�6萪O�Q犑y鬣镕K霵婬(b蠥U�+�'項j囌nz;]*�'項jc3壯w⒕肿a� #�$覭n3錶`匆猕up泸睨袙"F�?�:Q蚴0k浱U鞶琺� 嶀預棊膬/S;圾j硘嶀預棊膬飸菏N鎞箩邆5>(H7и篼}箩邆5>螬�0|⒙徇�5>;ㄓ$7� �蹰kL�*胾�,mQ�;nK9W{堗	�昸鳐3�%I栶賑?TN黸泃:f]{謑p�$∶聻Hf]{謑p隠�丽Υ俦�%L*-战�yzｅ�`vCP�%u�^�!�毭i�nN鵘J�,L虂脊F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这朸橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 夒嘕-WV8o;き8乿る嘕-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G垪/璘5�越竽��dd�a�:�4V劇D|5�::钝啤�5徇LT旫add�a�:tq(h魤o櫇混7�-坓�(鬄��1�8]Z嘕-WV8o�Tラ~�&-坓�(鬄�汬'这杤暕妝�#(ew`(琈sy*�杜`颀l+�鞯.r擣�0G#盱谑J諶�'(��苳乮5絚_}4n4�硓橂嘕-WV8o�%-<$�9E\$L釉��E光-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H镔5N�4�$坖)%��氋--X阡珙烍丘la  q硱s累値県が齛@帵桲$�輺�"B镆G喬
牔}囔揂—�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �5]_и�;� タY�脌祦�
監�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       P                .debug$S       D�               .debug$T       l                 .rdata         <       擜犞                         )   0       .text$mn       :      眡�     .debug$S                    .text$mn       0      燥"V     .debug$S       �             .text$mn    	   C     ��     .debug$S    
     \       	    .text$x        (      弳1�	    .text$mn             憟⑸     .debug$S    
   @  
           .text$mn       N  A   錽|�     .debug$S       鴖  �          .text$x              P 騧    .text$x              悈蹍    .text$x              �坕    .text$x              �
�    .text$x              f^j    .text$x              蕣/�    .text$x              �|a    .text$x              Q堸�    .text$x              <
猙    .text$x              鼜儕    .text$x              �
衒    .text$x              XA�    .text$x              . -�    .text$x              吻l�    .text$x              W腡�    .text$x              \�#�    .text$x               艁�    .text$x     !         !��    .text$x     "         糆b�    .text$x     #         蟙    .text$x     $         b蓝|    .text$mn    %   <      .ズ     .debug$S    &   0  
       %    .text$mn    '   <      .ズ     .debug$S    (   L  
       '    .text$mn    )   !      :著�     .debug$S    *   <         )    .text$mn    +   2      X于     .debug$S    ,   <         +    .text$mn    -   "       坼	     .debug$S    .   �         -    .text$mn    /   "       坼	     .debug$S    0   �         /    .text$mn    1   W      斷�     .debug$S    2   4         1    .text$mn    3   `      板@�     .debug$S    4   �         3    .text$mn    5   `      ,     .debug$S    6   �         5    .text$mn    7         ��#     .debug$S    8   �          7    .text$mn    9         ��#     .debug$S    :   �          9    .text$mn    ;   B      贘S     .debug$S    <             ;    .text$mn    =   B      贘S     .debug$S    >            =    .text$mn    ?   B      贘S     .debug$S    @   �          ?    .text$mn    A   H       襶.      .debug$S    B   �         A    .text$mn    C   �      kV倻     .debug$S    D   (  6       C    .text$x     E         "E萷C    .text$mn    F          aJ鄔     .debug$S    G   �          F    .text$mn    H         �ッ     .debug$S    I   �          H    .text$mn    J         �ッ     .debug$S    K   �          J    .text$mn    L   `     匮�5     .debug$S    M   �  B       L    .text$mn    N   =      菮�8     .debug$S    O   �         N    .text$mn    P   '       $�     .debug$S    Q            P    .text$mn    R         崪覩     .debug$S    S   �          R        \       A        x                �                �                �                �                �                �                      +        7      9        Q      R        q      ?        �          i�                    �      %        �      ;        �          i�                          )        3      7        X      '        �      =        �          i�                    �      F               H              L        r      5        �      3        �      -        �      /                      }      C        �               �                               �               �                     P        ,      N        Y      1        �      J        �      	        -              T              �              �              X              �      E        /	              �	              )
              �
              #              �                            �               
      !        �
      "              #        �              	      $        �                            ~              �              v              �              n              �               �                              %           memcpy           memmove          memset           $LN13       A    $LN5        +    $LN10       ?    $LN7        %    $LN13       ;    $LN10       '    $LN16       =    $LN3        F    $LN4        F    $LN3       H    $LN4        H    $LN144  `  L    $LN151      L    $LN39   `   5    $LN42       5    $LN39   `   3    $LN42       3    $LN10       -    $LN10       /    $LN1403 N      $LN1408         $LN70   �   C    $LN74       C    $LN20   =   N    $LN23       N    $LN30   W   1    $LN33       1    $LN3       J    $LN4        J    $LN107  C  	        =  
       $LN112      	    $LN4            $LN4            $LN14   :       $LN17           .xdata      T          F┑@A        �      T    .pdata      U         X賦鶤        �      U    .xdata      V          （亵+        �      V    .pdata      W          T枨+        (      W    .xdata      X          %蚘%?        P      X    .pdata      Y         惻竗?        w      Y    .xdata      Z          （亵%        �      Z    .pdata      [         2Fb�%        �      [    .xdata      \          %蚘%;        �      \    .pdata      ]         惻竗;              ]    .xdata      ^          （亵'        ;      ^    .pdata      _         2Fb�'        o      _    .xdata      `          %蚘%=        �      `    .pdata      a         惻竗=        �      a    .xdata      b          懐j濬              b    .pdata      c         Vbv鵉        5      c    .xdata      d          �9�H        d      d    .pdata      e         �1癏        �      e    .xdata      f          蔜-錖        �      f    .pdata      g         愶LL              g    .xdata      h         �qL僉        f      h    .pdata      i         ~蕉絃        �      i    .xdata      j         |盠        *      j    .pdata      k         瞚挨L        �      k    .xdata      l         S!熐L        �      l    .pdata      m         �o圠        P      m    .xdata      n          （亵5        �      n    .pdata      o         粻胄5        �      o    .xdata      p          （亵3        �      p    .pdata      q         粻胄3        "      q    .xdata      r         /
�-        F      r    .pdata      s         +eS�-              s    .xdata      t   	      �#荤-        �      t    .xdata      u         j-        �      u    .xdata      v          3狷 -        3      v    .xdata      w         /
�/        n      w    .pdata      x         +eS�/        �      x    .xdata      y   	      �#荤/        �      y    .xdata      z         j/              z    .xdata      {          3狷 /        _      {    .xdata      |   $      �mW        �      |    .pdata      }         偒(l              }    .xdata      ~   	      � )9        �      ~    .xdata         �      廰q        �          .xdata      �   S       园6�        x      �    .xdata      �         d閴C        �      �    .pdata      �         ﹎C        C      �    .xdata      �   	      � )9C        �      �    .xdata      �         S秢C        �      �    .xdata      �          猐�籆        H       �    .xdata      �          �9�N        �       �    .pdata      �         現�N        �       �    .xdata      �          （亵1        !      �    .pdata      �         啁鉥1        >!      �    .xdata      �          �9�J        t!      �    .pdata      �         �1癑        �!      �    .xdata      �         啄qJ	        �!      �    .pdata      �         何e	        d"      �    .xdata      �   
      B>z]	        �"      �    .xdata      �          �2g�	        W#      �    .xdata      �         T�8	        �#      �    .xdata      �         r%�	        Q$      �    .xdata      �          弯		        �$      �    .xdata      �          3賟P	        I%      �    .pdata      �         銀�*	        �%      �    .voltbl     �                  _volmd      �    .xdata      �          （亵        Z&      �    .pdata      �         �#洢        �&      �    .xdata      �          %蚘%        �&      �    .pdata      �         }S蛥        �&      �    .xdata      �          �9�        "'      �    .pdata      �         礝
        '      �    .rdata      �                      �'     �    .rdata      �          �;�         �'      �    .rdata      �                      (     �    .rdata      �                      0(     �    .rdata      �          �)         R(      �    .xdata$x    �                      ~(      �    .xdata$x    �         虼�)         �(      �    .data$r     �   /      嶼�         �(      �    .xdata$x    �   $      4��         �(      �    .data$r     �   $      鎊=         =)      �    .xdata$x    �   $      銸E�         W)      �    .data$r     �   $      騏糡         �)      �    .xdata$x    �   $      4��         �)      �        �)           .rdata      �          燺渾         *      �    .data       �           烀�          (*      �        \*     �    .rdata      �          築糰         �*      �    .rdata      �          0         �*      �    .rdata      �   
       V7         �*      �    .rdata      �          侽         �*      �    .rdata      �          �W         
+      �    .rdata      �          /f#-         0+      �    .rdata      �          詌!I         b+      �    .rdata      �          逛�         �+      �    .rdata      �          e摵Z         �+      �    .rdata      �          
�/�         �+      �    .rdata      �          �(�         ,      �    .rdata      �          IM         ",      �    .rdata$r    �   $      'e%�         H,      �    .rdata$r    �         �          `,      �    .rdata$r    �                      v,      �    .rdata$r    �   $      Gv�:         �,      �    .rdata$r    �   $      'e%�         �,      �    .rdata$r    �         }%B         �,      �    .rdata$r    �                      �,      �    .rdata$r    �   $      `         �,      �    .rdata$r    �   $      'e%�         -      �    .rdata$r    �         �弾         1-      �    .rdata$r    �                      R-      �    .rdata$r    �   $      H衡�         s-      �        �-           .rdata      �          � �         �-      �    _fltused         .debug$S    �   4          �    .debug$S    �   4          �    .debug$S    �   @          �    .chks64     �   8                �-  ?c_IdentityTransform@rt@nvrhi@@3QBMB ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ??1TextureDesc@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ??0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z ?InitializeNeighborOffsets@RtxdiResources@@QEAAXPEAVICommandList@nvrhi@@I@Z ?ComputePdfTextureSize@rtxdi@@YAXIAEAI00@Z ?FillNeighborOffsetBuffer@rtxdi@@YAXPEAEI@Z ?GetReservoirBufferParameters@ReSTIRDIContext@rtxdi@@QEBA?AURTXDI_ReservoirBufferParameters@@XZ ?GetStaticParameters@ReSTIRDIContext@rtxdi@@QEBAAEBUReSTIRDIStaticParameters@2@XZ ?getTotalSizeInElements@RISBufferSegmentAllocator@rtxdi@@QEBAIXZ ?getNextPowerOf2@@YAII@Z ?deallocate@?$allocator@E@std@@QEAAXQEAE_K@Z ??1?$vector@EV?$allocator@E@std@@@std@@QEAA@XZ ?_Xlength@?$vector@EV?$allocator@E@std@@@std@@CAXXZ ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ??$_Zero_range@PEAE@std@@YAPEAEQEAE0@Z ??$_Copy_memmove@PEAEPEAE@std@@YAPEAEPEAE00@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ?dtor$0@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$0@?0??InitializeNeighborOffsets@RtxdiResources@@QEAAXPEAVICommandList@nvrhi@@I@Z@4HA ?dtor$10@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$11@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$13@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$15@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$18@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$1@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$20@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$22@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$24@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$26@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$28@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$2@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$30@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$3@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$4@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$5@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$6@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$7@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$8@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA ?dtor$9@?0???0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z$0 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??1TextureDesc@nvrhi@@QEAA@XZ $pdata$??1TextureDesc@nvrhi@@QEAA@XZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBuffer@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z $pdata$??0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z $cppxdata$??0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z $stateUnwindMap$??0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z $ip2state$??0RtxdiResources@@QEAA@PEAVIDevice@nvrhi@@AEBVReSTIRDIContext@rtxdi@@AEBVRISBufferSegmentAllocator@4@IIII@Z $unwind$?InitializeNeighborOffsets@RtxdiResources@@QEAAXPEAVICommandList@nvrhi@@I@Z $pdata$?InitializeNeighborOffsets@RtxdiResources@@QEAAXPEAVICommandList@nvrhi@@I@Z $cppxdata$?InitializeNeighborOffsets@RtxdiResources@@QEAAXPEAVICommandList@nvrhi@@I@Z $stateUnwindMap$?InitializeNeighborOffsets@RtxdiResources@@QEAAXPEAVICommandList@nvrhi@@I@Z $ip2state$?InitializeNeighborOffsets@RtxdiResources@@QEAAXPEAVICommandList@nvrhi@@I@Z $unwind$?deallocate@?$allocator@E@std@@QEAAXQEAE_K@Z $pdata$?deallocate@?$allocator@E@std@@QEAAXQEAE_K@Z $unwind$??1?$vector@EV?$allocator@E@std@@@std@@QEAA@XZ $pdata$??1?$vector@EV?$allocator@E@std@@@std@@QEAA@XZ $unwind$?_Xlength@?$vector@EV?$allocator@E@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@EV?$allocator@E@std@@@std@@CAXXZ $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $tryMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $handlerMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z $unwind$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $pdata$?catch$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@EV?$allocator@E@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA $unwind$??$_Zero_range@PEAE@std@@YAPEAEQEAE0@Z $pdata$??$_Zero_range@PEAE@std@@YAPEAEQEAE0@Z $unwind$??$_Copy_memmove@PEAEPEAE@std@@YAPEAEPEAE00@Z $pdata$??$_Copy_memmove@PEAEPEAE@std@@YAPEAEPEAE00@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0L@PFMEAMKJ@TaskBuffer@ ??_C@_0BF@LIFPJGEE@PrimitiveLightBuffer@ ??_C@_09KANKKANP@RisBuffer@ ??_C@_0BD@DKLILEJG@RisLightDataBuffer@ ??_C@_0BA@OBDKCDF@LightDataBuffer@ ??_C@_0BO@NCNIKHGF@GeometryInstanceToLightBuffer@ ??_C@_0BI@BFBPFJAL@LightIndexMappingBuffer@ ??_C@_0BA@MGEDFABD@NeighborOffsets@ ??_C@_0BF@OEEBKJBB@LightReservoirBuffer@ ??_C@_0BC@HHMLPLLI@GIReservoirBuffer@ ??_C@_0O@IPFEKONO@LocalLightPdf@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __security_cookie __xmm@000000000000000f0000000000000000 