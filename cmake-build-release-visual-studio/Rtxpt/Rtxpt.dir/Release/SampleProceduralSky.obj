d�:驁�( �      .drectve        ~  $Y               
 .debug$S        ㄇ   J"        @ B.debug$T        l   �"             @ B.rdata          @   #             @ @@.text$mn        �   F# =$         P`.debug$S        �  �$ 3*        @B.text$x         &   7+ ]+         P`.text$mn           g+              P`.debug$S        @  o+ �,        @B.text$mn        �   �,              P`.debug$S        �  �- K/        @B.text$mn        n  �/              P`.debug$S        �  	1 �3        @B.text$mn        �   I4              P`.debug$S        ,  �4 6        @B.text$mn        :   T6 �6         P`.debug$S          �6 �8        @B.text$mn          D9 R:         P`.debug$S        P  �: 鐯     2   @B.text$mn        �   蹷 蜟         P`.debug$S        `  D tI        @B.text$x         &   (J NJ         P`.text$mn        �   XJ HK         P`.debug$S        L  嶬 贠        @B.text$x         &    萈         P`.text$mn        �   襊 翾         P`.debug$S        D  R LV        @B.text$x         &   W :W         P`.text$mn        �   DW 'X         P`.debug$S        �  cX 7]        @B.text$x         &   '^ M^         P`.text$mn           W^ _^         P`.debug$S        �   i^ I_        @B.text$mn           卂 峗         P`.debug$S        �   梍 w`        @B.text$mn        �  砢 7b     
    P`.debug$S        �	  沚 l     N   @B.text$mn        �  媜 q     
    P`.debug$S        �	  zq b{     L   @B.text$mn        �  Z~  �         P`.debug$S        �
  p� 8�     R   @B.text$mn           l�              P`.debug$S        �  厧 i�     
   @B.text$mn        	   蛺 謵         P`.debug$S        �   鄲 軕        @B.text$mn        :   ,� f�         P`.debug$S        L  p� 紦        @B.text$mn        #   �              P`.debug$S        �   /� #�        @B.text$mn        �   _� 魰         P`.debug$S        8   6�        @B.text$mn        �  陿 簺     	    P`.debug$S           � 4�        @B.text$mn        �   詹     Z    P`.debug$S          Y� �       @B.text$x            M4 Y4         P`.text$x            c4 o4         P`.text$x            y4 �4         P`.text$x            �4 �4         P`.text$x            �4 �4         P`.text$x            �4 �4         P`.text$x            �4 �4         P`.text$x            �4 5         P`.text$x            5 %5         P`.text$x            /5 ;5         P`.text$x            E5 Q5         P`.text$x            [5 g5         P`.text$x            q5 }5         P`.text$x            �5 �5         P`.text$x            �5 �5         P`.text$x            �5 �5         P`.text$x            �5 �5         P`.text$x            �5 �5         P`.text$x            �5 6         P`.text$x            6 6         P`.text$x            !6 -6         P`.text$x            76 C6         P`.text$x            M6 Y6         P`.text$x            c6 o6         P`.text$x            y6 �6         P`.text$x         )   �6 �6         P`.text$x         .   �6 �6         P`.text$x         .   �6 (7         P`.text$mn        M   27 7         P`.debug$S        <  �7 �8     
   @B.text$mn        <   =9 y9         P`.debug$S        0  �9 �:     
   @B.text$mn        <   +; g;         P`.debug$S        L  �; �<     
   @B.text$mn        !   5= V=         P`.debug$S        <  j= �>        @B.text$mn        2   �> ?         P`.debug$S        <  (? d@        @B.text$mn        <   蹳 A         P`.debug$S        8  6A nB     
   @B.text$mn        W   褺 )C         P`.debug$S        @  QC 慏     
   @B.text$mn        �   鮀 蹺         P`.debug$S        �  ,F 腏     "   @B.text$mn           L .L         P`.debug$S        �  8L 麺        @B.text$mn        "   8N              P`.debug$S        �  ZN 鍻        @B.text$mn        ^   哖 銹         P`.debug$S        X  鳳 PT        @B.text$mn           U U         P`.debug$S        h  'U 廣        @B.text$mn        K   薞              P`.debug$S        �  W 闤        @B.text$mn        K   vY              P`.debug$S        �  罽         @B.text$mn        K   -\              P`.debug$S        �  x\ L^        @B.text$mn        }  豝 U`         P`.debug$S        �  _` Sf     2   @B.text$mn           Gh Oh         P`.debug$S        �   Yh Qi        @B.text$mn           峣 爄         P`.debug$S        �   磇 榡        @B.text$mn           纉              P`.debug$S        �   胘 焝        @B.text$mn           踜 頺         P`.debug$S        �   l 鈒        @B.text$mn           m #m         P`.debug$S        �   -m %n        @B.text$mn           an tn         P`.debug$S        �   坣 `o        @B.text$mn        !   坥 ﹐         P`.debug$S        �   硂 損        @B.text$mn        B   蟨 q         P`.debug$S          /q 7r        @B.text$mn        !   sr 攔         P`.debug$S        �   瀝 ~s        @B.text$mn        B   簊 黶         P`.debug$S           t u        @B.text$mn        B   Vu 榰         P`.debug$S          秛 苬        @B.text$mn        B   w Dw         P`.debug$S        �   bw ^x        @B.text$mn        B   歺 躼         P`.debug$S          鷛         @B.text$mn        B   :z |z         P`.debug$S          歾         @B.text$mn        H   鈡              P`.debug$S        �  *| 題        @B.text$mn        F   L�     "    P`.debug$S        �  爞 D�        @B.text$x            鋱 饒         P`.text$mn        (   鷪              P`.debug$S        d  "� 唺        @B.text$mn        (                 P`.debug$S        h  &� 帉        @B.text$mn        (   �              P`.debug$S        `  .� 帋        @B.text$mn        (   �              P`.debug$S        h  .� 枑        @B.text$mn        (   �              P`.debug$S        h  6� 瀿        @B.text$mn        h  � ~�     0    P`.debug$S          ^� f�     L   @B.text$mn        >   ^� 湱         P`.debug$S          碍 窗        @B.text$mn        A   副              P`.debug$S        H   A�        @B.text$mn           钩 食         P`.debug$S        �   猿 檀        @B.text$mn            � (�         P`.debug$S        �   F� 
�        @B.text$mn        9   F� �         P`.debug$S        �   Ф 椃        @B.text$mn        9   绶  �         P`.debug$S          H� P�        @B.text$mn        a   牴 �         P`.debug$S          � -�        @B.text$mn           踅 �         P`.debug$S        �   � 尉        @B.text$mn        �   
� 惪         P`.debug$S        0  た 月        @B.text$mn        r   溍 �         P`.debug$S          6� F�        @B.text$mn           酒              P`.debug$S        L  善 �        @B.text$mn           e�              P`.debug$S        �  ~� 
�     
   @B.text$mn        ?   n�              P`.debug$S        �   √        @B.text$mn           A� R�         P`.debug$S        �   \� X�        @B.text$mn        N   斘 馕         P`.debug$S          鑫 �     
   @B.text$mn        �   j� �         P`.debug$S          =� Q�        @B.text$x            -� 9�         P`.text$mn           C� K�         P`.debug$S        �   U� A�        @B.text$mn           }� 呚         P`.debug$S        �   徹 {�        @B.text$mn        �   焚 O�         P`.debug$S        �  c� 鬏        @B.text$mn        �   愚 a�         P`.debug$S        �  k� +�        @B.text$mn           /� B�         P`.debug$S        �   L�  �        @B.xdata             \�             @0@.pdata             p� |�        @0@.xdata             氬             @0@.pdata             ㈠         @0@.xdata             体             @0@.pdata             劐 溴        @0@.xdata             �             @0@.pdata             
� �        @0@.xdata             4�             @0@.pdata             @� L�        @0@.xdata             j�             @0@.pdata             r� ~�        @0@.xdata             滄             @0@.pdata             ㄦ 存        @0@.xdata             益             @0@.pdata             阪 骀        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             6�             @0@.pdata             >� J�        @0@.xdata             h�             @0@.pdata             p� |�        @0@.xdata             氱             @0@.pdata             ︾ 茬        @0@.xdata             戌 溏        @0@.pdata             � �        @0@.xdata             ,� <�        @0@.pdata             Z� f�        @0@.xdata             勮             @0@.pdata             岃 樿        @0@.xdata             惰 舞        @0@.pdata             扈         @0@.xdata             � *�        @0@.pdata             H� T�        @0@.xdata             r�             @0@.pdata             z� 嗛        @0@.xdata             ら             @0@.pdata              搁        @0@.xdata             珠             @0@.pdata             忾 铋        @0@.voltbl            �               .xdata             �             @0@.pdata             � "�        @0@.xdata             @�             @0@.pdata             L� X�        @0@.xdata             v�             @0@.pdata             ~� 婈        @0@.xdata                          @0@.pdata             搓 狸        @0@.xdata             揸             @0@.pdata             觋 鲫        @0@.xdata             � (�        @0@.pdata             2� >�        @0@.xdata             \�             @0@.pdata             h� t�        @0@.xdata             掚             @0@.pdata             炿         @0@.xdata             入             @0@.pdata             须 茈        @0@.xdata                          @0@.pdata             � �        @0@.xdata             ,�             @0@.pdata             4� @�        @0@.xdata             ^� r�        @0@.pdata             嗢 掛        @0@.xdata          	   办 轨        @@.xdata             挽 造        @@.xdata             揿             @@.xdata             犰 蹯        @0@.pdata             	� �        @0@.xdata             3� 8�        @@.xdata             B�             @@.xdata             E�             @0@.pdata             M� Y�        @0@.xdata             w�             @0@.pdata             � 嬳        @0@.xdata             ╉ 鬼        @0@.pdata             晚 夙        @0@.xdata          	   黜  �        @@.xdata             � �        @@.xdata             $�             @@.xdata          (   '� O�        @0@.pdata             c� o�        @0@.xdata          	   嶎 栴        @@.xdata          �    f�        @@.xdata          I   堭             @@.xdata             佯             @0@.pdata             兖 屦        @0@.xdata             �             @0@.pdata             � �        @0@.xdata             5�             @0@.pdata             =� I�        @0@.voltbl            g�                .xdata             y� 戱        @0@.pdata             ヱ 瘪        @0@.xdata          	   像 伛        @@.xdata             祚 蝰        @@.xdata                          @@.voltbl            �                .xdata             � *�        @0@.pdata             >� J�        @0@.xdata          	   h� q�        @@.xdata             咈 嬺        @@.xdata             曭             @@.xdata             橋         @0@.pdata             简 闰        @0@.xdata          	   骝 矧        @@.xdata             � 	�        @@.xdata             �             @@.xdata             � &�        @0@.pdata             :� F�        @0@.xdata          	   d� m�        @@.xdata             侒 圀        @@.xdata             戵             @@.xdata             旙 ん        @0@.pdata             阁 捏        @0@.xdata          	   怏 塍        @@.xdata             �� �        @@.xdata             �             @@.xdata             � "�        @0@.pdata             6� B�        @0@.xdata          	   `� i�        @@.xdata             }� 凈        @@.xdata             嶔             @@.xdata          4   愻             @0@.pdata             聂 恤        @0@.xdata             铘 
�        @0@.pdata             � *�        @0@.xdata          	   H� Q�        @@.xdata             e� q�        @@.xdata          
   咍             @@.xdata             忰             @0@.pdata             楑 ｕ        @0@.xdata             刘 挣        @0@.pdata             篚 ��        @0@.xdata             � -�        @0@.pdata             K� W�        @0@.voltbl            u�               .xdata             w�             @0@.pdata             � 嬾        @0@.xdata              仅        @0@.pdata             埚 琏        @0@.xdata             � �        @0@.pdata             3� ?�        @0@.voltbl            ]�               .xdata             _�             @0@.pdata             g� s�        @0@.xdata             戺         @0@.pdata             明 削        @0@.xdata             眵         @0@.pdata             � '�        @0@.voltbl            E�               .xdata             G� c�        @0@.pdata             w� 凐        @0@.xdata          	   ▲         @@.xdata             绝 镍        @@.xdata             硒             @@.xdata             银             @0@.pdata             邙 骧        @0@.xdata             �             @0@.pdata             � $�        @0@.xdata          ,   B�             @0@.pdata             n� z�        @0@.xdata             橓             @0@.pdata              殆        @0@.xdata             淫 怡        @0@.pdata             鳄 �        @0@.xdata              � %�        @@.xdata             /�             @@.xdata             2�             @0@.pdata             F� R�        @0@.xdata             p� 匉        @0@.pdata             Ⅹ         @0@.xdata             铁 茭        @0@.pdata              �        @0@.xdata             $� 8�        @0@.pdata             V� b�        @0@.xdata             �� 慃        @0@.pdata              蝴        @0@.xdata             佧             @0@.pdata             棼 酐        @0@.xdata              � 2�        @0@.pdata             P� \�        @0@.xdata              z� 汓        @0@.pdata             更 狞        @0@.xdata             恻 螯        @0@.pdata             � �        @0@.xdata             :�             @0@.pdata             J� V�        @0@.xdata             t� 慅        @0@.pdata              糊        @0@.xdata             佚 酏        @0@.pdata             � �        @0@.xdata             <� L�        @0@.pdata             j� v�        @0@.xdata             旫 剥        @0@.pdata             宁 玄        @0@.xdata          	   铪 齄        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             '� 3�        @0@.xdata             Q� m�        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata             � �        @@.xdata             �             @@.xdata             �             @0@.pdata             � �        @0@.xdata                           @0@.pdata               &         @0@.xdata             D              @0@.pdata             L  X         @0@.xdata             v              @0@.pdata             �  �         @0@.xdata              �  �         @0@.pdata             �  �         @0@.xdata               8        @0@.pdata             V b        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata             � �        @0@.pdata                      @0@.xdata          	   2 ;        @@.xdata             O U        @@.xdata             _             @@.xdata             b             @0@.pdata             j v        @0@.xdata             � �        @0@.pdata             � �        @0@.xdata          	   � �        @@.xdata                      @@.xdata                          @@.xdata                          @0@.pdata             ' 3        @0@.rdata             Q i        @@@.rdata             �             @@@.rdata             � �        @@@.rdata             � �        @@@.rdata                          @@@.xdata$x            6        @@@.xdata$x           J f        @@@.data$r         /   � �        @@�.xdata$x        $   � �        @@@.data$r         $   �         @@�.xdata$x        $   # G        @@@.data$r         $   [         @@�.xdata$x        $   � �        @@@.rdata             �             @@@.data               �             @ @�.rdata             � 	        @@@.data$r         (   ' O        @@�.xdata$x        $   Y }        @@@.rdata             � �        @@@.rdata             � �        @@@.xdata$x           �         @@@.xdata$x        $   - Q        @@@.data$r         '   y �        @@�.xdata$x        $   � �        @@@.data$r         (   � 
        @@�.xdata$x        $    8        @@@.rdata          8   L �        @@@.rdata             �             @@@.rdata          8   � 
	        @@@.rdata             P	             @0@.rdata             W	             @@@.rdata             e	             @@@.rdata             x	             @@@.rdata             �	             @@@.rdata             �	             @@@.rdata             �	             @@@.rdata             �	             @@@.rdata             �	             @0@.rdata          6   
             @@@.rdata          2   ;
             @@@.rdata          3   m
             @@@.rdata          )   �
             @@@.rdata          (   �
             @@@.rdata          ^   �
             @P@.rdata             O             @0@.rdata             T             @@@.rdata             _             @@@.rdata             n             @@@.rdata             �             @@@.rdata             �             @@@.rdata             �             @@@.rdata             �             @@@.rdata             �             @@@.data              �         @@�.data                       @@�.rdata$r        $   ( L        @@@.rdata$r           j ~        @@@.rdata$r           � �        @@@.rdata$r        $   � �        @@@.rdata$r        $   � �        @@@.rdata$r           
 ,
        @@@.rdata$r           6
 J
        @@@.rdata$r        $   ^
 �
        @@@.rdata$r        $   �
 �
        @@@.rdata$r           �
 �
        @@@.rdata$r           �
         @@@.rdata$r        $   0 T        @@@.rdata$r        $   h �        @@@.rdata$r           � �        @@@.rdata$r           � �        @@@.rdata$r        $   �         @@@.data$rs        )   ( Q        @@�.rdata$r           [ o        @@@.rdata$r           y �        @@@.rdata$r        $   � �        @@@.rdata$r        $   � �        @@@.rdata$r           	         @@@.rdata$r           ' C        @@@.rdata$r        $   a �        @@@.rdata$r        $   � �        @@@.rdata$r           � �        @@@.rdata$r        $   �         @@@.rdata$r        $   E i        @@@.rdata$r        $   } �        @@@.data$rs        2   � �        @@�.rdata$r           �         @@@.rdata$r            -        @@@.rdata$r        $   A e        @@@.rdata$r        $   y �        @@@.data$rs        1   � �        @@�.rdata$r           � 
        @@@.rdata$r            (        @@@.rdata$r        $   < `        @@@.rdata             t             @0@.rdata             x             @0@.rdata             |             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @@@.rdata             �             @@@.rdata             �             @0@.rdata             �             @@@.rdata             �             @0@.rdata             �             @@@.rdata             �             @0@.rdata             �             @0@.rdata             �             @@@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @0@.rdata             �             @P@.rdata             �             @P@.rdata                          @P@.rdata                          @P@.rdata             ,             @P@.debug$S        8   < t        @B.debug$S        8   � �        @B.debug$S        8   �         @B.debug$S        4     T        @B.debug$S        D   h �        @B.debug$S        4   � �        @B.debug$S        @    H        @B.debug$S        D   \ �        @B.chks64         �  �              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"uuid.lib" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   �  a     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\SampleProceduralSky.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $tf  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Ensure_adl  $_Weak_order  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $app  $vfs  $math 	 $colors  $log  $Json 	 $stdext  $ImGui   �   � P U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gx O U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gy  緌    TYSPEC_CLSID  緌   TYSPEC_FILEEXT  緌   TYSPEC_MIMETYPE  緌   TYSPEC_FILENAME  緌   TYSPEC_PROGID  緌   TYSPEC_PACKAGENAME R U  ��Q std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Dx2 < U  �X呩std::integral_constant<__int64,31556952>::value P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx1 T U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gy D U  
� 纈*�   std::integral_constant<__int64,864000000000>::value ( U  �X呩std::ratio<31556952,1>::num $ U   std::ratio<31556952,1>::den x _   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment ( U   std::ratio<1,864000000000>::num 0 U  
� 纈*�   std::ratio<1,864000000000>::den  �1   std::_Consume_header  �1   std::_Generate_header  U   std::ratio<12,1>::num  U   std::ratio<12,1>::den K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Dx2 Q U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx1  U   std::ratio<1,12>::num  U   std::ratio<1,12>::den M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx2 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gx L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gy -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets ':    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi ; U  �r ( std::integral_constant<__int64,2629746>::value i _   std::allocator<std::shared_ptr<donut::engine::TextureData> >::_Minimum_asan_allocation_alignment ; :   std::atomic<unsigned __int64>::is_always_lock_free E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment ) <   nvrhi::ObjectTypes::SharedHandle - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer  �    LightType_None 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView  �   LightType_Directional  �   LightType_Spot - :    std::chrono::system_clock::is_steady 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView  �   LightType_Point : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Same_size_and_compatible * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_constructible 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_assignable + <  �  nvrhi::ObjectTypes::VK_Sampler 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule . <  �
  nvrhi::ObjectTypes::VK_RenderPass / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool $ U   std::ratio<1,10000000>::num 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet ( U  ��枠 std::ratio<1,10000000>::den 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo P U  �r ( std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Dx2 6 :   std::_Iterator_base0::_Unwrap_when_unverified � _   std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment j _   std::allocator<std::shared_ptr<donut::engine::TextureData> *>::_Minimum_asan_allocation_alignment N U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx2 R U  �r ( std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gx M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gy J U  std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Dx2 C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE 3 U  � std::integral_constant<__int64,200>::value E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity P U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx1   U   std::ratio<1,3600>::num   U  std::ratio<1,3600>::den _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment 7 :   std::_Iterator_base12::_Unwrap_when_unverified L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx2 L U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx2  :    std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi #:   std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard K U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gx K U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gy $ U  � std::ratio<200,146097>::num ( U  ��: std::ratio<200,146097>::den d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE � �   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Minimum_map_size f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size Q U  �X呩std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Dx2 O U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx2 S U  �X呩std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx2 � _   std::allocator<std::_List_node<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment N U  H std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gx N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gy F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 2 U  2 std::integral_constant<__int64,50>::value H U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gy 4 U  std::integral_constant<__int64,3600>::value : U  �� std::integral_constant<__int64,438291>::value  U   std::ratio<1,1>::num  U   std::ratio<1,1>::den ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos # U  2 std::ratio<50,438291>::num ' U  �� std::ratio<50,438291>::den N U  ��Q std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Dx2 L U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx2 P U  ��Q std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gx K U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gy i _   std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >::_Bytes 8 :   std::atomic<unsigned long>::is_always_lock_free n �   std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >::_Block_size J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N U  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 � �   std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Block_size 2 U   std::integral_constant<__int64,24>::value L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( U  ��枠 std::ratio<10000000,1>::num $ U   std::ratio<10000000,1>::den P U  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy  U   std::ratio<1,24>::num  U   std::ratio<1,24>::den < U  ��枠 std::integral_constant<__int64,10000000>::value N U  �r ( std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Nx2 / <  � nvrhi::rt::cluster::kClasByteAlignment J U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Dx2 . <   nvrhi::rt::cluster::kClasMaxTriangles - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex L U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx2 P U  �r ( std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gx K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gy + :    std::_Aligned_storage<72,8>::_Fits * :    std::_Aligned<72,8,char,0>::_Fits � :   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified  觪   PowerUserMaximum + :    std::_Aligned<72,8,short,0>::_Fits 2 U  
 std::integral_constant<__int64,10>::value - :   std::chrono::steady_clock::is_steady  �    DVEXTENT_CONTENT . :    std::integral_constant<bool,0>::value ) :   std::_Aligned<72,8,int,0>::_Fits O _   std::allocator<nvrhi::BufferRange>::_Minimum_asan_allocation_alignment & U   std::ratio<1,1000000000>::num * U  � 蕷;std::ratio<1,1000000000>::den # U  
 std::ratio<10,438291>::num ' U  �� std::ratio<10,438291>::den O U  �X呩std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Dx2 M U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx1 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx2 3_   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size 3_   std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets Q U  �X呩std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gx -:    std::_Hash<std::_Umap_traits<nvrhi::ITexture *,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> >,std::allocator<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gy 1 U   std::integral_constant<__int64,5>::value . :   std::integral_constant<bool,1>::value O U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Nx2 S U  � 蕷;std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Dx2 # $q   BINDSTATUS_FINDINGRESOURCE  $q   BINDSTATUS_CONNECTING  $q   BINDSTATUS_REDIRECTING % $q   BINDSTATUS_BEGINDOWNLOADDATA # $q   BINDSTATUS_DOWNLOADINGDATA # $q   BINDSTATUS_ENDDOWNLOADDATA + $q   BINDSTATUS_BEGINDOWNLOADCOMPONENTS ( $q   BINDSTATUS_INSTALLINGCOMPONENTS ) $q  	 BINDSTATUS_ENDDOWNLOADCOMPONENTS # $q  
 BINDSTATUS_USINGCACHEDCOPY " $q   BINDSTATUS_SENDINGREQUEST $ $q   BINDSTATUS_CLASSIDAVAILABLE % $q  
 BINDSTATUS_MIMETYPEAVAILABLE * $q   BINDSTATUS_CACHEFILENAMEAVAILABLE & $q   BINDSTATUS_BEGINSYNCOPERATION Q U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx1 $ $q   BINDSTATUS_ENDSYNCOPERATION # $q   BINDSTATUS_BEGINUPLOADDATA ! $q   BINDSTATUS_UPLOADINGDATA Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx1 # U   std::ratio<5,2629746>::num ! $q   BINDSTATUS_ENDUPLOADDATA ' U  �r ( std::ratio<5,2629746>::den # $q   BINDSTATUS_PROTOCOLCLASSID  $q   BINDSTATUS_ENCODING * U  � 蕷;std::ratio<1000000000,1>::num - $q   BINDSTATUS_VERIFIEDMIMETYPEAVAILABLE ( $q   BINDSTATUS_CLASSINSTALLLOCATION  $q   BINDSTATUS_DECODING & U   std::ratio<1000000000,1>::den & $q   BINDSTATUS_LOADINGMIMEHANDLER , $q   BINDSTATUS_CONTENTDISPOSITIONATTACH ( $q   BINDSTATUS_FILTERREPORTMIMETYPE ' $q   BINDSTATUS_CLSIDCANINSTANTIATE % $q   BINDSTATUS_IUNKNOWNAVAILABLE  $q   BINDSTATUS_DIRECTBIND  $q   BINDSTATUS_RAWMIMETYPE " $q    BINDSTATUS_PROXYDETECTING   $q  ! BINDSTATUS_ACCEPTRANGES  $q  " BINDSTATUS_COOKIE_SENT + $q  # BINDSTATUS_COMPACT_POLICY_RECEIVED % $q  $ BINDSTATUS_COOKIE_SUPPRESSED ( $q  % BINDSTATUS_COOKIE_STATE_UNKNOWN ' $q  & BINDSTATUS_COOKIE_STATE_ACCEPT U U  � 蕷;std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx2 ' $q  ' BINDSTATUS_COOKIE_STATE_REJECT Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx2 ' $q  ( BINDSTATUS_COOKIE_STATE_PROMPT & $q  ) BINDSTATUS_COOKIE_STATE_LEASH * $q  * BINDSTATUS_COOKIE_STATE_DOWNGRADE P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gx  $q  + BINDSTATUS_POLICY_HREF P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gy  $q  , BINDSTATUS_P3P_HEADER + $q  - BINDSTATUS_SESSION_COOKIE_RECEIVED . $q  . BINDSTATUS_PERSISTENT_COOKIE_RECEIVED + $q  / BINDSTATUS_SESSION_COOKIES_ALLOWED   $q  0 BINDSTATUS_CACHECONTROL . $q  1 BINDSTATUS_CONTENTDISPOSITIONFILENAME ) $q  2 BINDSTATUS_MIMETEXTPLAINMISMATCH & $q  3 BINDSTATUS_PUBLISHERAVAILABLE ( $q  4 BINDSTATUS_DISPLAYNAMEAVAILABLE $ $q  5 BINDSTATUS_SSLUX_NAVBLOCKED , $q  6 BINDSTATUS_SERVER_MIMETYPEAVAILABLE , $q  7 BINDSTATUS_SNIFFED_CLASSIDAVAILABLE " $q  8 BINDSTATUS_64BIT_PROGRESS  $q  8 BINDSTATUS_LAST  $q  9 BINDSTATUS_RESERVED_0  $q  : BINDSTATUS_RESERVED_1  $q  ; BINDSTATUS_RESERVED_2  $q  < BINDSTATUS_RESERVED_3 L U  ��Q std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Nx2  $q  = BINDSTATUS_RESERVED_4  $q  > BINDSTATUS_RESERVED_5  $q  ? BINDSTATUS_RESERVED_6 H U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Dx2  $q  @ BINDSTATUS_RESERVED_7  $q  A BINDSTATUS_RESERVED_8  $q  B BINDSTATUS_RESERVED_9 E U  
� 牳0F  std::integral_constant<__int64,3600000000000>::value  $q  C BINDSTATUS_RESERVED_A  $q  D BINDSTATUS_RESERVED_B  $q  E BINDSTATUS_RESERVED_C  $q  F BINDSTATUS_RESERVED_D  $q  G BINDSTATUS_RESERVED_E  $q  H BINDSTATUS_RESERVED_F  $q  I BINDSTATUS_RESERVED_10  $q  J BINDSTATUS_RESERVED_11  $q  K BINDSTATUS_RESERVED_12  $q  L BINDSTATUS_RESERVED_13  $q  M BINDSTATUS_RESERVED_14 J U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx1 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx1 J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx2 N U  ��Q std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx2 I U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gx I U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gy E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment 1 U  
� 牳0F  std::ratio<3600000000000,1>::num ) U   std::ratio<3600000000000,1>::den 4 U  �std::integral_constant<__int64,1440>::value $ 昵    D3D12_LIFETIME_STATE_IN_USE   U   std::ratio<1,1440>::num   U  �std::ratio<1,1440>::den � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::ITexture * const,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment V _   std::allocator<donut::app::IRenderPass *>::_Minimum_asan_allocation_alignment Z _   std::allocator<donut::math::vector<float,3> >::_Minimum_asan_allocation_alignment C :   std::atomic<std::chrono::tzdb_list *>::is_always_lock_free � _   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size Z:    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi ]:   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset o _   std::allocator<std::_List_node<donut::app::IRenderPass *,void *> >::_Minimum_asan_allocation_alignment k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size  Bq    CIP_DISK_FULL  Bq   CIP_ACCESS_DENIED ! Bq   CIP_NEWER_VERSION_EXISTS ! Bq   CIP_OLDER_VERSION_EXISTS  Bq   CIP_NAME_CONFLICT 1 Bq   CIP_TRUST_VERIFICATION_COMPONENT_MISSING + Bq   CIP_EXE_SELF_REGISTERATION_TIMEOUT  Bq   CIP_UNSAFE_TO_ABORT  Bq   CIP_NEED_REBOOT " 唓    Uri_PROPERTY_ABSOLUTE_URI  唓   Uri_PROPERTY_USER_NAME  唓   Uri_PROPERTY_HOST_TYPE  唓   Uri_PROPERTY_ZONE ? �   D3D12_STATE_SUBOBJECT_TYPE_RAYTRACING_PIPELINE_CONFIG1  萹    Uri_HOST_UNKNOWN  萹   Uri_HOST_DNS  萹   Uri_HOST_IPV4  萹   Uri_HOST_IPV6 � _   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos 2 <  �����std::shared_timed_mutex::_Max_readers L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 ( �    std::chrono::local_info::unique - �   std::chrono::local_info::nonexistent + �   std::chrono::local_info::ambiguous N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gy > U  � 蕷;std::integral_constant<__int64,1000000000>::value % _   std::ctype<char>::table_size G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 Z _   std::allocator<donut::math::vector<float,2> >::_Minimum_asan_allocation_alignment M U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gy G _   std::allocator<ImDrawVert>::_Minimum_asan_allocation_alignment L U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Nx2 P U  � 蕷;std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx1 e _   std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >::_Minimum_asan_allocation_alignment R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gy 1 嵘    D3D12_RAYTRACING_GEOMETRY_TYPE_TRIANGLES  q   BINDSTRING_HEADERS   q   BINDSTRING_ACCEPT_MIMES  q   BINDSTRING_EXTRA_URL  q   BINDSTRING_LANGUAGE  q   BINDSTRING_USERNAME  q   BINDSTRING_PASSWORD  q   BINDSTRING_UA_PIXELS  q   BINDSTRING_UA_COLOR  q  	 BINDSTRING_OS  q  
 BINDSTRING_USER_AGENT $ q   BINDSTRING_ACCEPT_ENCODINGS m_   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size  q   BINDSTRING_POST_COOKIE m_   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets " q  
 BINDSTRING_POST_DATA_MIME  q   BINDSTRING_URL  q   BINDSTRING_IID g:    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi ' q   BINDSTRING_FLAG_BIND_TO_OBJECT $ q   BINDSTRING_PTR_BIND_CONTEXT  q   BINDSTRING_XDR_ORIGIN   q   BINDSTRING_DOWNLOADPATH  q   BINDSTRING_ROOTDOC_URL $ q   BINDSTRING_INITIAL_FILENAME " q   BINDSTRING_PROXY_USERNAME " q   BINDSTRING_PROXY_PASSWORD ! q   BINDSTRING_ENTERPRISE_ID  q   BINDSTRING_DOC_URL R :    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified F U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Nx2 F U  �std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Dx2 � _   std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >::_Minimum_asan_allocation_alignment * :    std::chrono::utc_clock::is_steady H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx1   U  �std::ratio<1000,1>::num   U   std::ratio<1000,1>::den H U  �std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gy � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Same_size_and_compatible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_assignable 4 U  �std::integral_constant<__int64,1000>::value :    std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Multi :   std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>::_Standard * :    std::chrono::tai_clock::is_steady 6 v,  �奮�std::chrono::tai_clock::_Tai_epoch_adjust  U  < std::ratio<60,1>::num  U   std::ratio<60,1>::den c _   std::allocator<donut::math::vector<unsigned short,4> >::_Minimum_asan_allocation_alignment K _   std::allocator<unsigned short>::_Minimum_asan_allocation_alignment � _   std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >::_Minimum_asan_allocation_alignment * :    std::chrono::gps_clock::is_steady 6 v,  �w�*韘td::chrono::gps_clock::_Gps_epoch_adjust � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_assignable   �   std::_Iosb<int>::skipws ! �   std::_Iosb<int>::unitbuf # �   std::_Iosb<int>::uppercase  *q   PARSE_CANONICALIZE " �   std::_Iosb<int>::showbase  *q   PARSE_FRIENDLY  *q   PARSE_SECURITY_URL  *q   PARSE_ROOTDOCUMENT # �   std::_Iosb<int>::showpoint  *q   PARSE_DOCUMENT ! �    std::_Iosb<int>::showpos  *q   PARSE_ANCHOR ! *q   PARSE_ENCODE_IS_UNESCAPE  �  @ std::_Iosb<int>::left  *q   PARSE_DECODE_IS_ESCAPE 5 :    std::filesystem::_File_time_clock::is_steady  *q  	 PARSE_PATH_FROM_URL  �  � std::_Iosb<int>::right  *q  
 PARSE_URL_FROM_PATH  *q   PARSE_MIME " �   std::_Iosb<int>::internal  *q   PARSE_SERVER  *q  
 PARSE_SCHEMA  �   std::_Iosb<int>::dec  *q   PARSE_SITE  *q   PARSE_DOMAIN  �   std::_Iosb<int>::oct  *q   PARSE_LOCATION  *q   PARSE_SECURITY_DOMAIN  *q   PARSE_ESCAPE  �   std::_Iosb<int>::hex $ �   std::_Iosb<int>::scientific  �    std::_Iosb<int>::fixed  wr   PSU_DEFAULT " �   0std::_Iosb<int>::hexfloat # �   @std::_Iosb<int>::boolalpha  :q   QUERY_EXPIRATION_DATE " :q   QUERY_TIME_OF_LAST_CHANGE " �  � �std::_Iosb<int>::_Stdio  :q   QUERY_CONTENT_ENCODING  :q   QUERY_CONTENT_TYPE % �  �std::_Iosb<int>::adjustfield  :q   QUERY_REFRESH  :q   QUERY_RECOMBINE # �   std::_Iosb<int>::basefield  :q   QUERY_CAN_NAVIGATE  :q   QUERY_USES_NETWORK $ �   0std::_Iosb<int>::floatfield  :q  	 QUERY_IS_CACHED   :q  
 QUERY_IS_INSTALLEDENTRY " :q   QUERY_IS_CACHED_OR_MAPPED  :q   QUERY_USES_CACHE  :q  
 QUERY_IS_SECURE ! �    std::_Iosb<int>::goodbit  :q   QUERY_IS_SAFE  膓    ServerApplication   �   std::_Iosb<int>::eofbit ! :q   QUERY_USES_HISTORYFOLDER ! �   std::_Iosb<int>::failbit  Fs    IdleShutdown   �   std::_Iosb<int>::badbit  �   std::_Iosb<int>::in  �   std::_Iosb<int>::out  �   std::_Iosb<int>::ate  �   std::_Iosb<int>::app  �   std::_Iosb<int>::trunc # �  @ std::_Iosb<int>::_Nocreate $ �  � std::_Iosb<int>::_Noreplace   �    std::_Iosb<int>::binary  �    std::_Iosb<int>::beg   U   std::ratio<1,1000>::num  �   std::_Iosb<int>::cur   U  �std::ratio<1,1000>::den  �   std::_Iosb<int>::end , �  @ std::_Iosb<int>::_Default_open_prot M U  �r ( std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Nx2 I U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Dx2 D _   ��std::basic_string_view<char,std::char_traits<char> >::npos K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx2 O U  �r ( std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx2 J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gx J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gy  8q    FEATURE_OBJECT_CACHING  8q   FEATURE_ZONE_ELEVATION  8q   FEATURE_MIME_HANDLING  8q   FEATURE_MIME_SNIFFING $ 8q   FEATURE_WINDOW_RESTRICTIONS & 8q   FEATURE_WEBOC_POPUPMANAGEMENT  8q   FEATURE_BEHAVIORS $ 8q   FEATURE_DISABLE_MK_PROTOCOL & 8q   FEATURE_LOCALMACHINE_LOCKDOWN  8q  	 FEATURE_SECURITYBAND ( 8q  
 FEATURE_RESTRICT_ACTIVEXINSTALL & 8q   FEATURE_VALIDATE_NAVIGATE_URL & 8q   FEATURE_RESTRICT_FILEDOWNLOAD ! 8q  
 FEATURE_ADDON_MANAGEMENT " 8q   FEATURE_PROTOCOL_LOCKDOWN / 8q   FEATURE_HTTP_USERNAME_PASSWORD_DISABLE " 8q   FEATURE_SAFE_BINDTOOBJECT # 8q   FEATURE_UNC_SAVEDFILECHECK / 8q   FEATURE_GET_URL_DOM_FILEPATH_UNENCODED   8q   FEATURE_TABBED_BROWSING  8q   FEATURE_SSLUX * 8q   FEATURE_DISABLE_NAVIGATION_SOUNDS + 8q   FEATURE_DISABLE_LEGACY_COMPRESSION & 8q   FEATURE_FORCE_ADDR_AND_STATUS  8q   FEATURE_XMLHTTP ( 8q   FEATURE_DISABLE_TELNET_PROTOCOL  8q   FEATURE_FEEDS N U  �X呩std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Nx2 $ 8q   FEATURE_BLOCK_INPUT_PROMPTS J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Dx2 � :   std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Same_size_and_compatible   Q�    D3D_DRIVER_TYPE_UNKNOWN � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_constructible ! Q�   D3D_DRIVER_TYPE_HARDWARE " Q�   D3D_DRIVER_TYPE_REFERENCE  Q�   D3D_DRIVER_TYPE_NULL ! Q�   D3D_DRIVER_TYPE_SOFTWARE � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_assignable L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx2 ) 柷    D3D_PRIMITIVE_TOPOLOGY_UNDEFINED P U  �X呩std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx2 ) 柷   D3D_PRIMITIVE_TOPOLOGY_POINTLIST K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gx ( 柷   D3D_PRIMITIVE_TOPOLOGY_LINELIST ) 柷   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP , 柷   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gy - 柷   D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP , 柷  
 D3D_PRIMITIVE_TOPOLOGY_LINELIST_ADJ _ _   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment - 柷   D3D_PRIMITIVE_TOPOLOGY_LINESTRIP_ADJ 0 柷   D3D_PRIMITIVE_TOPOLOGY_TRIANGLELIST_ADJ 1 柷  
 D3D_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP_ADJ 9 柷  ! D3D_PRIMITIVE_TOPOLOGY_1_CONTROL_POINT_PATCHLIST 9 柷  " D3D_PRIMITIVE_TOPOLOGY_2_CONTROL_POINT_PATCHLIST 9 柷  # D3D_PRIMITIVE_TOPOLOGY_3_CONTROL_POINT_PATCHLIST 9 柷  $ D3D_PRIMITIVE_TOPOLOGY_4_CONTROL_POINT_PATCHLIST 9 柷  % D3D_PRIMITIVE_TOPOLOGY_5_CONTROL_POINT_PATCHLIST 9 柷  & D3D_PRIMITIVE_TOPOLOGY_6_CONTROL_POINT_PATCHLIST 9 柷  ' D3D_PRIMITIVE_TOPOLOGY_7_CONTROL_POINT_PATCHLIST 9 柷  ( D3D_PRIMITIVE_TOPOLOGY_8_CONTROL_POINT_PATCHLIST 9 柷  ) D3D_PRIMITIVE_TOPOLOGY_9_CONTROL_POINT_PATCHLIST : 柷  * D3D_PRIMITIVE_TOPOLOGY_10_CONTROL_POINT_PATCHLIST : 柷  + D3D_PRIMITIVE_TOPOLOGY_11_CONTROL_POINT_PATCHLIST : 柷  , D3D_PRIMITIVE_TOPOLOGY_12_CONTROL_POINT_PATCHLIST : 柷  - D3D_PRIMITIVE_TOPOLOGY_13_CONTROL_POINT_PATCHLIST : 柷  . D3D_PRIMITIVE_TOPOLOGY_14_CONTROL_POINT_PATCHLIST : 柷  / D3D_PRIMITIVE_TOPOLOGY_15_CONTROL_POINT_PATCHLIST : 柷  0 D3D_PRIMITIVE_TOPOLOGY_16_CONTROL_POINT_PATCHLIST : 柷  1 D3D_PRIMITIVE_TOPOLOGY_17_CONTROL_POINT_PATCHLIST : 柷  2 D3D_PRIMITIVE_TOPOLOGY_18_CONTROL_POINT_PATCHLIST : 柷  3 D3D_PRIMITIVE_TOPOLOGY_19_CONTROL_POINT_PATCHLIST : 柷  4 D3D_PRIMITIVE_TOPOLOGY_20_CONTROL_POINT_PATCHLIST : 柷  5 D3D_PRIMITIVE_TOPOLOGY_21_CONTROL_POINT_PATCHLIST : 柷  6 D3D_PRIMITIVE_TOPOLOGY_22_CONTROL_POINT_PATCHLIST : 柷  7 D3D_PRIMITIVE_TOPOLOGY_23_CONTROL_POINT_PATCHLIST : 柷  8 D3D_PRIMITIVE_TOPOLOGY_24_CONTROL_POINT_PATCHLIST : 柷  9 D3D_PRIMITIVE_TOPOLOGY_25_CONTROL_POINT_PATCHLIST : 柷  : D3D_PRIMITIVE_TOPOLOGY_26_CONTROL_POINT_PATCHLIST : 柷  ; D3D_PRIMITIVE_TOPOLOGY_27_CONTROL_POINT_PATCHLIST : 柷  < D3D_PRIMITIVE_TOPOLOGY_28_CONTROL_POINT_PATCHLIST : 柷  = D3D_PRIMITIVE_TOPOLOGY_29_CONTROL_POINT_PATCHLIST : 柷  > D3D_PRIMITIVE_TOPOLOGY_30_CONTROL_POINT_PATCHLIST : 柷  ? D3D_PRIMITIVE_TOPOLOGY_31_CONTROL_POINT_PATCHLIST : 柷  @ D3D_PRIMITIVE_TOPOLOGY_32_CONTROL_POINT_PATCHLIST # U   std::ratio<1,1000000>::num ' U  �@B std::ratio<1,1000000>::den K U  ��Q std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Dx2 Z _   std::allocator<donut::math::vector<float,4> >::_Minimum_asan_allocation_alignment   E�    D3D_PRIMITIVE_UNDEFINED  E�   D3D_PRIMITIVE_POINT  E�   D3D_PRIMITIVE_LINE  E�   D3D_PRIMITIVE_TRIANGLE  E�   D3D_PRIMITIVE_LINE_ADJ # E�   D3D_PRIMITIVE_TRIANGLE_ADJ , E�   D3D_PRIMITIVE_1_CONTROL_POINT_PATCH , E�  	 D3D_PRIMITIVE_2_CONTROL_POINT_PATCH , E�  
 D3D_PRIMITIVE_3_CONTROL_POINT_PATCH I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx1 , E�   D3D_PRIMITIVE_4_CONTROL_POINT_PATCH I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx1 , E�   D3D_PRIMITIVE_5_CONTROL_POINT_PATCH , E�  
 D3D_PRIMITIVE_6_CONTROL_POINT_PATCH , E�   D3D_PRIMITIVE_7_CONTROL_POINT_PATCH I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx2 , E�   D3D_PRIMITIVE_8_CONTROL_POINT_PATCH M U  ��Q std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx2 , E�   D3D_PRIMITIVE_9_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_10_CONTROL_POINT_PATCH H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gx - E�   D3D_PRIMITIVE_11_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_12_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_13_CONTROL_POINT_PATCH H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gy - E�   D3D_PRIMITIVE_14_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_15_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_16_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_17_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_18_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_19_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_20_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_21_CONTROL_POINT_PATCH : _    std::integral_constant<unsigned __int64,0>::value - E�   D3D_PRIMITIVE_22_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_23_CONTROL_POINT_PATCH - E�   D3D_PRIMITIVE_24_CONTROL_POINT_PATCH - E�    D3D_PRIMITIVE_25_CONTROL_POINT_PATCH - E�  ! D3D_PRIMITIVE_26_CONTROL_POINT_PATCH - E�  " D3D_PRIMITIVE_27_CONTROL_POINT_PATCH - E�  # D3D_PRIMITIVE_28_CONTROL_POINT_PATCH - E�  $ D3D_PRIMITIVE_29_CONTROL_POINT_PATCH - E�  % D3D_PRIMITIVE_30_CONTROL_POINT_PATCH - E�  & D3D_PRIMITIVE_31_CONTROL_POINT_PATCH - E�  ' D3D_PRIMITIVE_32_CONTROL_POINT_PATCH Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment "     D3D_SRV_DIMENSION_UNKNOWN !    D3D_SRV_DIMENSION_BUFFER $    D3D_SRV_DIMENSION_TEXTURE1D )    D3D_SRV_DIMENSION_TEXTURE1DARRAY $    D3D_SRV_DIMENSION_TEXTURE2D )    D3D_SRV_DIMENSION_TEXTURE2DARRAY &    D3D_SRV_DIMENSION_TEXTURE2DMS +    D3D_SRV_DIMENSION_TEXTURE2DMSARRAY $    D3D_SRV_DIMENSION_TEXTURE3D &   	 D3D_SRV_DIMENSION_TEXTURECUBE /_   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Bucket_size +   
 D3D_SRV_DIMENSION_TEXTURECUBEARRAY #    D3D_SRV_DIMENSION_BUFFEREX /_   std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Min_buckets ):    std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >::_Multi * <   donut::math::vector<float,3>::DIM a _   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment ':   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible !:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable  裶    URLZONE_LOCAL_MACHINE  裶   URLZONE_INTRANET  裶   URLZONE_TRUSTED  裶   URLZONE_INTERNET  炃    D3D_INCLUDE_LOCAL  炃   D3D_INCLUDE_SYSTEM  樓    D3D_SVC_SCALAR  樓   D3D_SVC_VECTOR  樓   D3D_SVC_MATRIX_ROWS  樓   D3D_SVC_MATRIX_COLUMNS  樓   D3D_SVC_OBJECT  樓   D3D_SVC_STRUCT   樓   D3D_SVC_INTERFACE_CLASS " 樓   D3D_SVC_INTERFACE_POINTER  芮   D3D_SVF_USERPACKED  芮   D3D_SVF_USED " 芮   D3D_SVF_INTERFACE_POINTER  ur    URLZONEREG_DEFAULT $ 芮   D3D_SVF_INTERFACE_PARAMETER  ur   URLZONEREG_HKLM  M�    D3D_SVT_VOID  M�   D3D_SVT_BOOL  M�   D3D_SVT_INT  M�   D3D_SVT_FLOAT  M�   D3D_SVT_STRING  M�   D3D_SVT_TEXTURE  M�   D3D_SVT_TEXTURE1D  M�   D3D_SVT_TEXTURE2D  M�   D3D_SVT_TEXTURE3D  M�  	 D3D_SVT_TEXTURECUBE  M�  
 D3D_SVT_SAMPLER  M�   D3D_SVT_SAMPLER1D  M�   D3D_SVT_SAMPLER2D  M�  
 D3D_SVT_SAMPLER3D  M�   D3D_SVT_SAMPLERCUBE  M�   D3D_SVT_PIXELSHADER  M�   D3D_SVT_VERTEXSHADER  M�   D3D_SVT_PIXELFRAGMENT  M�   D3D_SVT_VERTEXFRAGMENT  M�   D3D_SVT_UINT  M�   D3D_SVT_UINT8  M�   D3D_SVT_GEOMETRYSHADER  M�   D3D_SVT_RASTERIZER  M�   D3D_SVT_DEPTHSTENCIL  M�   D3D_SVT_BLEND  M�   D3D_SVT_BUFFER  M�   D3D_SVT_CBUFFER  M�   D3D_SVT_TBUFFER  M�   D3D_SVT_TEXTURE1DARRAY  M�   D3D_SVT_TEXTURE2DARRAY ! M�   D3D_SVT_RENDERTARGETVIEW ! M�   D3D_SVT_DEPTHSTENCILVIEW  M�    D3D_SVT_TEXTURE2DMS ! M�  ! D3D_SVT_TEXTURE2DMSARRAY ! M�  " D3D_SVT_TEXTURECUBEARRAY  M�  # D3D_SVT_HULLSHADER  M�  $ D3D_SVT_DOMAINSHADER " M�  % D3D_SVT_INTERFACE_POINTER  M�  & D3D_SVT_COMPUTESHADER  M�  ' D3D_SVT_DOUBLE  M�  ( D3D_SVT_RWTEXTURE1D ! M�  ) D3D_SVT_RWTEXTURE1DARRAY  M�  * D3D_SVT_RWTEXTURE2D ! M�  + D3D_SVT_RWTEXTURE2DARRAY  M�  , D3D_SVT_RWTEXTURE3D  M�  - D3D_SVT_RWBUFFER # M�  . D3D_SVT_BYTEADDRESS_BUFFER % M�  / D3D_SVT_RWBYTEADDRESS_BUFFER " M�  0 D3D_SVT_STRUCTURED_BUFFER $ M�  1 D3D_SVT_RWSTRUCTURED_BUFFER ) M�  2 D3D_SVT_APPEND_STRUCTURED_BUFFER * M�  3 D3D_SVT_CONSUME_STRUCTURED_BUFFER J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos  S�   D3D_SIF_USERPACKED # S�   D3D_SIF_COMPARISON_SAMPLER $ S�   D3D_SIF_TEXTURE_COMPONENT_0 $ S�   D3D_SIF_TEXTURE_COMPONENT_1 # S�   D3D_SIF_TEXTURE_COMPONENTS � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >::_Minimum_asan_allocation_alignment  =�    D3D_SIT_CBUFFER  =�   D3D_SIT_TBUFFER  =�   D3D_SIT_TEXTURE  =�   D3D_SIT_SAMPLER  =�   D3D_SIT_UAV_RWTYPED  =�   D3D_SIT_STRUCTURED ! =�   D3D_SIT_UAV_RWSTRUCTURED  =�   D3D_SIT_BYTEADDRESS " =�   D3D_SIT_UAV_RWBYTEADDRESS & =�  	 D3D_SIT_UAV_APPEND_STRUCTURED ' =�  
 D3D_SIT_UAV_CONSUME_STRUCTURED . =�   D3D_SIT_UAV_RWSTRUCTURED_WITH_COUNTER ( =�   D3D_SIT_RTACCELERATIONSTRUCTURE  �   D3D_CBF_USERPACKED  枨    D3D_CT_CBUFFER  枨   D3D_CT_TBUFFER " 枨   D3D_CT_INTERFACE_POINTERS " 枨   D3D_CT_RESOURCE_BIND_INFO i _   std::allocator<std::shared_ptr<donut::app::RegisteredFont> >::_Minimum_asan_allocation_alignment  [�    D3D_NAME_UNDEFINED  [�   D3D_NAME_POSITION  [�   D3D_NAME_CLIP_DISTANCE  [�   D3D_NAME_CULL_DISTANCE + [�   D3D_NAME_RENDER_TARGET_ARRAY_INDEX & [�   D3D_NAME_VIEWPORT_ARRAY_INDEX 1 鹎    D3D12_BACKGROUND_PROCESSING_MODE_ALLOWED  [�   D3D_NAME_VERTEX_ID  [�   D3D_NAME_PRIMITIVE_ID F 鹎   D3D12_BACKGROUND_PROCESSING_MODE_ALLOW_INTRUSIVE_MEASUREMENTS A 鹎   D3D12_BACKGROUND_PROCESSING_MODE_DISABLE_BACKGROUND_WORK  [�   D3D_NAME_INSTANCE_ID  [�  	 D3D_NAME_IS_FRONT_FACE  [�  
 D3D_NAME_SAMPLE_INDEX , [�   D3D_NAME_FINAL_QUAD_EDGE_TESSFACTOR + 溓    D3D12_MEASUREMENTS_ACTION_KEEP_ALL 1 溓   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS . [�   D3D_NAME_FINAL_QUAD_INSIDE_TESSFACTOR ? 溓   D3D12_MEASUREMENTS_ACTION_COMMIT_RESULTS_HIGH_PRIORITY + [�  
 D3D_NAME_FINAL_TRI_EDGE_TESSFACTOR - [�   D3D_NAME_FINAL_TRI_INSIDE_TESSFACTOR . [�   D3D_NAME_FINAL_LINE_DETAIL_TESSFACTOR / [�   D3D_NAME_FINAL_LINE_DENSITY_TESSFACTOR  [�   D3D_NAME_BARYCENTRICS  [�   D3D_NAME_SHADINGRATE  [�   D3D_NAME_CULLPRIMITIVE  [�  @ D3D_NAME_TARGET  [�  A D3D_NAME_DEPTH  [�  B D3D_NAME_COVERAGE % [�  C D3D_NAME_DEPTH_GREATER_EQUAL " [�  D D3D_NAME_DEPTH_LESS_EQUAL  [�  E D3D_NAME_STENCIL_REF   [�  F D3D_NAME_INNER_COVERAGE B _   std::allocator<float>::_Minimum_asan_allocation_alignment  C�   D3D_RETURN_TYPE_UNORM  C�   D3D_RETURN_TYPE_SNORM  C�   D3D_RETURN_TYPE_SINT  C�   D3D_RETURN_TYPE_UINT  C�   D3D_RETURN_TYPE_FLOAT  C�   D3D_RETURN_TYPE_MIXED  C�   D3D_RETURN_TYPE_DOUBLE " C�   D3D_RETURN_TYPE_CONTINUED ' Y�    D3D_REGISTER_COMPONENT_UNKNOWN & Y�   D3D_REGISTER_COMPONENT_UINT32 * <   donut::math::vector<float,4>::DIM & Y�   D3D_REGISTER_COMPONENT_SINT32 ' Y�   D3D_REGISTER_COMPONENT_FLOAT32 ) _�    D3D_TESSELLATOR_DOMAIN_UNDEFINED ' _�   D3D_TESSELLATOR_DOMAIN_ISOLINE # _�   D3D_TESSELLATOR_DOMAIN_TRI \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment $ _�   D3D_TESSELLATOR_DOMAIN_QUAD / 烨    D3D_TESSELLATOR_PARTITIONING_UNDEFINED - 烨   D3D_TESSELLATOR_PARTITIONING_INTEGER * 烨   D3D_TESSELLATOR_PARTITIONING_POW2 4 烨   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_ODD 5 烨   D3D_TESSELLATOR_PARTITIONING_FRACTIONAL_EVEN )     D3D_TESSELLATOR_OUTPUT_UNDEFINED * 錏        donut::math::lumaCoefficients %    D3D_TESSELLATOR_OUTPUT_POINT $    D3D_TESSELLATOR_OUTPUT_LINE +    D3D_TESSELLATOR_OUTPUT_TRIANGLE_CW ,    D3D_TESSELLATOR_OUTPUT_TRIANGLE_CCW ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy ' 厍    D3D12_COMMAND_LIST_TYPE_DIRECT ' 厍   D3D12_COMMAND_LIST_TYPE_BUNDLE ( 厍   D3D12_COMMAND_LIST_TYPE_COMPUTE % 厍   D3D12_COMMAND_LIST_TYPE_COPY - 厍   D3D12_COMMAND_LIST_TYPE_VIDEO_DECODE - �   std::_Invoker_pmf_refwrap::_Strategy . 厍   D3D12_COMMAND_LIST_TYPE_VIDEO_PROCESS J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos - 厍   D3D12_COMMAND_LIST_TYPE_VIDEO_ENCODE - �   std::_Invoker_pmf_pointer::_Strategy * <   donut::math::vector<float,2>::DIM , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy # 蘱   BINDHANDLETYPES_DEPENDENCY = <   donut::engine::c_MaxRenderPassConstantBufferVersions - �   std::_Invoker_pmd_pointer::_Strategy . �   donut::math::box<float,2>::numCorners 8 �    D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_DISCARD 9 �   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_PRESERVE 6 �   D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE_CLEAR � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable 5 G�    D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_DISCARD 6 G�   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_PRESERVE 5 G�   D3D12_RENDER_PASS_ENDING_ACCESS_TYPE_RESOLVE 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos 9 �   std::chrono::_Time_parse_fields::_Era_begin_wday  誵    TKIND_ENUM  誵   TKIND_RECORD  誵   TKIND_MODULE  誵   TKIND_INTERFACE  誵   TKIND_DISPATCH  誵   TKIND_COCLASS  誵   TKIND_ALIAS  誵   TKIND_UNION % 蚯   D3D12_COLOR_WRITE_ENABLE_RED  ;  ��I@donut::math::PI_f ' 蚯   D3D12_COLOR_WRITE_ENABLE_GREEN & 蚯   D3D12_COLOR_WRITE_ENABLE_BLUE " s  
�-DT�!	@donut::math::PI_d S _   std::allocator<std::chrono::time_zone>::_Minimum_asan_allocation_alignment ! ;  ��7�5donut::math::epsilon ' 蚯   D3D12_COLOR_WRITE_ENABLE_ALPHA " ;  �  �donut::math::infinity  ;  �  �donut::math::NaN  ?�    D3D12_LOGIC_OP_CLEAR  ?�   D3D12_LOGIC_OP_SET  "q    PIDMSI_STATUS_NORMAL  ?�   D3D12_LOGIC_OP_COPY % ?�   D3D12_LOGIC_OP_COPY_INVERTED  "q   PIDMSI_STATUS_NEW  "q   PIDMSI_STATUS_PRELIM  ?�   D3D12_LOGIC_OP_NOOP  ?�   D3D12_LOGIC_OP_INVERT  "q   PIDMSI_STATUS_DRAFT ! "q   PIDMSI_STATUS_INPROGRESS  ?�   D3D12_LOGIC_OP_AND  ?�   D3D12_LOGIC_OP_NAND  "q   PIDMSI_STATUS_EDIT  "q   PIDMSI_STATUS_REVIEW  ?�   D3D12_LOGIC_OP_OR  ?�  	 D3D12_LOGIC_OP_NOR  "q   PIDMSI_STATUS_PROOF  ?�  
 D3D12_LOGIC_OP_XOR  ?�   D3D12_LOGIC_OP_EQUIV # ?�   D3D12_LOGIC_OP_AND_REVERSE $ ?�  
 D3D12_LOGIC_OP_AND_INVERTED " ?�   D3D12_LOGIC_OP_OR_REVERSE ' 
�    D3D12_SHADER_CACHE_MODE_MEMORY J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 / U   std::ratio<1,1000000000000000000>::num 7 U  
�  dС多
std::ratio<1,1000000000000000000>::den L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy � _   std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >::_Minimum_asan_allocation_alignment % A�    D3D12_BARRIER_LAYOUT_PRESENT * A�   D3D12_BARRIER_LAYOUT_GENERIC_READ + A�   D3D12_BARRIER_LAYOUT_RENDER_TARGET . A�   D3D12_BARRIER_LAYOUT_UNORDERED_ACCESS 1 A�   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_WRITE 0 A�   D3D12_BARRIER_LAYOUT_DEPTH_STENCIL_READ - A�   D3D12_BARRIER_LAYOUT_SHADER_RESOURCE ) A�   D3D12_BARRIER_LAYOUT_COPY_SOURCE ' A�   D3D12_BARRIER_LAYOUT_COPY_DEST , A�  	 D3D12_BARRIER_LAYOUT_RESOLVE_SOURCE  Fq   CC_CDECL * A�  
 D3D12_BARRIER_LAYOUT_RESOLVE_DEST  Fq   CC_MSCPASCAL 1 A�   D3D12_BARRIER_LAYOUT_SHADING_RATE_SOURCE / A�   D3D12_BARRIER_LAYOUT_VIDEO_DECODE_READ  Fq   CC_PASCAL 0 A�  
 D3D12_BARRIER_LAYOUT_VIDEO_DECODE_WRITE  Fq   CC_MACPASCAL  Fq   CC_STDCALL 0 A�   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_READ  Fq   CC_FPFASTCALL 1 A�   D3D12_BARRIER_LAYOUT_VIDEO_PROCESS_WRITE / A�   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_READ  Fq   CC_SYSCALL 0 A�   D3D12_BARRIER_LAYOUT_VIDEO_ENCODE_WRITE  Fq   CC_MPWCDECL 1 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COMMON  Fq   CC_MPWPASCAL 7 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_GENERIC_READ ; A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_UNORDERED_ACCESS : A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_SHADER_RESOURCE 6 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_SOURCE  6q    FUNC_VIRTUAL 4 A�   D3D12_BARRIER_LAYOUT_DIRECT_QUEUE_COPY_DEST  6q   FUNC_PUREVIRTUAL 2 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COMMON  6q   FUNC_NONVIRTUAL  6q   FUNC_STATIC 8 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_GENERIC_READ < A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_UNORDERED_ACCESS ; A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_SHADER_RESOURCE 7 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_SOURCE 5 A�   D3D12_BARRIER_LAYOUT_COMPUTE_QUEUE_COPY_DEST L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos �:    std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Multi �:   std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0>::_Standard  0q    VAR_PERINSTANCE  0q   VAR_STATIC  0q   VAR_CONST . �   donut::math::box<float,3>::numCorners � _   std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >::_Minimum_asan_allocation_alignment U :   std::_String_view_iterator<std::char_traits<char> >::_Unwrap_when_unverified ; O�    D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_ROOT_SIGNATURE / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_VS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_HS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_GS / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CS ) <   donut::math::frustum::numCorners : O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_STREAM_OUTPUT 2 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_BLEND 8 O�  	 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_MASK 7 O�  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RASTERIZER : O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL 9 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_INPUT_LAYOUT ? O�  
 D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_IB_STRIP_CUT_VALUE ? O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_PRIMITIVE_TOPOLOGY B O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_RENDER_TARGET_FORMATS A O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL_FORMAT 8 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_SAMPLE_DESC 6 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_NODE_MASK 7 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_CACHED_PSO 2 O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_FLAGS ; O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_DEPTH_STENCIL1 / O�   D3D12_PIPELINE_STATE_SUBOBJECT_TYPE_MS "  �    D3D12_BARRIER_TYPE_GLOBAL #  �   D3D12_BARRIER_TYPE_TEXTURE X _   std::allocator<std::chrono::time_zone_link>::_Minimum_asan_allocation_alignment 1 <   donut::math::vector<unsigned int,2>::DIM  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment 1 <   donut::math::vector<unsigned int,3>::DIM  (q    DESCKIND_NONE  (q   DESCKIND_FUNCDESC  (q   DESCKIND_VARDESC  (q   DESCKIND_TYPECOMP   (q   DESCKIND_IMPLICITAPPOBJ  yr   COR_VERSION_MAJOR_V2 $ 嗲   ImGuiWindowFlags_NoTitleBar " 嗲   ImGuiWindowFlags_NoResize % 嗲   ImGuiWindowFlags_NoScrollbar $ 嗲    ImGuiWindowFlags_NoCollapse ' 嗲   ImGuiWindowFlags_NoMouseInputs ) 嗲  �   ImGuiWindowFlags_NoNavInputs ( 嗲  �   ImGuiWindowFlags_NoNavFocus : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift # 奕  � ImGuiChildFlags_FrameStyle ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask j _   std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >::_Minimum_asan_allocation_alignment " K�   ImGuiTreeNodeFlags_Framed ( K�   ImGuiTreeNodeFlags_AllowOverlap ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits , K�   ImGuiTreeNodeFlags_NoTreePushOnOpen + K�   ImGuiTreeNodeFlags_NoAutoOpenOnLog ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift �_   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Bucket_size ; _  �std::_Floating_type_traits<double>::_Exponent_mask �_   std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Min_buckets J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask �:    std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> >::_Multi L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask # 斍   ImGuiPopupFlags_AnyPopupId G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask & 斍   ImGuiPopupFlags_AnyPopupLevel K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask * �   ImGuiSelectableFlags_AllowOverlap  �  g D3D_SHADER_MODEL_6_7 $ 智   ImGuiComboFlags_HeightSmall & 智   ImGuiComboFlags_HeightRegular $ 智   ImGuiComboFlags_HeightLarge & 智   ImGuiComboFlags_HeightLargest W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified 1 奚  @ ImGuiTabBarFlags_FittingPolicyResizeDown - 奚  � ImGuiTabBarFlags_FittingPolicyScroll '    ImGuiFocusedFlags_ChildWindows %    ImGuiFocusedFlags_RootWindow ' 媲   ImGuiHoveredFlags_ChildWindows % 媲   ImGuiHoveredFlags_RootWindow 2 媲    ImGuiHoveredFlags_AllowWhenBlockedByPopup 7 媲  � ImGuiHoveredFlags_AllowWhenBlockedByActiveItem 4 媲   ImGuiHoveredFlags_AllowWhenOverlappedByItem 6 媲   ImGuiHoveredFlags_AllowWhenOverlappedByWindow . 媲   ImGuiHoveredFlags_AllowWhenOverlapped U _   std::allocator<std::chrono::leap_second>::_Minimum_asan_allocation_alignment 0 �   ImGuiDragDropFlags_AcceptBeforeDelivery 3 �   ImGuiDragDropFlags_AcceptNoDrawDefaultRect R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified  宷    SYS_WIN16  宷   SYS_WIN32  宷   SYS_MAC  艋  �ImGuiKey_COUNT 3 耷    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS ) <   donut::math::vector<bool,2>::DIM C 耷   D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS  艋   ImGuiMod_Ctrl  艋    ImGuiMod_Shift  艋   @ImGuiMod_Alt  艋  � �ImGuiMod_Super   艋   ImGuiKey_NamedKey_BEGIN  艋  �ImGuiKey_NamedKey_END � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >::_Minimum_asan_allocation_alignment  艋  �ImGuiKey_KeysData_SIZE 3     D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_0 3    D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER_1 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices  钋   ImGuiNavInput_COUNT ) <   donut::math::vector<bool,3>::DIM : 馇    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE I 馇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY J 馇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION H 馇   D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION  2q    CHANGEKIND_ADDMEMBER   2q   CHANGEKIND_DELETEMEMBER  2q   CHANGEKIND_SETNAMES $ 2q   CHANGEKIND_SETDOCUMENTATION  2q   CHANGEKIND_GENERAL  2q   CHANGEKIND_INVALIDATE # �        nvrhi::AllSubresources   2q   CHANGEKIND_CHANGEFAILED  �  5 ImGuiCol_COUNT ) <   donut::math::vector<bool,4>::DIM $ �   ��std::strong_ordering::less $ �    std::strong_ordering::equal & �   std::strong_ordering::greater )  �   ImGuiButtonFlags_MouseButtonLeft *  �   ImGuiButtonFlags_MouseButtonRight +  �   ImGuiButtonFlags_MouseButtonMiddle + 郧  �   ImGuiColorEditFlags_DisplayRGB + 郧  �    ImGuiColorEditFlags_DisplayHSV + 郧  �  @ ImGuiColorEditFlags_DisplayHex & 郧  �  � ImGuiColorEditFlags_Uint8 & 郧  �   ImGuiColorEditFlags_Float - 郧  �   ImGuiColorEditFlags_PickerHueBar / 郧  �   ImGuiColorEditFlags_PickerHueWheel ) 郧  �   ImGuiColorEditFlags_InputRGB ) 郧  �   ImGuiColorEditFlags_InputHSV & 毲  � ImGuiTableFlags_BordersInnerH & 毲   ImGuiTableFlags_BordersOuterH & 毲   ImGuiTableFlags_BordersInnerV & 毲   ImGuiTableFlags_BordersOuterV % 毲  �ImGuiTableFlags_BordersInner % 毲   ImGuiTableFlags_BordersOuter ' 毲    ImGuiTableFlags_SizingFixedFit ( 毲   @ImGuiTableFlags_SizingFixedSame * 毲   `ImGuiTableFlags_SizingStretchProp , 毲  � �ImGuiTableFlags_SizingStretchSame + 谇   ImGuiTableColumnFlags_WidthStretch ) 谇   ImGuiTableColumnFlags_WidthFixed / 谇  �   ImGuiTableColumnFlags_IndentEnable 0 谇  �   ImGuiTableColumnFlags_IndentDisable , 谇  �   ImGuiTableColumnFlags_IsEnabled , 谇  �   ImGuiTableColumnFlags_IsVisible + 谇  �   ImGuiTableColumnFlags_IsSorted , 谇  �   ImGuiTableColumnFlags_IsHovered ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong 3 �    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED - �   D3D12_MESSAGE_CATEGORY_MISCELLANEOUS . �   D3D12_MESSAGE_CATEGORY_INITIALIZATION ' �   D3D12_MESSAGE_CATEGORY_CLEANUP + �   D3D12_MESSAGE_CATEGORY_COMPILATION . �   D3D12_MESSAGE_CATEGORY_STATE_CREATION - �   D3D12_MESSAGE_CATEGORY_STATE_SETTING - �   D3D12_MESSAGE_CATEGORY_STATE_GETTING 5 �   D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION ) �  	 D3D12_MESSAGE_CATEGORY_EXECUTION * I�    D3D12_MESSAGE_SEVERITY_CORRUPTION % I�   D3D12_MESSAGE_SEVERITY_ERROR ' I�   D3D12_MESSAGE_SEVERITY_WARNING $ I�   D3D12_MESSAGE_SEVERITY_INFO ' U  �r ( std::ratio<2629746,1>::num # U   std::ratio<2629746,1>::den b <    std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::fractional_width           nvrhi::EntireBuffer A _   std::allocator<char>::_Minimum_asan_allocation_alignment ' �  ��std::chrono::year::_Year_min % �  �std::chrono::year::_Year_max ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask : _   std::integral_constant<unsigned __int64,1>::value ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity R _   std::allocator<nvrhi::BindingSetItem>::_Minimum_asan_allocation_alignment e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size / �    D3D12_RESOURCE_BARRIER_TYPE_TRANSITION - �   D3D12_RESOURCE_BARRIER_TYPE_ALIASING : _   std::integral_constant<unsigned __int64,2>::value T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos   �   )  C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Dx2  H    std::denorm_absent  H   std::denorm_present  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx1 ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 & :    std::_Num_base::has_quiet_NaN E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 * :    std::_Num_base::has_signaling_NaN D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gx D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gy # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix ) H   std::_Num_float_base::has_denorm + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed - :   std::_Num_float_base::is_specialized 4 _  @ _Mtx_internal_imp_t::_Critical_section_size * K   std::_Num_float_base::round_style 5 _   _Mtx_internal_imp_t::_Critical_section_align $ �   std::_Num_float_base::radix + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits * �   std::numeric_limits<bool>::digits + :    std::_Aligned<64,8,short,0>::_Fits ) :   std::_Aligned<64,8,int,0>::_Fits - :   std::numeric_limits<char>::is_signed - :    std::numeric_limits<char>::is_modulo :   ED3D12_MESSAGE_ID_ALPHA_BLEND_FACTOR_NOT_SUPPORTED * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 :   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 T U  �r ( std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Dx2 6 :   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 V U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx1 # U   std::ratio<1,2629746>::num ' U  �r ( std::ratio<1,2629746>::den 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10  抏   _Mtx_try  抏   _Mtx_recursive R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gy 1 :   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits $ 6g   TP_CALLBACK_PRIORITY_NORMAL 2 U   std::integral_constant<__int64,12>::value 0 �   std::numeric_limits<char16_t>::digits10 % 6g   TP_CALLBACK_PRIORITY_INVALID  蔱   std::_INVALID_ARGUMENT  蔱   std::_NO_SUCH_PROCESS & 蔱   std::_OPERATION_NOT_PERMITTED , 蔱   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蔱   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN 1 :   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits % �    _Atomic_memory_order_relaxed 0 �  	 std::numeric_limits<char32_t>::digits10 % �   _Atomic_memory_order_consume % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst i _   std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >::_Minimum_asan_allocation_alignment 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 R U  ��Q std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Dx2 . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10 T U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx1 :    std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Multi P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx1 ":   std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0>::_Standard ! U   std::ratio<1,86400>::num % U  ��Q std::ratio<1,86400>::den : _   std::integral_constant<unsigned __int64,3>::value P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx2 , :   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits T U  ��Q std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx2 + �  	 std::numeric_limits<int>::digits10 O U  � std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gy : U  ��: std::integral_constant<__int64,146097>::value - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 � _   std::allocator<std::vector<donut::engine::TextureSubresourceData,std::allocator<donut::engine::TextureSubresourceData> > >::_Minimum_asan_allocation_alignment 3 U  �std::integral_constant<__int64,400>::value 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 � _   std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >::_Minimum_asan_allocation_alignment D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment - �    std::integral_constant<int,0>::value 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 ( 淝   ImDrawFlags_RoundCornersTopLeft ) 淝    ImDrawFlags_RoundCornersTopRight + 淝  @ ImDrawFlags_RoundCornersBottomLeft , 淝  � ImDrawFlags_RoundCornersBottomRight % 淝   ImDrawFlags_RoundCornersNone ! <q    COINITBASE_MULTITHREADED $ 淝  � ImDrawFlags_RoundCornersAll 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 T U  �X呩std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Nx2 6 :   std::numeric_limits<unsigned long>::is_modulo P U   std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Dx2 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 V U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx1 $ U   std::ratio<1,31556952>::num ( U  �X呩std::ratio<1,31556952>::den 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx2 3 �  \ std::filesystem::path::preferred_separator V U  �X呩std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gx Q U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gy + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask  �   �  n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 �   F  N :   std::_Trivial_cat<char,char,char &,char &>::_Same_size_and_compatible K :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_constructible ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos H :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_assignable   �   �  � :   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible  �     � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable 2_   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Bucket_size 2_   std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Min_buckets ,:    std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> >::_Multi � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >::_Minimum_asan_allocation_alignment a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment  �   � ,:    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>::_Multi /:   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>::_Standard c _   std::allocator<std::shared_ptr<donut::engine::IView> >::_Minimum_asan_allocation_alignment / 羟    D3D12_DESCRIPTOR_HEAP_TYPE_CBV_SRV_UAV + 羟   D3D12_DESCRIPTOR_HEAP_TYPE_SAMPLER ' 羟   D3D12_DESCRIPTOR_HEAP_TYPE_RTV ' 羟   D3D12_DESCRIPTOR_HEAP_TYPE_DSV � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >::_Minimum_asan_allocation_alignment ( �    D3D12_DESCRIPTOR_RANGE_TYPE_SRV ( �   D3D12_DESCRIPTOR_RANGE_TYPE_UAV ( �   D3D12_DESCRIPTOR_RANGE_TYPE_CBV � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible 3 ]�    D3D12_ROOT_PARAMETER_TYPE_DESCRIPTOR_TABLE 2 ]�   D3D12_ROOT_PARAMETER_TYPE_32BIT_CONSTANTS & ]�   D3D12_ROOT_PARAMETER_TYPE_CBV & ]�   D3D12_ROOT_PARAMETER_TYPE_SRV � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable ' >q  �   CLSCTX_ACTIVATE_X86_SERVER Q U  �r ( std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Nx2 4 �    D3D12_STATIC_BORDER_COLOR_TRANSPARENT_BLACK M U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Dx2 / �   D3D12_STATIC_BORDER_COLOR_OPAQUE_BLACK S U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx2 S U  �r ( std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx2 N U  6 std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gx N U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gy + g   JOB_OBJECT_NET_RATE_CONTROL_ENABLE 2 g   JOB_OBJECT_NET_RATE_CONTROL_MAX_BANDWIDTH - g   JOB_OBJECT_NET_RATE_CONTROL_DSCP_TAG 0 g   JOB_OBJECT_NET_RATE_CONTROL_VALID_FLAGS 4 U  @std::integral_constant<__int64,1600>::value 7 U  �;緎td::integral_constant<__int64,48699>::value D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment * g   JOB_OBJECT_IO_RATE_CONTROL_ENABLE 5 g   JOB_OBJECT_IO_RATE_CONTROL_STANDALONE_VOLUME 9 g   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ALL A g   JOB_OBJECT_IO_RATE_CONTROL_FORCE_UNIT_ACCESS_ON_SOFT_CAP   U  std::ratio<3600,1>::num   U   std::ratio<3600,1>::den � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible $ U  @std::ratio<1600,48699>::num H :    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified & U  �;緎td::ratio<1600,48699>::den � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable + �!        nvrhi::rt::c_IdentityTransform R U  �X呩std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Dx2 T U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx2 T U  �X呩std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gx O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gy B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE , Dq   COMGLB_EXCEPTION_DONOT_HANDLE_FATAL D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask $ U  �std::ratio<400,146097>::num @ �   std::_General_precision_tables_2<float>::_Max_special_P ( U  ��: std::ratio<400,146097>::den O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity 8 �  ' std::_General_precision_tables_2<float>::_Max_P A �   std::_General_precision_tables_2<double>::_Max_special_P ; ;  ���donut::app::StreamlineInterface::kInvalidFloat : <  �����donut::app::StreamlineInterface::kInvalidUint 9 �  5std::_General_precision_tables_2<double>::_Max_P a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask ?_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Bucket_size n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity ?_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Min_buckets n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size 9:    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >::_Multi g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size  U   std::ratio<24,1>::num  U   std::ratio<24,1>::den 7 :   std::atomic<unsigned int>::is_always_lock_free I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx1 $ �   std::_Locbase<int>::collate I U  std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx2 " �   std::_Locbase<int>::ctype I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx2 % �   std::_Locbase<int>::monetary H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gx $ �   std::_Locbase<int>::numeric H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gy ! �   std::_Locbase<int>::time % �    std::_Locbase<int>::messages   �  ? std::_Locbase<int>::all ! �    std::_Locbase<int>::none A _   std::allocator<bool>::_Minimum_asan_allocation_alignment :    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi :   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard 9 U  ��Q std::integral_constant<__int64,86400>::value � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable ( <   donut::math::vector<int,2>::DIM 1 U   std::integral_constant<__int64,1>::value C _   std::_Locked_pointer<std::_Stop_callback_base>::_Lock_mask D _    std::_Locked_pointer<std::_Stop_callback_base>::_Not_locked R _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_not_needed N _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_needed I _   �黶td::_Locked_pointer<std::_Stop_callback_base>::_Ptr_value_mask T U  �r ( std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Dx2 % U  ��Q std::ratio<86400,1>::num ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos ! U   std::ratio<86400,1>::den � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx1 V U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx1 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx2 I _   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment V U  �r ( std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx2 Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gy F U  
� 泌�  std::integral_constant<__int64,26297460000000>::value * U   std::ratio<1,26297460000000>::num 2 U  
� 泌�  std::ratio<1,26297460000000>::den * 銮    D3D12_INDIRECT_ARGUMENT_TYPE_DRAW 2 銮   D3D12_INDIRECT_ARGUMENT_TYPE_DRAW_INDEXED . 銮   D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH 8 銮   D3D12_INDIRECT_ARGUMENT_TYPE_VERTEX_BUFFER_VIEW 7 銮   D3D12_INDIRECT_ARGUMENT_TYPE_INDEX_BUFFER_VIEW . 銮   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT : 銮   D3D12_INDIRECT_ARGUMENT_TYPE_CONSTANT_BUFFER_VIEW : 銮   D3D12_INDIRECT_ARGUMENT_TYPE_SHADER_RESOURCE_VIEW ; 銮   D3D12_INDIRECT_ARGUMENT_TYPE_UNORDERED_ACCESS_VIEW 3 銮  	 D3D12_INDIRECT_ARGUMENT_TYPE_DISPATCH_RAYS _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > >::_Minimum_asan_allocation_alignment  U   std::ratio<7,1>::num  U   std::ratio<7,1>::den  &q    NODE_INVALID U U  �X呩std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Nx2  &q   NODE_ELEMENT  &q   NODE_ATTRIBUTE Q U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Dx2  &q   NODE_TEXT  &q   NODE_CDATA_SECTION  &q   NODE_ENTITY_REFERENCE  &q   NODE_ENTITY $ &q   NODE_PROCESSING_INSTRUCTION I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx1  &q   NODE_COMMENT  &q  	 NODE_DOCUMENT I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx1  &q  
 NODE_DOCUMENT_TYPE  &q   NODE_DOCUMENT_FRAGMENT M U  ��Q std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx2  4q    XMLELEMTYPE_ELEMENT  4q   XMLELEMTYPE_TEXT H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gx  4q   XMLELEMTYPE_COMMENT H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gy  4q   XMLELEMTYPE_DOCUMENT  4q   XMLELEMTYPE_DTD  4q   XMLELEMTYPE_PI K :   std::atomic<std::_Stop_callback_base const *>::is_always_lock_free S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx1 W U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx1 S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx2 / :   std::atomic<long>::is_always_lock_free W U  �X呩std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx2 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gx R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gy : U  ��:	 std::integral_constant<__int64,604800>::value  @q   VT_I2  @q   VT_I4  @q   VT_BSTR  @q  	 VT_DISPATCH  @q  
 VT_ERROR  @q   VT_VARIANT  @q  
 VT_UNKNOWN 3 竡   DISPLAYCONFIG_SCANLINE_ORDERING_INTERLACED  @q   VT_I1 G U  
� <$A std::integral_constant<__int64,315569520000000>::value  @q   VT_I8  @q  $ VT_RECORD - <  `std::_Big_integer_flt::_Maximum_bits - <    std::_Big_integer_flt::_Element_bits . <  s std::_Big_integer_flt::_Element_count  @q  � �VT_RESERVED + U   std::ratio<1,315569520000000>::num ( U  ��: std::ratio<146097,400>::num 3 U  
� <$A std::ratio<1,315569520000000>::den $ U  �std::ratio<146097,400>::den T U  ��: std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx1 P U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx1 T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment T U  ��Q std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx2  晄  LPPARAMDESCEX  6q  FUNCKIND  檚  tagPARAMDESCEX  梥  PARAMDESC  梥  tagPARAMDESC  搒  tagARRAYDESC  Fq  CALLCONV  (q  DESCKIND  Rs  ELEMDESC  憇  BINDPTR  峴  tagFUNCDESC  Nr  INVOKEKIND  Hs  TLIBATTR  憇  tagBINDPTR  rs  tagSTATSTG  Ys  tagTYPEDESC  峴  FUNCDESC  "   HREFTYPE  宷  SYSKIND  !   ImWchar16  硆  tagVARDESC  誵  TYPEKIND  坰  IEnumSTATSTG  rs  STATSTG  ps  ITypeComp  Ys  TYPEDESC  Os  IDLDESC  Rs  tagELEMDESC  Os  tagIDLDESC  鋑  VARIANTARG  Ms  EXCEPINFO  Ms  tagEXCEPINFO 
    DISPID     MEMBERID  �  _CatchableType  u   UINT ' 鹎  D3D12_BACKGROUND_PROCESSING_MODE  罨  ImNewWrapper  A�  D3D12_BARRIER_LAYOUT  袄  ImVector<ImFont *>  &r  tagCAUL  Hs  tagTLIBATTR  6g  _TP_CALLBACK_PRIORITY " �  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  '0  __std_tzdb_error  H�  ImFontConfig & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const>  艋  ImGuiKey  柷  D3D_PRIMITIVE_TOPOLOGY  Fs  tagShutdownType  q   OLECHAR G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �0  _Ctypevec  奕  ImGuiChildFlags_  簈  tagCABSTR   �  D3D12_BARRIER_TYPE  Fq  tagCALLCONV  誵  tagTYPEKIND   �  D3D12_STATIC_BORDER_COLOR & �  $_TypeDescriptor$_extraBytes_28  鋑  VARIANT     ImS16  #   uintmax_t  s  ISequentialStream     int64_t  塺  BSTRBLOB    _Smtx_t    ImGuiTextBuffer  邫  MISHeuristic  齚  _Thrd_result  #   rsize_t  炃  _D3D_INCLUDE_TYPE  #   DWORD_PTR  }r  TYPEATTR  婢  ImVector<ImDrawVert>     VARIANT_BOOL  �>  __std_fs_find_data  ~�  ImVector<ImVec2> &   $_TypeDescriptor$_extraBytes_23 
 鑗  PUWSTR - cZ  $_s__CatchableTypeArray$_extraBytes_32 # )D  __std_fs_reparse_data_buffer Z _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^  _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> `   __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �>  __std_fs_dir_handle  �  ImGuiOnceUponAFrame ( g  JOB_OBJECT_NET_RATE_CONTROL_FLAGS  rg  AR_STATE  5s  tagCADBL  乬  _DEVICE_DATA_SET_RANGE  �/  __std_access_rights  0q  VARKIND 3 馇  D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE    _TypeDescriptor & NZ  $_TypeDescriptor$_extraBytes_34 ! 羟  D3D12_DESCRIPTOR_HEAP_TYPE  wr  _tagPSUACTION  B�  ImFontAtlasCustomRect 
 7s  tagDEC  9s  CALPSTR     LONG_PTR  q  tagBINDSTRING  渇  _Stl_critical_section 	 I  tm   M�  _D3D_SHADER_VARIABLE_TYPE ! 樓  _D3D_SHADER_VARIABLE_CLASS  祌  tagCACLIPDATA  #   ULONG_PTR " �  D3D12_RESOURCE_BARRIER_TYPE % �  _s__RTTICompleteObjectLocator2 " �  D3D12_DESCRIPTOR_RANGE_TYPE  裶  tagURLZONE  峄  ImGuiTableSortSpecs  頶  PUWSTR_C  *g  PTP_CLEANUP_GROUP  Bq  __MIDL_ICodeInstall_0001  p  PCHAR  $q  tagBINDSTATUS  蚮  _GUID  ur  _URLZONEREG  梣  _LARGE_INTEGER ' <s  _LARGE_INTEGER::<unnamed-type-u>    ImGuiFocusedFlags_ & kZ  $_TypeDescriptor$_extraBytes_30  蚯  D3D12_COLOR_WRITE_ENABLE  纐  CLIPDATA  憅  CAFILETIME  9s  tagCALPSTR  r  CALPWSTR 
  q  CAL  �r  tagCABSTRBLOB      ImU8  遯  tagSAFEARRAYBOUND  茨  ImDrawChannel  偨  ImDrawCallback  4s  tagCAFLT A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 焃  $_TypeDescriptor$_extraBytes_46 
 �  ImFont 
 攓  tagCAH  7s  DECIMAL  聁  tagCAUI  !   WORD  �  _s__CatchableType  牻  ImDrawListSplitter  3�  ImVector<unsigned int>  噐  CAUH  [�  D3D_NAME  .q  tagCADATE  �  ImGuiDragDropFlags_  �  D3D_SHADER_MODEL  5s  CADBL  �  LPCOLESTR  K�  ImGuiTreeNodeFlags_  頶  PCUWSTR  巕  CAPROPVARIANT  媲  ImGuiHoveredFlags_  4s  CAFLT & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t  t   ImGuiMouseCursor ' g  JOB_OBJECT_IO_RATE_CONTROL_FLAGS 9   __vcrt_va_list_is_reference<wchar_t const * const>  觪  _USER_ACTIVITY_PRESENCE  uy  ProgressBar  銩  __std_fs_filetime E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const>  h�  ImColor    PLONG & �  $_TypeDescriptor$_extraBytes_20  �  ProceduralSkyConstants  醧  DISPPARAMS  坬  _FILETIME  p  va_list  F�  ImDrawList  攇  FS_BPIO_INFLAGS - �  $_s__CatchableTypeArray$_extraBytes_16   SD  __std_fs_copy_file_result  �5  __std_code_page  僩  PDEVICE_DSM_DEFINITION      BYTE . �  D3D12_RENDER_PASS_BEGINNING_ACCESS_TYPE % 嵘  D3D12_RAYTRACING_GEOMETRY_TYPE 
 �  PCWSTR  2s  IStream � 葝  std::_Default_allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > d 讔  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > G 啀  std::_Simple_types<std::pair<nvrhi::BindingSetItem const ,int> > � 瓖  std::_Simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � �  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > a 顛  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1> d  �  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> > > ] 鍗  std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > ^ f�  std::_Default_allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > >  A   std::max_align_t z 韃  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> [ 賺  std::allocator_traits<std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > � 蕧  std::allocator_traits<std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > f 呦  std::_Compressed_pair<std::default_delete<donut::app::ImGui_NVRHI>,donut::app::ImGui_NVRHI *,1> 3 紫  std::default_delete<donut::app::ImGui_NVRHI> � <�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � 粛  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > = 閶  std::_Default_allocator_traits<std::allocator<float> > � 瘝  std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > C 獚  std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> > � 爫  std::_Compressed_pair<std::allocator<donut::math::vector<float,2> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > >,1> Q   std::_Default_allocator_traits<std::allocator<donut::app::IRenderPass *> > �   std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > � 笇  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > C 槏  std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > � 悕  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > >,1> | 亶  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > M J�  std::_Default_allocator_traits<std::allocator<nvrhi::BindingSetItem> > L 垗  std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > s 儘  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > > T r�  std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > > @ g�  std::_Arg_types<donut::app::DeviceManager &,unsigned int> U h�  std::allocator_traits<std::allocator<donut::math::vector<unsigned short,4> > > � Z�  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::TextureData> >,std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > >,1> j 葡  std::_Default_allocator_traits<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > > _ R�  std::_Deque_val<std::_Deque_simple_types<std::shared_ptr<donut::engine::TextureData> > > : 榝  std::_Vector_val<std::_Simple_types<unsigned int> > D L�  std::allocator_traits<std::allocator<nvrhi::BindingSetItem> > � >�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > � �  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > 6 緡  std::_Ptr_base<donut::engine::DescriptorHandle> � .�  std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemHasher,std::_Compressed_pair<donut::engine::DescriptorTableManager::BindingSetItemsEqual,float,1>,1> F 栁  std::_Func_class<void,donut::app::DeviceManager &,unsigned int> P 酉  std::_Func_class<void,donut::app::DeviceManager &,unsigned int>::_Storage U �  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,3> > > e "�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> *> > \ $�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> *> > � �  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > �鰠  std::_Hash<std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> > � O]  std::_Non_trivial_copy<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > "p�  std::_Hash<std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> > [ �  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::TextureData> > > �魧  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > >,1> 4 曂  std::_Simple_types<donut::app::IRenderPass *> c 閷  std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1> U 鐘  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,2> > > d 嬐  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > 4 鄬  std::allocator<donut::math::vector<float,2> > � 邢  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::app::RegisteredFont> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::app::RegisteredFont> > >,1> = �  std::allocator<donut::math::vector<unsigned short,4> > K 賹  std::_Vector_val<std::_Simple_types<donut::math::vector<float,2> > > p 巉  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> U ﹪  std::_Default_allocator_traits<std::allocator<donut::math::vector<float,4> > > 1 聤  std::_Ptr_base<donut::engine::BufferGroup> � 蠈  std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > M k^  std::_Normal_allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > � �  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > F脤  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > >,1> � 皩  std::_Compressed_pair<std::allocator<nvrhi::BindingSetItem>,std::_Vector_val<std::_Simple_types<nvrhi::BindingSetItem> >,1> e Y�  std::_Default_allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > s �  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � Y�  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > { ▽  std::list<std::pair<nvrhi::BindingSetItem const ,int>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > > a 认  std::allocator_traits<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > > � 倖  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > � 废  std::_Compressed_pair<std::allocator<std::_List_node<donut::app::IRenderPass *,void *> >,std::_List_val<std::_List_simple_types<donut::app::IRenderPass *> >,1> , <�  std::allocator<nvrhi::BindingSetItem> K 5�  std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > > . 綶  std::_Ptr_base<donut::vfs::IFileSystem> � +�  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > J g�  std::_Default_allocator_traits<std::allocator<nvrhi::BufferRange> > � �  std::_Uhash_choose_transparency<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,void> � 匽  std::_Non_trivial_move_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > H   std::allocator_traits<std::allocator<donut::app::IRenderPass *> > � 猑  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> ��  std::_Umap_traits<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > >,0> L �  std::allocator_traits<std::allocator<donut::math::vector<float,3> > >  m�  std::allocator<float> � 	�  std::_Compressed_pair<std::allocator<donut::math::vector<unsigned short,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<unsigned short,4> > >,1> 鷭  std::_Umap_traits<nvrhi::BindingSetItem,int,std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> >,0> � 髬  std::_Compressed_pair<std::allocator<donut::math::vector<float,3> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,3> > >,1> k ,]  std::_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0> 4 雼  std::allocator_traits<std::allocator<float> > < �  std::_Conditionally_enabled_hash<nvrhi::ITexture *,1> [ 輯  std::allocator<std::_List_node<std::pair<nvrhi::BindingSetItem const ,int>,void *> > Q謰  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > >,1> � a]  std::_Non_trivial_move<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > 螊  std::list<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > � )�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > [ b�  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > > C X�  std::allocator<std::shared_ptr<donut::engine::TextureData> > � Q�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::BindingSetItem const ,int> > > > > > ] 栂  std::unique_ptr<donut::app::ImGui_NVRHI,std::default_delete<donut::app::ImGui_NVRHI> > [ 坁  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' {I  std::default_delete<wchar_t [0]> . /  std::_Conditionally_enabled_hash<int,1> A C  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> > �;�  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >,1>  �  std::_Lockit  坃  std::_Stop_callback_base  b  std::timed_mutex D �;  std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > � 3�  std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty - �?  std::reverse_iterator<wchar_t const *> 6 刕  std::allocator_traits<std::allocator<char8_t> > " 媂  std::_Char_traits<char,int> � Z|  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >  0  std::_Fs_file  �=  std::optional<int> � ,�  std::_Uhash_compare<nvrhi::BindingSetItem,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual>  "   std::_Atomic_counter_t  N  std::_Num_base & $/  std::hash<std::error_condition> K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 5 $0  std::_Tzdb_deleter<__std_tzdb_time_zones_info> # �  std::numeric_limits<char8_t>  /,  std::_Big_uint128    std::condition_variable � �  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >  �=  std::optional<__int64> � 	�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > > E m^  std::allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > G ^^  std::allocator_traits<std::allocator<std::chrono::leap_second> > ) 怷  std::_Narrow_char_traits<char,int> i 魥  std::initializer_list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > L 閵  std::allocator_traits<std::allocator<donut::math::vector<float,2> > >  j  std::hash<float> E D~  std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc> ! �4  std::__floating_decimal_64 S 蘕  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone_link> > R 歿  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > \ 絹  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > " 唂  std::_Align_type<double,64>  �5  std::less<void>  /  std::hash<int> � =|  std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *>  P  std::_Num_int_base  i3  std::ctype<wchar_t> " �/  std::_System_error_category / 揚  std::_Conditionally_enabled_hash<bool,1> 1 剂  std::_Ptr_base<donut::app::RegisteredFont> 2 蹔  std::shared_ptr<donut::engine::BufferGroup> + 琠  std::_Atomic_storage<unsigned int,4> � 瘖  std::unordered_map<nvrhi::BindingSetItem,int,donut::engine::DescriptorTableManager::BindingSetItemHasher,donut::engine::DescriptorTableManager::BindingSetItemsEqual,std::allocator<std::pair<nvrhi::BindingSetItem const ,int> > >  �5  std::_Format_arg_index � K�  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> > >  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > / y2  std::codecvt<char32_t,char8_t,_Mbstatet> 4 鑹  std::shared_ptr<donut::engine::LoadedTexture> k =]  std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > f �;  std::pair<std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >,bool> 碶  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> ! 蓅  std::_Ptr_base<std::mutex> �}  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >,1> u 訵  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > . �  std::_Ptr_base<donut::engine::MeshInfo> 6 餧  std::allocator_traits<std::allocator<wchar_t> >  t  std::shared_timed_mutex & ;~  std::equal_to<unsigned __int64> � s]  std::_Non_trivial_copy_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  `  std::bad_cast  JR  std::equal_to<void> 4 D�  std::allocator<donut::math::vector<float,4> > � 粿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > q 綁  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > > � 媺  std::vector<donut::math::vector<unsigned short,4>,std::allocator<donut::math::vector<unsigned short,4> > >::_Reallocation_policy o 踈  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1>     std::_Compare_t " u  std::numeric_limits<double> $ d  std::atomic<unsigned __int64>  �  std::__non_rtti_object < 鰒  std::_Ptr_base<donut::engine::DescriptorTableManager> , K2  std::_Codecvt_guard<char8_t,char16_t> ( @  std::_Basic_container_proxy_ptr12 � 巤  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> � D�  std::_Default_allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > 2 #L  std::allocator<std::chrono::time_zone_link> 4 F�  std::allocator<donut::math::vector<float,3> > = 6`  std::_Atomic_pointer<std::_Stop_callback_base const *> > 刦  std::vector<unsigned int,std::allocator<unsigned int> > T Tf  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy   �5  std::_Decode_result<char> 1    std::array<nvrhi::FramebufferAttachment,8>  q  std::_Num_float_base  抈  std::stop_token  �-  std::logic_error � ?�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > > > 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety / 頚  std::allocator<std::chrono::leap_second> P �  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> > f 鈭  std::vector<nvrhi::BindingSetItem,std::allocator<nvrhi::BindingSetItem> >::_Reallocation_policy ! 頬  std::char_traits<char32_t> � ﹫  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  Q1  std::locale  �1  std::locale::_Locimp  b1  std::locale::facet   k1  std::locale::_Facet_guard  1  std::locale::id - VL  std::allocator<std::chrono::time_zone>  �  std::_Compare_ncmp s 竂  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   R  std::numeric_limits<bool>  S6  std::_Fmt_codec<char,0> # *Y  std::_WChar_traits<char16_t> _ 殘  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > > u i�  std::vector<donut::math::vector<float,2>,std::allocator<donut::math::vector<float,2> > >::_Reallocation_policy P絴  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> P 獻  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> � 納  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> > Z W  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> >  綻  std::stop_source R �9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > h {9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >::_Reallocation_policy M i?  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � +�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  F.  std::overflow_error " x5  std::_Basic_format_arg_type , b2  std::_Codecvt_guard<char16_t,char8_t> % L  std::_One_then_variadic_args_t W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * jb  std::_Vb_val<std::allocator<bool> > E 鞢  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j 靅  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   闿  std::char_traits<wchar_t> \ :  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > r �9  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> >::_Reallocation_policy  pa  std::recursive_mutex   �  std::pmr::memory_resource  	>  std::pair<int,int> ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> � 鶉  std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > � 鵷  std::unordered_map<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > � 駠  std::_Compressed_pair<donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::_Compressed_pair<std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,float,1>,1> � 鐕  std::_Compressed_pair<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >,std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::engine::MeshGeometry> > >,1> n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy "邍  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0>  砞  std::false_type S 賴  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > 泧  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > > #讄  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � �:  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! a  std::hash<std::thread::id> $ 
6  std::_Fmt_fixed_buffer_traits  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > c 蠶  std::_Alloc_construct_ptr<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  扟  std::fpos<_Mbstatet> b gQ  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone> > > , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  俛  std::adopt_lock_t o 奓  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_current_zone_info>,__std_tzdb_current_zone_info *,1>  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> 7 讎  std::shared_ptr<donut::engine::DescriptorHandle> , n  std::numeric_limits<unsigned __int64> F L  std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >  �0  std::_Locinfo 6 B  std::_Ptr_base<std::filesystem::_Dir_enum_impl> L 珖  std::allocator_traits<std::allocator<donut::math::vector<float,4> > > # �  std::hash<nvrhi::ITexture *> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 4 婯  std::_Atomic_padded<std::chrono::tzdb_list *> s 奤  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > N 諼  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone> > � 潎  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > > 9 DJ  std::basic_streambuf<char,std::char_traits<char> > $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16> � 獅  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void> f ;�  std::_Simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > = 媷  std::shared_ptr<donut::engine::DescriptorTableManager>  �  std::string_view  �  std::wstring_view � 憒  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound  Qa  std::_Mutex_base �by  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > b u�  std::_Compressed_pair<std::allocator<float>,std::_Vector_val<std::_Simple_types<float> >,1>  Z5  std::money_base  縘  std::money_base::pattern  y0  std::_Timevec  胉  std::nostopstate_t D f�  std::allocator<std::shared_ptr<donut::engine::MeshGeometry> >  f  std::defer_lock_t ? ^�  std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >   �.  std::_Init_once_completer v  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > �   std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >  絔  std::endian j sC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � BC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �a  std::scoped_lock<> + �2  std::codecvt<wchar_t,char,_Mbstatet> h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> d xK  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::leap_second> > > Q 籡  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16> � _�  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >    std::_Iterator_base12  mN  std::_Pocma_values . B0  std::_Tzdb_deleter<__std_tzdb_sys_info> 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> * 皒  std::shared_lock<std::shared_mutex> ! /  std::hash<std::error_code> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > N 	@  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > ( 筣  std::ratio<1,1000000000000000000> @ nY  std::_Default_allocator_traits<std::allocator<char32_t> > ( d  std::_Atomic_padded<unsigned int> D R�  std::allocator<std::shared_ptr<donut::engine::TextureData> *>  �?  std::allocator<char32_t> ? !D  std::unique_ptr<char [0],std::default_delete<char [0]> > � }  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > $ �  std::_Atomic_integral<long,4> � 1�  std::list<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  L<  std::_Flist_unchecked_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,std::_Iterator_base0> R =K  std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >  桺  std::hash<bool> � K�  std::allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > >     std::streamsize 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty � �;  std::_Compressed_pair<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >,std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,1> ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> � �:  std::tuple<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > " b  std::lock_guard<std::mutex> N 蔝  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone> > K I�  std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > > ] �:  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � ?�  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > S 礥  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone_link> > | 嫔  std::_Uhash_choose_transparency<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *>,void> k =�  std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �  std::hash<long double> � 侤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � Q@  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy 2 (~  std::equal_to<nvrhi::TextureSubresourceSet> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy [ �<  std::_Flist_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > � 鑯  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >   �  std::_Comparison_category / 8�  std::shared_ptr<donut::engine::MeshInfo> X}  std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0>  f  std::try_to_lock_t  $�  std::array<bool,3> � }  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ �5  std::_Decode_result<char32_t> # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0 F 镂  std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1> J 菸  std::_List_val<std::_List_simple_types<donut::app::IRenderPass *> >  w  std::hash<double> L 奈  std::function<void __cdecl(donut::app::DeviceManager &,unsigned int)> O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> n �:  std::_Tuple_val<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > D �  std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> " 猘  std::_Align_type<double,72> G �;  std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > x �  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �5  std::_Lazy_locale � ]x  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >,std::_Iterator_base0> / Y  std::_Char_traits<char32_t,unsigned int>  >/  std::_System_error < 朿  std::_Atomic_padded<std::_Stop_callback_base const *> ( �)  std::hash<nvrhi::FramebufferInfo>  螸  std::_Fmt_buffer<char> 9 桰  std::allocator<std::filesystem::_Find_file_handle> b 衶  std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >  �.  std::error_condition % 砞  std::integral_constant<bool,0> | �  std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > > � 鈫  std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Is_bidi � 鄦  std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > >::_Pop_direction  �  std::bad_exception & 肐  std::_Zero_then_variadic_args_t � 眧  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > / 甝  std::_General_precision_tables_2<double> ? $`  std::_Atomic_storage<std::_Stop_callback_base const *,8> � ]L  std::_Compressed_pair<std::allocator<std::chrono::time_zone>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >,1>  �  std::u32string N {J  std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>  �-  std::invalid_argument   玗  std::char_traits<char8_t> [   std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > . ]  std::_General_precision_tables_2<float> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > � 剢  std::allocator_traits<std::allocator<std::_List_node<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> >,void *> > > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 1 臺  std::_Atomic_integral_facade<unsigned int>  瀉  std::cv_status 9 椡  std::_List_simple_types<donut::app::IRenderPass *> S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 3 s�  std::_Vector_val<std::_Simple_types<float> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > b L:  std::unique_ptr<__std_tzdb_time_zones_info,std::_Tzdb_deleter<__std_tzdb_time_zones_info> > + rD  std::pair<enum __std_win_error,bool> A i�  std::allocator_traits<std::allocator<nvrhi::BufferRange> > S  ?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char8_t> > > � t�  std::_Uhash_choose_transparency<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,void>  �5  std::_Fmt_codec_base<0> J ]  std::allocator_traits<std::allocator<std::chrono::time_zone_link> >  鑐  std::thread  a  std::thread::id S Y?  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �-  std::length_error \ [�  std::allocator_traits<std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >  3a  std::jthread F TU  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 烮  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! s  std::numeric_limits<float>  �4  std::time_base   }4  std::time_base::dateorder ) �  std::_Atomic_integral_facade<long> i 鐊  std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >  絘  std::mutex Q �  std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > % 盤  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " VP  std::hash<unsigned __int64> f 9  std::unique_ptr<__std_tzdb_current_zone_info,std::_Tzdb_deleter<__std_tzdb_current_zone_info> > 1 癒  std::_Flist_node<std::chrono::tzdb,void *>  ]  std::ratio<60,1> k 軰  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_time_zones_info>,__std_tzdb_time_zones_info *,1> : +6  std::_String_view_iterator<std::char_traits<char> > 3 覊  std::_Ptr_base<donut::engine::LoadedTexture>  '  std::exception_ptr  �  std::strong_ordering  ]  std::ratio<1,1000000> % �  std::_Itraits_pointer_strategy [ 嵧  std::allocator_traits<std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > � M�  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> > > )   std::hash<enum nvrhi::BlendFactor> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t>  �.  std::once_flag  �.  std::error_code T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy ' �4  std::pair<char *,enum std::errc> * �=  std::_Optional_destruct_base<int,1> ! �4  std::__floating_decimal_32 J :w  std::enable_shared_from_this<donut::engine::DescriptorTableManager>  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < �\  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  �3  std::_Iosb<int>   �3  std::_Iosb<int>::_Seekdir ! �3  std::_Iosb<int>::_Openmode   �3  std::_Iosb<int>::_Iostate ! �3  std::_Iosb<int>::_Fmtflags # �3  std::_Iosb<int>::_Dummy_enum 7 齖  std::allocator_traits<std::allocator<char32_t> >  ~[  std::nano ( }�  std::_Ptr_base<donut::vfs::IBlob> I 7L  std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> � 蛝  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > | =�  std::_Compressed_pair<std::allocator<nvrhi::BufferRange>,std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> >,1> C �  std::allocator<std::shared_ptr<donut::app::RegisteredFont> > M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > j5�  std::unordered_map<donut::engine::CommonRenderPasses::PsoCacheKey,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline>,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey>,std::allocator<std::pair<donut::engine::CommonRenderPasses::PsoCacheKey const ,nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> > > > 1 Y  std::_Char_traits<char16_t,unsigned short> a _<  std::_Flist_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > $ �)  std::hash<nvrhi::BufferRange> V ^;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> > l -;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >::_Reallocation_policy T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  "1  std::_Locbase<int> " 鈙  std::shared_ptr<std::mutex> i 恾  std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> 9 $z  std::shared_ptr<donut::engine::CommonRenderPasses> S bX  std::forward_list<std::chrono::tzdb,std::_Crt_allocator<std::chrono::tzdb> > ! 郳  std::char_traits<char16_t> � 瞾  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > > � 鍇  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<> 5 �<  std::_Atomic_pointer<std::chrono::tzdb_list *> P 燯  std::_Uninitialized_backout_al<std::allocator<std::chrono::leap_second> >  �  std::_Container_base12 W 轡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  t  std::shared_mutex  	/  std::io_errc  4  std::ios_base  )4  std::ios_base::_Fnarray  #4  std::ios_base::_Iosarray  �3  std::ios_base::Init  �3  std::ios_base::failure  �3  std::ios_base::event ) X  std::numeric_limits<unsigned char> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type  p�  std::array<bool,349>   d  std::numeric_limits<long> " 衆  std::initializer_list<char> � p�  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > >  �  std::_Invoker_strategy  鯟  std::nothrow_t � [�  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � L�  std::_Compressed_pair<std::allocator<donut::math::vector<float,4> >,std::_Vector_val<std::_Simple_types<donut::math::vector<float,4> > >,1>  檁  std::_Stop_state 荺  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �  std::_Default_allocate_traits   璍  std::_Fmt_buffer<wchar_t> M 0?  std::_String_iterator<std::_String_val<std::_Simple_types<char8_t> > > N �?  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 瞈  std::allocator_traits<std::allocator<char> > ! `  std::numeric_limits<short>  u   std::_Vbase . S  std::allocator<nvrhi::rt::GeometryDesc> # 醓  std::unique_lock<std::mutex>  痋  std::ratio<2629746,1> ( 苭  std::array<nvrhi::BufferRange,11> ; �  std::basic_string_view<char,std::char_traits<char> > 1 [�  std::_Ptr_base<donut::engine::TextureData> ' K�  std::equal_to<nvrhi::ITexture *> ! �3  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > � qx  std::_List_const_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > | �  std::_Compressed_pair<std::hash<nvrhi::ITexture *>,std::_Compressed_pair<std::equal_to<nvrhi::ITexture *>,float,1>,1> + �5  std::_Grapheme_Break_property_values < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 0  �  std::allocator<donut::app::IRenderPass *> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty 2 化  std::_Ptr_base<donut::engine::TextureCache> O 镻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock � 鯧  std::_Compressed_pair<std::allocator<std::chrono::leap_second>,std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >,1>  �  std::bad_alloc  ^.  std::underflow_error B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> ) 礚  std::_Fmt_counting_buffer<wchar_t> c 蔏  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_leap_info [0]>,__std_tzdb_leap_info *,1>  =�  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > > � �  std::vector<std::shared_ptr<donut::engine::MeshGeometry>,std::allocator<std::shared_ptr<donut::engine::MeshGeometry> > >::_Reallocation_policy J 侷  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D oI  std::_Compressed_pair<std::default_delete<char [0]>,char *,1> � A}  std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,std::_Iterator_base0> Z 尢  std::_Vector_val<std::_Simple_types<std::shared_ptr<donut::app::RegisteredFont> > >   騙  std::atomic<unsigned int>  L5  std::messages_base - 遊  std::_Crt_allocator<std::chrono::tzdb> g 癥  std::_Normal_allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  .  std::out_of_range # f  std::numeric_limits<__int64> _ 苿  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > > u 晞  std::vector<donut::math::vector<float,3>,std::allocator<donut::math::vector<float,3> > >::_Reallocation_policy i 鮅  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > " 骵  std::initializer_list<bool>  +3  std::ctype<char> @ W�  std::_Vector_val<std::_Simple_types<nvrhi::BufferRange> > d �:  std::_Tuple_val<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > >  h  std::memory_order `   std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � M�  std::queue<std::shared_ptr<donut::engine::TextureData>,std::deque<std::shared_ptr<donut::engine::TextureData>,std::allocator<std::shared_ptr<donut::engine::TextureData> > > > ! (b  std::recursive_timed_mutex  �4  std::chars_format " 磗  std::condition_variable_any 6 鮯  std::condition_variable_any::_Cv_any_notify_all }   std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > > � z�  std::vector<std::shared_ptr<donut::app::RegisteredFont>,std::allocator<std::shared_ptr<donut::app::RegisteredFont> > >::_Reallocation_policy  T4  std::nullopt_t  V4  std::nullopt_t::_Tag  賉  std::ratio<3600,1> � &�  std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > # �  std::_Atomic_storage<long,4> , �<  std::atomic<std::chrono::tzdb_list *> # *  std::hash<nvrhi::BlendState> / 譡  std::shared_ptr<donut::vfs::IFileSystem>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  U  std::in_place_t z f}  std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> > D jL  std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >  a/  std::system_error < {X  std::_Default_allocator_traits<std::allocator<char> > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > g 楺  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> > >    std::ratio<1,1> I 匪  std::allocator<std::_List_node<donut::app::IRenderPass *,void *> > ^ �;  std::unique_ptr<__std_tzdb_leap_info [0],std::_Tzdb_deleter<__std_tzdb_leap_info [0]> > 2 F6  std::_Measure_string_prefix_iterator_legacy ) �=  std::_Optional_construct_base<int>   D[  std::forward_iterator_tag  ..  std::runtime_error �倈  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   
  std::bad_array_new_length ; 殈  std::_Conditionally_enabled_hash<nvrhi::IShader *,1> E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > � �  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > / 綾  std::_Atomic_storage<unsigned __int64,8> v �  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �0  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z WY  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > /'y  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::engine::TextureData>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > >,0> >  鑕  std::allocator<bool>  �  std::u16string _ 	�  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > > u 貎  std::vector<donut::math::vector<float,4>,std::allocator<donut::math::vector<float,4> > >::_Reallocation_policy ]蝩  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >  ^  std::nested_exception  �  std::_Distance_unknown H jX  std::pointer_traits<std::_Flist_node<std::chrono::tzdb,void *> *> ) 殐  std::allocator<nvrhi::BufferRange> , 妝  std::lock_guard<std::recursive_mutex> 7 K  std::basic_ostream<char,std::char_traits<char> > ( j  std::numeric_limits<unsigned int> < 7U  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> +檨  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > ) 搩  std::shared_ptr<donut::vfs::IBlob> � ,}  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > > E   std::allocator_traits<std::allocator<std::chrono::time_zone> > , 2  std::codecvt<char32_t,char,_Mbstatet> | �}  std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff 0 h�  std::vector<float,std::allocator<float> > F 6�  std::vector<float,std::allocator<float> >::_Reallocation_policy  �  std::atomic<long> � 藎  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> , �2  std::_Codecvt_guard<char8_t,char32_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> � f�  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,1> ' w  std::numeric_limits<long double>  /  std::errc V W�  std::list<donut::app::IRenderPass *,std::allocator<donut::app::IRenderPass *> > " hX  std::pointer_traits<char *> V 仁  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > ; 唟  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > , 凷  std::default_delete<std::_Facet_base>  v.  std::range_error  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �4  std::to_chars_result  颽  std::_UInt_is_zero  �  std::_Compare_eq y 調  std::_Uhash_choose_transparency<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64>,void>  ~[  std::ratio<1,1000000000>  �?  std::allocator<char16_t> $ hI  std::default_delete<char [0]> . 糱  std::vector<bool,std::allocator<bool> > J 鷤  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> > ` 蓚  std::vector<nvrhi::BufferRange,std::allocator<nvrhi::BufferRange> >::_Reallocation_policy ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  |[  std::ratio<1,1000> � 媯  std::_Uhash_compare<donut::engine::CommonRenderPasses::PsoCacheKey,donut::engine::CommonRenderPasses::PsoCacheKey::Hash,std::equal_to<donut::engine::CommonRenderPasses::PsoCacheKey> >  z[  std::ratio<1,10000000> ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  $1  std::_Crt_new_delete % �/  std::_Iostream_error_category2 * 糛  std::_String_constructor_concat_tag 4 c`  std::atomic<std::_Stop_callback_base const *>  �8  std::allocator<char> 秚  std::_Hash<std::_Umap_traits<unsigned __int64,nvrhi::RefCountPtr<nvrhi::IBindingSet>,std::_Uhash_compare<unsigned __int64,std::hash<unsigned __int64>,std::equal_to<unsigned __int64> >,std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > >,0> >    std::nullptr_t . 鏧  std::_Char_traits<char8_t,unsigned int> ' x[  std::_Floating_to_chars_overload & H[  std::random_access_iterator_tag ; 梷  std::_Conditionally_enabled_hash<unsigned __int64,1> R 酙  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> 2 q�  std::shared_ptr<donut::engine::TextureData> � 倄  std::_List_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > 3 P0  std::_Tzdb_deleter<__std_tzdb_leap_info [0]> @ 闎  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> # 鄀  std::allocator<unsigned int>  �0  std::_Yarn<wchar_t> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  b  std::wstring 5 譪  std::_Atomic_integral_facade<unsigned __int64> z F�  std::allocator_traits<std::allocator<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > ' V  std::numeric_limits<signed char> � 獲  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �-  std::domain_error  �  std::u32string_view  �  std::_Container_base � M|  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  5  std::_Big_integer_flt 2 樟  std::shared_ptr<donut::app::RegisteredFont>  �  std::_Literal_zero ; w  std::weak_ptr<donut::engine::DescriptorTableManager> $ cP  std::hash<nvrhi::IResource *> 3 爷  std::shared_ptr<donut::engine::TextureCache>  �4  std::from_chars_result � *L  std::_Compressed_pair<std::allocator<std::chrono::time_zone_link>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >,1> " w4  std::_Nontrivial_dummy_type 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char> D 譭  std::_Default_allocator_traits<std::allocator<unsigned int> > 8 �6  std::chrono::duration<int,std::ratio<2629746,1> > i u;  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<3600,1> > > & 28  std::chrono::year_month_weekday  u6  std::chrono::day 9 D-  std::chrono::duration<__int64,std::ratio<1,1000> > j %7  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<86400,1> > > n :=  std::chrono::time_point<std::chrono::gps_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  7  std::chrono::weekday  �,  std::chrono::nanoseconds  �6  std::chrono::year ' 8  std::chrono::year_month_day_last  -  std::chrono::minutes  �6  std::chrono::days y N=  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �,  std::chrono::duration<__int64,std::ratio<1,1000000000> > # L7  std::chrono::weekday_indexed 9 �6  std::chrono::duration<int,std::ratio<31556952,1> >  �6  std::chrono::years , fX  std::chrono::duration_values<__int64>  v,  std::chrono::seconds " �8  std::chrono::time_zone_link 6 �6  std::chrono::duration<int,std::ratio<86400,1> > 3 -  std::chrono::duration<int,std::ratio<60,1> > 6 v,  std::chrono::duration<__int64,std::ratio<1,1> > s �,  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   q[  std::chrono::steady_clock   X  std::chrono::system_clock 6 *-  std::chrono::duration<double,std::ratio<60,1> > + X8  std::chrono::year_month_weekday_last  <  std::chrono::tzdb_list + l<  std::chrono::tzdb_list::_Unique_lock + *<  std::chrono::tzdb_list::_Shared_lock ; �-  std::chrono::duration<double,std::ratio<1,1000000> > > �-  std::chrono::duration<double,std::ratio<1,1000000000> > = \,  std::chrono::duration<__int64,std::ratio<1,10000000> > " �7  std::chrono::month_day_last  %7  std::chrono::sys_days n =  std::chrono::time_point<std::chrono::utc_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  �8  std::chrono::time_zone q L,  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >   `7  std::chrono::weekday_last ! �7  std::chrono::month_weekday 5 �,  std::chrono::duration<int,std::ratio<3600,1> >  i7  std::chrono::last_spec & �7  std::chrono::month_weekday_last  :7  std::chrono::local_days j �8  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,1> > > N SK  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > > Z .>  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::<lambda_1>  �8  std::chrono::sys_seconds  �7  std::chrono::year_month 8 Z-  std::chrono::duration<double,std::ratio<1,1000> >  s7  std::chrono::month_day " �7  std::chrono::year_month_day  �,  std::chrono::hours < q-  std::chrono::duration<__int64,std::ratio<1,1000000> > @ m[  std::chrono::zoned_traits<std::chrono::time_zone const *> 5 �,  std::chrono::duration<double,std::ratio<1,1> >  �6  std::chrono::months  89  std::chrono::tzdb e :7  std::chrono::time_point<std::chrono::local_t,std::chrono::duration<int,std::ratio<86400,1> > >  @Y  std::chrono::gps_clock  =Y  std::chrono::tai_clock  �6  std::chrono::month n "=  std::chrono::time_point<std::chrono::tai_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  dX  std::chrono::utc_clock  h[  std::chrono::local_t & �=  std::chrono::_Time_parse_fields 8 y=  std::chrono::_Time_parse_fields::_Leap_second_rep = w=  std::chrono::_Time_parse_fields::_Parse_tp_or_duration 3 Y=  std::chrono::_Time_parse_fields::_FieldFlags H g[  std::chrono::duration<__int64,std::ratio<1,1000000000000000000> >  �8  std::chrono::leap_second 8 �,  std::chrono::duration<double,std::ratio<3600,1> >  �2  std::ctype_base  EA  std::filesystem::perms ' 繟  std::filesystem::directory_entry $ JA  std::filesystem::copy_options ( 5A  std::filesystem::filesystem_error 7 rR  std::filesystem::_Path_iterator<wchar_t const *> ) �>  std::filesystem::_Find_file_handle & y>  std::filesystem::_Is_slash_oper . 螧  std::filesystem::_Should_recurse_result $   std::filesystem::perm_options 4 滳  std::filesystem::recursive_directory_iterator . �A  std::filesystem::_File_status_and_error & 9B  std::filesystem::_Dir_enum_impl 0 KB  std::filesystem::_Dir_enum_impl::_Creator @ QB  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! TA  std::filesystem::file_type . nB  std::filesystem::_Directory_entry_proxy " 揇  std::filesystem::space_info * 桞  std::filesystem::directory_iterator & N=  std::filesystem::file_time_type 0 虰  std::filesystem::_Recursive_dir_enum_impl ) 鞟  std::filesystem::directory_options # gA  std::filesystem::file_status u 霡  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( U[  std::filesystem::_File_time_clock  ?  std::filesystem::path $ �>  std::filesystem::path::format * ?R  std::filesystem::_Normal_conversion < gU  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �1  std::codecvt<char16_t,char,_Mbstatet> , �2  std::_Codecvt_guard<char32_t,char8_t> � u  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > > >  R[  std::char_traits<char> � 镮  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �.  std::error_category ) �.  std::error_category::_Addr_storage  \~  std::_Wrap<std::mutex> i o:  std::tuple<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! �/  std::_System_error_message  �  std::_Unused_parameter " 瀪  std::hash<nvrhi::IShader *> h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  l4  std::bad_optional_access A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > W   std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > , 確  std::_Atomic_integral<unsigned int,4> u 嵤  std::vector<nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � >�  std::vector<nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::allocator<nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Reallocation_policy 7 !B  std::shared_ptr<std::filesystem::_Dir_enum_impl> �   std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> " DT  std::_Floating_point_string 8 z  std::_Ptr_base<donut::engine::CommonRenderPasses> = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q 1~  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1> 2 �5  std::_Extended_Pictographic_property_values  �1  std::_Codecvt_mode @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > z 爙  std::_Compressed_pair<std::hash<unsigned __int64>,std::_Compressed_pair<std::equal_to<unsigned __int64>,float,1>,1> ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn $ q?  std::ranges::_Iter_move::_Cpo ) o?  std::ranges::_Iter_move::_Cpo::_St % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn    std::_Exact_args_t  P[  std::ratio<86400,1>  V6  std::default_sentinel_t � A  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > >  N[  std::ratio<31556952,1> 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8> {b|  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > >,1>  =K  std::ostringstream � w|  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >  6  std::_Fmt_buffer_traits \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  g0  std::_Facet_base ' �)  std::hash<nvrhi::BindingSetItem> " 甔  std::_WChar_traits<wchar_t> 2 �2  std::codecvt<unsigned short,char,_Mbstatet> . �=  std::_Optional_destruct_base<__int64,1> - �=  std::_Optional_construct_base<__int64> # �/  std::_Generic_error_category  扟  std::streampos  B[  std::input_iterator_tag 2 JU  std::_Wrap<std::filesystem::_Dir_enum_impl> X 孲  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> 0 芻  std::_Atomic_integral<unsigned __int64,8> ^ �  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > , 塪  std::_Atomic_padded<unsigned __int64> ' 綪  std::hash<enum nvrhi::ColorMask> / ,2  std::codecvt<char16_t,char8_t,_Mbstatet> 5 
`  std::_Locked_pointer<std::_Stop_callback_base> 3 豃  std::basic_ios<char,std::char_traits<char> >  �1  std::codecvt_base & 譒  std::_Fmt_counting_buffer<char> �   std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > *>  錎  std::bad_function_call O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag ' P~  std::_Ref_count_obj2<std::mutex> v 鮸  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> � 5�  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > > > > ; 賓  std::allocator_traits<std::allocator<unsigned int> > ' 蜠  std::hash<std::filesystem::path> 	r}  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > >,1> � L}  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned __int64 const ,nvrhi::RefCountPtr<nvrhi::IBindingSet> > > > > _ ?[  std::allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > > 7 �<  std::_Atomic_storage<std::chrono::tzdb_list *,8>  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > l ��  std::_Uhash_compare<nvrhi::ITexture *,std::hash<nvrhi::ITexture *>,std::equal_to<nvrhi::ITexture *> > 7 40  std::_Tzdb_deleter<__std_tzdb_current_zone_info> " �  std::_Asan_aligned_pointers �   std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m 剉  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � Sv  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  q5  std::format_error  �  std::partial_ordering # |5  std::_Decode_result<wchar_t> � ,�  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::engine::TextureData> > > . <#  std::array<nvrhi::BindingLayoutItem,16>  9[  std::_Default_sentinel P 耎  std::_Default_allocator_traits<std::allocator<std::chrono::leap_second> > $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int>   �5  std::_GB11_LeftHand_regex * �5  std::_GB11_LeftHand_regex::_State_t 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access 
 !   wint_t  t   ImDrawFlags  �  ImGuiStorage % 婕  ImGuiStorage::ImGuiStoragePair  !   ImWchar  祌  CACLIPDATA # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling $ $�  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets . d�  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  &  nvrhi::GraphicsState * DH  nvrhi::RefCountPtr<nvrhi::ISampler> / W  nvrhi::static_vector<nvrhi::Viewport,16>  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray ) 艷  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! �(  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray . 鮞  nvrhi::RefCountPtr<nvrhi::ICommandList>  艷  nvrhi::DeviceHandle   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture ! 蒪  nvrhi::utils::ScopedMarker $ =b  nvrhi::utils::BitSetAllocator . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  DH  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # {w  nvrhi::DescriptorTableHandle  �(  nvrhi::TimerQueryHandle 2 {w  nvrhi::RefCountPtr<nvrhi::IDescriptorTable>   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �(  nvrhi::HeapHandle # �(  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> - H  nvrhi::RefCountPtr<nvrhi::IBindingSet> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  d�  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  賧  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  lH  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 $�  nvrhi::RefCountPtr<nvrhi::IGraphicsPipeline> 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 賧  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  H  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 繸  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   �(  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �(  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # 齺  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  鮞  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable * lH  nvrhi::RefCountPtr<nvrhi::ITexture>  �&  nvrhi::ComputeState 2 齺  nvrhi::RefCountPtr<nvrhi::rt::IAccelStruct>  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery  硆  VARDESC & 枥  ImVector<ImFontAtlasCustomRect>     LONG  皉  ITypeLib  $r  tagCACY  塺  tagBSTRBLOB  噐  tagCAUH  8g  _TP_CALLBACK_ENVIRON_V3 0 Bg  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u> B Og  _TP_CALLBACK_ENVIRON_V3::<unnamed-type-u>::<unnamed-type-s>  "r  _ULARGE_INTEGER ( 剅  _ULARGE_INTEGER::<unnamed-type-u>  �/  __std_win_error  S0  __std_tzdb_leap_info  辡  LPVARIANT  泀  SAFEARRAY  �0  lconv    D3D_SRV_DIMENSION  乺  tagCABOOL   �  __RTTIBaseClassDescriptor  
�  D3D12_SHADER_CACHE_MODE  @�  ImVector<float>  r  tagBLOB 
 乺  CABOOL   ]�  D3D12_ROOT_PARAMETER_TYPE 
    _off_t  #   ULONG64 
 �  SNB  8q  _tagINTERNETFEATURELIST  �r  CABSTRBLOB 
 #   SIZE_T  }r  tagTYPEATTR    stat  t   ImFontAtlasFlags  智  ImGuiComboFlags_  t   int32_t  Z  timespec & JZ  $_TypeDescriptor$_extraBytes_37  cD  __std_fs_file_id 
 !   _ino_t 
 沘  _Cnd_t  奚  ImGuiTabBarFlags_  A   DATE # yr  ReplacesCorHdrNumericDefines  榞  FS_BPIO_OUTFLAGS  郧  ImGuiColorEditFlags_  "   DWORD  t   ImGuiSliderFlags # 70  __std_tzdb_current_zone_info  0g  PTP_CALLBACK_INSTANCE ' ID  __std_fs_create_directory_result 
   PSHORT    D3D12_MESSAGE_ID  "   TP_VERSION  q  BSTR  Q�  D3D_DRIVER_TYPE  !   uint16_t  t   ImGuiViewportFlags  �/  __std_fs_stats  旨  ImVector<char>  譹  CAUB  sr  ITypeInfo $ 僃  donut::engine::ICompositeView    donut::engine::IView ( qz  donut::engine::CommonRenderPasses 5 眝  donut::engine::CommonRenderPasses::PsoCacheKey ; 箆  donut::engine::CommonRenderPasses::PsoCacheKey::Hash " $x  donut::engine::TextureCache , =x  donut::engine::TextureCache::Iterator $ 謥  donut::engine::BlitParameters   蜦  donut::engine::PlanarView ( Cu  donut::engine::FramebufferFactory ! 焪  donut::engine::BufferGroup ! 﨑  donut::engine::ShaderMacro  鈝  donut::engine::MeshInfo  褀  donut::engine::MeshType " +t  donut::engine::BindingCache # 劕  donut::engine::LoadedTexture & 輛  donut::engine::DescriptorHandle , Ow  donut::engine::DescriptorTableManager B w  donut::engine::DescriptorTableManager::BindingSetItemsEqual B w  donut::engine::DescriptorTableManager::BindingSetItemHasher % 墂  donut::engine::VertexAttribute % t   donut::engine::DescriptorIndex &   donut::app::StreamlineInterface 6 苋  donut::app::StreamlineInterface::DLSSRRSettings 5 厝  donut::app::StreamlineInterface::DLSSRROptions A 內  donut::app::StreamlineInterface::DLSSRRNormalRoughnessMode 4 伻  donut::app::StreamlineInterface::DLSSRRPreset 2 匀  donut::app::StreamlineInterface::DLSSGState 3 w�  donut::app::StreamlineInterface::DLSSGStatus 4 腥  donut::app::StreamlineInterface::DLSSGOptions A t�  donut::app::StreamlineInterface::DLSSGQueueParallelismMode 2 r�  donut::app::StreamlineInterface::DLSSGFlags 1 p�  donut::app::StreamlineInterface::DLSSGMode 3 倘  donut::app::StreamlineInterface::ReflexState 4 侨  donut::app::StreamlineInterface::ReflexReport 5 萌  donut::app::StreamlineInterface::ReflexOptions 2 c�  donut::app::StreamlineInterface::ReflexMode 6 咳  donut::app::StreamlineInterface::DeepDVCOptions 3 Z�  donut::app::StreamlineInterface::DeepDVCMode 2 蝗  donut::app::StreamlineInterface::NISOptions . S�  donut::app::StreamlineInterface::NISHDR / Q�  donut::app::StreamlineInterface::NISMode 4 啡  donut::app::StreamlineInterface::DLSSSettings 3 橙  donut::app::StreamlineInterface::DLSSOptions 2 A�  donut::app::StreamlineInterface::DLSSPreset 0 ?�  donut::app::StreamlineInterface::DLSSMode 1   donut::app::StreamlineInterface::Constants .   donut::app::StreamlineInterface::Extent  祷  donut::app::IRenderPass   吇  donut::app::DeviceManager 3 3�  donut::app::DeviceManager::PipelineCallbacks + 敾  donut::app::DeviceCreationParameters % 幓  donut::app::InstanceParameters ! ┝  donut::app::ImGui_Renderer ! z�  donut::app::RegisteredFont  WI  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3  EG  donut::math::float2 # 錏  donut::math::vector<float,3> * 謾  donut::math::vector<unsigned int,3>  u   donut::math::uint  /F  donut::math::plane # F  donut::math::vector<float,4>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes % 慫  donut::math::matrix<float,3,4>  (G  donut::math::uint2  F  donut::math::float4 % WI  donut::math::matrix<float,4,4> # 揈  donut::math::affine<float,3>   z{  donut::math::box<float,2>   4I  donut::math::box<float,3> " *E  donut::math::vector<bool,2>  4I  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> * (G  donut::math::vector<unsigned int,2>  踘  tagPROPVARIANT  &r  CAUL M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>  $r  CACY    _Mbstatet  "r  ULARGE_INTEGER   �  ImGuiButtonFlags_  6g  TP_CALLBACK_PRIORITY  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ! )0  __std_tzdb_time_zones_info  @q  VARENUM     intmax_t  蛁  tagCASCODE # 烨  D3D_TESSELLATOR_PARTITIONING    AtmosphereParameters  Q�  ImGuiViewport    terminate_handler  �  _s__RTTIBaseClassArray  ,q  tagCACLSID  Ug  MACHINE_ATTRIBUTES & VZ  $_TypeDescriptor$_extraBytes_52  C�  D3D_RESOURCE_RETURN_TYPE  x�  ImFontAtlas 
 Y  ldiv_t 0 u�  ImVector<ImGuiTextFilter::ImGuiTextRange>  r  tagCALPWSTR  �/  __std_fs_file_flags  �0  _Cvtvec  !   ImDrawIdx  r  BLOB  #   DWORD64  u   _Thrd_id_t  t   ImDrawListFlags  !   PROPVAR_PAD1 - �  $_s__RTTIBaseClassArray$_extraBytes_24  3g  PTP_SIMPLE_CALLBACK  �  D3D12_MESSAGE_CATEGORY 
 t   INT  �  _CatchableTypeArray  r  IStorage  [�  ImGuiPlatformImeData  鋑  tagVARIANT 
 蟩  tagCAI 
 A   DOUBLE      UCHAR  �  ImGuiPayload   �  _D3D_SHADER_CBUFFER_FLAGS  "   LCID      BOOLEAN  &g  PTP_CALLBACK_ENVIRON  �/  __std_fs_copy_options     ptrdiff_t  緌  tagTYSPEC  籫  LPVERSIONEDSTREAM  
  _stat64i32  ?�  D3D12_LOGIC_OP  醧  tagDISPPARAMS  E0  __std_tzdb_sys_info  嚱  ImDrawCmd 
 !   USHORT  �  _PMD  �  ImVector<ImVec4>      uint8_t  鑗  LPUWSTR    ImVector<unsigned short>  0q  tagVARKIND & 衼  $_TypeDescriptor$_extraBytes_41  ,  type_info  �  ImFontGlyph    PVOID  遯  SAFEARRAYBOUND ' �  _s__RTTIClassHierarchyDescriptor  Yq  IUnknown  t   errno_t  q   WCHAR     PBYTE  _�  D3D_TESSELLATOR_DOMAIN  t   ImGuiWindowFlags  �/  __std_fs_reparse_tag  単  _DEVICE_DSM_DEFINITION 
 苢  tagCAC  譹  tagCAUB  \  _lldiv_t 
 蚮  IID 
 栈  ImVec4 ! 芮  _D3D_SHADER_VARIABLE_FLAGS  �  ImGuiCol_  :q  _tagQUERYOPTION  嗲  ImGuiWindowFlags_  q  LPOLESTR  E�  D3D_PRIMITIVE  �  tagExtentMode  萹  __MIDL_IUri_0002     HRESULT  =�  _D3D_SHADER_INPUT_TYPE  C  __std_type_info_data 
 蟩  CAI  zg  PDEVICE_DSM_INPUT & �  $_TypeDescriptor$_extraBytes_27  淝  ImDrawFlags_  蛁  CASCODE  G  _s__ThrowInfo  c6  __std_fs_convert_result /   D3D12_SHARED_RESOURCE_COMPATIBILITY_TIER ! 蘱  __MIDL_IGetBindHandle_0001  �/  __std_fs_stats_flags  妐  tagCY 
    LONG64  |�  ImVector<ImDrawCmd>  <q  tagCOINITBASE  頶  LPCUWSTR  "   ULONG  �  __RTTIBaseClassArray ! �  D3D12_STATE_SUBOBJECT_TYPE  !   VARTYPE  t   BOOL 
 苢  CAC / F�  ImVector<ImGuiStorage::ImGuiStoragePair>  �  __crt_locale_data_public  膓  tagApplicationType  �  ImFontGlyphRangesBuilder 0 廹  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES_OUTPUT  摽  ImDrawVert  �  LPCWSTR & 竡  DISPLAYCONFIG_SCANLINE_ORDERING - �  $_s__CatchableTypeArray$_extraBytes_24  @   FLOAT  !   PROPVAR_PAD3  &q  tagDOMNodeType  聁  CAUI  纐  tagCLIPDATA  �  ImGuiSelectableFlags_  Ya  _Mtx_internal_imp_t  泀  tagSAFEARRAY & 4Z  $_TypeDescriptor$_extraBytes_25  "   DEVICE_DSM_ACTION % �  __RTTIClassHierarchyDescriptor  �0  _Collvec   艭  __std_fs_volume_name_kind  紂  tagVersionedStream 0 �  __vcrt_va_list_is_reference<char const *> 
 簈  CABSTR     __time64_t  2q  tagCHANGEKIND 
    fpos_t 
 u   UINT32  �  FILE  宷  tagSYSKIND  憧  ImVector<ImDrawList *>  u   ImGuiID 
 Ma  _Mtx_t 3 �  __vcrt_va_list_is_reference<wchar_t const *>  秖  IDispatch  蚮  CLSID    mbstate_t  ?  _PMFN  #   uintptr_t 
 q  LPWSTR  踘  PROPVARIANT  絞  LPSAFEARRAY  #   UINT_PTR  谇  ImGuiTableColumnFlags_  (g  PTP_POOL  �  _s__CatchableTypeArray   榛  ImGuiTableColumnSortSpecs  DD  __std_fs_remove_result  蚮  GUID * #g  _CUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG '   D3D_TESSELLATOR_OUTPUT_PRIMITIVE # 銮  D3D12_INDIRECT_ARGUMENT_TYPE  厍  D3D12_COMMAND_LIST_TYPE  8g  TP_CALLBACK_ENVIRON_V3  6q  tagFUNCKIND  u   ImU32  钋  ImGuiNavInput  効  ImDrawCmdHeader  梣  LARGE_INTEGER 
 攓  CAH  t   ImGuiChildFlags  t   INT32  憅  tagCAFILETIME 
   HANDLE  昵  D3D12_LIFETIME_STATE  "q  PIDMSI_STATUS_VALUE  枨  _D3D_CBUFFER_TYPE  #   ULONGLONG    ImTextureID  巕  tagCAPROPVARIANT ( ,g  PTP_CLEANUP_GROUP_CANCEL_CALLBACK  7�  SampleProceduralSky  t   ImGuiSortDirection 	 妐  CY  靈  _Thrd_t  坬  FILETIME  g  PDEVICE_DSM_RANGE ( 耷  D3D12_DEBUG_DEVICE_PARAMETER_TYPE - �  $_s__RTTIBaseClassArray$_extraBytes_16  唓  __MIDL_IUri_0001    ImDrawData 
 済  REGCLS  仪  ImVector<ImFontGlyph> - /Z  $_s__RTTIBaseClassArray$_extraBytes_32  u   DXGI_USAGE  剄  IRecordInfo  t   ImGuiInputTextFlags 
 #   size_t  峠  PDEVICE_DSM_OUTPUT 
    time_t  �/  __std_fs_file_attr     LONGLONG   溓  D3D12_MEASUREMENTS_ACTION  �  __std_exception_data * O�  D3D12_PIPELINE_STATE_SUBOBJECT_TYPE 
 u   _dev_t  紺  __std_ulong_and_error ) |g  _DEVICE_MANAGE_DATA_SET_ATTRIBUTES  毲  ImGuiTableFlags_  Dq  tagGLOBALOPT_EH_VALUES 
 然  ImVec2 w p�  GenericScope<`SampleProceduralSky::DebugGUI'::`2'::<lambda_1>,`SampleProceduralSky::DebugGUI'::`2'::<lambda_2> > * !g  PCUSTOM_SYSTEM_EVENT_TRIGGER_CONFIG  :�  ImGuiTextFilter & 伡  ImGuiTextFilter::ImGuiTextRange  A   __std_tzdb_epoch_milli  \  lldiv_t     SHORT  S�  ImGuiListClipper    PLONG64  Y  _ldiv_t  爂  COWAIT_FLAGS     SCODE  >q  tagCLSCTX  斍  ImGuiPopupFlags_  俳  ImVector<ImDrawChannel>  [  _timespec64     intptr_t     INT_PTR  S�  _D3D_SHADER_INPUT_FLAGS  捛  ImVector<ImFontConfig>  u   uint32_t  4q  tagXMLEMEM_TYPE " Y�  D3D_REGISTER_COMPONENT_TYPE 
 �  _iobuf 
 .q  CADATE !   ImGuiInputTextCallbackData  p   CHAR  ,q  CACLSID  !   PROPVAR_PAD2  *q  _tagPARSEACTION  I�  D3D12_MESSAGE_SEVERITY + G�  D3D12_RENDER_PASS_ENDING_ACCESS_TYPE  p  LPSTR  K�  ImVector<void *>  (q  tagDESCKIND  �  __crt_locale_pointers 
  q  tagCAL  #   DWORDLONG  �   P5      �(M↙溋�
q�2,緀!蝺屦碄F觡  M    吿韕(�座|戼嫋俒F舾�8j�/*j浱継�  �    G�膢刉^O郀�/耦��萁n!鮋W VS  �    *u\{┞稦�3壅阱\繺ěk�6U�      o�椨�4梠"愜��
}z�$ )鰭荅珽X  h   璯�;懳繌Π羮T?堥鮶魔�
肒櫤襄韽  �   |?e煒绸t�1K諥X=d�3郃沠#+Od厨[  �   ㄠ� xK:冼碙a賙X舠f膕�,ú闿郮w磒  H   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  �   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   b痙�o'丿鍰$fx$CW\+嬥鼊�%叩B�     P�6q�鏻L筳N騒隴9d/櫌Y�%_綷�  [   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   潝(綊r�*9�6}颞7V竅\剫�8値�#  ?   窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  �   �l{`G2
`0m襭皗犿鷕g貋⒌joゥ  �   ^憖�眜蘓�y冊日/缁ta铁6殔  *   魯f�u覬n\��zx騖笹笾骊q*砎�,�  r    萾箒�$.潆�j閖i转pf-�稃陞��  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮     Pフ+
V
*戋zydD躩=壠�jA�N盪{u5  V   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  �   跻]�	隗[鼀hF鰘廰)鋥Le6�攬R7     iu�	��&? 蔬釥�#簤Ηu-塘b@遍迭  e   ]嘹愂�\)�&6辛
*~�学&Y佀董肝�&  �   仫減f╋萩}_遫R衵9弧0Up*q蹩鎍,  �   溶�$椉�
悇� 騐`菚y�0O腖悘T  E   �!+趠艘k掤	嘟z{ 鹤N.髐露媜/  �   峷_�@浦J	裺�'o锕j-�;Z瞣%G3鶛  �   f扥�,攇(�
}2�祛浧&Y�6橵�  	   �芮�>5�+鮆"�>fw瘛h�=^���  k	   [届T藎秏1潴�藠?鄧j穊亘^a  �	   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �	   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  
   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  Z
   �=A�%K鹹圛19振╯鵽C殾錦`蔣  �
   �	玮媔=zY沚�c簐P`尚足,\�>:O  �
   仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说     哳訵a醴泉撬4wO�)鹁疜�)誊)�=跹�  d   <峰隁謠]匔唗~朽k髭妖哹�5麦
T  �   +4[(広
倬禼�溞K^洞齹誇*f�5      !m�#~6蠗4璟飜陷]�絨案翈T3骮�  B    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  �   �3掍S\絧飦戀轝
5鍄0d
妺XE��
  �   閯�価=�<酛皾u漑O�髦jx`-�4睲�  
   a�傌�抣?�g]}拃洘銌刬H-髛&╟  S
   �="V�A�D熈fó 喦坭7b曉叼o1  �
   饵嶝{郀�穮炗
AD2峵濝k鴖N  �
   双:Pj �>[�.ぷ�<齠cUt5'蠙砥     �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  o   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  �   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�      п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  X   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  �   妇舠幸佦郒]泙茸餈u)	�位剎  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�  )   r�L剟FsS鏴醼+E千I呯贄0鬬/�  t   靋!揕�H|}��婡欏B箜围紑^@�銵  �   仐嚫+'連Gwa錬貎襅.侕*岋N纆肏     �颠喲津,嗆y�%\峤'找_廔�Z+�  N   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   t�j噾捴忊��
敟秊�
渷lH�#  �   q捉v�z�1乱#嗋�搇闚Q缨外
毛�     �,〓�婆谫K7涄D�
Cf�
X9U▏TG  J   �-�雧n�5L屯�:I硾�鮎访~(梱  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   ��8/�
0躚/﨤h盙裉餠G怤爛��]�     �"睱建Bi圀対隤v��cB�'窘�n  h   猿�
1傽ゑ净iN2$胓z倝�0痙O懤袨P�  �   澏泼寊H"鱄LUHFy]厽I媢h�J赫Cp   �   |t逷/蛾9栕渗ⅱ`洉庚磘@鲏倌�  2   葱z!幠<y5�1盿F�=)�;褣�)Yb膷W  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   _%1糠7硘籺蚻q5饶昈v纪嗈�     �:2K] �
j�苊赁e�
湿�3k椨�  ^   樸7 忁�珨��3]"Fキ�:�,郩�  �   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �   D���0�郋鬔G5啚髡J竆)俻w��  .   犢x琿儳莻k鬿锘HC閷�!(/捱&eM殙�  v    瀥�	qtY糙-l嗏|澬+顪�!so�$&�  �   掴'圭,@H4sS裬�!泉:莠й�"fE)     <瑓�傻a鱼张隥T$ vJ黠I鯝�0  \   �茬� 霚FmH暃蜽.駢宫誖�6?靵oP  �   U恂{榸冾�fⅢ��Hb釃"�6e`a  �   
捃閺嚞?� 龀�*�煾/踈0�R璷�  >   窌暈�'q鹏�I�
V5<鵆濱f|92圭K	�  �   K
;�8戢嵣軥勘	觜に沂瘨�:L�
庈�:  �   嵮楖"qa�$棛獧矇oPc续忴2#
  %   僘u鹋� !敒99DK汜簯�叮瀒蛂  `   )�Q�Ａ9氫�&煝]R埬�楜� 磛庋p�  �   �)D舼PS橼鈝{#2{r�#獷欲3x(  �   W躊��:(蚏濠迤鵢僛L生N!g`璣{  2   �fE液}髢V壥~�?"浬�^PEΡ4L�  x   嶹栢ABZC凂U久Gk�!貟~龡单癉Q  �   K�:荳)a懃J�拌� ,寨吙u⑺�     詄�9LTU�梀黂[&瓬0櫽鲑C墅P  U   葦鮆培XDW駐鰄�W{�+ノ2$<F$帰B  �   t$�>喰k柸窣鄾埉笺%撙>2 pg�,  �   �9俅�(� .]嬝暧烵啤�秾*~w�=[@�  (   蜅�萷l�/费�	廵崹
T,W�&連芿  e   8�'预P�憖�0R�(3銖� pN*�  �   ;覿�叞)J��5賲�蓅<`顚ㄎ搢樦�  �   sL&%�znOdz垗�M,�:吶1B滖  G   c�#�'�縌殹龇D兺f�$x�;]糺z�  �   �7謅�:J钧{啟t,岘]祙馗X�缁�娟�  �   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  7   渐袿.@=4L笴速婒m瑜;_琲M %q�  �   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   }q粪Q膳.螙羚忕櫥�绮 藟鑸{|�瘠     弔釺塦敚圢iu�$矷8FQ@e魙&f逽完E戈  g   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  �   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  �   谢疲�%�>\宧�8奕U瞷>肔滰J書愝jS  /    RX鰷稐蒋駏U	�>�5妆癫�
8A/  ~    �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �    �>2
^�﨟2W酟傲X{b?荼猲�;  �    k�8.s��鉁�-[粽I*1O鲠-8H� U  =!   +YE擋%1r+套捑@鸋MT61' p廝 飨�  ~!   h覄� �3(/瞖�乫虻躟蛕&踖$PゴMD�  �!   交�,�;+愱`�3p炛秓ee td�	^,  
"   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  `"   熄鉨m�h櫁攸諿]硷嵿澴滂乿滺裡�  �"   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  �"   _臒~I��歌�0蘏嘺QU5<蝪祰S  %#   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  ]#   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  �#   b骺_�(4参♁� N�z陾Ia癓t�&醇  �#   '怤�	8t�ぱ鏏d奧$凛觢�0"$�&  3$   .�-髳�o2o~翵4D�8鷗a殔氰3籃G  �$   廐*ψ繉c佧>iV�1!{�0�8琑9Q疩�2$  �$   憒峦锴摦懣苍劇o刦澬z�/s▄![�  %   v-�+鑟臻U裦@驍�0屽锯
砝簠@  I%   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s  �%   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  �%   丩{F*}皦N誫l雘啫椊�梮,圶`�  &   �n儹`
舔�	Y氀�:b
#p:  i&   "�挨	b�'+舒�5<O�呱_歲+/�P�?  �&   
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �&   郖�Χ葦'S詍7,U若眤�M进`  B'   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  '   ��?	檂O6巡贐Z儌樨9�4w�駚(�吶  �'   6觏v畿S倂9紵"�%��;_%z︹  (   屃鞕[電]ONf)2啠協p鵁厛�/仚�螠  X(   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �(   猯�諽!~�:gn菾�]騈购����'  �(   嚵c
縸捻yx蚰郜�黾鵏笷鈝藃Pw訠   ,)   dhl12� 蒑�3L� q酺試\垉R^{i�  k)   悯R痱v 瓩愿碀"禰J5�>xF痧  �)   矨�陘�2{WV�y紥*f�u龘��  �)   o藾錚\F鄦泭|嚎醖b&惰�_槮  >*   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �*   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  �*   龀缬@唶�鱮�瓗禽鐑�^]>Xz豹tN  +   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  r+   衠琪槡铟钭}_XO>�蛭X�7Mp处d  �+   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  ,   �.讜+�8W(M嗙6[屌昛\�鍉柶遊�  X,   �~鴧傳.P怬WsP-"焫#N�:�&場璁  �,   �fwv鋽砻毆�經�⒂k秼芴襚扉w  �,   伙a蕑堐v!@曓�+梭�'驂蒀飫鴐  5-   �涱庡藩侁鳉�摻0v鑮;��?$楴湑a  ~-   zY{���睃R焤�0聃
扨-瘜}  �-   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �-   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  <.   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  |.   D,y邥鞃黎v)�8%遾1�*8赩�婯�  �.   駒og暼硚~鮼_5屏�8BL.9妖"�r/�-R  /   冁�4G瓟�vsU仡縪�鷧)怣�笰5�  S/   ]a卸sN饍,0H 栊?覞$缺�踌惴*�0  �/   5睔`&N_鏃|�<�$�獖�!銸]}"  �/   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  %0   ┫緞A$窄�0� NG�%+�*�
!7�=b  t0   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  �0   镽A蔳b咡卵玵繑蘵婳耘(辤[俋Y�  1   険L韱#�簀O闚样�4莿Y丳堟3捜狰  E1   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �1   �
跸閙cJA顨梩�qe鴈l唔]�;翷娵  �1   I�(槾+蟂屮鹵孥]?鹺�9NP�3Q鄠MJ婤  )2   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  k2   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �2   頒牛/�	� G犨韈圂J�.山o楾鐴  �2   Yt'".�/骜h婇�&-教嗹�
惋�4ǎ鮩D  E3   喲8s7痺撆�&!林qA>ブ�Y~a璳`N3�  �3   j簐昊靊~谯カ)0M�/綦潯e齺�	\�@Z  �3    狾闘�	C縟�&9N�┲蘻c蟝2  $4   摛!躚粻〣嬁�6
H(偅�鱠艘:
Qx  Z4   qAp�6敁p銋�,c .諵輕底髫L灇	9�  �4   ��(`.巑QEo"焷�"娧汝l毮89fб�  �4   滣獒傻\|5�Z樸b�跶3	珳鬹b摆蜝  A5   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �5   彲 ㄋ牐媝e哟�<N7%�Yn�52�峟�  �5   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  6   �8��/X昋旒�.胱#h=J"髈篒go#  O6   欭侚FAP鮥c没曺;億"B7驺櫒^ 隉9  �6   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �6   チ畴�
�&u?�#寷K�資 +限^塌>�j  7   a: 覩徿舻壼紾甉� l(候鼱\Q?m�  :7   |�8�>�$帻;鉁@轠VP｀鴖Vf*w郗g  �7   �儔14褥緅�3]饃鹷�hK3g搋bA竑  �7   綔)\�谑U⒊磒'�!W磼B0锶!;  8   � 罟)M�:J榊?纸i�6R�CS�7膧俇  p8   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �8   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �8   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  89   觑v�#je<d鼋^r
u��闑鯙珢�  c9   ct冝�-=@蒮柃╉#奃i��嫭|h^襻�  �9   E縄�7�g虩狱呂�/y蛨惏l斋�笵  �9   V� c鯐鄥杕me綻呥EG磷扂浝W)  I:   穫農�.伆l'h��37x,��
fO��  �:   5�\營	6}朖晧�-w氌rJ籠騳榈  �:   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-  	;   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  L;   泭盨p榩,^藎�髈V尦�懰?v��`  �;   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  �;   �暊M茀嚆{�嬦0亊2�;i[C�/a\  �;   p�& ;Tt<秆�0鎹盾麷呖#56眆婧�  K<   �呾��+h7晃O枖��*谵|羓嗡捬  �<   �*o驑瓂a�(施眗9歐湬

�  �<   蕰уt厅隍2GeE升QU"柬�)忲嗅{≌洮  =    栀��綔&@�.�)�C�磍萘k  Z=   d2軇L沼vK凔J!女計j儨杹3膦���  �=   �0�*е彗9釗獳+U叅[4椪 P"��  �=    I嘛襨签.濟;剕��7啧�)煇9触�.  >   t	*=Pr,�8qQ镯椅鯘�=咽Bz  M>    
!�G箊戲鈮懧轌[蠅Uま=	悽� f  �>   �=蔑藏鄌�
艼�(YWg懀猊	*)  �>   B�睃娏�,棒繪�1舸麂筓1�0m蘉##  ?   2W瓓�<X	綧]�龐IE?'笼t唰��  Y?   x)憂^恛閶{籮>鐼末BA<颭~儽&'%;-g�  �?   L�9[皫zS�6;厝�楿绷]!��t  �?   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  !@   9庺�'z踃�^�3柢鐃E"簬槤怤摍%Lq  j@   .QVBL籺'Z鬖b濁蟂�8w,/kr彨�$擠�  瑻   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  頏   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  >A   齝D屜u�偫[篔聤>橷�6酀嘧0稈  |A   F?糧#▎.Wi�/0��=搐	潼�9*W�-�  葾   _O縋[HU-銌�鼪根�鲋薺篮�j��  B   �}頜"蕜�1づ_2釕嵢eG揅塲鞖禨頠  dB   l籴靈LN~噾2u�< 嵓9z0iv&jザ  禕   �F9�6K�v�/亅S诵]t婻F廤2惶I  C   襋钡�/翤㏄^觜�藔膛vЮ7��37嵇  RC   莏弗c2>氨濐K谘坯]�<-魵"憌f[?A  滳   ii(祍P%舌+;�'縿E╪嫏脈3L籾
�  酑   馒{蓱蕩L蘯e�LW�疗譄幗IR:;a�  1D   ﹪冓�嗈�1蓌�)eF�;珉5� �橌5偧  xD   W簅r�hc鷟;9攫�.�6Y柦阁��}.J╗  翫   錵s铿勃砓b棬偡遯鮓尛�9泂惻  
E   �'稌� 变邯D)\欅)	@'1:A:熾/�  VE   $G\|R_熖泤煡4勄颧绖�?(�~�:     掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  酔   �7穲碶⒖鍉鸻�:怉婤莞b=竱�  *F   �2梉�7伆鼂:E囂!2Wｔ孵>羑�輡  uF   N�惨NE吓覾宄H3禟4龤9晐f譛	鲇根r  篎   擐�0阅累~-�X澐媆P 舋gD�  �F   	{Z�范�F�m猉	痹缠!囃ZtK�T�  >G   鋢av*KQ湂檡�~h3Us�<厏TQr皶湃)  咷   鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  臛   N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  H   嫎V濺蕠&kZ峠l甩u8蝬QW蹚z`k
+  LH   沶�烦/珀k<鏾|&頂f�$a鹮pz骡攍\  橦   曀"�H枩U传嫘�"繹q�>窃�8  豀   飂/穆耖�?�?2鞁?緒瘐I}iv&�  I   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  PI   蓏翰bsF讍E熑j]�0萛b*xs*q翻惲�  桰   �5�-�塎To>我c撙侹�迫�蛷铕.~�!  釯   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  ,J   窤8c樝B�'u糷飹�>惞Z� 
�#鳚瑝�  bJ   繃S,;fi@`騂廩k叉c.2狇x佚�  獼   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  霬   `k�"�1�^�`�d�.	*貎e挖芺
脑�  .K   +FK茂c�G1灈�7ほ��F�鳺彷餃�  _K   孆x�0队<堛�猬dh梧`sR顛	k�7[M@  睰   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  霮   譫鰿3鳪v鐇�6瘻x侃�h�3&�  *L   �
bH<j峪w�/&d[荨?躹耯=�  iL   bRè1�5捘:.z錨{娯啹}坬麺P  禠   帙瀇CBc%菘JmN#)庬�颕韑暛濎�
�  M   6��7@L�.�梗�4�檕�!Q戸�$�  NM   ,奍2 [MA�:�纟]Z3pDQ�2N﹏��  昅   �<�?Y�2P洪堠濶M�:;x[鳲��:巪e  譓   j轲P[塵5m榤g摏癭 鋍1O骺�*�   N   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  rN   +椬恡�
	#G許�/G候Mc�蜀煟-  睳   (鄁盯J錭澥A��/�!c� ;b卹  﨨   鐳莚lBsm鱀E砪Q�鉀填}W� 弚�ぢ睉  OO   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  汷   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  贠   FLHEBV�d巜阌鮳丧� S羫籸�"剾Jso  &P   覽s鴧罪}�'v,�*!�
9E汲褑g;  sP   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  繮   鹴y�	宯N卮洗袾uG6E灊搠d�  Q   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  XQ   罽Y隿*膀}+�;hs�(s秸f恑@潛n鶕鼙�     G髼*悭�2睆�侻皣軁舃裄樘珱)  騋   副謐�斦=犻媨铩0
龉�3曃譹5D   4R   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  tR   狅^ｕ騒zΩO度E澄唈	�
X橅0r牅  絉   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  鶵   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  BS   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  孲   豊+�丟uJo6粑'@棚荶v�g毩笨C  蟂   禿辎31�;添谞擎�.H闄(岃黜��  T   戹�j-�99檽=�8熈讠鳖铮�  aT   娖转湖[:^X螅pr@俣趰�x紤W蕦犗�  琓   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  齌   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  =U   ��嵉氒髅嘁棭够*ヅ�
�'徺p4  �U   揾1鵿寳%�.u9堍'"軐V蛣b囬E啒  裊   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  V      jV   ,�<鈬獿鍢憁�g$��8`�"�  禫   匐衏�$=�"�3�a旬SY�
乢�骣�   W   傠3UX�<頹%O磳me鸘 涒雋xW5叒z迦  GW   �-考X韵歎K�fUi2�'跎Zq@锍�2aJ�  慦   8蟴B或绢溵9"C dD揭鞧Vm5TB�  軼   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  (X   鹰杩@坓!)IE搒�;puY�'i憷n!  pX   Eム聂�
C�?潗'{胿D'x劵;釱�  腦   聤�苮g8鄞<aZ�%4)闪�|袉uh�  Y   0T砞獃钎藰�0逪喌I窐G(崹�  XY   =J�(o�'k螓4o奇缃�
黓睆=呄k_  擸   渦]k鸦
\4曫r裙NJhHTu6'餍\燪  萗   檅鋲�1o婈$�;�芯厁%rP�衃K設  Z   <AE#(�/y鍹籲贚痶蹒-	蒸|邨VWTR  LZ   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  奪   蠯3
掽K謈 � l�6襕鞜��H#�  荶   煋�	y鋵@$5х葑愔*濋>�( 懪銳  [   �X�& 嗗�鹄-53腱mN�<杴媽1魫  L[   齛|)3h�2%籨糜/N_燿C虺r_�9仌  榌   v峞M� {�:稚�闙蛂龣 �]<��  轠   鏀q�N�&}
;霂�#�0ncP抝  \   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  j\   揾配饬`vM|�%
犕�哝煹懿鏈椸  猏   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  醆   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  ,]   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  r]   謷蘋愦	豢]麸Md�觙 G澿f1e
T奞~  繻   $^IXV嫓進OI蔁
�;T6T@佮m琦�  鱙   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �         �  �-  �  �     B   �     H   �     Y   �     �   b  x   �  ]  (-     d  �    l  X%    p     ;   r  ((  U   s  ((  �   �  x   �  �  x   �  �  �$  h   �  �  c   �  �  �   �  �  �   �  �  
  �  �  4    �  �  
  �    N    2   �  P   K             �      �  F    �  N     �  �  x  f   �  x  g   �  �  �       �  4    B  <    �	  B      E    �  J    B  M    �
  O    �	  P    �	  [    
  `    
  d    �  �  �3  �   �  �  �   �  �  �       �      �      �      �      �  %    �  &    �  +    �  -    �  /    �  0    �  3  ((  �  >    �  ?    0   F    0   �    �  �    D
  �    �  �    D
  �    �  �    O   �      �  P  �   �  ((  �  S    �  �     �  �  x    �  x  �   �  x  �   �  x  �   �  x  /  �  �  �   �  ((  �  6  ((  �  7  ((  �  @  ((  �  A  ((  �  C    L
  c  ((  �   e  P   �   �  �  �   �  �  �   �    �  �    �  �    �  �    '  �      �    �	  �    �  �  ((  �  �    �      s      �  
    �      s      �      �  i  ((  �  �     �   �  x  +  �    )
  �    )
  (  ((  �   +       {    x  �    <   �  x  '  -   ((  �  9   ((  �   m   ((  �  �   ((  @   �'  �  �   (  x   �  &(  x   t  3+  (5  :   H+  (5  6   I+  (5  4   �,    �  d-    �  T.  x   �  e.  x   t  �.  x      �.  x   5  1  �  r
  !1     '   �1  x   �  �1  x   Z  �1  x   5  �1  x   t  F2  x   D  k2  x   n  �2       �2     �   �2      �2     �   �4  �  X   �4  �     �4  (5  z   �4     �  �4  �
  @   �4  �
  �   �4  �
  �   �4  �  !   �4  �      �4  x   �  �4  �3  �   �4  x   �  �4  x   d  �4    �  �4    �  �4  x   z  �4  x  �   �4  �  �  �4  (5  :   �4  (5  @   �4  x   :  �4  P   �   �4    �  �4  (5  6   �4  (5  4   �4  P  9  �4  P  k  �4  P  �   �4  P  �   �   0^   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securityappcontainer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\coml2api.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ioapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\utilapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objidlbase.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcnterr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\shellapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcasync.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\realtimeapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_tzdb.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsock.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\nb30.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wow64apiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\format C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\urlmon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\enclaveapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_format_ucd_tables.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\queue D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\joystickapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\deque D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winperf.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dlgs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bit C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\handleapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\securitybaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\imm.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winioctl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\RTXPT\Rtxpt\Lighting\Distant\SampleProceduralSky.cpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winefs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnetwk.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack4.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\servprov.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wnnc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincon.h D:\RTXPT\External\Donut\include\donut\core\math\box.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ncrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincontypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack1.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mciapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\lzexpand.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3d12sdklayers.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgicommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\interlockedapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu.h D:\RTXPT\External\Donut\include\donut\app\imgui_renderer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu_tables.h D:\RTXPT\External\Donut\include\donut\app\DeviceManager.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ole2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winspool.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\DXGI.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\objbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\prsht.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmiscapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stop_token D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\combaseapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi3.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\windows.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winapifamily.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\errhandlingapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winpackagefamily.h D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwn.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdkddkver.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\excpt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\ktmtypes.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\profileapi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3dcommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ime_cmodes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\playsoundapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\libloaderapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winreg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processthreadsapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\heapapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winsvc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\cguid.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\synchapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoollegacyapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\forward_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapifromapp.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdcep.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winver.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fileapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\bcrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\verrsrc.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ocidl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleidl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdarg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\windef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\minwindef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings.h D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\poppack.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_strict.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winscard.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypesbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsystem.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\specstrings_undef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono D:\RTXPT\Rtxpt\Lighting\Distant\SampleProceduralSky.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\wtypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mmsyscom.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\driverspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\RTXPT\External\Donut\include\donut\engine\BindingCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcndr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\sdv_driverspecs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\Rtxpt\SampleCommon.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\rpcnsip.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcdce.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\stringapiset.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidlbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\RTXPT\External\Donut\include\donut\app\StreamlineInterface.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\RTXPT\External\Donut\include\donut\engine\View.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\stralign.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\External\Donut\include\donut\app\imgui_nvrhi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\msxml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oaidl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Donut\thirdparty\imgui\imgui.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\propidl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\thirdparty\imgui\imconfig.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namespaceapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\charconv D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\jobapi2.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\guiddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\threadpoolapiset.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgitype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\memoryapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\shared_mutex C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\dxgiformat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dpapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\condition_variable C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\fibersapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winerror.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\oleauto.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\cderr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\mcx.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\dde.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnls.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\commdlg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\datetimeapi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\RTXPT\Rtxpt\Lighting\Distant\SampleProceduralSky.hlsli D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\ddeml.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\tvout.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\RTXPT\Rtxpt\Lighting\Distant\precomputed_sky.hlsli C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\RTXPT\External\Donut\include\donut\core\log.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\systemtopologyapi.h D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timezoneapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\namedpipeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\debugapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h D:\RTXPT\External\Donut\include\donut\engine\CommonRenderPasses.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\inaddr.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\kernelspecs.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\consoleapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\basetsd.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\rpcsal.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\pshpack8.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winuser.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\timeapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\apiquery2.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3native.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\apisetcconv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\predefined C++ types (compiler internal) C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\minwinbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\d3d12.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wincrypt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processenv.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\wingdi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\processtopologyapi.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\unknwnbase.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\reason.h D:\RTXPT\External\Donut\include\donut\shaders\light_types.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Utils\Utils.hlsli D:\RTXPT\External\Donut\include\donut\engine\TextureCache.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_print.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\RTXPT\External\Donut\thirdparty\glfw\include\GLFW\glfw3.h D:\RTXPT\External\Donut\include\donut\engine\SceneTypes.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\sysinfoapi.h D:\RTXPT\External\Donut\include\donut\engine\DescriptorTableManager.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\shared\winsmcrd.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h �       Lj}  迪      瓜     
 �    �   
 � �   � �  
 耯     苃    
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁裥砓>Y7?樰�=      �?                  �?                  �?    H塡$H塴$VWAVH冹@H嬺H嬞H塋$03�墊$0H塋$8H媕H儂vH�2�    D嬸W�H墈H荂   f�;荄$0   H呿t`H価���ww墊$ E3蒁嬇H嬛嬋�    H孁H凌 吚ucE3繦c譎嬎�    L嬎H儃vL�墊$ D嬇H嬛A嬑�    H嬋H凌 吚uH嬅H媆$hH媗$pH兡@A^_^肏灵 �    坦   �    蘃溜 嬒�    �7   �   y   �   �   �   �   �   �   �   �   �   �   �      �   \  � G            �      �   02        �std::filesystem::path::path<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0> 
 >�>   this  B0          AI       � �   AJ          D`    >�   _Source  AK        ;  >�>   __formal  Ah        ;  Dp     M        �2  (	��

  M        �2  6	��


 Z   (!  6 M        !1  A %	*	@

 Z   �"    �"  
   M        <  A M          D$ N M        7  A M        �  A M          A N N N N M        N  ��	
 Z       N M          
�� M          ��# >q    _Result  AQ  �       N N M        N  ��e
 Z       N N N M        �2  ( M        �2  
, M        /  , >�    _Result  AL       � �   N N N N @                    0@ r h   v  N          <  =  A  `  a  d    /  B  �  �  u  �  7  �    !1  �2  �2  �2  �2         $LN61  `   �>  Othis  h   �  O_Source  p   �>  O__formal  O�   0           �         $       � ��   � ��   � ��     � F            &                    �`std::filesystem::path::path<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0>'::`1'::dtor$3 
 >�>   this  EN  `            >�>   __formal  EN  p                                  �  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �      �  
   �   #  �  
 �  �   �  �  
 e  �   i  �  
   y     y  
 p  �   t  �  
 �  ,   �  ,  
 ]  ,   a  ,  
 �  ,   �  ,  
 @UH冹 H嬯婨0冟吚t
僥0鸋婱8�    H兡 ]�   �   H�9 斃�   �   �   S G                      �4        �std::operator==<donut::engine::LoadedTexture>  >x   _Left  AJ          >   __formal  AK          D                           H� 
 h   �4      x  O_Left       O__formal  O   �   0              x      $       w �    x �   y �,   �   0   �  
 y   �   }   �  
 �   �   �   �  
   �     �  
 3繪+罤�L嬞H堿H兟H堿A�   H堿堿 H嬃I岺D  A�   ff�     驛 �YBX � 驜L �YJX润��Y�X馏 H兝I冮u綡兟H冮I冭I冴u淚嬅�   �   @  I G            �       �   �4        �donut::math::operator*<float,3,3,3> 
 >僂   a  AK         
 >僂   b  AP          M        �4     M        �4   (
 N N                        H & h   �  �  �  �  �4  �4  �4  �4      僂  Oa     僂  Ob     _I  Oresult  O�   `           �   P  	   T       9 �    : �   9 �   : �0   < �@   > ��   ; ��   ? ��   @ �,   
   0   
  
 k   
   o   
  
 �   
   �   
  
 T  
   X  
  
 H塡$WH冹@M嬋)t$0H孂荄$     H�$M嬝L+蒐岯W繦�$W蒆嬟$I峇A�   L$D  驛X   驛`A(ff�     (�(捏BY(腕BYD�X�Y�X畜X洋H兝H冮u薎兝H冴I冮I冴u欝s(�S$(铙AYk(迡D$ �c,(麦AY(蘃媆$P驛YK驛Ys�X梵AY[(麦AYC驛YS�X�(腆AYc 驛YK�X伢AXk$$�X蝮X�L$驛X[(�X�O塆 H嬊塍AXs,�o$�w,(t$0H兡@_�   �   4  E G            n     �   �4        �donut::math::operator*<float,3> 
 >鍱   a  AI  8     �  AK        8 
 >鍱   b  AP        $  AS  $     J% M        �  ��[

	 N M        �  �" N* M        �4  ��6@ N- M        �4  


5 M        �4  


' M        �4  


 N N N @                     @ > h   �    �  �  �  �  �  �  �4  �4  �4  �4  �4  �4   X   鍱  Oa  `   鍱  Ob  P   ]I  Oresult  O�   �           n  �3  
   t       �  �
   �  �   �  �   �  �!   �  �$   �  �5   �  �8   �  ��   �  ��   �  �M  �  �P  �  �c  �  �,      0     
 g      k     
 w      {     
 �      �     
 �      �     
 H     L    
 H冹�bH嬃�j(泽AYP(腕AYH)4$�2(�(摅AY 驛Yp驛YX�X�(捏AY`驛Y@�X�(腕AYh �X趔AYH�X伢�X躞X袤q(4$�YH兡�   �   �   C G            �   '   �   �4        �donut::math::operator*<float> 
 >匛   a  AK        � 
 >僂   b  AP        �                        H 
 h      (   匛  Oa  0   僂  Ob      鐴  Oresult  O�   8           �   P     ,       k �   m �	   p ��   q �,      0     
 e      i     
 �      �     
 �      �     
 H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �   /   �   5   �      �   �  k G            :      :   9         �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >_   _Bytes  AJ        9  $  >_    _Block_size  AH       1 
   >_    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        r  
 Z      N Z   �  q   (                      H 
 h   r         $LN14  0   _  O_Bytes  O   �   h           :   ((  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,      0     
 �      �     
 �      �     
 �      �     
          
 s  �   w  �  
 �     �    
 H塡$H塴$H墊$ AVH冹 H�������I嬝H嬯L嬹L;�囎   H茿   H凔wH塝�    B�3 閿   H嬅H塼$0H內H;莢)H�       �H兝'H嬋�    H吚t~H峱'H冩郒塅8�   H孁H;罤B鵋峅H侚   rH岮'H;羦Q刖H吷t
�    H嬸�3鯨嬅I�6H嬚I塣H嬑I墌�    � H媡$0H媆$8H媗$@H媩$HH兡 A^描    惕    惕    藾   ?   u   �   �   �   �   ?   �   �     �   	  �      �   s  � G                   C        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *> 
 >�   this  AJ        (  AV  (     � �  
 >�   _Arg  AK        %  AN  %     � �   >_   _Count  AI  "     � �   AP        "  >#     _New_capacity  AH  m       AJ  �     h   % >  AM       � N  � i  AH q       AJ q     c  P  AM �      % M        �  q	*I >p    _Fancy_ptr  AL  �       AL �     "  M        �  q.I M        �  q.I/ M        (  q.		
%
:. M        9   q(%"
P	 Z   �  q   >_    _Block_size  AH  �     [  O  AH q       >_    _Ptr_container  AH  y     �  p  AH �      
 >�    _Ptr  AL  �       AL �     "  M        r  q
 Z      N N M        r  ��
 Z      N N N N N M        �  R2! M          R') >_    _Masked  AH  ^     f   N  _   AH �       M        �  �� N N N M        ?   C N M        ?   �� N
 Z   ~                         H Z h   �  �  r  x  y  �  $  ?  �  �  �  �  �  �    �  �  '  (  /   9          $LN87  0   �  Othis  8   �  O_Arg  @   _  O_Count  O �   �                  �       L
 �   V
 �1   ^
 �?   _
 �C   f
 �H   g
 �M   q
 �R   u
 �q   v
 ��   u
 ��   v
 ��   
 ��   w
 ��   
 ��   y
 ��   
 ��   z
 ��   
 ��   �
 ��   �
 ��   v
 �  W
 �,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
 '     +    
 O     S    
 _     c    
 w     {    
 �     �    
 �     �    
 �     �    
 �         
          
 �     �    
 �          
 %     )    
 9     =    
 X     \    
 h     l    
 '     +    
 C     G    
 3  ~   7  ~  
 �     �    
 H塡$H塴$VWAVH冹@H嬺H嬞H塋$83�墊$0H媕H儂vH�2�    D嬸W�H墈H荂   f�;荄$0   H呿t`H価���ww墊$ E3蒁嬇H嬛嬋�    H孁H凌 吚ucE3繦c譎嬎�    L嬎H儃vL�墊$ D嬇H嬛A嬑�    H嬋H凌 吚uH嬅H媆$hH媗$pH兡@A^_^肏灵 �    坦   �    蘃溜 嬒�    �2   �   t   �   �   �   �   �   �   �   �   �   �   �      �   �  � G            �      �   �2        �std::filesystem::_Convert_Source_to_wide<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::filesystem::_Normal_conversion>  >�   _Source  AK        6 
 >?R   _Tag  AX        6  Dp     M        �2  1	��


 Z   (!  6 M        !1  < %	*	@

 Z   �"    �"  
   M        <  < M          ?$ N M        7  < M        �  < M          < N N N N M        N  ��	
 Z       N M          
�� M          ��# >q    _Result  AQ  �       N N M        N  ��e
 Z       N N N M        �2  # M        �2  
' M        /  ' >�    _Result  AL       � �   N N N @                    H n h   v  N          <  =  A  `  a  d    /  B  �  �  u  �  7  �    !1  �2  �2  �2         $LN57  h   �  O_Source  p   ?R  O_Tag  O �   8           �         ,        �#    ��    ��    ��     � F            &                    �`std::filesystem::_Convert_Source_to_wide<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::filesystem::_Normal_conversion>'::`1'::dtor$2 
 >?R   _Tag  EN  p                                  �  O  ,      0     
 �      �     
 �          
 �     �    
 #     '    
 �  �   �  �  
          
 p  )   t  )  
 4  )   8  )  
 @UH冹 H嬯婨0冟吚t
僥0鸋婱8�    H兡 ]�   �   H塡$H塴$ VWAVH冹@H嬟H孂H塋$83韷l$0�    D嬸W�H塷H荊   f�/荄$0   H媠H咑teH侢���w|塴$ E3蒁嬈fH~虷嬚嬋�    H嬝H凌 吚ucE3繦c親嬒�    L嬒H�vL�塡$ D嬈H嬚A嬑�    H嬋H凌 吚uH嬊H媆$pH媗$xH兡@A^_^肏灵 �    坦   �    蘃岭 嬎�    �$   �   r   �   �   �   �   �   �   �   �   �   �   �      �   '  v G            �      �   �2        �std::filesystem::_Convert_stringoid_to_wide<std::filesystem::_Normal_conversion>  >�   _Input  AI       � d `  AK          AI �       >?R   __formal  AX        (  Dp   6 M        !1  1 )	$*	@

 Z   �"    �"  
   M        <  1 M          4$ N M        7  1 M        �  1 M          1 N N N N M        N  ��	
 Z       N M          
�� M          ��# >q    _Result  AQ  �       N N M        N  e
 Z       N N
 Z   (!   @                    H N h   v  N          <  =  `  a    B  �  u  7  �    !1         $LN41  h   �  O_Input  p   ?R  O__formal  O �   8           �         ,       �  �#   �  ��   �  ��   �  ��   �   � F            &                    �`std::filesystem::_Convert_stringoid_to_wide<std::filesystem::_Normal_conversion>'::`1'::dtor$1  >?R   __formal  EN  p                                  �  O,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 :     >    
 �  �   �  �  
 <     @    
 �     �    
 "     &    
 @UH冹 H嬯婨0冟吚t
僥0鼿婱8�    H兡 ]�   �   H塡$H塴$ VWAVH冹@H嬟H孂H塋$83韷l$0�    D嬸W�H塷H荊   @�/荄$0   H媠H咑teH侢���w|塴$ E3蒁嬈fH~虷嬚嬋�    H嬝H凌 吚ucHc覧3繦嬒�    L嬒H�vL�塡$ D嬈H嬚A嬑�    H嬋H凌 吚uH嬊H媆$pH媗$xH兡@A^_^肏灵 �    坦   �    蘃岭 嬎�    �$   �   r   �   �   �   �   �   �   �   �   �   �   �      �     y G            �      �   �        �std::filesystem::_Convert_wide_to<std::char_traits<char>,std::allocator<char>,char>  >�   _Input  AI       � d `  AK          AI �       >�   _Al  AP        (  Dp   8 M        �  1$!)	$N
	3

 Z   �"  �  �"  
   M        �  1 M        &  4$ N M        i  1 M        �  1 M          1 N N N N M        N  ��	
 Z       N M        �  
�� M        0  ��# >p    _Result  AQ  �       N N M        N  e
 Z       N N
 Z   (!   @                    H J h   v  N    [  \  &  0  �  �  �  �  �  �  �    i  �         $LN41  h   �  O_Input  p   �  O_Al  O  �   8           �         ,       �  �#   �  ��   �  ��   �  ��   �   � F            &                    �`std::filesystem::_Convert_wide_to<std::char_traits<char>,std::allocator<char>,char>'::`1'::dtor$1  >�   _Al  EN  p                                  �  O  ,      0     
 �      �     
 �      �     
 �      �     
 �      �     
 :     >    
 �  �   �  �  
 4     8    
 �     �    
          
 @UH冹 H嬯婨0冟吚t
僥0鼿婱8�    H兡 ]�   �   H塡$H塴$H塋$VWAVH冹@D嬺H嬞3缐D$0W�H堿H茿   �荄$0   I媝H咑tdH侢���w{I�(塂$ E3蒁嬈H嬚A嬑�    H孁H凌 吚ucHc譋3繦嬎�    L嬎H儃vL�墊$ D嬈H嬚A嬑�    H嬋H凌 吚uH嬅H媆$hH媗$pH兡@A^_^肏灵 �    坦   �    蘃溜 嬒�    蘣   �   ~   �   �   �   �   �   �   �   �   �      �   f  p G            �      �   �        �std::_Convert_wide_to_narrow<std::char_traits<char>,std::allocator<char> >  >�5   _Code_page  A           An       � �   >�   _Input  AP        � ^ n  AP �       >�   _Al  AQ        � [ q  AQ �       Dx    M        �  & M        &  )$ N M        i  & M        �  & M          & N N N N M        [  ? N M        N  ��	
 Z       N M        �  
�� M        0  ��# >p    _Result  AQ  �       M        �  �� N N N M        N  re
 Z       N Z   �"  �  �"  
   @                    H F h   v  N    [  \  &  0  �  �  �  �  �  �  �    i         $LN36  h   �5  O_Code_page  p   �  O_Input  x   �  O_Al  `   �  O_Output  O  �   �           �        |       <  �&   =  �7   O  �?   ?  �H   @  �Q   D  �t   G  ��   I  ��   K  ��   O  ��   P  ��   K  ��   A  ��   D  ��   �    F            &                    �`std::_Convert_wide_to_narrow<std::char_traits<char>,std::allocator<char> >'::`1'::dtor$0  >�   _Al  EN  x                                  �  O   ,      0     
 �      �     
 �      �     
 �      �     
 �      �     
          
          
 5     9    
   �     �  
 |     �    
 ,     0    
 �     �    
 @UH冹 H嬯婨0冟吚t
僥0﨟婱`�    H兡 ]�   �   H�    �   �      �   �   b G                      �        �std::_Immortalize_memcpy_image<std::_Generic_error_category>                         H�  �/        _Static  O�   0              �     $       � �    � �   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H�    �   �      �   �   a G                      �        �std::_Immortalize_memcpy_image<std::_System_error_category>                         H�  �/        _Static  O �   0              �     $       � �    � �   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 @SVATAUAWH冹 L媦H�������L媎$pH嬅I+荕嬮H嬹H;�侷  H塴$PH媔H墊$XL塼$`M�4I嬛H兪H;觲:H嬐H嬅H验H+罤;鑧)H�)H嬟H;蠬B豀岰H=   r9H岺'H;�嗚   �H�       �H兞'�    H吚勀   H峹'H冪郒塆H吚t
H嬋�    H孁�3�L塿M�4?H塣M嬊H嬒H凖vMH�H嬘�    M嬆I嬚I嬑�    H峌C�& H侜   rH婯鳫兟'H+貶岰鳫凐wJH嬞H嬎�    �H嬛�    M嬆I嬚I嬑�    C�& H�>H嬈H媩$XH媗$PL媡$`H兡 A_A]A\^[描    惕    惕    獭   �   �   �   �   ?   �   ?   /  �   9  ?   G  ?   s  �   y  �     �      �   
	  � G            �  
   �  �4        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append'::`2'::<lambda_1>,char const *,unsigned __int64> 
 >�   this  AJ        ,  AL  ,     XD  >_   _Size_increase  AK        �O / >缧   _Fn  AX        ��  �  � � }  AX �       D`    >�   <_Args_0>  AQ        )  AU  )     [D  >#    <_Args_1>  AT        dO  EO  (           Dp    >_    _Old_size  AW       sZ  >#     _New_capacity  AH  {      * N  U �  AI       i`  � �  AJ  �       AH �     �  + S B  AJ �       >_    _New_size  AV  L     2� �  AV r      >�    _Old_ptr  AI  �     3  AI +    F 
   M        �  w>�� >p    _Fancy_ptr  AM  �       AM �     � �   M        �  {>�� M        �  {>��* M        (  {

*%
��- M        9   ��	)
��
 Z   q   >_    _Block_size  AJ  �     �  �  AJ �       >_    _Ptr_container  AH  �       AH �     � # B m 5 
 >�    _Ptr  AM  �       AM �     � �   M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  +L M          L* >_    _Masked  AK  S     *R  v  } �  AK �     h  G  M        �  
m N N N M        �  �)	h M        3  )�
h M        c  �
)G
 Z   �  
 >   _Ptr  AI +    F 
   >#    _Bytes  AK      .  AK r     # M        s  
�#
J
 Z   �   >_    _Ptr_container  AJ        AJ +    L  D  >_    _Back_shift  AI      
  AI r      N N N N M        �4  �� M        ?   �� N M        ?   �� N N M        �4  �5( M        ?   �= N M        ?   �5 N N
 Z   ~               (          @ j h   �  �  r  s  t  �  $  3  ?  �  �  �  �  c  �  �  �    �  �  '  (  /   9   �4         $LN143  P   �  Othis  X   _  O_Size_increase  `   缧  O_Fn  h   �  O<_Args_0>  p   #   O<_Args_1>  O   �   �           �       �       � �
   � �   � �5   � �L   � �w   � ��   � ��   � ��   � ��   � ��   � �  � �  � �
  � �3  � �5  � �P  � �e  � �r  � �x  � �~  � �,      0     
 #     '    
 3     7    
 `     d    
 �     �    
 �     �    
 �     �    
 �     �    
      
    
      "    
 J     N    
 v     z    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 
         
 -     1    
 =     A    
 �     �    
 �     �    
 _     c    
 s     w    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 _     c    
 �     �    
 �     �    
 �     �    
 �         
 (     ,    
 8     <    
 �  �   �  �  
 $	     (	    
 @SVATAVH冹(L媞H�������H嬅M嬦I+艸嬹H;�俉  H塴$PI�,H墊$XH嬚L塴$`H兪L媔L墊$ H;觲:I嬐H嬅H验H+罫;鑧)J�)H嬟H;蠬B豀岰H=   r9H岺'H;�嗰   �H�       �H兞'�    H吚勍   H峹'H冪郒塆H吚t
H嬋�    H孁�3�D緗$pM嬈H塶I�,>H塣H嬒I凖vMH�H嬘�    M嬆A嬜H嬐�    I峌A�, H侜   rH婯鳫兟'H+貶岰鳫凐wMH嬞H嬎�    �H嬛�    M嬆A嬜H嬐�    A�, H�>H嬈L媗$`H媩$XH媗$PL媩$ H兡(A^A\^[描    惕    惕    虩   �   �   �   �   ?     A   3  �   =  ?   K  A   z  �   �  �   �  �      �   		  � G            �     �  �4        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<`std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append'::`2'::<lambda_1>,unsigned __int64,char> 
 >�   this  AJ        %  AL  %     fR  >_   _Size_increase  AK        �? F >T�   _Fn  AX        ��  �  � � �  AX �     	  D`    >#    <_Args_0>  AQ          AT       lW  >p    <_Args_1>  EO  (           Dp    >_    _Old_size  AV       |e  >#     _New_capacity  AH  y      * N  U �  AI       r`  � �  AJ  �       AH �     �  + Y B  AJ �       >_    _New_size  AN  7     N� �  AN y      >�    _Old_ptr  AI  �     3  AI /    I 
   M        �  u>�� >p    _Fancy_ptr  AM  �       AM �     � �   M        �  y>�� M        �  y>��* M        (  y

*%
��- M        9   ��	)
��
 Z   q   >_    _Block_size  AJ  �     �  �  AJ �       >_    _Ptr_container  AH  �       AH �     � ) B s 8 
 >�    _Ptr  AM  �       AM �     � �   M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  <$
# M          <
# >_    _Masked  AK  H     <[    � �  AK �     n $ G  M        �  
k N N N M        �  �)	k M        3  )�k M        c  �)J
 Z   �  
 >   _Ptr  AI /    I 
   >#    _Bytes  AK  	    .  AK y     # M        s  
�#
M
 Z   �   >_    _Ptr_container  AJ        AJ /    O  G  >_    _Back_shift  AI  "    
  AI y      N N N N M        �4  �� M        �  �� N M        ?   �� N N M        �4  �9( M        �  丄 N M        ?   �9 N N
 Z   ~   (                      @ v h   �  �  r  s  t  �  $  3  ?  �  �  �  �  c  s  �  �  �  �      �  �  '  (  /   9   �4         $LN166  P   �  Othis  X   _  O_Size_increase  `   T�  O_Fn  h   #   O<_Args_0>  p   p   O<_Args_1>  O   �   �           �       �       � �   � �   � �.   � �<   � �H   � �Q   � �u   � ��   � ��   � ��   � ��   � �  � �	  � �  � �7  � �9  � �T  � �n  � �y  � �  � ��  � �,   
   0   
  
   
     
  
 +  
   /  
  
 X  
   \  
  
 z  
   ~  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
   
     
  
 .  
   2  
  
 Z  
   ^  
  
 v  
   z  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
   
     
  
 !  
   %  
  
 j  
   n  
  
 z  
   ~  
  
 C  
   G  
  
 W  
   [  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
 O  
   S  
  
 t  
   x  
  
 �  
   �  
  
 �  
   �  
  
 �  
   �  
  
   
     
  
 (  
   ,  
  
 �  �   �  �  
  	  
   $	  
  
 @UWAVAWH冹(L媦H傀������H嬊I嬮I+荓嬹H;�倰  H塡$PH塼$XI�4L塪$`H嬛L媋H兪L塴$ E3鞨;譾H�������H� 隦I嬏H嬊H验H+罫;鄓H�������H� �1J�!H孃H;蠬B鳫�������H峅H;��  H蒆侚   r,H岮'H;�嗱   H嬋�    H吚勠   H峏'H冦郒塁H吷t
�    H嬝�I嬢I墌K�<?I塿L嬊H嬎I凕vZI�6H嬛�    H鸋呿t稤$pH嬐f螳J�e   I�/fD�,CH侜   rH婲鳫兟'H+馠岶鳫凐wVH嬹H嬑�    �$I嬛�    H鸋呿t稤$pH嬐f螳I�/fD�,CI�I嬈L媎$`H媡$XH媆$PL媗$ H兡(A_A^_]描    惕    惕    烫   �   �   �     ?   e  �   o  ?   �  �   �  �   �  �      �   �	  G            �     �  �4        �std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<`std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append'::`2'::<lambda_1>,unsigned __int64,wchar_t> 
 >�   this  AJ        %  AV  %     ��  >_   _Size_increase  AK        �D | >眯   _Fn  AX        ��  �  � � �  AX �       D`    >#    <_Args_0>  AN       ��  AQ          >q    <_Args_1>  EO  (           Dp    >_    _Old_size  AW       ��  >#     _New_capacity  AH  c     C   %   AJ  g     K   #   AM       �J  k  �  AJ �      �  >_    _New_size  AL  <     �� W S+  AL �    +    >�    _Old_ptr  AL      @  AL a    > 
 $   M        �  ��	
S� >q    _Fancy_ptr  AI  �       AI �     � �   M        -   
��S� M        m   
��S�& M        (  ��)
,%
��( M        9   ��$	()
��
 Z   q   >_    _Block_size  AH  �       AH �      >_    _Ptr_container  AH  �       AH �     � # O z A 
 >�    _Ptr  AI  �       AI �     � �   M        r  ��
 Z      N N M        r  ��
 Z      N N M        �   

��
	 N N N N M        �  AG	) M          A	&" >_    _Masked  AK  L     s�  �  � �  AK �     u  T  M        �  
�� N N N M        �  �/)t M        �  �/)t M        c  丂)S
 Z   �  
 >   _Ptr  AL a    > 
 $  >#    _Bytes  AK  7    2  AK �     # M        s  
両#
V
 Z   �   >_    _Ptr_container  AJ  M      AJ a    X  P  >_    _Back_shift  AL  T    
  AL �      N N N N M        �4  �	 M        �  � >�   _First  AM        AM /    � < $ �   M        l  � N N M        F   � N N M        �4  乲(8	 M        �  乿 >�   _First  AM  v      AM �    ,  M        l  乿 N N M        F   乲 N N
 Z   ~   (                      @ z h   �  �  l  r  s  t  �      B  F  �  �  �  �  u  c  j  n  �    �  (  -   .   9   m   �   �4         $LN191  P   �  Othis  X   _  O_Size_increase  `   眯  O_Fn  h   #   O<_Args_0>  p   q   O<_Args_1>  O �   �           �       �       � �   � �   � �.   � �A   � �D   � �H   � ��   � ��   � �  � �  � �  � �$  � �/  � �7  � �@  � �i  � �k  � ��  � ��  � ��  � ��  � ��  � �,      0     
 0     4    
 @     D    
 m     q    
 �     �    
 �     �    
 �     �    
 �     �    
          
 C     G    
 o     s    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 "     &    
 2     6    
 }     �    
 �     �    
 O     S    
 _     c    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
 �     �    
          
 !     %    
 K     O    
 [     _    
 �     �    
 �     �    
 j     n    
 z     ~    
 k	  �   o	  �  
 �	     �	    
 H儂H婤vH�H堿H嬃H��   �   �  � G                      �2        �std::filesystem::_Stringoid_from_Source<char,std::char_traits<char>,std::allocator<char> >  >�   _Source  AK          AK        M        d   N M        �2   	 M        /   	 >�    _Result  AK        M        �    N N N                        H  h   A  d  /  �  �  �2      �  O_Source  O   �   0                    $       �  �    �  �   �  �,      0     
 �      �     
 �      �     
 (     ,    
 �     �    
 �Z砷       =      �   �   : G            	          �4        �fmod<double,float,0>  >A    _Left  A�         	  >@    _Right  A�                                  H�     A   O_Left     @   O_Right  O�               	   �            � �,   �   0   �  
 `   �   d   �  
 �   �   �   �  
 �   �   �   �  
 �A��I�Y殷Y荔Y审X�W荔X�.聎W荔Q旅(麻    6   C      �     B G            :       5   �        �donut::math::length<float,3> 
 >匛   a  AJ        :  M        d  %
 >@    _Xx  A�   %       N M        �   ! M        �   ! N N                        H  h   d  �  �      匛  Oa  O  �               :   x            + �,   	   0   	  
 d   	   h   	  
 �   	   �   	  
 ,  	   0  	  
 �I��Q�Y荔Y审Y殷X馏X旅   �   �   I G            #       "   �        �donut::math::lengthSquared<float,3> 
 >匛   a  AJ        #  M        �  "  N                        H 
 h   �      匛  Oa  O  �               #   x            ' �,      0     
 k      o     
 �      �     
 @SH冹P)t$@H嬞�2)|$0(煮z(求Y煮Y荄)D$ 驞B�X蠥(润AY�W荔X�.聎	W荔Q码(妈    �^餒嬅�^D^荔3(t$@�{(|$0驞CD(D$ H兡P[肻   C      �     E G            �   0   t   �        �donut::math::normalize<float,3> 
 >匛   a  AK        `  AK `     5  M        �  `

 M        �  p	
 >@    _x  A�   d       >@    _y  A�   k       >@    _z  A  p       N N M        �  	 M        d  J >@    _Xx  A�   J       A�  `     5  N M        �  	 M        �  	 N N N P                     H  h   d  �  �  �  �  �   h   匛  Oa  O  �               �   x            / �,       0      
 g       k      
 w       {      
 �       �      
 �       �      
            
 Z      ^     
 j      n     
            
 H嬆H塜H墄UH峢℉侅P  )p鐷嬟�2H孂)x�(艱)@菵)H窪)P―)X樿    D(�(畦    �s(�(畦    D(�(畦    �sD(�(畦    D(�(畦    (
    H峀$,(畜DE�L$pH峌狼E�    �
    H岴繟(麦D]�W馏}��D$(L峀$t(    DW罝W袤DE�ED$$    H+�W荔}�W审DL$ E狼D$,    A�   驞T$8W鲶DL$@�U橌D]んU℉荄$0  �?荄$<    荅�    M星E�    @ �     驛Y   驛!驛iff�     (�(捏YT�(腕Y�X�YL�X畜X洋H兝H冴u蘄兞H兞鬒冴u�W繢塗$hW蒆峂峊$HA�   D$HH+蔋岲$HL$XL峂�@ ff�     驛Y   驛!驛iff�     (�(捏YT�(腕Y�X�YL�X畜X洋H兝H冴u蘄兞H兞鬒冴u�D$H婦$hL崪$P  L$XI媅A({郋(C蠩(K繣(S癊([�(��O塆 H嬊�G$�w,I媨A(s餓嬨]肁   B   M   ;   ]   B   i   ;   z   B   �   ;   �   �   �   �   �   �      �   t  B G            �  @   {  �4        �donut::math::rotation<float>  >匛   euler  AI       n AK         
 >@     sinZ  A  �     #
 >@     cosX  A�   Y     8
 >綞    matX  Dp   
 >@     cosY  A  v     %
 >@     sinY  A  e     ;
 >@     cosZ  A�   �     � 
 >綞    matZ  D�   
 >@     sinX  A  I     M
 >綞    matY  D    ! M        �  	俽"

 N% M        �4  佌%94 M        �4  佌% M        �4  佌X	 N N N% M        �4  �K4 M        �4  �K M        �4  �K N N N* M        �  ��D N0 M        �  ��$
	 N+ M        �  ��  N M        �4  �� N M        �4  m	 N M        �4  e
 N M        �4  Q N M        �4  I
 N+ M        �4  	Q&! N P                    @ B h   �    �  �  �  �  �  �  �4  �4  �4  �4  �4  �4  �4   h  匛  Oeuler  p   綞  OmatX  �   綞  OmatZ      綞  OmatY  O�   �          �  �3  0   �      � �   � �"   � �)   � �I   � �Q   � �V   � �Y   � �e   � �m   � �r   � �v   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �  � �  � �  � �  � �   � �1  � �A  � �R  � �Y  � �{  � ��  � ��  � ��  � ��  � �,      0     
 h      l     
 x      |     
 �      �     
 �      �     
 �      �     
          
 *     .    
 `     d    
 �     �    
 H塡$UVWATAUAVAWH崿$帻��H侅   H�    H3腍墔  I嬞H塡$pM嬭L塂$PL嬹H塎L塃癏塢窰嫷�  H塼$`3�墊$4H�9H塓H呉t
H�H嬍�P怚墌I墌I婨H吚t�@I婨 I塅I婨I塅I墌 I墌(I墌0I墌8I墌@I墌HI墌PI墌XI墌`I墌hA荈p殭�?A荈t��?A荈x\彚?A荈|  �?A菃�     燖A菃�   吞L?A菃�     朇A菃�   吞叹A壘�   A菃�   �?A菃�     @?A菃�      @I菃�      @A壘�   W�E怘荅�   H荅�   �    塃��   f塃斊E� H峌怘峂x�    怚婨 H塃�H� H婡H塃圚�H塂$@L峞xH兘�   LGexL嫿�   �    塂$8W�EXH嬒H塎h�   H塙p圡X荄$4   M�勭   I����嘝
  墊$ E3蒃嬊I嬙嬋�    H孁H凌 吚�8
  Hc螲婾hH;蕎H岴XH儅pHGEXH塎h� 隱H嬹H+騆婨pI嬂H+翲;饂'H塎hH峕XI凐HG]XH贚嬈3襀嬎�    �3 �艱$  L嬑D禗$0H嬛H峂X�    H媡$`L峂XH儅pLGMX墊$ E嬊I嬙婰$8�    H嬋H凌 吚厱  H婾pH婱h�   H嬄H+罤凐5r5H岮5H塃hH峕XH凓HG]XH貲岹.H�    H嬎�    艭5 H岴X�$H荄$ 5   L�
    D禗$0�5   H峂X�    W�厴   W审崹    厴   H崹   3跦塜H茾   �兿墊$4E3繦崟�   H崓x  �    怘塼$(H婦$@H塂$ E3蒐崊x  H峌繦婱��U圚嬋H� H婹H�H塝I塅 I媈(I塚(����H呟t-嬈�罜凐uH�H嬎�嬈�罜凐u	H�H嬎�P媩$4H媇菻呟t-嬈�罜凐uH�H嬎�嬈�罜凐u	H�H嬎�P媩$4H崓x  �    怘嫊�   H凓v4H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐囖
  �    fo    �叏   茀�    H婾pH凓v1H�翲婱XH嬃H侜   rH兟'H婭鳫+罤兝鳫凐噿
  �    M媫 L墋�I�H婡H塂$HL媎$pI�$H塂$@H峕xH兘�   HG]xH塢圠嫮�   �    塂$8W�E3蒆塎(�   H塙0圡猴墊$4M呿勸   I価����
  塋$ E3蒃嬇H嬘嬋�    L孁H凌 吚咠	  Ic螲婾(H;蕎H岴H儅0HGEH塎(� 隸L嬦L+釲婨0I嬂H+翷;鄔,H塎(H峕I凐HG]H贛嬆3襀嬎�    B�# H媇堧艱$  M嬏D禗$0I嬙H峂�    L媎$pL峂H儅0LGMD墊$ E嬇H嬘婰$8�    H嬋H凌 吚匲	  H婾0H婱(L媫�瑚佅�   H嬄H+罤凐1r7H岮1H塃(H峕H凓HG]H貯�1   H�    H嬎�    艭1 H岴�$H荄$ 1   L�
    D禗$0�1   H峂�    W�吀   W审嵢    吀   H嵢   3跦塜H茾   �猴	墊$4E3繦崟�   H崓�  �    怘婦$`H塂$(H婦$@H塂$ E3蒐崊�  H峌蠭嬒�T$HH嬋H� H婹H�H塝I塅0I媈8I塚8H呟t-嬈�罜凐uH�H嬎�嬈�罜凐u	H�H嬎�P媩$4H媇豀呟t+嬈�罜凐uH�H嬎��羢凗u	H�H嬎�P媩$4H崓�  �    怘嫊�   H凓v4H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚁  �    fo    �吶   茀�    H婾0H凓v1H�翲婱H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘓  �    L媗$PI媢 H塼$@H�H婡H塂$HI�$H塂$PL峞xH兘�   LGexL嫿�   �    塂$8W�E83蒆塎H�   H塙P圡8猴
墊$4M�勭   I����囄  塋$ E3蒃嬊I嬙嬋�    H孁H凌 吚叾  Hc螲婾HH;蕎H岴8H儅PHGE8H塎H� 隱H嬹H+騆婨PI嬂H+翲;饂'H塎HH峕8I凐HG]8H贚嬈3襀嬎�    � �艱$  L嬑D禗$0H嬛H峂8�    H媡$@L峂8H儅PLGM8墊$ E嬊I嬙婰$8�    H嬋H凌 吚�  H婾PH婱HH嬄H+罤凐2r7H岮2H塃HH峕8H凓HG]8H貯�2   H�    H嬎�    艭2 H岴8�$H荄$ 2   L�
    D禗$0�2   H峂8�    W�呚   W审嶈    呚   H嶈   3跦塜H茾   �E3繦崟�   H崓�  �    怢媩$`L墊$(H婦$PH塂$ E3蒐崊�  H峌郒嬑�T$HH嬋H� H婹H�H塝I塅@I婲HI塚HH吷t�    H婱鐷吷t�    怘崓�  �    怘嫊�   H凓v4H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚨  �    fo    �呰   茀�    H婾PH凓v1H�翲婱8H嬃H侜   rH兟'H婭鳫+罤兝鳫凐噄  �    I媢 H�H媥L媎$pI�$H岴xH兘�   HGExH塂$pH媴�   H塂$x(D$pfD$PL岲$0H峊$PH崓8  �    怉�(   H�    H崓8  �    W�咗   W审�   咗   H�  H茾    H茾   �  E3繦崟�   H崓�  �    怢墊$(H塡$ E3蒐崊�  H峌餒嬑�譎嬋H� H婹E3繪�L堿I塅PI婲XI塚XH吷t�    H婱鳫吷t�    怘崓�  �    怘嫊  H凓v4H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐�  �    fo    ��  茀�    L媴P  I凐vH嫊8  H崓8  �    I媢 H�H媥I�$H岴xH兘�   HGExH塂$`H媴�   H塂$h(D$`fD$PL岲$0H峊$PH崓X  �    怉�'   H�    H崓X  �    W��  W审�(   �  H�(  H茾    H茾   �  E3繦崟  H崓�  �    怢墊$(H塡$ E3蒐崊�  H峌 H嬑�譎嬋H� H婹E3繪�L堿I塅`I婲hI塚hH吷t�    H婱H吷t�    怘崓�  �    怘嫊0  H凓v4H�翲媿  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐囕   �    fo    ��(  茀   L媴p  I凐vH嫊X  H崓X  �    I�I嬒�P(I婲L墊$PH�E3蒃岮H峊$P�惏  I婲H��惱  I�I嬒�P I崕�   3褹笭   �    怘峂x�    怚嬐�    怚婰$H吷t�    I嬈H媿  H3惕    H嫓$h  H伳   A_A^A]A\_^]描    惞   �    蘃溜 嬒�    蘃灵 �    愯    愯    惞   �    蘄溜 A嬒�    蘃灵 �    愯    愯    惞   �    蘃溜 嬒�    蘃灵 �    愯    愯    愯    �"   �   c  �   m  �   �  �   �  �     �   t  A   �  
   �  �     �   
  @   '  �   ;     �  �   M  �   �  �   �  �   �  �   $  �   s  �   �  A     
   (  �   ~  �   �  @   �  �   �       �   �  �     �     �   Y  �   �  �   �  �   Q	  A   q	  
   �	  �   �	  �   �	  @   
  �   
     e
  �   �
  �   �
  �   �
  �     �     �   e  �   �     �  �   �  �   )  �   q  �     �   �  �   �  �   �  �   
  �   W
     e
  �   q
  �   �
  �     �     �   '  �   i  �   q  �   �  �   �  A   �  �     �     �   &  9   F  �   Q  �   ]  �   g  �   m  �   s  �   ~  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �      �   F7  N G            �  0   �  	3        �SampleProceduralSky::SampleProceduralSky 
 >&�   this  D   AJ        C  AV  C     ��  D`   >))   device  AK        v  AK w      >   textureCache " BP   @     f�& �& #& Y$ @� D�    AP        ;  AU  ;     ��? =.  Dp   >鱵   commonPasses  Bp   8     �[� D�    AI  3     /, e� AQ        3 " AT  �    �
� ] ��C
 M
- �
-  AI �    �Z ) � � Dx   > )   commandList  B`   [     ~� AH      
  AW  o
    j�
 �|  AL �    �@� EO  (           D�  
 >?    path  Dx   M        (  �	 M        &(  �	

 Z   �   N N M        N  	庼 M        4  	庼
 Z      N N M        1  幎 > )   commandList  AL  V     6  AL �     ��X Qn BP   �     �6�  N M        J   巹 M        %  巹
 Z   �   N N M        J  X�,�& M        %  �,
4
�� M        �  4�9� M        3  1�<�	  M        c  嶧)��
 Z   �  
 >   _Ptr  AH  F      AJ  C      AH h      >#    _Bytes  AK  <    1 �  M        s  嶰d��
 Z   �   >_    _Ptr_container  AH  Z      AJ  W      N N N N N N M        N  � M        4  �
 Z      N N M        T.  � M        e.  �	
 Z   �   N N M        �4  '嶉 M        T.  �	 M        e.  �	
 Z   �   N N M        �4  嶛 M        �4  嶛 M        e  嶠
 >�    _Tmp  AK  �
       AK         N M        �4  嶛 N N N M        �4  嶉 M        �4  嶉#D N N N M        S  峔
 M        M  峹
 M        -  0崐 M        �  崐 N N M        @  峹
 M        �  峹��
 M          峹 N N N N M        E  峔
 Z   +   N N M        �4  C� M        �  C�
 Z   �   M          "� M        F  �) N M          �
 >�    _Result  AH  
      N N N N M        J   岄 M        %  岄
 Z   �   N N M        J  X寫傿& M        %  寫
4
傫 M        �  4尀�5 M        3  1尅�2  M        c  尗)�
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    71  M        s  尨d�
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N N M        N  寗 M        4  寗
 Z      N N M        T.  寀 M        e.  寀	
 Z   �   N N M        �4  '孨 M        T.  宯 M        e.  宯
 Z   �   N N M        �4  宊 M        �4  宊 M        e  宑
 >�    _Tmp  AK  U       AK u        N M        �4  宊 N N N M        �4  孨 M        �4  孨#D N N N M        S  嬃
 M        M  嬢
 M        -  0嬶 M        �  嬶 N N M        @  嬢
 M        �  嬢��
 M          嬢 N N N N M        E  嬃
 Z   +   N N M        �4  C媫 M        �  C媫
 Z   �   M          "媫 M        F  嫀 N M          媫
 >�    _Result  AH  �      N N N N M        J  ;�.劅 M        %  �.1
剷 M        �  1�8剷 M        3  .�;剴  M        c  婤)刬
 Z   �  
 >   _Ptr  AH  B      AJ  ?      AH d      >#    _Bytes  AK  ;    �. d M        s  婯d剋
 Z   �   >_    _Ptr_container  AH  V      AJ  S      N N N N N N M        J  X娭勸& M        %  娭
4
劆 M        �  4娿勪 M        3  1婃勧  M        c  婐)劦
 Z   �  
 >   _Ptr  AH  �
      AJ  �
      AH       >#    _Bytes  AK  �
    �1 � M        s  婛d劽
 Z   �   >_    _Ptr_container  AH        AJ        N N N N N N M        N  娚 M        4  娚
 Z      N N M        T.  姾 M        e.  姾	
 Z   �   N N M        �4  $姈 M        T.  姵 M        e.  姵
 Z   �   N N M        �4  姢 M        �4  姢 M        e  姩
 >�    _Tmp  AK  �
      AK �
        N M        �4  姢 N N N M        �4  姈 M        �4  姈#D N N N M        S  g壊j
 M        M  �
 M        -  0�. M        �  �. N N M        @  �
 M        �  ���
 M          � N N N N M        E  g壊& M        +  壊&F(-$$
 Z   �4   >_   _Old_size  AH  �	    a 
 3  AJ  �	      C       �    =  C      �	    �4  b � M        �  L壷 >�   _First1  AI  �	      AI 
    ,  N M        0  壠	
 >嘚   this  AI  �	    	  >p    _Result  AI  �	      N N N N" M        �4  !垁�$
�	" M        �  !垁�$
�	" M        �  	垬�
�	
 Z   (!  > M        �  垽$!&
#z*
�	 Z   �"  �"  
   M        �  垽 M        &  埁&	 N M        i  垽 M        �  垽 M          垽 N N N N M        N  墹	�
 Z       N M        �  塽 M        0  塽
	
 >嘚   this  AQ  ~	    
  >p    _Result  AQ  �	      N N M        �  堽)V >_   _New_size  AJ  �    r R   AJ z	      M        
  � M        0  �

 >嘚   this  AH  	    
  >p    _Result  AH  	    
  AH z	    !  N N$ M          �/E$-$
 Z   �4   >_   _Count  AH  /	    F &   AL  "	    
  M        �  
塇 >�   _First  AI  H	      AI u	    RU + � x N M        0  �8	
 >嘚   this  AI  <	    	  >p    _Result  AI  E	      N N N M        N  堳喖
 Z       N N N M          垁 M        F  垜 N M          垁
 N N N N M        J  ;�"噡 M        %  �"1
噒 M        �  1�,噒 M        3  .�/噏  M        c  �6)嘓
 Z   �  
 >   _Ptr  AH  6      AJ  3      AH X      >#    _Bytes  AK  /    v. C M        s  �?d嘨
 Z   �   >_    _Ptr_container  AH  J      AJ  G      N N N N N N M        J  X囀囆& M        %  囀
4
� M        �  4囎嚸 M        3  1囑嚴  M        c  囦)嚁
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH       >#    _Bytes  AK  �    �1 � M        s  図d嚔
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N N M        N  嚱 M        4  嚱
 Z      N N M        T.  4噮 M        e.  噮'
 M        �  噿,
 >�   this  AI  �    0� �% AI u	    RU + � x M        b  嚘	
 N N N N M        �4  H�= M        T.  +嘮 M        e.  嘮) M        �  嘰, M        b  噋	 N N N N M        �4  嘖 M        �4  嘖 M        e  嘜
 >�    _Tmp  AK  D    ,  AK �    D !   N M        �4  嘖 N N N M        �4  �= M        �4  �=#D N N N M        S  g哘j5
 M        M  喕
 M        -  0喭 M        �  喭 N N M        @  喕
 M        �  喕��
 M          喕 N N N N M        E  g哘& M        +  哘&F(-$$
 Z   �4   >_   _Old_size  AH  W    a 
 3  AJ  C      C       5    =  C      G    6	>  l � M        �  L唘 >�   _First1  AI  u      AI �    ,  N M        0  唀	
 >嘚   this  AI  i    	  >p    _Result  AI  r      N N N N& M        �4  %��(
�
�1	
& M        �  %��(�
�1	
& M        �  	�#��
�1	
 Z   (!  > M        �  �/$!&
#+
塦	
 Z   �"  �"  
   M        �  �/ M        &  �3&	 N M        i  �/ M        �  �/ M          �/ N N N N M        N  �5	塠
 Z       N M        �  � M        0  �
	
 >嘚   this  AQ      
  >p    _Result  AQ        N N M        �  厜)[ >_   _New_size  AJ  �    w R   AJ 
      M        
  厭 M        0  厭

 >嘚   this  AH  �    
  >p    _Result  AH  �    
  AH 
    "  N N$ M          吅E$-%
 Z   �4   >_   _Count  AH  �    K &   AT  �    
  M        �  
呌 >�   _First  AI  �      N M        0  吤	
 >嘚   this  AI  �    	  >p    _Result  AI  �      N N N M        N  厐�
 Z       N N N M          � M        F  � N M          � N N N N M        J  ;劒娙 M        %  劒1
娋 M        �  1劥娋 M        3  .劮娀  M        c  劸)姃
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    �
. �
 M        s  勄d姞
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N N M        J  X凴�& M        %  凴
4
娚 M        �  4刜�
 M        3  1刡�
  M        c  刲)娹
 Z   �  
 >   _Ptr  AH  l      AJ  i      AH �      >#    _Bytes  AK  b    1 �
 M        s  剈d婌
 Z   �   >_    _Ptr_container  AH  �      AJ  }      N N N N N N M        N  凟 M        4  凟
 Z      N N M        T.  6� M        e.  �)
 M        �  �,
 >�   this  AI      e� a
 M        b  �,	 N N N N M        �4  M兙 M        T.  +冟 M        e.  冟) M        �  冣, M        b  凍	 N N N N M        �4  兲 M        �4  兲 M        e  冃
 >�    _Tmp  AK  �    1  AK     F !   N M        �4  兲 N N N M        �4  兙 M        �4  兙#D N N N M        S  e傏h4
 M        M  傿
 M        -  0僒 M        �  僒 N N M        @  傿
 M        �  傿��
 M          傿 N N N N M        E  e傏& M        +  傏&F(-$$
 Z   �4   >_    _Old_size  AH  �    _ 
 1  AJ  �    �
= � k M        �  L傼 >�   _First1  AI  �      AI ?    ,  N M        0  傤	
 >嘚   this  AI  �    	  >p    _Result  AI  �      N N N N" M        �4  !仮�$
崊	" M        �  !仮�$
崊	" M        �  	伜�
崊	
 Z   (!  > M        �  伷$!&
#z*
尃	 Z   �"  �"  
   M        �  伷 M        &  伿'	 N M        i  伷 M        �  伷 M          伷 N N N N M        N  偳	寷
 Z       N M        �  倶 M        0  倶
	
 >嘚   this  AQ  �    
  >p    _Result  AQ  �      N N M        �  �!)V >_   _New_size  AJ  !    r R   AJ �      M        
  �* M        0  �*

 >嘚   this  AH  .    
  >p    _Result  AH  8    
  AH �    !  N N$ M          俁E$-$
 Z   �4   >_   _Count  AH  R    F &   AL  E    
  M        �  
俴 >�   _First  AI  k      AI �    �Z ) � � N M        0  俒	
 >嘚   this  AI  _    	  >p    _Result  AI  h      N N N M        N  ��>
 Z       N N N M          仮 M        F  伋 N M          仮
 N N N N M        O  丮( M        C  &丵( M        ?   乤 N N M        A  丮 M        �  丮 M          丮 N N N N M        �  �� N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �.  �� M        �.  ��� N N M        �1  w M        F2  M M        k2  	 M        �  �� N N N M        �1  �w N N M        �  d M        �  h	 N N" Z   �2  02  02  02  02  02  �1              8         A �hm   �  �  b  r  s  t  v  x  y  �  �  N  �      F  N  �  �    4  5  A  E  J  K  M  O  S  [  \    $  %  &  +  -  0  3  >  ?  �  �  �  �  �  �  �  S  u  �  �  �  �  <  @  A  C  ^  b  c  e  k  l  s  �  �  �  �  �  �  �  �  �  
          i  �  �  �  '  (  �  /   9   (  &(  T.  e.  �.  �.  1  g1  �1  �1  �1  ,2  F2  k2  �4  �4  �4  �4  �4  �4  �4  
 :  O        $LN1481  `  &�  Othis  h  ))  Odevice  p    OtextureCache  x  鱵  OcommonPasses  �   )  OcommandList  x  ?  Opath  9s       E   9�      x   9�      �   9      �   9*      �   9>      �   96      x   9n      �   9�      �   9�      �   9�      �   9�
      x   9I      x   9�
      x   9�      *)   9�      )   9�      )   9�      *)   O  �   X          �  �
               �8        a   =  ��
     $   d     �w     ��      �8     l   �   H  ��   I  ��   J  ��   K  ��   L  �  M  �  N  �  O  �"  Q  �-  R  �8  S  �C  V  ��
     �   M  !  ��  #  ��  $  �]  %  �i  &  �	
  '  ��  *  ��  +  ��  ,  ��  -  ��  /  ��  0  �E  '  �K  #  �x  $  ��  %  ��  &  ��   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$0 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$1 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$2 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$3 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$4 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$5 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$6 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$7 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Y  ] F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$8 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O   �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$10 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F            )      #             �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$37 
 >&�   this  EN          #  EN  `        #  >   textureCache  EN  �         #  EN  p        #  >鱵   commonPasses  EN  �         #  EN  x        # 
 >?    path  EN  x        #                        �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$11 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$12 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$13 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F            .      (             �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$53 
 >&�   this  EN          (  EN  `        (  >   textureCache  EN  �         (  EN  p        (  >鱵   commonPasses  EN  �         (  EN  x        ( 
 >?    path  EN  x        (                        �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$15 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$16 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$17 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F            .      (             �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$69 
 >&�   this  EN          (  EN  `        (  >   textureCache  EN  �         (  EN  p        (  >鱵   commonPasses  EN  �         (  EN  x        ( 
 >?    path  EN  x        (                        �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$19 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$20 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$21 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$23 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$24 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$25 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$27 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$28 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  �   Z  ^ F                                �`SampleProceduralSky::SampleProceduralSky'::`1'::dtor$29 
 >&�   this  EN            EN  `          >   textureCache  EN  �           EN  p          >鱵   commonPasses  EN  �           EN  x         
 >?    path  EN  x                                 �  O  ,   �   0   �  
 {   �      �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 +  �   /  �  
 f  �   j  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 (  �   ,  �  
 @  �   D  �  
 X  �   \  �  
 5  �   9  �  
 E  �   I  �  
 ]  �   a  �  
 p  �   t  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
   �     �  
 w  �   {  �  
 �  �   �  �  
 }  �   �  �  
 �	  �   �	  �  
 �	  �   �	  �  
 �	  �   �	  �  
 �	  �   �	  �  
 .
  �   2
  �  
 >
  �   B
  �  
 �  �   �  �  
 �  �   �  �  
 �
  �   �
  �  
 j  �   n  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 �  �   �  �  
 �  �     �  
   �     �  
 0  �   4  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �   
  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 *  �   .  �  
 g  �   k  �  
 �  �   �  �  
 d  �   h  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 5  �   9  �  
 W  �   [  �  
 g  �   k  �  
 �  �   �  �  
 �  �   �  �  
 
  �     �  
   �   !  �  
 b  �   f  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 o  �   s  �  
   �   �  �  
 Z  �   ^  �  
 j  �   n  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 (   �   ,   �  
 8   �   <   �  
 u   �   y   �  
 �   �   �   �  
 ~"  �   �"  �  
 �"  �   �"  �  
 �"  �   �"  �  
  #  �   #  �  
 O#  �   S#  �  
 q#  �   u#  �  
 �#  �   �#  �  
 �#  �   �#  �  
 �#  �   �#  �  
 '$  �   +$  �  
 d$  �   h$  �  
 �$  �   �$  �  
 �%  �   �%  �  
 �%  �   �%  �  
 �%  �   �%  �  
 &  �   &  �  
 q&  �   u&  �  
 �&  �   �&  �  
 \'  �   `'  �  
 l'  �   p'  �  
 |'  �   �'  �  
 �'  �   �'  �  
 �'  �   �'  �  
 (  �   
(  �  
 �(  �   �(  �  
 �)  �   �)  �  
 �)  �   �)  �  
 �+  �   �+  �  
 �+  �   �+  �  
 �+  �   �+  �  
 �+  �   �+  �  
 3,  �   7,  �  
 U,  �   Y,  �  
 0.  �   4.  �  
 R.  �   V.  �  
 �.  �   �.  �  
 �.  �   �.  �  
 /  �   /  �  
 #/  �   '/  �  
 3/  �   7/  �  
 �/  �   �/  �  
 �/  �   �/  �  
 �/  �   �/  �  
 �/  �   �/  �  
 .0  �   20  �  
 P0  �   T0  �  
 �5  l   �5  l  
 26  �   66  �  
 B6  �   F6  �  
 R6  �   V6  �  
 b6  �   f6  �  
 r6  �   v6  �  
 �6  �   �6  �  
 �6  �   �6  �  
 �6  �   �6  �  
 �6  �   �6  �  
 �6  �   �6  �  
 �6  �   �6  �  
 �6  �   �6  �  
 �6  �   �6  �  
 7  �   7  �  
 7  �   7  �  
 "7  �   &7  �  
 27  �   67  �  
 B7  �   F7  �  
 \7  �   `7  �  
 �8     �8    
 69     :9    
 J9     N9    
 u9     y9    
 �9     �9    
 �9     �9    
 �9     �9    
 �9     �9    
 @:      D:     
 �:      �:     
 �:      �:     
 �:      �:     
 �:      �:     
 ;      ;     
 ,;      0;     
 O;      S;     
 �;  *   �;  *  
 �;  *   <  *  
 <  *   <  *  
 =<  *   A<  *  
 Q<  *   U<  *  
 |<  *   �<  *  
 �<  *   �<  *  
 �<  *   �<  *  
 =  -   =  -  
 b=  -   f=  -  
 v=  -   z=  -  
 �=  -   �=  -  
 �=  -   �=  -  
 �=  -   �=  -  
 �=  -   �=  -  
 >  -   >  -  
 l>  .   p>  .  
 �>  .   �>  .  
 �>  .   �>  .  
 ?  .   	?  .  
 ?  .   ?  .  
 D?  .   H?  .  
 X?  .   \?  .  
 {?  .   ?  .  
 �?  0   �?  0  
 *@  0   .@  0  
 >@  0   B@  0  
 i@  0   m@  0  
 }@  0   丂  0  
 ˊ  0   珸  0  
 粿  0   繞  0  
 這  0   鉆  0  
 4A  2   8A  2  
 嶢  2   扐  2  
   2     2  
 虯  2   袮  2  
 酇  2   錋  2  
 B  2   B  2  
  B  2   $B  2  
 CB  2   GB  2  
 楤  3   淏  3  
 駼  3   鯞  3  
 C  3   
C  3  
 1C  3   5C  3  
 EC  3   IC  3  
 pC  3   tC  3  
 凜  3   圕  3  
   3   獵  3  
 麮  4    D  4  
 VD  4   ZD  4  
 jD  4   nD  4  
 旸  4   橠  4  
 〥  4   璂  4  
 訢  4   谼  4  
 鐳  4   霥  4  
 E  4   E  4  
 `E     dE    
 籈     縀    
 螮     覧    
 鶨     﨓    
 F     F    
 9F     =F    
 MF     QF    
 pF     tF    
 腇  +   菷  +  
 G  +   #G  +  
 3G  +   7G  +  
 ^G  +   bG  +  
 rG  +   vG  +  
 滸  +     +  
 盙  +   礕  +  
 訥  +   谿  +  
 (H     ,H    
 僅     嘓    
 桯     汬    
 翲     艸    
 諬     贖    
 I     I    
 I     I    
 8I     <I    
 孖     怚    
 鏘     隝    
 鸌     �I    
 &J     *J    
 :J     >J    
 eJ     iJ    
 yJ     }J    
 淛     燡    
 餔     鬔    
 KK     OK    
 _K     cK    
 奒     嶬    
 濳         
 蒏     蚄    
 軰     酜    
  L     L    
 TL  /   XL  /  
 疞  /   矻  /  
 肔  /   荓  /  
 頛  /   騆  /  
 M  /   M  /  
 -M  /   1M  /  
 AM  /   EM  /  
 dM  /   hM  /  
 窶     糓    
 N     N    
 'N     +N    
 RN     VN    
 fN     jN    
 慛     昇    
      ㎞    
 萅     蘊    
 O      O    
 wO     {O    
 婳     廜    
 禣     篛    
 蔕     蜲    
 鮋     鵒    
 	P     
P    
 ,P     0P    
 �P     凱    
 跴     逷    
 颬     驪    
 Q     Q    
 .Q     2Q    
 YQ     ]Q    
 mQ     qQ    
 怮     擰    
 銺  1   鑁  1  
 ?R  1   CR  1  
 SR  1   WR  1  
 ~R  1   俁  1  
 扲  1   朢  1  
 絉  1   罵  1  
 裄  1   誖  1  
 鬜  1   鳵  1  
 HS     LS    
          
 稴     籗    
 釹     鍿    
 鯯     鶶    
 !T     %T    
 5T     9T    
 XT     \T    
 琓  !   癟  !  
 U  !   U  !  
 U  !   U  !  
 FU  !   JU  !  
 ZU  !   ^U  !  
 匲  !   塙  !  
 橴  !   漊  !  
 糢  !   繳  !  
 V  "   V  "  
 kV  "   oV  "  
 V  "   僔  "  
 猇  "   甐  "  
 綱  "   耉  "  
 閂  "   鞻  "  
 齎  "   W  "  
  W  "   $W  "  
 tW  #   xW  #  
 蟇  #   覹  #  
 鉝  #   鏦  #  
 X  #   X  #  
 "X  #   &X  #  
 MX  #   QX  #  
 aX  #   eX  #  
 刋  #   圶  #  
 豖  $   躕  $  
 3Y  $   7Y  $  
 GY  $   KY  $  
 rY  $   vY  $  
 哬  $   奩  $  
 盰  $   礩  $  
 臲  $   蒠  $  
 鑉  $   靁  $  
 <Z  %   @Z  %  
 梈  %   沍  %  
 玓  %   痁  %  
 諾  %   赯  %  
 闦  %   頩  %  
 [  %   [  %  
 )[  %   -[  %  
 L[  %   P[  %  
 燵  &     &  
 鸞  &   �[  &  
 \  &   \  &  
 :\  &   >\  &  
 N\  &   R\  &  
 y\  &   }\  &  
 峔  &   慭  &  
 癨  &   碶  &  
 ]  '   ]  '  
 _]  '   c]  '  
 s]  '   w]  '  
 瀅  '     '  
 瞉  '   禲  '  
 輂  '   醈  '  
 馷  '   鮙  '  
 ^  '   ^  '  
 h^  (   l^  (  
 胇  (   荿  (  
 譤  (   踍  (  
 _  (   _  (  
 _  (   _  (  
 A_  (   E_  (  
 U_  (   Y_  (  
 x_  (   |_  (  
 H媻�   �       �   H媻�   �       �   H媻  H兞�       �   H媻  H兞�       �   H媻  H兞 �       �   H媻  H兞0�       �   H媻  H兞@�       �   H媻  H兞P�       �   H媻  H兞`�       �   H崐x  �       �   H崐X  �       �   H崐�  �       �   H崐x  �       �   H崐  �       �   H崐�  �       �   H崐�  �       �   H崐8  �       �   H崐�  �       �   H崐�  �       �   H崐8  �       �   H崐�  �       �   H崐�  �       �   H崐X  �       �   H崐  �       �   H崐�  �       �   @UH冹 H嬯婨4冟吚t僥4鱄崓X  �    H兡 ]�   �   @UH冹 H嬯婨4%   吚t乪4���H崓  �    H兡 ]�$   �   @UH冹 H嬯婨4%    吚t乪4���H崓8  �    H兡 ]�$   �   H塡$WH冹 H嬟H�    H�H峇H孂W�H岾�    H�    H�H嬊CH媆$0GH兡 _�   T   )   �   0   �      �   /  G G            M   
   >           �std::_System_error::_System_error 
 >(/   this  AJ          AM       .  >//   __that  AI  
     6  AK        
  M        �  
	
 Z   �   N                       H�  h   �  �   0   (/  Othis  8   //  O__that  O ,   �   0   �  
 l   �   p   �  
 |   �   �   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   T   %   �   ,   Z      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   T   %   �   ,   ]      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H�    H茿    H堿H�    H�H嬃�   `      ]      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !         ,       �  �    �  �   �  �   �  �,   �   0   �  
 z   �   ~   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   T   %   �      �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2         $       H  �   I  �)   J  �,   �   0   �  
 d   �   h   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   T   %   �   ,   �      �   +  G G            <      6   �        �std::runtime_error::runtime_error 
 >.   this  AI  	     2  AJ        	  >!.   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   .  Othis  8   !.  O__that  O ,   �   0   �  
 l   �   p   �  
 |   �   �   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H嬟H�    H�H峇H孂W�H岾�    H�    H�H�    CH媆$0H�H嬊GH兡 _�   T   )   �   0   �   :   �      �   1  E G            W   
   B           �std::system_error::system_error 
 >F/   this  AJ          AM       8  >K/   __that  AI  
     :  AK        
  M        �  
	
 Z   �   N                       @�  h   �  �     0   F/  Othis  8   K/  O__that  O   ,   �   0   �  
 j   �   n   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹`H�    H3腍塂$PH塋$ H嬞H婮H孃D�H峊$0H��PH億$HH�
    H�H峉W榔D$(H岲$0HGD$0H峀$ H塂$ �    H婽$HH�    H�H凓v.H婰$0H�翲嬃H侜   rH婭鳫兟'H+罤兝鳫凐w4�    H�    H�H嬅CH婰$PH3惕    H嫓$�   H兡`_描    �
   �   ?   T   k   �   w   �   �   �   �   �   �   9   �   �      �   b  E G            �      �           �std::system_error::system_error 
 >F/   this  B         4  AI  !     � �   AJ        !  Dp    >�.   _Errcode  AK        (  AM  (     � �   M          !"h%u M        J  o4c M        %  o.] M        �  o N M        �  .��] M        3  ��&U M        c  ��)4
 Z   �  
 >   _Ptr  AH  �       AJ  �       AH �       >#    _Bytes  AK  �     Z & /  M        s  
��
>
 Z   �   >_    _Ptr_container  AH  �       AJ  �       N N N N N N M        �  6"
' M        p  <
 Z   �   >�    _InitData  B    R     �  N M        B  6 M        /  6 >�    _Result  AH  W       M        �  6 N N N N M        �  ! N N `                     A b h   �  p  s  t  �  �  �  �    B  J  K  $  %  /  3  �  �  �  �  �  ^  c  
 :P   O        $LN60  p   F/  Othis  x   �.  O_Errcode  93       �.   O  �               �   �            � �,   �   0   �  
 j   �   n   �  
 z   �   ~   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 Z  �   ^  �  
 j  �   n  �  
 �  �   �  �  
 D  �   H  �  
 #  ^   '  ^  
 ^  �   b  �  
 x  �   |  �  
 H冹(H�� �    怘兡(�   �      �   �  	F                     �4        �GenericScope<`SampleProceduralSky::DebugGUI'::`2'::<lambda_1>,`SampleProceduralSky::DebugGUI'::`2'::<lambda_2> >::~GenericScope<`SampleProceduralSky::DebugGUI'::`2'::<lambda_1>,`SampleProceduralSky::DebugGUI'::`2'::<lambda_2> > 
 >j�   this  AJ          M        �4  
 Z    5   N (                     0H� 
 h   �4   0   j�  Othis  O  �                  �            !  �,      0     
 .     2    
 �     �    
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >   this  AH         AJ          AH        M        �  GCE
 >))    temp  AJ  
       AJ        N (                     0H� 
 h   �   0     Othis  9       E   O  �   0           "   �     $       �  �   �  �   �  �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 B  �   F  �  
 \  �   `  �  
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;   �   Y   �      �   �  � G            ^      ^   J        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >�   this  AI  
     Q J   AJ        
 ) M        %  ,(
	 M        �   N M        �  ,E M        3  &? M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        s  
"#
!
 Z   �   >_    _Ptr_container  AP  &     7    AP :       >_    _Back_shift  AJ  -     0 
   N N N N N                       H� : h
   �  s  t  K  $  %  3  �  �  �  �  ^  c         $LN35  0   �  Othis  O�   H           ^        <       B �   C �
   B �
   C �R   J �X   C �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 ,  �   0  �  
 @  �   D  �  
 f  �   j  �  
 �  Q   �  Q  
   �     �  
 �       �      �   ,  � G                       4        �std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > 
 >�   this  AJ         
 Z                             H� 
 h   5      �  Othis  O�   (                          B �    C �,   �   0   �  
 �   �   �   �  
 @  �   D  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  ~ G            K      E   T.        �std::shared_ptr<donut::engine::LoadedTexture>::~shared_ptr<donut::engine::LoadedTexture> 
 >謮   this  AJ        +  AJ @       M        e.  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  e.   0   謮  Othis  9+       �   9=       �   O�   0           K   x      $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �   
  �  
 |  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  � G            K      E   (        �std::shared_ptr<donut::engine::CommonRenderPasses>::~shared_ptr<donut::engine::CommonRenderPasses> 
 >z   this  AJ        +  AJ @       M        &(  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  &(   0   z  Othis  9+       �   9=       �   O  �   0           K   x      $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 @SH冹 H媃H呟t6H墊$0����嬊�罜凐uH�H嬎��羬�u	H�H嬎�PH媩$0H兡 [�   �   �  | G            K      E   �1        �std::shared_ptr<donut::engine::TextureCache>::~shared_ptr<donut::engine::TextureCache> 
 >刊   this  AJ        +  AJ @       M        �1  &, M        �  
 >�   this  AI  
     @  M        b  -	
 N N N                       H�  h   b  �  �1   0   刊  Othis  9+       �   9=       �   O  �   0           K   x      $       � �   � �E   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
 H塡$H塼$WH冹 H孂H峇`H�: t	H婭�    H媤h����H咑t)嬅�罠凐uH�H嬑�嬅�罠凐u	H�H嬑�PH媤XH咑t)嬅�罠凐uH�H嬑�嬅�罠凐u	H�H嬑�PH媤HH咑t)嬅�罠凐uH�H嬑�嬅�罠凐u	H�H嬑�PH媤8H咑t)嬅�罠凐uH�H嬑�嬅�罠凐u	H�H嬑�PH媤(H咑t)嬅�罠凐uH�H嬑�嬅�罠凐u	H�H嬑�PH媤H咑t(嬅�罠凐uH�H嬑��羄凔u
H�H嬑�P怘婳H吷tH荊    H��P怘媆$0H媡$8H兡 _�!   �      �   �  O G            }     m  
3        �SampleProceduralSky::~SampleProceduralSky 
 >&�   this  AJ          AM       j M        �  乁 M        �  乁DE
 >))    temp  AJ  Y      AJ m      N N M        �1  0�$ M        �1  �$'	 M        �  �-,
 >�   this  AL  (    O  M        b  丄	
 N N N N M        T.  2�� M        e.  ��)	 M        �  ��,
 >�   this  AL  �     2  M        b  �	 N N N N M        T.  2�� M        e.  ��)	 M        �  ��,
 >�   this  AL  �     2  M        b  ��	 N N N N M        T.  2�� M        e.  ��)	 M        �  ��,
 >�   this  AL  �     2  M        b  ��	 N N N N M        T.  2\ M        e.  \)	 M        �  e,
 >�   this  AL  `     2  M        b  y	 N N N N M        T.  7% M        e.  %) M        �  3,
 >�   this  AL  )     7  M        b  G	 N N N N
 Z   b3                        @� 2 h   b  �  �  �  T.  e.  �1  �1  ,2  �4  �4   0   &�  Othis  9E       �   9Y       �   9w       �   9�       �   9�       �   9�       �   9�       �   9�       �   9
      �   9!      �   9?      �   9Q      �   9i      E   O  �   8           }  �
     ,       3  �   4  �   5  �%   6  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 Z  �   ^  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  �   �  �  
 <  �   @  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
   �     �  
 "  �   &  �  
 2  �   6  �  
 B  �   F  �  
 R  �   V  �  
 b  �   f  �  
 r  �   v  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 H�	�       �      �   �   X G                              �std::_System_error_message::~_System_error_message 
 >u/   this  AJ         
 Z   �"                          H�     u/  Othis  O  �   (              �             �     �,   �   0   �  
 }   �   �   �  
 �   �   �   �  
 H�    H�H兞�       T      �      �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,   �   0   �  
 {   �      �  
 �     �   �   J G                       �        �std::error_category::~error_category 
 >�.   this  AJ          D                           H�     �.  Othis  O�                  �            W  �,   �   0   �  
 o   �   s   �  
 �   �   �   �  
 H�    H�H兞�       T      �      �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   8                          H�     �  Othis  O  �   (                           Y  �
   Z  �,   �   0   �  
 e   �   i   �  
 �   �   �   �  
 �       �      �   �   B G                       N        �std::filesystem::path::~path 
 >�>   this  AJ          M        4    N                        H�  h   4  5      �>  Othis  O   �                               � �,   �   0   �  
 g   �   k   �  
 �   �   �   �  
 H�    H�H兞�       T      �      �   �   F G                      	        �std::system_error::~system_error 
 >F/   this  AJ          M        �   	
 N                        H�  h   �  �        F/  Othis  O ,   �   0   �  
 k   �   o   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   ` G            !                 �std::_Generic_error_category::`scalar deleting destructor' 
 >�/   this  AI  	       AJ        	                        @� 
 h      0   �/  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�(   H嬒�    H媆$0H嬊H兡 _�
   T      �   0   �      �   �   V G            B   
   4           �std::_System_error::`scalar deleting destructor' 
 >(/   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �     0   (/  Othis  O ,   �   0   �  
 {   �      �  
 �   �   �   �  
 @SH冹 H嬞雎t
�   �    H嬅H兡 [�   �      �   �   _ G            !                 �std::_System_error_category::`scalar deleting destructor' 
 >�/   this  AI  	       AJ        	                        @� 
 h      0   �/  Othis  O   ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   T      �   0   �      �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   T      �   0   �      �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �  �   0   �  Othis  O  ,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   T      �   0   �      �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @� 
 h   �   0   �  Othis  O ,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   T      �   0   �      �   �   V G            B   
   4   �        �std::runtime_error::`scalar deleting destructor' 
 >.   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   .  Othis  O ,   �   0   �  
 {   �      �  
 �   �   �   �  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�(   H嬒�    H媆$0H嬊H兡 _�
   T      �   0   �      �   �   U G            B   
   4           �std::system_error::`scalar deleting destructor' 
 >F/   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �    	   0   F/  Othis  O  ,   �   0   �  
 z   �   ~   �  
 �   �   �   �  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,   �   0   �  
 g   �   k   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 !  �   %  �  
 1  �   5  �  
 A  �   E  �  
 �  �   �  �  
 H塡$�L$UVWH冹@)t$0H孂H�
    �    H岲$hH塂$`�D$h�    �3鰤t$(H�-    H塴$ W�W襀峎|H�
    �    W荔_G|�]    �G|塼$(H塴$ W�W襀崡�   H�
    �    W荔_噣   �]    �噣   塼$(H塴$ W�W襀崡�   H�
    �    W荔_噭   �]    �噭   塼$(H塴$ W�W襀崡�   H�
    �    W荔_噲   �]    �噲   塼$(H塴$ �    �5    (諬崡�   H�
    �    �_穼   �]5    �穼   塼$(H塴$ �    �5    (諬崡�   H�
    �    �_窅   �]5    �窅   塼$(H塴$ W�W襀崡�   H�
    �    �    �_嚁   �]    �嚁   H崡�   塼$(H塴$ �    W襀�
    �    愺D$h�    怘媆$p(t$0H兡@_^]�   �   "   �   7   �   E   �   [   �   `   �   p   �   �   �   �   �   �   �   �   �   �   �   �   �     �     �   $  �   =  �   E  �   V  �   [  �   k  �   �  �   �  �   �  �   �  �   �  �   �  �   �  �   �  p   �  �     �     �   #  �   /  �      �   t  C G            F     4  �4        �SampleProceduralSky::DebugGUI 
 >&�   this  AJ          AM       ) >@    indent  A�         &  Dh   ! >p�    _generic_raii_scopevar_0  B`   0      M        �4  �( M        �4  �(
 Z    5   N N M        3+  佮 M        I+  侌 N M        H+  佮 N N M        3+  仸 M        I+  伄 N M        H+  仸 N N M        3+  乢 M        I+  乬 N M        H+  乢 N N M        3+  � M        I+  �  N M        H+  � N N M        3+  �� M        I+  �� N M        H+  �� N N M        3+  �� M        I+  �� N M        H+  �� N N M        3+  
g M        I+  l N M        H+  g N N M        �4  & M        �4  0
 Z   5   N N* Z	   �4  5  5  5  5  5  5  5  5   @                    @ * h	   3+  H+  I+  �4  �4  �4  �4  �4  �4   `   &�  Othis  h   @   Oindent % `   p�  O_generic_raii_scopevar_0 6 b�  SampleProceduralSky::DebugGUI::__l2::<lambda_2> 6 K�  SampleProceduralSky::DebugGUI::__l2::<lambda_1>  O�   x           F  �
     l       �  �   �  �&   �  �<   �  �y   �  ��   �  ��   �  �0  �  �w  �  ��  �  �   �  �(  �  ��   �   R F                                �`SampleProceduralSky::DebugGUI'::`1'::dtor$0  >@    indent  EN  h                                  �  O ,   �   0   �  
 h   �   l   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 (     ,    
 y     }    
 H崐`   �          @SH冹0H嬟H婣PH�H�
H吷tH��P怘嬅H兡0[�   �   -  K G            (      "   3        �SampleProceduralSky::GetCloudsTexture 
 >,�   this  AJ          M        �'  = >OH   other  AH  
       AH        M        �   N N 0                    @  h   �  �'  �4   @   ,�  Othis  9       E   O   �               (   �
            ;  �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 �   �   �   �  
 )  �   -  �  
 D  �   H  �  
 @SH冹0H嬟H婣@H�H�
H吷tH��P怘嬅H兡0[�   �   1  O G            (      "   
3        �SampleProceduralSky::GetIrradianceTexture 
 >,�   this  AJ          M        �'  = >OH   other  AH  
       AH        M        �   N N 0                    @  h   �  �'  �4   @   ,�  Othis  9       E   O   �               (   �
            :  �,   �   0   �  
 t   �   x   �  
 �   �   �   �  
 �   �   �   �  
 -  �   1  �  
 H  �   L  �  
 @SH冹0H嬟H婣`H�H�
H吷tH��P怘嬅H兡0[�   �   ,  J G            (      "   3        �SampleProceduralSky::GetNoiseTexture 
 >,�   this  AJ          M        �'  = >OH   other  AH  
       AH        M        �   N N 0                    @  h   �  �'  �4   @   ,�  Othis  9       E   O�               (   �
            <  �,   �   0   �  
 o   �   s   �  
 �   �   �   �  
 �   �   �   �  
 (  �   ,  �  
 @  �   D  �  
 @SH冹0H嬟H婣0H�H�
H吷tH��P怘嬅H兡0[�   �   2  P G            (      "   3        �SampleProceduralSky::GetScatterringTexture 
 >,�   this  AJ          M        �'  = >OH   other  AH  
       AH        M        �   N N 0                    @  h   �  �'  �4   @   ,�  Othis  9       E   O  �               (   �
            9  �,   �   0   �  
 u   �   y   �  
 �   �   �   �  
 �   �   �   �  
 .  �   2  �  
 H  �   L  �  
 @SH冹0H嬟H婣 H�H�
H吷tH��P怘嬅H兡0[�   �   4  R G            (      "   3        �SampleProceduralSky::GetTransmittanceTexture 
 >,�   this  AJ          M        �'  = >OH   other  AH  
       AH        M        �   N N 0                    @  h   �  �'  �4   @   ,�  Othis  9       E   O�               (   �
            8  �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
 �   �   �   �  
 0  �   4  �  
 H  �   L  �  
 H嬆H塜H塸H墄L塦 UAVAWH峫$怘侅p  )p豈嬸)x菻嬹D)@�3褼)Hˋ笭   I嬑D)P業嬞(    �n|�   (朋YFp(腕Vt�^x�Y捏Y鼠AF@�Y塍Y腆Y祗AND驛nH�唲   �5    Z�(悟Y氰    W蒃3潋Z润AN\A荈`殭�>A荈d殭�>A荈h殭�>�灁   �^    驞    驛Y隗A^l�杸   (�(鼠Y    驛�Y
    Z皿AN�Y    驛V驛^A荈�!�;A荈`!^<A荈邠=A荈 榔EA荈 Qi�:A荈$Qi�:A荈(Qi�:�Y    A荈, 犎EA荈0吞L?A荈4 5;II荈8  pB�    �    (蓑\萬Z洋Y    驛Vx媶�   A墕�   媶�   A墕�   A菃�   fff?A菃�   殭�>媶�   A墕�   A菃�   股�;A菃�   股�;A菃�   股�;�唸   �帉   Z�Z沈Y球^乞X硫
    �X描    L媨W鲶D
    H嬎H媨�Z痼A\馡�vH�H�u(L嬊H�    �    吚u�钉   �定   椤  �5    W繦嬎�\>�_球]    fZ鳬�vH�H�u L嬊H�    �    吚u
�5    榱   H嬎I�vH�H�u L嬊H�    �    吚u
�5    閺   H嬎I�vH�H�uL嬊H�    �    吚u
�5    隸H嬎I�vH�H�uL嬊H�    �    吚u
�5    �1I�vH�H�u"L嬊H�    H嬎�    吚uM塮@A(馝塮H�Y=    驞    AT�W=    (氰    A(审\菵8グ   tA(审啫   (煮\畜Y洋X畜啢   �枿   �\畜Y�(误X畜    �\鼠枻   AT�/羨(蝮AY�(畦    (�(畦    (餎W�(�(求Y求Y误AX荔X�W�.葁�Q离�    �^鳫峊$ L塪$ H峀$`驞^荔^痼啇   �Y    �D$(�    H峊$ D塪$ H峂惽D$$吞尶H孁D塪$(�    H峊$ 荄$ 吞L縃峂繪塪$$H嬝�    L嬅H峂餒嬓�    L嬊H峀$0H嬓�    A(豀崬�   �Y\$<A(蠥笭   H嬘�YT$@(荌嬑�YD$0(误YL$H驞YD$D�X�(求Y|$8�YD$4�X�(误YL$L驞X求Yt$P�X畜DX企X�隍A^P驟FX�    A吚暲ANKAF C AN0K0AF@C@ANPKPAF`C`AFpCpA巰   媭   A啇   L崪$p  I媠(I媨0M媍8A(s餉({郋(C蠩(K繣(S�儛   I媅 I嬨A_A^]肧   A   �   �   �   =   �   �   �   �     �   )  �   :  �   �  �   �  :   �  �   �  �   T  �   ]  =   m  �   �  �   �  >   �  �   �  �   �  �   �  >      �   !  �   &  >   2  v   S  �   X  >   d  |   �  �   �  >   �     �  �   �  >   �  y   �  �   �  �   �  <   =  m   b  ;   m  B   �  C   �  s   �     �          %     5     �  >      �   �  A G            h  L   '  3        �SampleProceduralSky::Update 
 >&�   this  AJ        2  AL  2      >A    sceneTime  A�         W  >/�   outConstants  AP        +  AV  +     ; >�   presetType  AQ        W  >0    forceInstantUpdate  EO  (           D�   >@     cloudsTime  A�   �     P  >@     timeOfTheDay  A�   �    ?  A�  Y      >;    rotateZ  A�   �      >錏   sunDir  C�      �    �  >揈    earthRot  D0    >0     changes  A   �    �  >@     deltaTime  A�   �    �  >@     lerpK  A�   �    ?  >@     timeOfDayTarget  A�   �      A�  �    �  M        �4  侾 >A    _Left  A�   \      N M        �  � N M        �4  �� >A    _Left  A�   �       N M        �  y N M        �4  d
 N M        �  W N M        �,  俛" M        d-  俛" M        +  倞 M        {  倫 N N" M        /  俛 >�    _Result  AJ  t    +  AJ �      M        �  俛  N N N N M        �4  偸 >A    value  A�   �      M        �4  偽 N M        �4  偸 N N M        �,  偯" M        d-  偯" M        +  傘 M        {  傞 N N M        /  偯 >�    _Result  AJ  �    2  AJ 	      M        �  傏 N N N N M        �,  %�	 M        d-  %�	 M        +  � M        {  � N N M        /  �	# >�    _Result  AJ        AJ ;      M        �  � N N N N M        �,  %�; M        d-  %�; M        +  僄 M        {  僊 N N M        /  �;# >�    _Result  AJ  >      AJ j      M        �  �> N N N N M        �,  %僯 M        d-  %僯 M        +  僾 M        {  億 N N M        /  僯# >�    _Result  AJ  m      AJ �    U    M        �  僲 N N N N M        �,  %儥 M        d-  %儥 M        +  儮 M        {  儴 N N M        /  儥 >�    _Result  AI  O     � M        �  儥 N N N N M        �4  ,兪 M        �  
円 N N M        ]  凪 >@    _Xx  A�   E      M        �  凪 N N M        �4  �*
 >@    a  A�   "      N M        �4  �
 >@    a  A�         N M        �  劔# N M        �4  劸 N M        �  剎	# M        �  	劦 M        �  	劦 >@    _x  A  �    �  N N M        �  剎 M        d  剴 >@    _Xx  A�   �        N M        �  剎 M        �  剎 N N N N M        �4  刕 N M        �4  刬 N  M        �4  匤	
/1 N M        �  � N M        �  勣	 N Z   �4  �4  �4  �4  �4   p                    @ � h   �  ]  d  �  �    /  >  �  �  �  �  �  �  �  +  {  �  �,  d-  �4  �4  �4  �4  �4  �4  �4  �4  �4  �4  �4   �  &�  Othis  �  A   OsceneTime  �  /�  OoutConstants  �  �  OpresetType  �  0   OforceInstantUpdate  0   揈  OearthRot  O�             h  �
  ?         E  �7   F  �W   H  ��   K  ��   [  �  R  �-  ]  �6  R  �D  S  �J  T  �b  U  �j  V  ��  ]  ��  _  ��  `  ��  a  ��  b  �   c  �
  d  �>  h  �a  j  �e  h  �q  j  �x  h  ��  j  ��  �  ��  n  ��  o  ��  l  ��  o  ��  p  �	  q  �.  r  �;  s  �`  t  �j  u  ��  v  ��  w  ��  y  ��  z  ��  ~  ��  �  ��  �  �  �  �  �  �2  �  �5  �  �9  �  �Y  �  ��  �  ��  �  ��  �  ��  �  �=  �  �J  �  �N  �  �W  �  �`  �  �c  �  ��  �  ��  �  ��  �  �'  �  �,   �   0   �  
 f   �   j   �  
 v   �   z   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 '  �   +  �  
 T  �   X  �  
 {  �     �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 3  �   7  �  
 S  �   W  �  
 }  �   �  �  
 �  �   �  �  
 �  �   �  �  
    �   $  �  
 $  �   (  �  
 4  �   8  �  
 �  �   �  �  
 l  �   p  �  
 |  �   �  �  
 @  �   D  �  
 P  �   T  �  
   �     �  
 $  �   (  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 L	  �   P	  �  
 �	  �   �	  �  
 �	  �   �	  �  
 g
  �   k
  �  
 �
  �   �
  �  
 �  �   �  �  
 H冹(H嬄I峆H侜   rH婬鳫兟'H+罤兝鳫凐wH嬃H嬋H兡(�    �    �4   �   9   �      �   �  � G            >      >   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Deallocate_for_capacity  >   _Al  AJ          AJ ,       D0    >�   _Old_ptr  AK          >_   _Capacity  AP        =  M        3  $
(  M        c  )
 >   _Ptr  AH ,       >#    _Bytes  AK       2 " M        s  
#

 Z   �   >_    _Ptr_container  AJ       %    AJ ,       >_    _Back_shift  AH         AH ,       N N N (                      H�  h   �  s  3  c         $LN23  0     O_Al  8   �  O_Old_ptr  @   _  O_Capacity  O�   8           >        ,       D
 �   F
 �/   G
 �3   F
 �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 `  �   d  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 $  �   (  �  
 r  O   v  O  
 �  �   �  �  
 H塡$WH冹 ����H嬞嬊�罙凐uH���羬�u	H�H嬎�PH媆$0H兡 _�   �   �   C G            A   
   6   �        �std::_Ref_count_base::_Decref 
 >�   this  AI       )  AJ          M        b  #	
 N                       H� 
 h   b   0   �  Othis  9!       �   93       �   O  �   @           A   x      4       � �
   � �   � �#   � �6   � �,   �   0   �  
 h   �   l   �  
 x   �   |   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 H�    �H堿H嬃�   �      �   �   3 G                              �std::_Make_ec  >�/   _Errno  A           M        �  
  N                        @�  h   �        �/  O_Errno  O�   0              �     $       � �    � �   � �,   �   0   �  
 Z   �   ^   �  
 �   �   �   �  
 H冹HH峀$ �    H�    H峀$ �    �
   �      c      5      �   �   F G                       q        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               x#            J �   K �,   �   0   �  
 �   K   �   K  
 �   �   �   �  
 H冹x嬔H峀$0�    H峊$ H峀$@ )D$ �    H�    H峀$@�    �   �   #   �   *   �   4   5      �   �   > G            9      9   
        坰td::_Throw_system_error  >/   _Ec  A           Z   �     x                      @        $LN3  �   /  O_Ec  O  �   (           9   �             �   	 �,   �   0   �  
 b   �   f   �  
 �   b   �   b  
 �   �   �   �  
 H冹x嬔H峀$0�    H峊$ H峀$@ )D$ �    H�    H峀$@�    �   �   #   �   *   �   4   5      �   �   Q G            9      9            坰td::_Throw_system_error_from_std_win_error  >�/   _Errno  A           Z        x                      @        $LN3  �   �/  O_Errno  O �   (           9   �            � �   � �,   �   0   �  
 x   �   |   �  
 �   i   �   i  
 �   �   �   �  
 @SH冹 H婹H嬞H凓v1H�	H�U   H侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦荂   H塁f�H兡 [描    藹   �   \   �      �   �  � G            a      a           �std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate 
 >�   this  AI  
     T M   AJ        
  M           N M        �  1H M        �  1H M        c  )
 Z   �  
 >   _Ptr  AJ       .  
  >#    _Bytes  AK       B &  " M        s  
'#

 Z   �   >_    _Ptr_container  AP  +     5    AP ?       >_    _Back_shift  AJ  2     . 
   N N N N                       @� 2 h   �  s  t      B  �  �  �  Y  c         $LN32  0   �  Othis  O �   h           a     
   \       � �   � �
   � �
   � �   � �D   � �F   � �R   � �U   � �[   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 U  �   Y  �  
 z  �   ~  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 �  V   �  V  
 �  �   �  �  
 H冹(H�
    �    �   |      �      �   w   7 G                     ~        坰td::_Xlen_string 
 Z   �!   (                      @        $LN3  O �   (                          		 �   
	 �,   �   0   �  
 s   M   w   M  
 �   �   �   �  
 H塼$WH冹0H孂I嬸H婭L婫I嬂H+罤;饂?H塡$HH�1H塆H嬊I凐vH�H�L嬈H嬎�    �3 H嬊H媆$HH媡$PH兡0_肈禗$@L嬍H嬛H塼$ H嬒�    H媡$PH兡0_肎   @   w         �   �  r G            �   
   {   +        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append 
 >�   this  AJ        
  AM  
     x T  
 >�   _Ptr  AK        n K   >_   _Count  AL       p L   AP          >_    _Old_size  AJ       b 2   M        �  L@ >�   _First1  AI  @       N M        0  0# >p    _Result  AH  3       M        �  3 N N
 Z   �4   0                     H  h   �  0  �  �  �  �   @   �  Othis  H   �  O_Ptr  P   _  O_Count e 缧  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append::__l2::<lambda_1>  O�   p           �        d       � �   � �   � �(   � �0   � �<   � �K   � �O   � �W   � �b   � �{   � �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
   �     �  
 '  �   +  �  
 b  �   f  �  
 �  �   �  �  
 �  �   �  �  
 H塡$WH冹 A孁H嬟E吚uD�H�    H塀H嬄H媆$0H兡 _脣翔    吚u�;H�    H塁H嬅H媆$0H兡 _脡H�    H塁H嬅H媆$0H兡 _�   �   4   �   A   �   \   �      �   �  Z G            r   
   g           �std::_System_error_category::default_error_condition 
 >�/   this  AJ        3  D0    >t    _Errval  A   
     d #  I   Ah        
  >�    _Posv  A   8     ( 
   M        �   N M        �  	< N M        �  	W N
 Z   &"                        0@�  h   �       0   �/  Othis  @   t   O_Errval  O   �   h           r   �  
   \       R �   S �   T �#   ^ �1   X �8   Y �<   Z �E   ^ �W   \ �`   ^ �,   �   0   �  
    �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 D�H嬄H塉�   �     R G                   
   �        �std::error_category::default_error_condition 
 >�.   this  AJ          >t    _Errval  Ah          M        �    N                        @� 
 h   �      �.  Othis     t   O_Errval  O  �   0              �     $       � �    � �
   � �,   �   0   �  
 w   �   {   �  
 �   �   �   �  
   �      �  
 H婤L婬L9IuD9u��2烂   �   8  E G                      �        �std::error_category::equivalent 
 >�.   this  AJ          >�.   _Code  AK          >t    _Errval  Ah          M        �    N                        @�  h   �  �  �  �      �.  Othis     �.  O_Code     t   O_Errval  O�   @              �     4       � �    � �   � �   � �   � �,   �   0   �  
 j   �   n   �  
 �   �   �   �  
 �   �   �   �  
 L  �   P  �  
 @SH冹0H�I嬝D嬄H峊$ �PH婯L婬H婹I9Qu�9u�H兡0[�2繦兡0[�   �   �  E G            ?      9   �        �std::error_category::equivalent 
 >�.   this  AJ          >t    _Errval  A           >�.   _Cond  AI       2 *   AP          M        �   >�.   _Left  AH       "    M        �   N N 0                     @�  h   �  �  �  �  �   @   �.  Othis  H   t   O_Errval  P   �.  O_Cond  9       �.   O   �   @           ?   �     4       � �   � �1   � �7   � �9   � �,   �   0   �  
 j   �   n   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 H�    �H堿H嬃�   �      �   �   : G                      �        �std::make_error_code  >/   _Ec  A           M        �  
  N                        @�  h   �        /  O_Ec  O   �   0              �     $       � �    � �   � �,   �   0   �  
 ^   �   b   �  
 �   �   �   �  
 @SH冹0A嬋H嬟�    W繧抢����H荂    H荂    f怚�繠�<  u鯤嬓H嬎�    H嬅H兡0[�
   �   A         �   �  K G            N      H           �std::_Generic_error_category::message 
 >�/   this  AJ        	  D@    >t    _Errcode  Ah          M        O  
 Z   C  
 >�   _Ptr  AH       4  M        >  
 N M        A   M        �  �� M           N N N N
 Z   �!   0                     @ " h   O  S  >  <  A  �     @   �/  Othis  P   t   O_Errcode  O  �   0           N   �     $       & �   ' �H   ( �,   �   0   �  
 p   �   t   �  
 �   �   �   �  
 �   �   �   �  
 �  �   �  �  
 H塡$WH冹@H嬟3�H墊$(H峊$(A嬋�    H塂$0W�H吚u5H荂
   H荂   �    ��   塁�   圕@坽
�H墈H墈L嬂H婽$(H嬎�    怘婰$(�    H嬅H媆$PH兡@_�   �   E   �   O   �   Y   �   z      �   �      �   �  J G            �   
   �           �std::_System_error_category::message 
 >�/   this  AJ          DP    >t    _Errcode  Ah        ! 
 >�/    _Msg  D(    M        
  
 Z   f!   N M        P  31 M        C  &1( M        ?   A N N N M        P  f
 Z   C   M        A  f M        �  ��f N N N
 Z   �"   >�  _Unknown_error  C      S     
  C      ]     	  C          
  @                    0@ z h   �  �  r  x  y  
    �  P  S  $  ?  �  �  �  A  C  �  �  �  �      �  �  '  (  /   9    P   �/  Othis  `   t   O_Errcode  (   �/  O_Msg  �        _Unknown_error  O�   X           �   �     L       F �   G �&   H �,   G �/   H �1   K �f   N �   P ��   �   Y F                                �`std::_System_error_category::message'::`1'::dtor$0 
 >�/   this  EN  P          
 >�/    _Msg  EN  (                                  �  O ,   �   0   �  
 o   �   s   �  
 �   �   �   �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
 p     t    
 �     �    
 �     �    
 H崐(   �       �   H�    �   �      �   �   H G                              �std::_Generic_error_category::name 
 >�/   this  AJ          D                           @�     �/  Othis  O  �   0              �     $       " �    # �   $ �,   �   0   �  
 m   �   q   �  
 �   �   �   �  
 H�    �   �      �   �   G G                              �std::_System_error_category::name 
 >�/   this  AJ          D                           @�     �/  Othis  O   �   0              �     $       B �    C �   D �,   �   0   �  
 l   �   p   �  
 �   �   �   �  
 H冹8L婹L岻I;襴H儁vH�	I�� H兡8肔媃H墊$0I嬅H孃I+翴+鶫;鴚7H塡$HI�I凔vH�	J�A拘H嬎L嬊�    �; H媆$HH媩$0H兡8肈圖$ L嬒D禗$@H嬜�    H媩$0H兡8胊   A   �   
      �     r G            �      �   �        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::resize 
 >�   this  AJ        �   R &  AJ      B  +  >_   _New_size  AK        � Z   >�   _Ch  AX        � `   M        
   M        0   >p    _Result  AJ        M        �   N N N+ M          '	J#)	
 Z   �4   >_   _Count  AM  6     ] =   >_    _Old_size  AR       � ]   M        �  V >�   _First  AI  V       N M        0  I >p    _Result  AJ R       M        �  I N N N 8                      H 6 h   �  �  A  0  �  �  �  s  �  
       @   �  Othis  H   _  O_New_size  P   �  O_Ch  O   �   `           �     	   T       ' �   ) �   * �   + �"   / �'   - �s   / �x   - ��   / �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 M  �   Q  �  
 �  �   �  �  
 �  �   �  �  
 *  �   .  �  
 i  �   m  �  
 4  �   8  �  
 @WH冹0L婭H峺L嬔I;褀H儁vL�3繦�fA�RH兡0_肕媄H嬍I嬅I+蒊+罤;葁)H�I凔vM�K�<JH吷tA防f螳3纅A�RH兡0_胒D塂$ L嬌D禗$@H嬔I嬍�    H兡0_脛         �   L  { G            �      �           �std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::resize 
 >�   this  AJ        7  >_   _New_size  AK        �  >�   _Ch  A`        }  M           M           >q    _Result  AR         M           N N N( M          0H#)
 Z   �4   >_   _Count  AJ  7     L * 
  AJ a     
  >_    _Old_size  AQ  
     m  M        �  U >�   _First  AM  U       AM a       M        l  U N N M          H >q    _Result  AR       w    AR         M          H N N N 0                     @ : h
   �  �  l      �      B  �  �  u  n   @   �  Othis  H   _  O_New_size  P   �  O_Ch  O�   `           �     	   T       ' �   ) �   * �   + �*   / �0   - �h   / �n   - ��   / �,   �   0   �  
 �   �   �   �  
 �   �   �   �  
 �   �   �   �  
 2  �   6  �  
 �  �   �  �  
 �  �   �  �  
 �  �   �  �  
   �     �  
 (  �   ,  �  
 �  �   �  �  
 �  �   �  �  
 `  �   d  �  
 H婹H�    H呉HE旅   W      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0                    $       ^  �    _  �   `  �,   �   0   �  
 _   �   c   �  
 �   �   �   �  
  d T 4 2p    H           D      D      �    20    2           E      E      �   
 
4 
2p    B           F      F      �    20    <           G      G      �   
 
4 
2p    B           H      H      �    20    <           I      I      �   
 
4 
2p    B           J      J      �    �                  L      L      �    B                 N      N      �    B      >           P      P      �    20    ^           R      R      �   
 
d
 
Rp    #           S      S      �   ! 4	     #          S      S      �   #   b           S      S      �   !       #          S      S      �   b   �           S      S      �    b      +           T      T      �   ! 4	 t     +          T      T      �   +   x           T      T      �   !   t     +          T      T      �   x   �           T      T      �    Rp    �           U      U      �    20    a           W      W      �   
 
4 
2p    A           X      X         # 20    <           Y      Y         
 
4 
2p    B           Z      Z          R0    ?           [      [         
 
4 
2p    M           \      \          
 
4 
2p    B           ]      ]      &    
4 
�p    P      7       �           _      _      ,   
 
4 
2p    W           `      `      2   
 
4 
2p    B           a      a      8    �      9           c      c      >    R0    N           d      d      D    20    !           e      e      J   
 
4
 
rp           6      V       �           f      f      P   (           Y      \   
    P   �   �
 
4 
2p           6      e       r           g      g      _   `       h   f  20    !           h      h      k    �      9           j      j      q    B             6      }       "           k      k      w   h           �      �          �   20 4m d ���
�p`P               8       �       �          m      m      �   (           �      �   :    �<    �>    f    .    .    .    .    .    .    *    �>    b    a:    a:    �	    u    aB    �:    a
�    5    �B    a:    �
�    �B    �:    a�    a	B    a:    �   �   	   �      �      *      -      .   $   0   )   2   .   3   3   4   8   �   ?   +   D   �   K   �   R   �   Z   /   `   �   g   �   n   �   v   1   |   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   �   >�9I��N!	 �"�$V&�(�*�,V.�40�2B4546�8B:�^ V8" :""(8* 2P    )           +      +      �    2P    .           /      /      �    2P    .           1      1      �   � ��.^r�� d 4 2p           6      �       }          n      n      �   h           �      �          �   @ %5 I g { � � � � � /A R0           6      �       (           o      o      �   (           �      �          �   6 R0           6      �       (           p      p      �   (           �      �          �   6 R0           6      �       (           q      q      �   (           �      �          �   6 R0           6      �       (           r      r      �   (           �      �          �   6 R0           6      �       (           s      s      �   (                           �   6L L� >� 7� /x (h $�5 $t4 $d3 $42 $. ��P      h          t      t          h 4 rp
`P           6             F          u      u      
   (                    
    �>             �   B 6�  20               v      v         ! t               v      v            E           v      v      "   !                 v      v         E   K           v      v      (   - 20               w      w      1   ! t               w      w      1      E           w      w      7   !                 w      w      1   E   K           w      w      =   - 20               x      x      F   ! t               x      x      F      E           x      x      L   !                 x      x      F   E   K           x      x      R   - T 4
 r�p`           6      a       �           z      z      [   (           d      g          ,   E 2P    &           ,      ,      j   0 0� x h �0    �           {      {      p   @ @� ;� 6� 1� )x h t- 4, * P      �          |      |      v    h 
4
 
rp    n          }      }      |    B             6      �                              �   `       �     t	 T 4 2�    U                       �   ! d     U                      �   U   �                       �   !       U                      �   �   �                       �   !   d     U                      �   �                         �   !       U                      �                           �    B��pP      .           �      �      �   !# #� � 
d 4
     .          �      �      �   .   �          �      �      �   !   �  �  d  4
     .          �      �      �   �  �          �      �      �   !       .          �      �      �   �  �          �      �      �   
 
2	���`0    5           �      �      �   ! � t T
     5          �      �      �   5   r          �      �      �   !   �  t  T
     5          �      �      �   r  ~          �      �      �   !       5          �      �      �   ~  �          �      �      �    T 4 r�p`           6      �       �           �      �      �   (           �      �             ) 2P    &                       �    T 4
 r�p`           6      �       �           �      �      �   (           �      �          )   1 2P    &           )      )          ' 'h  "      �           �      �          B      :           �      �          B��`0      .           �      �         !# #� � t T
     .          �      �         .   y          �      �         !   �  �  t  T
     .          �      �         y  �          �      �         !       .          �      �         �  �          �      �      $    T 4
 r�p`           6      0       �           �      �      *   (           3      6             � 2P    &                       9    T 4 r�p`           6      E       �           �      �      ?   (           H      K             ) 2P    &                       N                               �      �      �   Unknown exception                                   �      �                                     �      �   bad array new length                                �      f                                 l      r      x                   .?AVbad_array_new_length@std@@     y               ����                      i      �                   .?AVbad_alloc@std@@     y              ����                      o      �                   .?AVexception@std@@     y               ����                      u      �   string too long     ����    ����        ��������                                  �      �                   .?AVruntime_error@std@@     y               ����                      �      �                               7      �      �                               C      �      �                                  �      �                                         �      �      �      x                   .?AVsystem_error@std@@     y               ����    (                  �      �                   .?AV_System_error@std@@     y               ����    (                  �      �                                                               O      �      �      �       �   (   �   0   �   generic                                                             ^      �      �      �       �   (   �   0   �   system unknown error ==PROCEDURAL_SKY== ==PROCEDURAL_SKY_MORNING== ==PROCEDURAL_SKY_MIDDAY== ==PROCEDURAL_SKY_EVENING== ==PROCEDURAL_SKY_DAWN== ==PROCEDURAL_SKY_PITCHBLACK== Assets /StandaloneTextures/q2rtx_env/transmittance_earth.dds /StandaloneTextures/q2rtx_env/inscatter_earth.dds /StandaloneTextures/q2rtx_env/irradiance_earth.dds /StandaloneTextures/q2rtx_env/clouds.dds /StandaloneTextures/RGBANoiseMedium.png This is a simple example procedural sky used to stress test dynamic environment map sampling. %.3f Brightness Sun Brightness Cloud movement speed Sun movement speed Sun time of day offset Sun east west rotation Sun angular diameter (deg) Cloud density offset                    �                      �                                         u      �      �                                                           ����    @                   u      �                                         o      
                               
                                               ����    @                   o      
                                         i                                                                                                  ����    @                   i                                               �      "                               %                           (                    ����    @                   �      "                   .?AVerror_category@std@@     y                         1                   4               ����    @                   +      .                                         �      :      7                         =                                   @      (                    ����    @                   �      :                                         �      F      C                         I                                           L      @      (                    ����    @                   �      F                                         R      U      O                   .?AV_Generic_error_category@std@@     y                         X                           [      4              ����    @                   R      U                                         a      d      ^                   .?AV_System_error_category@std@@     y                         g                           j      4              ����    @                   a      d   费8
�#<5鷰<吞�=   ?\�?瓽!?  �??333333�?      �?栀�?      �?没�?       @�I@�葽     魼  4C @F   G  ��  ��  4���  �?                                         �?������������   �   �   �   �   �   ,   * 
�        std::runtime_error::`vftable'    �      �  
    �   ,   * 
�        std::_System_error::`vftable'    �      �  
    �   +   ) 
�        std::system_error::`vftable'     �      �  
    �   (   & 
�        std::exception::`vftable'    T      T  
    �   6   4 
0Z        std::_Generic_error_category::`vftable'      �      �  
    �   (   & 
�        std::bad_alloc::`vftable'    Z      Z  
    �   3   1 
�        std::bad_array_new_length::`vftable'     ]      ]  
    �   5   3 
0Z        std::_System_error_category::`vftable'       �      �  
 斻C�bC{E桃玌刦k網叜月o"0"勾斻I:qD尷�)4嬃检_枞�+$鍖{屌鏮浡=
W"衕嶗寳G質s︳-�30夦�?E~�嵟�j7b,鳅舘H14h�K蜌�(9^i蒍裓滔偩�\ 杒+殶檸瘟OE3M脔 繘�
良靇枞�+n山餧鲥&nkm��<N�絸u勲鹠�亩�臈M笵纭N�絸u勲島Y狅�翚uVq�.
佾yR傿�/铏B3咬嶆�
B�/铏B3掖饷lpUk碩Z�àB�.坙聨.粀u悁]诲嗱D� th浆<*U
鈛M<绾M;�)[軨C艷I謣抙.N╔� [��黜�LΛ�湱諻h濨 p|櫲B
泅n黢
墌r怂螪[彯諔嫼J諷厬)RZFm�d蒄毓壖ft`�"訕ua瘱鉵悔
R巠eb玵x鳿m�7荱猢嫨奜咂灗嬍P{�):�5�2D=y�SZ{L鹝ぃ�	�=�铮噏兏�5T[騰檎~"片�(AD�'k
)o&靁ghm畺m秵溁f凍(x筊te@垤�!嚙廛�07鋸釬襍�06囔G腑n苗XY瑏	K?R黊妪jia�5鎲�3 o娺q覿j寏獋�2�w彉v2�	冪�?滄"湘уQ罺茲鹂UJ颊Y3�)`岩�嚤踖p禭�?As贛嚤踖p禭飓D堦縵�6萪O��-t肇|霵婬(︺m'<嚤踖p禭琇孑沧T2戜泶Yl罠�+H鉷窝桮技e瞍齐撆鐽�'捪Pf1拦X￡8喷'項jK�8(馚�:b8�4n削J筡幹桦�'洋m|2蕽<{�&e$愜w獛啯]Q杦寐K�$愜w獛啯倵教{��$愜w獛啯掾鷿"q7-S邝腤W3鼾骗=Y�7%�&谁ねk6嶀預棊膬/S;圾j硘端祆癜~t鋠枯�.嶀預棊膬絷嬺粀�8桦�'洋m|靷�
絚 龒犷A棊膬/Ｗ� ~谓嶿JI怣�7NZ癰*墦c.憾咍恔N==鄾#�嶿JI怣N�1箩邆5>(H7и篼}箩邆5>螬�0|⒙徇�5>;ㄓ$7箩邆5>蓈_�!i朿揷.憾咍惙['q箻亱� �蹰kL�*胾�,Fe碮沩�'o鲽�'tJd跃奰QI蚶90眗8�.t肾濜d霆慙5\�籫�0��0=�哪05�蚇b蕫腙鋃m赉>薢老P髁笴蜨)nMS�6`M]`2卧龠G墁轀y緤]昊袈i谪8蟾jC蕧V~h ^8N� 兓姩蘊�3g叄N%I栶賑?T� 鏋溦I>李{彻抦卑淨BJ]L汤顊彻抦北夃a_约塧#欰抎=葫瞠慺]{謑p揵齠HQ�^捋顢蛌鹈f�筈O
塓K怱?I�X桜z魹N陡暟v疚r�0桭�,\�扆�.[p鴇�+藐聗�R �<4\N� 兓姩虇鵾N8蹬&狒�*r>%y众覛蝕⒗Y妃o�)� 蕁�#&w進♁恗昷B�/铏B3覈�牥�9B�/铏B3襚悡鱭J
|廍擂tY�	H"�痖C髗\嬉_�'�5礼U妌N鵘J庹Ean╤F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这�-坓�(鬄鮐搜n2竌V雵J-WV8o��腫62V醒L淠顜ti觧vmGc疣誠醍G鈋xぃ聈姜{�/@�6儂草 ^篓萫孮.�>额	hQ�)J鍟�~
nN敤�� o忱x
V0,摶�8铳絟�>�
h刣0@-;�&Ee闺嘕-WV8o正╡怌9E\$L釉辸�>i,夿3'伽╪鮢雵J-WV8o��&{-�9E\$L釉蕤;[純o她餯繅鬮R��$m翲9E\$L釉轑垣"�/�9E\$L釉蕤;[純o藀%nI)eJ泊ff黀�9E\$L釉瞢Tラ~�&9E\$L釉蕤;[純o藢�?郂鞿x蝿壒eV餯繅鬮Y�6	褔雵J-WV8oc8曀黩6�)s�f舑:�傫A皋dd�a�:2槔j謠氎1+ZY威C帲晗D丢遧�6H挎驻趀�-鈚舓"`雵J-WV8oc8曀黩6尋?郂鞿x蝿壒eV�5YJq覜垒�咞taR�,F_棢杻#Q`�G堭`t鼼坧踍�E�$5dd�a�:bg
藨c图瞝��6;樢閣yQ)傂螨樢閣yQ�
,騤樢閣yQ�
,騤躾Ц8q�9觛楟迺�%%騏�唖,咞taR�,F_棢杻#Q濠︹O@儠x�&	鬏В焦貋i5絚_}4dd�a�:_棢杻#Q"能6洽�В焦貋i5絚_}4dd�a�:_棢杻#Q"能6洽�В焦貋i5絚_}4dd�a�:_棢杻#Q"能6洽�В焦貋i5絚_}4dd�a�:_棢杻#Q"能6洽�В焦貋i5絚_}4dd�a�:_棢杻#Q"能6洽琋�?'韹"%K夼a2叼5磔z铻�.�dd�a�:镁＜!棂S�"@8栟雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛雵J-WV8o蒴W�$峾嚚秱%J槧競e�&'瓎�灧NU喁層砟椚鰁破舛俐潫k捾燝杻澔dd�a�:_棢杻#Q�弡�7)紭议wyQ}�!罱4=cs�487銢TD椣y�k殎�nh怪嚢tC甉h嗓斠[G`嵇莚Y了5YJq�0]Z�9�$H挎驻趀顥婾轡d;@�(潮螣P雟禑)媗ㄣ(PI^ub&鷜3!敲:G火s昖廘�汚$S匜*媵-/�謚帊敲:G火�Ｈ屌座
mJ#(S篩�
,騤o2刏靊a肍qQI拖杠%Z@�6�2觿祒w礩僠僶藧纤徂(饎鍄劺.r俼�5v｛hr咉!a幙�6�>a葘Ij-榶鋬f为+F�$暱轺閔%堙E畍縝媌�7�dd�a�:_棢杻#Q0urLH恋q樢閣yQ}�!罱4=俐潫k�諷袲dd�a�:_棢杻#Q祶噳蚺麟樢閣yQ}�!罱4=2倃齼_*窶圹涩�6�-坓�(鬄�/ｎ	蜍R碢燶丘l�
,騤}q%>+iV忖_5)蓦8;繾"`>�庼l^礩僠僶藧xm凂慆� #叙亶{錌S⒈�dd�a�:_棢杻#Q磩^∧[汭樢閣yQ}�!罱4=h%堙E畍縝媌�7�dd�a�:_棢杻#Q0urLH恋q樢閣yQ}�!罱4=潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H潗幭恫V蕨战X0唘輢A+|潗幭恫V逎悗隙睼�:蓰咨尼桟t荫孿唘�
靛之E�E刍BUB誅瓻�E亃v(�rB>�衷亃v(鏔@@澿譍薣� 懣]-8惀�>h$&?\I`xN眒B不隢祒�:2扼爰|褧"�l{馛工
�均湉_*ch{[鲘r凙礗啦栽[W扝m]/剮�邖1餖國"X)讅ES饘h(Ic/耲t!涤諄-顁=嫌繴潩�
?愷�=E聾�輥豯2T鉃".�6峱!胴咴Y吋桘^J'��=mh牢�\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �\&2�%ZZ�$为赞G刹~赣 "^惋砤�#4蠋�#Q"�:邍A愦靮鸬2�>料C5��\&2渿#qM�5<A:蓰咨难栨熸� �\&2滭YlL�^鴐禵諢覸鰛B	挿;�\&2湦�
v薈爲%ZZ�$为赞G刹~赣 "^惋砤��\&2渳o啨�:�%ZZ�$为赞G刹~赣 "^惋砤�/5態yV�
f絸帓GＪ鴸豴喳6rA苜瀀CRC冼gYo�?a喣笩槶壥
拎^笵A傮 [錩轎)磝U2膐�*螸鶾B蔲�(踹Ag�H唋<9n值胢�(6復�?q
缊�36:塪逮0[豍&��1h猠滷K!�滪�'殇 XC塹泈M};.桔QW户x=茍0牊朱腏�(汐哪珙馕摊o5yI~�5]_иz个耼O榖苃V觔.癔�sG﹋枞睛﹟ｇd)配�M氱a企o葞;� タY焩:5V�&脌祦�
監�H题瘅q        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       ~                .debug$S       ㄇ              .debug$T       l                 .rdata         @       5G碚                         7          .text$mn       �      �
�#     .debug$S       �             .text$x        &      c�?�    .text$mn              l;     .debug$S    	   @             .text$mn    
   �       港�+     .debug$S       �         
    .text$mn       n      鐨$�     .debug$S    
   �             .text$mn       �       f聰s     .debug$S       ,             .text$mn       :      眡�     .debug$S                    .text$mn            �<N�     .debug$S       P  2           .text$mn       �      �?H     .debug$S       `             .text$x        &      c�?�    .text$mn       �      轜cp     .debug$S       L             .text$x        &      尊�0    .text$mn       �      }�*     .debug$S       D             .text$x        &      尊�0    .text$mn       �      彲�     .debug$S       �             .text$x        &      ��2{    .text$mn              覲A     .debug$S    !   �               .text$mn    "         覲A     .debug$S    #   �          "    .text$mn    $   �  
   麬榍     .debug$S    %   �	  N       $    .text$mn    &   �  
   镡�     .debug$S    '   �	  L       &    .text$mn    (   �     揥柏     .debug$S    )   �
  R       (    .text$mn    *          �     .debug$S    +   �  
       *    .text$mn    ,   	      乇葩     .debug$S    -   �          ,    .text$mn    .   :      s懅n     .debug$S    /   L         .    .text$mn    0   #       v^
�     .debug$S    1   �          0    .text$mn    2   �      媾     .debug$S    3   8         2    .text$mn    4   �  	   �=榔     .debug$S    5             4    .text$mn    6   �  Z   4嶷a     .debug$S    7           6    .text$x     8         6-餎6    .text$x     9         .�(�6    .text$x     :         鎝6    .text$x     ;         饉G6    .text$x     <         谨C6    .text$x     =         &跻@6    .text$x     >         瑛餕6    .text$x     ?         |�&H6    .text$x     @         妖\L6    .text$x     A         ｄ(�6    .text$x     B          f賉6    .text$x     C         He�6    .text$x     D         @悃W6    .text$x     E         eK�6    .text$x     F         腌颽6    .text$x     G         玝憁6    .text$x     H         ょ�6    .text$x     I         Of�=6    .text$x     J         郹�6    .text$x     K         G�5�6    .text$x     L         熹}�6    .text$x     M         琣�6    .text$x     N         鉧V�6    .text$x     O         鋌�6    .text$x     P         泸16    .text$x     Q   )      �<毅6    .text$x     R   .      D瑉�6    .text$x     S   .      潇艒6    .text$mn    T   M      7捽�     .debug$S    U   <  
       T    .text$mn    V   <      .ズ     .debug$S    W   0  
       V    .text$mn    X   <      .ズ     .debug$S    Y   L  
       X    .text$mn    Z   !      :著�     .debug$S    [   <         Z    .text$mn    \   2      X于     .debug$S    ]   <         \    .text$mn    ^   <      .ズ     .debug$S    _   8  
       ^    .text$mn    `   W      �主     .debug$S    a   @  
       `    .text$mn    b   �      爇�     .debug$S    c   �  "       b    .text$mn    d         Ac     .debug$S    e   �         d    .text$mn    f   "       坼	     .debug$S    g   �         f    .text$mn    h   ^      wP�     .debug$S    i   X         h    .text$mn    j         �%     .debug$S    k   h         j    .text$mn    l   K       }'     .debug$S    m   �         l    .text$mn    n   K       }'     .debug$S    o   �         n    .text$mn    p   K       }'     .debug$S    q   �         p    .text$mn    r   }     壯槩     .debug$S    s   �  2       r    .text$mn    t         6摙r     .debug$S    u   �          t    .text$mn    v         ��#     .debug$S    w   �          v    .text$mn    x          .B+�     .debug$S    y   �          x    .text$mn    z         ��#     .debug$S    {   �          z    .text$mn    |         �%     .debug$S    }   �          |    .text$mn    ~         ��#     .debug$S       �          ~    .text$mn    �   !      -嵎     .debug$S    �   �          �    .text$mn    �   B      ≡3     .debug$S    �            �    .text$mn    �   !      -嵎     .debug$S    �   �          �    .text$mn    �   B      贘S     .debug$S    �             �    .text$mn    �   B      贘S     .debug$S    �            �    .text$mn    �   B      贘S     .debug$S    �   �          �    .text$mn    �   B      贘S     .debug$S    �            �    .text$mn    �   B      ≡3     .debug$S    �            �    .text$mn    �   H       襶.      .debug$S    �   �         �    .text$mn    �   F  "   窫
     .debug$S    �   �         �    .text$x     �         T��    .text$mn    �   (       Y譟     .debug$S    �   d         �    .text$mn    �   (       壞圔     .debug$S    �   h         �    .text$mn    �   (       )g7t     .debug$S    �   `         �    .text$mn    �   (       柜     .debug$S    �   h         �    .text$mn    �   (       i H     .debug$S    �   h         �    .text$mn    �   h  0   ]隫     .debug$S    �     L       �    .text$mn    �   >      篬cX     .debug$S    �            �    .text$mn    �   A       麔囝     .debug$S    �   H         �    .text$mn    �         rZ     .debug$S    �   �          �    .text$mn    �          aJ鄔     .debug$S    �   �          �    .text$mn    �   9      5u駀     .debug$S    �   �          �    .text$mn    �   9      5u駀     .debug$S    �            �    .text$mn    �   a      q�w     .debug$S    �            �    .text$mn    �         �ッ     .debug$S    �   �          �    .text$mn    �   �      �.     .debug$S    �   0         �    .text$mn    �   r      ︹�     .debug$S    �            �    .text$mn    �          釩U1     .debug$S    �   L         �    .text$mn    �          惌甩     .debug$S    �   �  
       �    .text$mn    �   ?       i8賙     .debug$S    �   �         �    .text$mn    �         rZ     .debug$S    �   �          �    .text$mn    �   N      m >     .debug$S    �     
       �    .text$mn    �   �      N�6N     .debug$S    �            �    .text$x     �         Kバg�    .text$mn    �         覲A     .debug$S    �   �          �    .text$mn    �         覲A     .debug$S    �   �          �    .text$mn    �   �      uY顡     .debug$S    �   �         �    .text$mn    �   �      夡c�     .debug$S    �   �         �    .text$mn    �         崪覩     .debug$S    �   �          �        \       �        x                �                �                �                �                �                �                      \        7      z        Q      �        q      �        �          i�                   �      V        �      �        �          i�                         Z        3      v        X      X        �      �        �          i�                   �      �               �              �        �      h        �      �        1      �        �      j        �      �               �        z      �        �               �               �      ^              �        2          i�                   U      x        t      �        �      �        �      �        9      �        m      T        �      �        �          i�                   �      b              ~               `        D      �        f          i�                   �      �        �               �               �      t        	      �        >	      �        �	      �        �	          i�                   
      �        0
      �        �
      �        �
      �                  i�                   D               �      "        
      �        A      �        �      f        �      6        a
      r        �
      �        �
      �        =      �        �      �        �      �        H      �        �      �        �      p        0      l        k               ~               �               �      |        �               E               [               s               �               �               �               E      n        �              5              �      ,        �      2        �      4        7              o      d        �              ?      (        C      $        I                            B      .        x      
        �                            Z      &        T              6      *        �              �      0        �              �      8        �      �        �      �        D       A        �       B        �!      C        o"      D        (#      E        �#      F        �$      G        S%      H        &              �&              �'      9        �(      I        M)      J        *      K        �*      L        x+      M        1,      N        �,      O        �-      P        \.              �/      :        H0      Q        1              �1      ;        x2      <        03      R        �3      =        �4      S        Z5      >        6      ?        �6      @        �7               �7               �7               �7               �7           cos              cosf             expf             fmod             memcmp           memcpy           memmove          memset           sinf             sqrtf            $LN13       �    $LN5        \    $LN10       �    $LN7        V    $LN13       �    $LN10       X    $LN16       �    $LN3        �    $LN4        �    $LN3       �    $LN4        �    $LN23   >   �    $LN26       �    $LN35   ^   h    $LN38       h    $LN43       �    $LN50       �    $LN59       �    $LN32   a   �    $LN35       �    $LN11       �    $LN7        ^    $LN13       �    $LN13       �    $LN10       T    $LN16       �    $LN60   �   b    $LN64       b    $LN13       `    $LN19       �    $LN3    9   �    $LN4        �    $LN21       �    $LN8        �    $LN120      �    $LN16       �    $LN8        �    $LN3    9   �    $LN4        �    $LN10       f    $LN1481 �  6    $LN1504     6    $LN81       r    $LN14       �    $LN14       �    $LN14       �    $LN14       �    $LN14       �    $LN242      �    $LN89       �    $LN18       p    $LN18       l    $LN18       n    $LN61   �       $LN67           $LN20       2    $LN115      4    $LN55           $LN87         $LN92           $LN191  �  (    $LN195      (    $LN143  �  $    $LN149      $    $LN41   �       $LN47           $LN57   �       $LN63           $LN4            $LN14   :       $LN17           $LN166  �  &    $LN172      &    $LN36   �       $LN42           $LN41   �       $LN47           .xdata      �          F┑@�        �7      �    .pdata      �         X賦鷲        
8      �    .xdata      �          （亵\        -8      �    .pdata      �          T枨\        V8      �    .xdata      �          %蚘%�        ~8      �    .pdata      �         惻竗�        �8      �    .xdata      �          （亵V        �8      �    .pdata      �         2Fb襐        �8      �    .xdata      �          %蚘%�        9      �    .pdata      �         惻竗�        C9      �    .xdata      �          （亵X        i9      �    .pdata      �         2Fb襒        �9      �    .xdata      �          %蚘%�        �9      �    .pdata      �         惻竗�        :      �    .xdata      �          懐j灖        3:      �    .pdata      �         Vbv        c:      �    .xdata      �          �9��        �:      �    .pdata      �         �1隘        �:      �    .xdata      �          �9��        �:      �    .pdata      �         OAG悺        R;      �    .xdata      �          （亵h        �;      �    .pdata      �         翎珸h         <      �    .xdata      �          �/�9�        o<      �    .pdata      �         礶鵺�        �<      �    .xdata      �         %琛�        0=      �    .pdata      �         萎a        �=      �    .xdata      �         Y彯伪        �=      �    .pdata      �         蕕=Q�        V>      �    .xdata      �          1�7�        �>      �    .pdata      �          ~て        ?      �    .xdata      �         )寺�        g?      �    .pdata      �         吁�        �?      �    .xdata      �         穵豹�        @      �    .pdata      �         NX#奁        r@      �    .xdata      �          "沂�        薂      �    .pdata      �         钘盕�        'A      �    .xdata      �          （亵�        侫      �    .pdata      �         %燗�        銩      �    .xdata      �          %蚘%�        EB      �    .pdata      �         s�7澹        sB      �    .voltbl     �          'ι專    _volmd      �    .xdata      �          （亵^        燘      �    .pdata      �         2Fb襘        虰      �    .xdata      �          %蚘%�        鵅      �    .pdata      �         惻竗�        $C      �    .xdata      �          僣脊        NC      �    .pdata      �         袮韁�        朇      �    .xdata      �          %蚘%T        軨      �    .pdata      �         <讟睺        
D      �    .xdata      �          %蚘%�        6D      �    .pdata      �         惻竗�        aD      �    .xdata      �         徭i裝        婦      �    .pdata               x,塨        綝          .xdata               %蚘%`        餌         .pdata              啁鉥`        E         .xdata               %蚘%�        GE         .pdata              惻竗�        qE         .xdata               眃街�        欵         .pdata              VH倸�        薊         .xdata               僣冀        鸈         .pdata              咝<�        nF         .xdata      	         （亵�        郌      	   .pdata      
        萣�5�        G      
   .xdata              誋�"�        IG         .pdata              杞E%�        籊         .xdata      
  	      � )9�        ,H      
   .xdata              籧o]�        燞         .xdata               }6 丝        I         .xdata              �酑�        嶪         .pdata              頄u畛        鐸         .xdata              Mw2櫝        AJ         .xdata               O#0�        滼         .xdata               （亵�        鵍         .pdata              萣�5�        -K         .xdata               眃街�        `K         .pdata              VH倸�        甂         .xdata              /
        鸎         .pdata              +eS籪        4L         .xdata        	      �#荤f        lL         .xdata              jf                 .xdata               3狷 f        鐻         .xdata        (      譊e�6        #M         .pdata              ��6        訫         .xdata        	      � )96        凬         .xdata         �      磾蹽6        7O          .xdata      !  I       枷灁6        餙      !   .xdata      "         k�6              "   .pdata      #        }y9�6        dQ      #   .xdata      $         k�6        $R      $   .pdata      %        dp6        錜      %   .xdata      &         k�6              &   .pdata      '        dp6        fT      '   .voltbl     (         5u^�6    _volmd      (   .xdata      )        圇�
r        &U      )   .pdata      *        鯊"憆        NU      *   .xdata      +  	      �#荤r        uU      +   .xdata      ,        jr        烾      ,   .xdata      -         =漬r        蟄      -   .voltbl     .         ZVr    _volmd      .   .xdata      /        簄餹�        鵘      /   .pdata      0        銀�*�        `V      0   .xdata      1  	      � )9�        芕      1   .xdata      2        j�        /W      2   .xdata      3         7$杁�        濿      3   .xdata      4        簄餹�        X      4   .pdata      5        銀�*�        lX      5   .xdata      6  	      � )9�        蠿      6   .xdata      7        j�        7Y      7   .xdata      8         7$杁�              8   .xdata      9        簄餹�        Z      9   .pdata      :        銀�*�        oZ      :   .xdata      ;  	      � )9�        襔      ;   .xdata      <        j�        8[      <   .xdata      =         7$杁�              =   .xdata      >        簄餹�        
\      >   .pdata      ?        銀�*�        j\      ?   .xdata      @  	      � )9�        蒤      @   .xdata      A        j�        +]      A   .xdata      B         7$杁�        揮      B   .xdata      C        簄餹�        鮙      C   .pdata      D        銀�*�        T^      D   .xdata      E  	      � )9�        瞊      E   .xdata      F        j�        _      F   .xdata      G         7$杁�        z_      G   .xdata      H  4       苓D鶡        踎      H   .pdata      I        4庌`�        i`      I   .xdata      J        縎D�        鯼      J   .pdata      K        C趍x�        &a      K   .xdata      L  	      � )9�        Ua      L   .xdata      M        ▄P顠        嘺      M   .xdata      N  
       巗狿�        縜      N   .xdata      O         （亵p        馻      O   .pdata      P        � 賞        3b      P   .xdata      Q        范^損        tb      Q   .pdata      R        鳶�p        穊      R   .xdata      S        @鴚`p        鷅      S   .pdata      T        [7躳        =c      T   .voltbl     U         飾殪p    _volmd      U   .xdata      V         （亵l        �c      V   .pdata      W        � 賚        胏      W   .xdata      X        范^搇        d      X   .pdata      Y        鳶�l        Id      Y   .xdata      Z        @鴚`l        峝      Z   .pdata      [        [7躭        裠      [   .voltbl     \         飾殪l    _volmd      \   .xdata      ]         （亵n        e      ]   .pdata      ^        � 賜        ]e      ^   .xdata      _        范^搉              _   .pdata      `        鳶�n        韊      `   .xdata      a        @鴚`n        6f      a   .pdata      b        [7躰        f      b   .voltbl     c         飾殪n    _volmd      c   .xdata      d        m�PU        萬      d   .pdata      e        S        �g      e   .xdata      f  	      � )9        7h      f   .xdata      g        j        駂      g   .xdata      h         刞         眎      h   .xdata      i         k�        kj      i   .pdata      j        裬?        2k      j   .xdata      k         NTo�2        鴎      k   .pdata      l        暫`g2        ?l      l   .xdata      m  ,       Ｈ%I4        卨      m   .pdata      n        5g倯4        誰      n   .xdata      o         ��	        $m      o   .pdata      p        =�c        dm      p   .xdata      q        /
              q   .pdata      r        �8院d        n      r   .xdata      s        Mw2檇        恘      s   .xdata      t         筧d        	o      t   .xdata      u         �-th        俹      u   .pdata      v        �        雘      v   .xdata      w        銎�        Sp      w   .pdata      x        �g�        絧      x   .xdata      y        N懁        'q      y   .pdata      z        
        憅      z   .xdata      {        Z�	W        鹮      {   .pdata      |        敵4        er      |   .xdata      }        N懁        蟫      }   .pdata      ~        赴t        9s      ~   .xdata               鑉�(                 .pdata      �        dp(        痶      �   .xdata      �         _�(        簎      �   .pdata      �        鬯(        莢      �   .xdata      �         鬓�6(        詗      �   .pdata      �        伛穓(        醲      �   .xdata      �        垰玌(        顈      �   .pdata      �        訠攱(        鹺      �   .xdata      �         滝絰$        |      �   .pdata      �        ]-�$        }      �   .xdata      �        摊k?$        #~      �   .pdata      �        M軋�$        2      �   .xdata      �        >i_�$        A�      �   .pdata      �        紿R$        P�      �   .xdata      �        醴zt$        _�      �   .pdata      �        �
髛$        n�      �   .xdata      �        �9        }�      �   .pdata      �        Jk�        Y�      �   .xdata      �  	      � )9        4�      �   .xdata      �        j        �      �   .xdata      �         J董A        鰢      �   .xdata      �         k�        詧      �   .pdata      �        裬?        繅      �   .xdata      �        m�PU              �   .pdata      �        7N�        謰      �   .xdata      �  	      � )9        �      �   .xdata      �        j        1�      �   .xdata      �         侱楽        f�      �   .xdata      �         k�        晲      �   .pdata      �        裬?        褢      �   .xdata      �         ,t         �      �   .pdata      �        v��        `�      �   .xdata      �         �9�        硴      �   .pdata      �        礝
        �      �   .xdata      �         Z�&        l�      �   .pdata      �        dp&        n�      �   .xdata      �         qJ<V&        o�      �   .pdata      �        閴h�&        r�      �   .xdata      �         鴓�&        u�      �   .pdata      �        僚&        x�      �   .xdata      �        垰玌&        {�      �   .pdata      �        U虘&        ~�      �   .xdata      �        "苘        仠      �   .pdata      �        �"_
        k�      �   .xdata      �  	      � )9        T�      �   .xdata      �        j        @�      �   .xdata      �         u假        2�      �   .xdata      �         k�        �      �   .pdata      �        裬?        �      �   .xdata      �        �9        �      �   .pdata      �        Jk�        酰      �   .xdata      �  	      � )9        冥      �   .xdata      �        j        楼      �   .xdata      �         J董A        唉      �   .xdata      �         k�        槯      �   .pdata      �        裬?        崹      �   .rdata      �                     仼     �   .rdata      �         �;�         槱      �   .rdata      �                     咯     �   .rdata      �                     蜘     �   .rdata      �         �)               �   .xdata$x    �                     $�      �   .xdata$x    �        虼�)         F�      �   .data$r     �  /      嶼�         i�      �   .xdata$x    �  $      4��         帾      �   .data$r     �  $      鎊=         悛      �   .xdata$x    �  $      銸E�               �   .data$r     �  $      騏糡         <�      �   .xdata$x    �  $      4��         V�      �       暙           .rdata      �         燺渾         ǐ      �   .data       �          烀�          潍      �       �     �   .rdata      �                     )�     �   .data$r     �  (      `蔠�         D�      �   .xdata$x    �  $      4��         b�      �   .rdata      �                     ┈     �   .rdata      �                     默     �   .xdata$x    �                     蕃      �   .xdata$x    �  $      Y腠N               �   .data$r     �  '      H�         �      �   .xdata$x    �  $      I妥9         0�      �   .data$r     �  (      �e 8         u�      �   .xdata$x    �  $      I妥9         摥      �   .rdata      �  8                   诃     �   .rdata      �         +黮�         ��      �   .rdata      �  8                   �     �   .rdata      �         J'�5         =�      �   .rdata      �         2种;         V�      �   .rdata      �         n04         佼      �   .rdata      �         �         �      �   .rdata      �         捞�2         G�      �   .rdata      �         Ax勖         伅      �   .rdata      �         漽夳         集      �   .rdata      �         氏�         舣      �   .rdata      �         5v獖         2�      �   .rdata      �  6       sr�         K�      �   .rdata      �  2       �5K         偘      �   .rdata      �  3       匽         拱      �   .rdata      �  )       纪          鸢      �   .rdata      �  (       崞追         '�      �   .rdata      �  ^       /�?�         ]�      �   .rdata      �         戯F\         柋      �   .rdata      �         ��         北      �   .rdata      �         譐�         媳      �   .rdata      �         F摓G         虮      �   .rdata      �         �p�         �      �   .rdata      �         I柙�         F�      �   .rdata      �         淑_�         t�      �   .rdata      �         湚g�         ⒉      �   .rdata      �         A剖�         俨      �   .data       �        �弾         �      �   .data       �        	�
         y�      �   .rdata$r    �  $      'e%�         畛      �   .rdata$r    �        �          �      �   .rdata$r    �                     �      �   .rdata$r    �  $      Gv�:         2�      �   .rdata$r    �  $      'e%�         Q�      �   .rdata$r    �        }%B         i�      �   .rdata$r    �                     �      �   .rdata$r    �  $      `         暣      �   .rdata$r    �  $      'e%�         创      �   .rdata$r    �        �弾         状      �   .rdata$r    �                           �   .rdata$r    �  $      H衡�         �      �   .rdata$r    �  $      'e%�         C�      �   .rdata$r    �        }%B         _�      �   .rdata$r    �                     y�      �   .rdata$r    �  $      `         摰      �   .data$rs    �  )      �xW         兜      �   .rdata$r    �        �          盏      �   .rdata$r                          鸬          .rdata$r      $      Gv�:         �         .rdata$r      $      'e%�         /�         .rdata$r            �弾         K�         .rdata$r                         e�         .rdata$r      $      H衡�         �         .rdata$r      $      'e%�         ⒍         .rdata$r            �J�         蕉         .rdata$r      $                   侄         .rdata$r    	  $      o咔b         锒      	   .rdata$r    
  $      'e%�         �      
   .data$rs      2      AW鈇         7�         .rdata$r            }%B         _�         .rdata$r    
                     兎      
   .rdata$r      $      `         Х         .rdata$r      $      'e%�         苑         .data$rs      1      A��                  .rdata$r            }%B          �         .rdata$r                         C�         .rdata$r      $      `         f�         .rdata               s塔�         捀         .rdata               7冉v         ⒏         .rdata               �7甚         哺         .rdata               瑣�#         赂         .rdata               =-f�         腋         .rdata               ｂ�         飧         .rdata               樷         蚋         .rdata               v靛�         �         .rdata               b@离         �         .rdata               c雈.         "�         .rdata               艳�         :�         .rdata               钓>�         R�         .rdata                �腾�         b�          .rdata      !         漎Ek         z�      !   .rdata      "         怉躹         姽      "   .rdata      #         y蘮�         ⒐      #   .rdata      $         2T頄         补      $   .rdata      %         牔"
         鹿      %   .rdata      &         葶�T         诠      &   .rdata      '         84R	         旯      '   .rdata      (         3愿�               (   .rdata      )         �Z         
�      )   .rdata      *         V6]`         �      *   .rdata      +         齚G�         *�      +   .rdata      ,         ǜ8�         :�      ,       J�           .rdata      -         z�         \�      -   .rdata      .         � �         兒      .   .rdata      /         v靛�               /   .rdata      0         iI         押      0   .rdata      1         �a�               1   _fltused         .debug$S    2  8          �   .debug$S    3  8          �   .debug$S    4  8          �   .debug$S    5  4          �   .debug$S    6  D          �   .debug$S    7  4          �   .debug$S    8  @          �   .debug$S    9  D          �   .chks64     :  �                �  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ?_Deallocate_for_capacity@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CAXAEAV?$allocator@D@2@QEAD_K@Z ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ?resize@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAX_K_W@Z ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ?_Decref@_Ref_count_base@std@@QEAAXXZ __std_system_error_allocate_message __std_system_error_deallocate_message ??0runtime_error@std@@QEAA@AEBV01@@Z ??_Gruntime_error@std@@UEAAPEAXI@Z ??_Eruntime_error@std@@UEAAPEAXI@Z ??1error_category@std@@UEAA@XZ ?default_error_condition@error_category@std@@UEBA?AVerror_condition@2@H@Z ?equivalent@error_category@std@@UEBA_NHAEBVerror_condition@2@@Z ?equivalent@error_category@std@@UEBA_NAEBVerror_code@2@H@Z ?make_error_code@std@@YA?AVerror_code@1@W4errc@1@@Z ??0_System_error@std@@QEAA@AEBV01@@Z ??_G_System_error@std@@UEAAPEAXI@Z ??_E_System_error@std@@UEAAPEAXI@Z ??0system_error@std@@QEAA@Verror_code@1@@Z ??1system_error@std@@UEAA@XZ ??0system_error@std@@QEAA@AEBV01@@Z ??_Gsystem_error@std@@UEAAPEAXI@Z ??_Esystem_error@std@@UEAAPEAXI@Z ?_Throw_system_error@std@@YAXW4errc@1@@Z ?_Syserror_map@std@@YAPEBDH@Z ?_Winerror_map@std@@YAHH@Z ??1_System_error_message@std@@QEAA@XZ ?name@_Generic_error_category@std@@UEBAPEBDXZ ?message@_Generic_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z ??_G_Generic_error_category@std@@UEAAPEAXI@Z ??_E_Generic_error_category@std@@UEAAPEAXI@Z ?name@_System_error_category@std@@UEBAPEBDXZ ?message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z ?default_error_condition@_System_error_category@std@@UEBA?AVerror_condition@2@H@Z ??_G_System_error_category@std@@UEAAPEAXI@Z ??_E_System_error_category@std@@UEAAPEAXI@Z ??$_Immortalize_memcpy_image@V_Generic_error_category@std@@@std@@YAAEBV_Generic_error_category@0@XZ ??$_Immortalize_memcpy_image@V_System_error_category@std@@@std@@YAAEBV_System_error_category@0@XZ ?_Make_ec@std@@YA?AVerror_code@1@W4__std_win_error@@@Z ?_Throw_system_error_from_std_win_error@std@@YAXW4__std_win_error@@@Z ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z ??1SampleProceduralSky@@QEAA@XZ ?GetTransmittanceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ ?GetScatterringTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ ?GetIrradianceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ ?GetCloudsTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ ?GetNoiseTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ ?Update@SampleProceduralSky@@QEAA_NNAEAUProceduralSkyConstants@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z ?DebugGUI@SampleProceduralSky@@QEAAXM@Z ??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ ??1?$shared_ptr@ULoadedTexture@engine@donut@@@std@@QEAA@XZ __std_fs_code_page __std_fs_convert_narrow_to_wide __std_fs_convert_wide_to_narrow ??1path@filesystem@std@@QEAA@XZ ?UnloadTexture@TextureCache@engine@donut@@QEAA_NAEBV?$shared_ptr@ULoadedTexture@engine@donut@@@std@@@Z ?Indent@ImGui@@YAXM@Z ?Unindent@ImGui@@YAXM@Z ?TextWrapped@ImGui@@YAXPEBDZZ ?SliderFloat@ImGui@@YA_NPEBDPEAMMM0H@Z ?InputFloat@ImGui@@YA_NPEBDPEAMMM0H@Z ?GetLocalPath@@YA?AVpath@filesystem@std@@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@@Z ??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ ??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z ??$?8ULoadedTexture@engine@donut@@@std@@YA_NAEBV?$shared_ptr@ULoadedTexture@engine@donut@@@0@$$T@Z ??$fmod@NM$0A@@@YANNM@Z ??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z ??$rotation@M@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@@Z ??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z ??1?$GenericScope@V<lambda_1>@?1??DebugGUI@SampleProceduralSky@@QEAAXM@Z@V<lambda_2>@?1??23@QEAAXM@Z@@@QEAA@XZ ??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z ??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z ??$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z ??$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z ??$length@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ??$?DM$02$02$02@math@donut@@YA?AU?$matrix@M$02$02@01@AEBU201@0@Z ??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z ??$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z ??$_Stringoid_from_Source@DU?$char_traits@D@std@@V?$allocator@D@2@@filesystem@std@@YA?A_PAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z ??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z ??$lengthSquared@M$02@math@donut@@YAMAEBU?$vector@M$02@01@@Z ?dtor$0@?0???$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z@4HA ?dtor$0@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$0@?0??DebugGUI@SampleProceduralSky@@QEAAXM@Z@4HA ?dtor$0@?0??message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z@4HA ?dtor$10@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$11@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$12@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$13@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$15@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$16@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$17@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$19@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$1@?0???$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z@4HA ?dtor$1@?0???$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z@4HA ?dtor$1@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$20@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$21@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$23@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$24@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$25@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$27@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$28@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$29@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$2@?0???$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z@4HA ?dtor$2@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$37@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$3@?0???$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z@4HA ?dtor$3@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$4@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$53@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$5@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$69@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$6@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$7@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA ?dtor$8@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$?_Deallocate_for_capacity@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CAXAEAV?$allocator@D@2@QEAD_K@Z $pdata$?_Deallocate_for_capacity@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CAXAEAV?$allocator@D@2@QEAD_K@Z $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $unwind$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z $pdata$?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z $chain$1$?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z $pdata$1$?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z $chain$3$?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z $pdata$3$?resize@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAX_KD@Z $unwind$?resize@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAX_K_W@Z $pdata$?resize@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAX_K_W@Z $unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ $pdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ $unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ $pdata$?_Decref@_Ref_count_base@std@@QEAAXXZ $unwind$??0runtime_error@std@@QEAA@AEBV01@@Z $pdata$??0runtime_error@std@@QEAA@AEBV01@@Z $unwind$??_Gruntime_error@std@@UEAAPEAXI@Z $pdata$??_Gruntime_error@std@@UEAAPEAXI@Z $unwind$?equivalent@error_category@std@@UEBA_NHAEBVerror_condition@2@@Z $pdata$?equivalent@error_category@std@@UEBA_NHAEBVerror_condition@2@@Z $unwind$??0_System_error@std@@QEAA@AEBV01@@Z $pdata$??0_System_error@std@@QEAA@AEBV01@@Z $unwind$??_G_System_error@std@@UEAAPEAXI@Z $pdata$??_G_System_error@std@@UEAAPEAXI@Z $unwind$??0system_error@std@@QEAA@Verror_code@1@@Z $pdata$??0system_error@std@@QEAA@Verror_code@1@@Z $unwind$??0system_error@std@@QEAA@AEBV01@@Z $pdata$??0system_error@std@@QEAA@AEBV01@@Z $unwind$??_Gsystem_error@std@@UEAAPEAXI@Z $pdata$??_Gsystem_error@std@@UEAAPEAXI@Z $unwind$?_Throw_system_error@std@@YAXW4errc@1@@Z $pdata$?_Throw_system_error@std@@YAXW4errc@1@@Z $unwind$?message@_Generic_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z $pdata$?message@_Generic_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z $unwind$??_G_Generic_error_category@std@@UEAAPEAXI@Z $pdata$??_G_Generic_error_category@std@@UEAAPEAXI@Z $unwind$?message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z $pdata$?message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z $cppxdata$?message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z $stateUnwindMap$?message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z $ip2state$?message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@H@Z $unwind$?default_error_condition@_System_error_category@std@@UEBA?AVerror_condition@2@H@Z $pdata$?default_error_condition@_System_error_category@std@@UEBA?AVerror_condition@2@H@Z $cppxdata$?default_error_condition@_System_error_category@std@@UEBA?AVerror_condition@2@H@Z $ip2state$?default_error_condition@_System_error_category@std@@UEBA?AVerror_condition@2@H@Z $unwind$??_G_System_error_category@std@@UEAAPEAXI@Z $pdata$??_G_System_error_category@std@@UEAAPEAXI@Z $unwind$?_Throw_system_error_from_std_win_error@std@@YAXW4__std_win_error@@@Z $pdata$?_Throw_system_error_from_std_win_error@std@@YAXW4__std_win_error@@@Z $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z $pdata$??0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z $cppxdata$??0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z $stateUnwindMap$??0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z $ip2state$??0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z $unwind$?dtor$37@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA $pdata$?dtor$37@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA $unwind$?dtor$53@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA $pdata$?dtor$53@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA $unwind$?dtor$69@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA $pdata$?dtor$69@?0???0SampleProceduralSky@@QEAA@PEAVIDevice@nvrhi@@V?$shared_ptr@VTextureCache@engine@donut@@@std@@V?$shared_ptr@VCommonRenderPasses@engine@donut@@@4@PEAVICommandList@2@@Z@4HA $unwind$??1SampleProceduralSky@@QEAA@XZ $pdata$??1SampleProceduralSky@@QEAA@XZ $cppxdata$??1SampleProceduralSky@@QEAA@XZ $stateUnwindMap$??1SampleProceduralSky@@QEAA@XZ $ip2state$??1SampleProceduralSky@@QEAA@XZ $unwind$?GetTransmittanceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $pdata$?GetTransmittanceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $cppxdata$?GetTransmittanceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $stateUnwindMap$?GetTransmittanceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $ip2state$?GetTransmittanceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $unwind$?GetScatterringTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $pdata$?GetScatterringTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $cppxdata$?GetScatterringTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $stateUnwindMap$?GetScatterringTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $ip2state$?GetScatterringTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $unwind$?GetIrradianceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $pdata$?GetIrradianceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $cppxdata$?GetIrradianceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $stateUnwindMap$?GetIrradianceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $ip2state$?GetIrradianceTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $unwind$?GetCloudsTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $pdata$?GetCloudsTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $cppxdata$?GetCloudsTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $stateUnwindMap$?GetCloudsTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $ip2state$?GetCloudsTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $unwind$?GetNoiseTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $pdata$?GetNoiseTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $cppxdata$?GetNoiseTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $stateUnwindMap$?GetNoiseTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $ip2state$?GetNoiseTexture@SampleProceduralSky@@QEBA?AV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@XZ $unwind$?Update@SampleProceduralSky@@QEAA_NNAEAUProceduralSkyConstants@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z $pdata$?Update@SampleProceduralSky@@QEAA_NNAEAUProceduralSkyConstants@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z $unwind$?DebugGUI@SampleProceduralSky@@QEAAXM@Z $pdata$?DebugGUI@SampleProceduralSky@@QEAAXM@Z $cppxdata$?DebugGUI@SampleProceduralSky@@QEAAXM@Z $stateUnwindMap$?DebugGUI@SampleProceduralSky@@QEAAXM@Z $ip2state$?DebugGUI@SampleProceduralSky@@QEAAXM@Z $unwind$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VTextureCache@engine@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@ULoadedTexture@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@ULoadedTexture@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@ULoadedTexture@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@ULoadedTexture@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@ULoadedTexture@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@ULoadedTexture@engine@donut@@@std@@QEAA@XZ $unwind$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$0$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $chain$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $pdata$1$??1?$shared_ptr@VCommonRenderPasses@engine@donut@@@std@@QEAA@XZ $unwind$??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z $pdata$??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z $cppxdata$??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z $stateUnwindMap$??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z $ip2state$??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z $unwind$?dtor$3@?0???$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z@4HA $pdata$?dtor$3@?0???$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$0A@@path@filesystem@std@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@W4format@012@@Z@4HA $unwind$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $pdata$??$normalize@M$02@math@donut@@YA?AU?$vector@M$02@01@AEBU201@@Z $unwind$??$rotation@M@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@@Z $pdata$??$rotation@M@math@donut@@YA?AU?$affine@M$02@01@AEBU?$vector@M$02@01@@Z $unwind$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $pdata$??$?DM$02@math@donut@@YA?AU?$affine@M$02@01@AEBU201@0@Z $unwind$??1?$GenericScope@V<lambda_1>@?1??DebugGUI@SampleProceduralSky@@QEAAXM@Z@V<lambda_2>@?1??23@QEAAXM@Z@@@QEAA@XZ $pdata$??1?$GenericScope@V<lambda_1>@?1??DebugGUI@SampleProceduralSky@@QEAAXM@Z@V<lambda_2>@?1??23@QEAAXM@Z@@@QEAA@XZ $cppxdata$??1?$GenericScope@V<lambda_1>@?1??DebugGUI@SampleProceduralSky@@QEAAXM@Z@V<lambda_2>@?1??23@QEAAXM@Z@@@QEAA@XZ $ip2state$??1?$GenericScope@V<lambda_1>@?1??DebugGUI@SampleProceduralSky@@QEAAXM@Z@V<lambda_2>@?1??23@QEAAXM@Z@@@QEAA@XZ $unwind$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$0$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$1$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$2$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $chain$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $pdata$3$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z $unwind$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $pdata$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $chain$3$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $pdata$3$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $chain$5$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $pdata$5$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $chain$6$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $pdata$6$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV34@_K_W@Z@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0_W@Z@_K_W@Z $unwind$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $pdata$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $chain$2$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $pdata$2$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $chain$4$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $pdata$4$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $chain$5$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $pdata$5$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z $unwind$??$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z $pdata$??$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z $cppxdata$??$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z $stateUnwindMap$??$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z $ip2state$??$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z $unwind$?dtor$1@?0???$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z@4HA $pdata$?dtor$1@?0???$_Convert_wide_to@U?$char_traits@D@std@@V?$allocator@D@2@D@filesystem@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@V?$basic_string_view@_WU?$char_traits@_W@std@@@1@AEBV?$allocator@D@1@@Z@4HA $unwind$??$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z $pdata$??$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z $cppxdata$??$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z $stateUnwindMap$??$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z $ip2state$??$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z $unwind$?dtor$2@?0???$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z@4HA $pdata$?dtor$2@?0???$_Convert_Source_to_wide@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U_Normal_conversion@filesystem@2@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@U_Normal_conversion@01@@Z@4HA $unwind$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $pdata$??$?DM@math@donut@@YA?AU?$vector@M$02@01@AEBU201@AEBU?$matrix@M$02$02@01@@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $unwind$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $pdata$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $chain$3$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $pdata$3$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $chain$5$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $pdata$5$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $chain$6$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $pdata$6$??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@_KD@Z@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@0D@Z@_KD@Z $unwind$??$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z $pdata$??$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z $cppxdata$??$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z $stateUnwindMap$??$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z $ip2state$??$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z $unwind$?dtor$0@?0???$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z@4HA $pdata$?dtor$0@?0???$_Convert_wide_to_narrow@U?$char_traits@D@std@@V?$allocator@D@2@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@W4__std_code_page@@V?$basic_string_view@_WU?$char_traits@_W@std@@@0@AEBV?$allocator@D@0@@Z@4HA $unwind$??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z $pdata$??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z $cppxdata$??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z $stateUnwindMap$??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z $ip2state$??$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z $unwind$?dtor$1@?0???$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z@4HA $pdata$?dtor$1@?0???$_Convert_stringoid_to_wide@U_Normal_conversion@filesystem@std@@@filesystem@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@1@V?$basic_string_view@DU?$char_traits@D@std@@@1@U_Normal_conversion@01@@Z@4HA ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7runtime_error@std@@6B@ ??_R0?AVruntime_error@std@@@8 _CT??_R0?AVruntime_error@std@@@8??0runtime_error@std@@QEAA@AEBV01@@Z24 ??_7_System_error@std@@6B@ ??_7system_error@std@@6B@ _TI4?AVsystem_error@std@@ _CTA4?AVsystem_error@std@@ ??_R0?AVsystem_error@std@@@8 _CT??_R0?AVsystem_error@std@@@8??0system_error@std@@QEAA@AEBV01@@Z40 ??_R0?AV_System_error@std@@@8 _CT??_R0?AV_System_error@std@@@8??0_System_error@std@@QEAA@AEBV01@@Z40 ??_7_Generic_error_category@std@@6B@ ??_C@_07DCLBNMLN@generic@ ??_7_System_error_category@std@@6B@ ??_C@_06FHFOAHML@system@ ?_Unknown_error@?4??message@_System_error_category@std@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@3@H@Z@4QBDB ??_C@_0BD@BBPPAGPO@?$DN?$DNPROCEDURAL_SKY?$DN?$DN@ ??_C@_0BL@EDJEFJOB@?$DN?$DNPROCEDURAL_SKY_MORNING?$DN?$DN@ ??_C@_0BK@GCPKINAN@?$DN?$DNPROCEDURAL_SKY_MIDDAY?$DN?$DN@ ??_C@_0BL@CGFONFAN@?$DN?$DNPROCEDURAL_SKY_EVENING?$DN?$DN@ ??_C@_0BI@KHLHEPEC@?$DN?$DNPROCEDURAL_SKY_DAWN?$DN?$DN@ ??_C@_0BO@EHPJAOIA@?$DN?$DNPROCEDURAL_SKY_PITCHBLACK?$DN?$DN@ ??_C@_06OIDJFGLE@Assets@ ??_C@_0DG@NGPPOAHB@?1StandaloneTextures?1q2rtx_env?1t@ ??_C@_0DC@KLENIPAB@?1StandaloneTextures?1q2rtx_env?1i@ ??_C@_0DD@MELACPGG@?1StandaloneTextures?1q2rtx_env?1i@ ??_C@_0CJ@JJHCLCOJ@?1StandaloneTextures?1q2rtx_env?1c@ ??_C@_0CI@KBMEAEKP@?1StandaloneTextures?1RGBANoiseMe@ ??_C@_0FO@KKCLEHEM@This?5is?5a?5simple?5example?5proced@ ??_C@_04GFJLOHHD@?$CF?43f@ ??_C@_0L@JCLLFKLB@Brightness@ ??_C@_0P@DDODLGMP@Sun?5Brightness@ ??_C@_0BF@PJGFKJDC@Cloud?5movement?5speed@ ??_C@_0BD@PGIHCFBC@Sun?5movement?5speed@ ??_C@_0BH@PILDDPC@Sun?5time?5of?5day?5offset@ ??_C@_0BH@CMAADMHB@Sun?5east?5west?5rotation@ ??_C@_0BL@POCDHNA@Sun?5angular?5diameter?5?$CIdeg?$CJ@ ??_C@_0BF@DBPMDF@Cloud?5density?5offset@ ?_Static@?1???$_Immortalize_memcpy_image@V_Generic_error_category@std@@@std@@YAAEBV_Generic_error_category@1@XZ@4V21@B ?_Static@?1???$_Immortalize_memcpy_image@V_System_error_category@std@@@std@@YAAEBV_System_error_category@1@XZ@4V21@B ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R4runtime_error@std@@6B@ ??_R3runtime_error@std@@8 ??_R2runtime_error@std@@8 ??_R1A@?0A@EA@runtime_error@std@@8 ??_R0?AVerror_category@std@@@8 ??_R3error_category@std@@8 ??_R2error_category@std@@8 ??_R1A@?0A@EA@error_category@std@@8 ??_R4_System_error@std@@6B@ ??_R3_System_error@std@@8 ??_R2_System_error@std@@8 ??_R1A@?0A@EA@_System_error@std@@8 ??_R4system_error@std@@6B@ ??_R3system_error@std@@8 ??_R2system_error@std@@8 ??_R1A@?0A@EA@system_error@std@@8 ??_R4_Generic_error_category@std@@6B@ ??_R0?AV_Generic_error_category@std@@@8 ??_R3_Generic_error_category@std@@8 ??_R2_Generic_error_category@std@@8 ??_R1A@?0A@EA@_Generic_error_category@std@@8 ??_R4_System_error_category@std@@6B@ ??_R0?AV_System_error_category@std@@@8 ??_R3_System_error_category@std@@8 ??_R2_System_error_category@std@@8 ??_R1A@?0A@EA@_System_error_category@std@@8 __real@38d1b717 __real@3c23d70a __real@3c8efa35 __real@3dcccccd __real@3f000000 __real@3f028f5c __real@3f2147ae __real@3f800000 __real@3fbcac08 __real@3fd3333333333333 __real@3fe0000000000000 __real@3fecd9e8 __real@3ff0000000000000 __real@3ff4bbc3 __real@4000000000000000 __real@40490fdb __real@40c90fdb __real@40f5180000000000 __real@43340000 __real@461c4000 __real@47000000 __real@be800000 __real@bf800000 __real@c3340000 __real@ff7fffff __security_cookie __xmm@0000000000000000000000003f800000 __xmm@000000000000000f0000000000000000 __xmm@3f800000000000000000000000000000 __xmm@7fffffff7fffffff7fffffff7fffffff __xmm@80000000800000008000000080000000 