^D:\RTXPT\RTXPT\ACCUMULATIONPASS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\ACCUMULATIONPASS.CPP
^D:\RTXPT\RTXPT\COMPUTEPASS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\COMPUTEPASS.CPP
^D:\RTXPT\RTXPT\EXTENDEDSCENE.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\EXTENDEDSCENE.CPP
^D:\RTXPT\RTXPT\GPUSORT\GPUSORT.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\GPUSORT\GPUSORT.CPP
^D:\RTXPT\RTXPT\LIGHTING\DISTANT\ENVMAPBAKER.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\LIGHTING\DISTANT\ENVMAPBAKER.CPP
^D:\RTXPT\RTXPT\LIGHTING\DISTANT\ENVMAPIMPORTANCESAMPLINGBAKER.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\LIGHTING\DISTANT\ENVMAPIMPORTANCESAMPLINGBAKER.CPP
^D:\RTXPT\RTXPT\LIGHTING\DISTANT\SAMPLEPROCEDURALSKY.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\LIGHTING\DISTANT\SAMPLEPROCEDURALSKY.CPP
^D:\RTXPT\RTXPT\LIGHTING\LIGHTSBAKER.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\LIGHTING\LIGHTSBAKER.CPP
^D:\RTXPT\RTXPT\LOCALCONFIG.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\LOCALCONFIG.CPP
^D:\RTXPT\RTXPT\MATERIALS\MATERIALSBAKER.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\MATERIALS\MATERIALSBAKER.CPP
^D:\RTXPT\RTXPT\MISC\COMMANDLINE.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\MISC\COMMANDLINE.CPP
^D:\RTXPT\RTXPT\MISC\KORGI.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\MISC\KORGI.CPP
^D:\RTXPT\RTXPT\NRD\NRDCONFIG.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\NRD\NRDCONFIG.CPP
^D:\RTXPT\RTXPT\NRD\NRDINTEGRATION.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\NRD\NRDINTEGRATION.CPP
^D:\RTXPT\RTXPT\OPACITYMICROMAP\OMMBAKER.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\OPACITYMICROMAP\OMMBAKER.CPP
^D:\RTXPT\RTXPT\OPACITYMICROMAP\OMMBUILDQUEUE.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\OPACITYMICROMAP\OMMBUILDQUEUE.CPP
^D:\RTXPT\RTXPT\PTPIPELINEBAKER.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\PTPIPELINEBAKER.CPP
^D:\RTXPT\RTXPT\POSTPROCESS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\POSTPROCESS.CPP
^D:\RTXPT\RTXPT\RTXDI\GENERATEPDFMIPSPASS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\RTXDI\GENERATEPDFMIPSPASS.CPP
^D:\RTXPT\RTXPT\RTXDI\PREPARELIGHTSPASS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\RTXDI\PREPARELIGHTSPASS.CPP
^D:\RTXPT\RTXPT\RTXDI\RAYTRACINGPASS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\RTXDI\RAYTRACINGPASS.CPP
^D:\RTXPT\RTXPT\RTXDI\RTXDIAPPLICATIONSETTINGS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\RTXDI\RTXDIAPPLICATIONSETTINGS.CPP
^D:\RTXPT\RTXPT\RTXDI\RTXDIPASS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\RTXDI\RTXDIPASS.CPP
^D:\RTXPT\RTXPT\RTXDI\RTXDIRESOURCES.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\RTXDI\RTXDIRESOURCES.CPP
^D:\RTXPT\RTXPT\RENDERTARGETS.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\RENDERTARGETS.CPP
^D:\RTXPT\RTXPT\SAMPLE.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\SAMPLE.CPP
^D:\RTXPT\RTXPT\SAMPLECOMMON.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\SAMPLECOMMON.CPP
^D:\RTXPT\RTXPT\SAMPLEGAME\SAMPLEGAME.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\SAMPLEGAME\SAMPLEGAME.CPP
^D:\RTXPT\RTXPT\SAMPLEGAME\SAMPLEGAMEPROP.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\SAMPLEGAME\SAMPLEGAMEPROP.CPP
^D:\RTXPT\RTXPT\SAMPLEUI.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\SAMPLEUI.CPP
^D:\RTXPT\RTXPT\SHADERDEBUG.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\SHADERDEBUG.CPP
^D:\RTXPT\RTXPT\TONEMAPPER\TONEMAPPINGPASSES.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\TONEMAPPER\TONEMAPPINGPASSES.CPP
^D:\RTXPT\RTXPT\ZOOMTOOL.CPP
/c /ID:\RTXPT\RTXPT\..\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /ID:\RTXPT\EXTERNAL\CXXOPTS\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\JSONCPP\SRC\LIB_JSON\..\..\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\MINIZ /I"D:\RTXPT\CMAKE-BUILD-RELEASE-VISUAL-STUDIO\EXTERNAL\DONUT\THIRDPARTY\MINIZ" /ID:\RTXPT\EXTERNAL\DONUT\NVRHI\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\STB /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\CGLTF /ID:\RTXPT\EXTERNAL\DONUT\SHADERMAKE\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TASKFLOW /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\TINYEXR /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\GLFW\INCLUDE /ID:\RTXPT\EXTERNAL\DONUT\THIRDPARTY\IMGUI /ID:\RTXPT\EXTERNAL\NRD\INCLUDE /ID:\RTXPT\EXTERNAL\RTXDI\INCLUDE /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-LIB\INCLUDE" /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\GLM /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\STB /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\XXHASH\CMAKE_UNOFFICIAL\.. /ID:\RTXPT\EXTERNAL\OMM\EXTERNAL\LZ4\BUILD\CMAKE\..\..\LIB /I"D:\RTXPT\EXTERNAL\OMM\LIBRARIES\OMM-GPU-NVRHI" /Zi /nologo /W1 /WX /diagnostics:column /O2 /Ob2 /D _MBCS /D WIN32 /D _WINDOWS /D _DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR /D NDEBUG /D "RTXPT_LOCAL_CONFIG_ID_STRING=std::string(\"NONAME\")" /D NOMINMAX /D _CRT_SECURE_NO_WARNINGS /D DONUT_WITH_MINIZ /D MINIZ_STATIC_DEFINE /D DONUT_WITH_TASKFLOW /D DONUT_WITH_TINYEXR /D DONUT_WITH_DX11=0 /D DONUT_WITH_DX12=1 /D DONUT_WITH_VULKAN=0 /D DONUT_WITH_STATIC_SHADERS=0 /D DONUT_WITH_AFTERMATH=0 /D DONUT_WITH_STREAMLINE=1 /D "CMAKE_INTDIR=\"Release\"" /EHsc /MT /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /openmp /std:c++20 /Fo"RTXPT.DIR\RELEASE\\" /Fd"RTXPT.DIR\RELEASE\VC143.PDB" /external:W0 /Gd /TP  /external:I "D:/RTXPT/External/Streamline/include" /external:I "D:/RTXPT/External/Donut/nvrhi/thirdparty/DirectX-Headers/include" /external:I "D:/RTXPT/External/nvapi" /utf-8 D:\RTXPT\RTXPT\ZOOMTOOL.CPP
