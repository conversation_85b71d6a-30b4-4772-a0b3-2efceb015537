d唸蒹Gh� �      .drectve        P  T=               
 .debug$S        (  �>  蘜        @ B.debug$T        l   _             @ B.rdata          @   坃             @ @@.text$mn        :   萠 `         P`.debug$S           ` ,b        @B.text$mn        �  竍 ld         P`.debug$S           ╠ 萲     :   @B.text$mn        *  n 6o         P`.debug$S        �  @o 蘵     ,   @B.text$mn        1   剋 祑         P`.debug$S        �  縲 穣        @B.text$mn        j  z 閩         P`.debug$S        0"  潂 蜖     �   @B.text$x         F   々 绌         P`.text$x         D   � I�         P`.text$x         �   ]� 岐         P`.text$x         <   	� E�         P`.text$x         W   Y� 矮         P`.text$mn        X   潍 &�         P`.debug$S        �  :� 颦        @B.text$mn        y   挳 �         P`.debug$S          � !�        @B.text$x            9� E�         P`.text$mn        �   O� 卮         P`.debug$S        �  獯 豆        @B.text$x            夂 詈         P`.text$mn        u    m�         P`.debug$S        (  伝 ├         @B.text$x            榱 趿         P`.text$x            �� �         P`.text$mn        �   �              P`.debug$S        �  甭 q�        @B.text$mn        }   M� 嗜         P`.debug$S        p  匀 D�        @B.text$mn            �              P`.debug$S          +� ;�        @B.text$mn          w� 徬         P`.debug$S          讼 壑     4   @B.text$mn        <   阖 �         P`.debug$S        0  =� m�     
   @B.text$mn        <   掩 
�         P`.debug$S        L  +� w�     
   @B.text$mn        !   圮          P`.debug$S        <  � L�        @B.text$mn        2   堔 恨         P`.debug$S        <  无 
�        @B.text$mn        "   傕              P`.debug$S        �  む <�        @B.text$mn        "   茆              P`.debug$S        �   婁        @B.text$mn        
   *� 7�         P`.debug$S        �  A� 冁        @B.text$mn        2   � G�         P`.debug$S        �  Q� =�        @B.text$mn        2   甸 玳         P`.debug$S        �  耖 匐        @B.text$mn        2   Q� 冹         P`.debug$S        �  嶌 q�        @B.text$mn        ^   轭 G�         P`.debug$S        X  [� 瞅        @B.text$mn        �   {� ,�         P`.debug$S        �  J� 
�        @B.text$mn        �   � 岿         P`.debug$S        �  甍 朂        @B.text$mn           欮 烚         P`.debug$S        h            @B.text$mn        ?   L  �          P`.debug$S        \  �  �        @B.text$mn        9   s �         P`.debug$S        �   � �        @B.text$mn        �   � �         P`.debug$S        �  � q	     *   @B.text$mn        �    �         P`.debug$S        �  � �        @B.text$mn        �   � `         P`.debug$S        �  ~      $   @B.text$mn           n �         P`.debug$S        �   � y        @B.text$mn           � �         P`.debug$S        �   � �        @B.text$mn        �   � o         P`.debug$S        �  � G        @B.text$mn        �   7 2          P`.debug$S        \  <  �&     4   @B.text$mn        �   �( :)         P`.debug$S        �  D) @-         @B.text$mn        B   �. �.         P`.debug$S           �. �/        @B.text$mn        B   0 ^0         P`.debug$S          |0 �1        @B.text$mn        B   �1 
2         P`.debug$S        �   (2 $3        @B.text$mn        H   `3              P`.debug$S        �  �3 l5        @B.text$mn        �  �6              P`.debug$S        �  88 �>     6   @B.text$mn        j
  A 侹     ;    P`.debug$S        �8  蠱 `�     �  @B.text$x            ,� 8�         P`.text$x            B� N�         P`.text$x         -   X� 厳         P`.text$x            櫁          P`.text$x            瘲 粭         P`.text$x            艞 褩         P`.text$x            蹢 鐥         P`.text$x            駰 龡         P`.text$x            � �         P`.text$x         -   � J�         P`.text$x            ^� j�         P`.text$x            t� ��         P`.text$x         -   姌 窐         P`.text$x            藰 讟         P`.text$x            針 順         P`.text$x            鳂 �         P`.text$x            
� �         P`.text$mn        �  #� 稓         P`.debug$S          鼩 �     :   @B.text$mn        �  H� 抓         P`.debug$S          � !�     :   @B.text$mn            e� 叝         P`.debug$S        �   ／ g�        @B.text$mn        �   ０ .�         P`.debug$S        H  L� 敶        @B.text$mn           樀 ┑         P`.debug$S        �   降 q�        @B.text$mn            径         P`.debug$S          叶 薹        @B.text$mn           � +�         P`.debug$S          ?� G�        @B.text$mn           児 敼         P`.debug$S           ü ê        @B.text$mn        `  浜 D�         P`.debug$S        �  敿 �     B   @B.text$mn        A   ㄇ 榍         P`.debug$S        �   笔        @B.text$mn           邓 人         P`.debug$S        �   宜 μ        @B.xdata             馓             @0@.pdata             鎏 �        @0@.xdata              �             @0@.pdata             (� 4�        @0@.xdata             R�             @0@.pdata             ^� j�        @0@.xdata             埻             @0@.pdata             愅 溚        @0@.xdata             和             @0@.pdata             仆 彝        @0@.xdata             鹜             @0@.pdata              �        @0@.xdata             "�             @0@.pdata             .� :�        @0@.xdata             X�             @0@.pdata             `� l�        @0@.xdata             娢             @0@.pdata             捨 炍        @0@.xdata             嘉             @0@.pdata             形 芪        @0@.xdata                          @0@.pdata             � �        @0@.xdata             0�             @0@.pdata             8� D�        @0@.xdata             b�             @0@.pdata             v� 傁        @0@.xdata             犗 聪        @0@.pdata             蚁 尴        @0@.xdata              �        @0@.pdata             *� 6�        @0@.xdata             T� h�        @0@.pdata             喰 捫        @0@.xdata             靶 佬        @0@.pdata             孕 嘈        @0@.xdata          	    �        @@.xdata             � !�        @@.xdata             +�             @@.xdata             .� >�        @0@.pdata             R� ^�        @0@.xdata          	   |� 呇        @@.xdata             櫻 熝        @@.xdata             ┭             @@.xdata              佳        @0@.pdata             醒 苎        @0@.xdata          	    �        @@.xdata             � �        @@.xdata             '�             @@.xdata             ,� <�        @0@.pdata             P� \�        @0@.xdata          	   z� 円        @@.xdata             椧 澮        @@.xdata             б             @@.xdata              囊        @0@.pdata             匾 湟        @0@.xdata          	   � �        @@.xdata             � %�        @@.xdata             /�             @@.xdata             :� N�        @0@.pdata             b� n�        @0@.xdata          	   層 曈        @@.xdata             ┯         @@.xdata             褂             @@.xdata             居 钟        @0@.pdata             暧 鲇        @0@.xdata          	   � �        @@.xdata             1� 7�        @@.xdata             A�             @@.xdata             T�             @0@.pdata             `� l�        @0@.xdata             娫 炘        @0@.pdata             荚 仍        @0@.xdata             嬖 鲈        @0@.pdata             �  �        @0@.xdata             >�             @0@.pdata             J� V�        @0@.xdata              t� 斦        @0@.pdata             舱 菊        @0@.xdata             苷 鹫        @0@.pdata             � �        @0@.xdata             8� H�        @0@.pdata             f� r�        @0@.xdata              愔 爸        @0@.pdata             沃 谥        @0@.xdata              �        @0@.pdata             &� 2�        @0@.xdata             P�             @0@.pdata             X� d�        @0@.xdata             傋             @0@.pdata             幾 氉        @0@.xdata             缸 套        @0@.pdata             曜 鲎        @0@.xdata             � $�        @0@.pdata             B� N�        @0@.xdata             l�             @0@.pdata             x� 勜        @0@.xdata              ⒇ 仑        @0@.pdata             嘭 熵        @0@.xdata             
� �        @0@.pdata             <� H�        @0@.xdata             f� v�        @0@.pdata             斮 犢        @0@.xdata              举 拶        @0@.pdata              �        @0@.xdata             &� 6�        @0@.pdata             T� `�        @0@.xdata             ~�             @0@.pdata             嗂 捼        @0@.xdata             摆 磊        @0@.pdata             在 嘹        @0@.xdata          	    �        @@.xdata             � !�        @@.xdata             +�             @@.xdata          $   .� R�        @0@.pdata             f� r�        @0@.xdata          	   愛 欅        @@.xdata          �    A�        @@.xdata          k   �             @@.xdata             堓             @0@.pdata             愝 溳        @0@.xdata             狠             @0@.pdata             螺 屋        @0@.xdata             燧             @0@.pdata             糨  �        @0@.xdata             �             @0@.pdata             :� F�        @0@.xdata             d�             @0@.pdata             l� x�        @0@.xdata             栟             @0@.pdata             炥         @0@.xdata             绒             @0@.pdata             赞 噢        @0@.xdata              �        @0@.pdata             0� <�        @0@.xdata             Z� j�        @0@.pdata             堖 斶        @0@.xdata             策             @0@.pdata             哼 七        @0@.xdata              溥 �        @0@.pdata             � $�        @0@.xdata          	   B� K�        @@.xdata             _� e�        @@.xdata             o�             @@.xdata             }�             @0@.pdata             夃 曕        @0@.xdata             赤 相        @0@.pdata             汔 镟        @0@.xdata          
   
� �        @@.xdata             8�             @@.xdata             ?� U�        @@.xdata             s� z�        @@.xdata             勧 嬦        @@.xdata             曖 溼        @@.xdata             ︶             @@.xdata             滇             @0@.pdata             玲 歪        @0@.xdata             脶 �        @0@.pdata             � #�        @0@.xdata             A� O�        @@.xdata             m�             @@.xdata             q� y�        @@.xdata             冣 娾        @@.xdata             斺             @@.xdata             椻             @0@.pdata             ｂ         @0@.xdata             外 邂        @0@.pdata              �        @0@.xdata             #� 1�        @@.xdata             O�             @@.xdata             S� [�        @@.xdata             e� l�        @@.xdata             v�             @@.xdata             y�             @0@.pdata             呫 戙        @0@.voltbl                           .voltbl            般               .voltbl            便               .voltbl            层               .voltbl            炽               .xdata             淬 秀        @0@.pdata             溷 疸        @0@.xdata          	   � �        @@.xdata             +� 2�        @@.xdata             <�             @@.xdata             ?� [�        @0@.pdata             o� {�        @0@.xdata          	   欎         @@.xdata             朵 间        @@.xdata             其             @@.xdata             黉 皲        @0@.pdata             � �        @0@.xdata          	   /� 8�        @@.xdata             L� X�        @@.xdata             l�             @@.xdata             s� 嬪        @0@.pdata             熷         @0@.xdata          	   慑 义        @@.xdata          
   驽 箦        @@.xdata             �             @@.xdata             �             @0@.pdata             � (�        @0@.xdata             F�             @0@.pdata             R� ^�        @0@.xdata             |�             @0@.pdata             堟 旀        @0@.xdata             叉 奇        @0@.pdata             阪 骀        @0@.xdata          	   � 
�        @@.xdata             !� '�        @@.xdata             1�             @@.xdata             <�             @0@.pdata             H� T�        @0@.xdata             r�             @0@.pdata             z� 嗙        @0@.rdata             ょ 肩        @@@.rdata             阽             @@@.rdata             扃 �        @@@.rdata             "� :�        @@@.rdata             X�             @@@.xdata$x           m� 夎        @@@.xdata$x           濊 硅        @@@.data$r         /   阻 �        @@�.xdata$x        $   � 4�        @@@.data$r         $   H� l�        @@�.xdata$x        $   v� 氶        @@@.data$r         $    议        @@�.xdata$x        $   荛  �        @@@.rdata             �             @@@.data               $�             @ @�.rdata          "   D�             @@@.rdata             f�             @@@.rdata             t�             @0@.rdata             y�             @0@.rdata             {�             @0@.rdata             傟             @0@.rdata          	   囮             @@@.rdata             愱             @@@.rdata             涥             @0@.rdata             ㈥             @@@.rdata$r        $   碴 株        @@@.rdata$r           絷 �        @@@.rdata$r           � �        @@@.rdata$r        $   (� L�        @@@.rdata$r        $   `� 勲        @@@.rdata$r           ㈦ 峨        @@@.rdata$r           离 噪        @@@.rdata$r        $   桦 �        @@@.rdata$r        $    � D�        @@@.rdata$r           b� v�        @@@.rdata$r           �� 滌        @@@.rdata$r        $   红 揿        @@@.debug$S        4   蜢 &�        @B.debug$S        4   :� n�        @B.debug$S        @   傢 马        @B.chks64         @  猪              
     /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   k  \     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\RayTracingPass.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Ensure_adl  $_Weak_order  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 
 $filesystem 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $vfs  $math 	 $colors  $log 	 $stdext  �   5�  # U  2 std::ratio<50,438291>::num ' U  �� std::ratio<50,438291>::den N U  ��Q std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<86400,1> >::_Dx2 L U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Nx2 P U  ��Q std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gx K U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,86400> >::_Gy -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Bucket_size -_   std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Min_buckets ':    std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> >::_Multi 2 U   std::integral_constant<__int64,24>::value  U   std::ratio<1,24>::num  U   std::ratio<1,24>::den W _   std::allocator<donut::engine::ShaderMacro>::_Minimum_asan_allocation_alignment N U  �r ( std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<2629746,1> >::_Dx2 L U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Nx2 P U  �r ( std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gx K U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,2629746> >::_Gy  U   std::ratio<1,1>::num  U   std::ratio<1,1>::den 2 U  
 std::integral_constant<__int64,10>::value # U  
 std::ratio<10,438291>::num ' U  �� std::ratio<10,438291>::den ; :   std::atomic<unsigned __int64>::is_always_lock_free J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N U  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >::_Minimum_asan_allocation_alignment L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( U  ��枠 std::ratio<10000000,1>::num $ U   std::ratio<10000000,1>::den O U  �X呩std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<31556952,1> >::_Dx2 P U  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy M U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx1 ) <   nvrhi::ObjectTypes::SharedHandle M U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Nx2 - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext Q U  �X呩std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Dx2 / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gx 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView L U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,31556952> >::_Gy 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device < U  ��枠 std::integral_constant<__int64,10000000>::value 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + <  �  nvrhi::ObjectTypes::VK_Sampler 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule 1 U   std::integral_constant<__int64,5>::value . <  �
  nvrhi::ObjectTypes::VK_RenderPass / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo 6 :   std::_Iterator_base0::_Unwrap_when_unverified - :   std::chrono::steady_clock::is_steady # U   std::ratio<5,2629746>::num ' U  �r ( std::ratio<5,2629746>::den C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity & U   std::ratio<1,1000000000>::num * U  � 蕷;std::ratio<1,1000000000>::den 7 :   std::_Iterator_base12::_Unwrap_when_unverified L U  ��Q std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Nx2 H U   std::_Ratio_divide<std::ratio<60,1>,std::ratio<86400,1> >::_Dx2 d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE J U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx1 f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx1 q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity J U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Nx2 q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size N U  ��Q std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Dx2 I U  < std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gx I U   std::_Ratio_multiply<std::ratio<60,1>,std::ratio<1,86400> >::_Gy j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size 4 U  �std::integral_constant<__int64,1440>::value   U   std::ratio<1,1440>::num   U  �std::ratio<1,1440>::den C :   std::atomic<std::chrono::tzdb_list *>::is_always_lock_free O U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Nx2 S U  � 蕷;std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Dx2 ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos � :   std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >::_Unwrap_when_unverified Q U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx1 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx1 * U  � 蕷;std::ratio<1000000000,1>::num & U   std::ratio<1000000000,1>::den U U  � 蕷;std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx2 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx2 P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gx P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gy E U  
� 牳0F  std::integral_constant<__int64,3600000000000>::value 8 :   std::atomic<unsigned long>::is_always_lock_free 1 U  
� 牳0F  std::ratio<3600000000000,1>::num ) U   std::ratio<3600000000000,1>::den / <  � nvrhi::rt::cluster::kClasByteAlignment . <   nvrhi::rt::cluster::kClasMaxTriangles - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex . :    std::integral_constant<bool,0>::value ( �    std::chrono::local_info::unique - �   std::chrono::local_info::nonexistent + �   std::chrono::local_info::ambiguous % _   std::ctype<char>::table_size  ;  ��I@donut::math::PI_f " s  
�-DT�!	@donut::math::PI_d ! ;  ��7�5donut::math::epsilon " ;  �  �donut::math::infinity  ;  �  �donut::math::NaN G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 M U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,1> >::_Gy . :   std::integral_constant<bool,1>::value L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gy : _   std::integral_constant<unsigned __int64,1>::value > U  � 蕷;std::integral_constant<__int64,1000000000>::value E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment F U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Nx2 F U  �std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000> >::_Dx2 * :    std::chrono::utc_clock::is_steady H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx1   U  �std::ratio<1000,1>::num   U   std::ratio<1000,1>::den C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE H U  �std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Nx2 E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gx P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity G U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000,1> >::_Gy ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask 4 U  �std::integral_constant<__int64,1000>::value d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size L U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Nx2 P U  � 蕷;std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Dx2 * :    std::chrono::tai_clock::is_steady 6 v,  �奮�std::chrono::tai_clock::_Tai_epoch_adjust N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gy C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 C U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx1 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Nx2 E U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Dx2 D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gx D U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,1> >::_Gy * :    std::chrono::gps_clock::is_steady 6 v,  �w�*韘td::chrono::gps_clock::_Gps_epoch_adjust ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos   �   std::_Iosb<int>::skipws ! �   std::_Iosb<int>::unitbuf # �   std::_Iosb<int>::uppercase " �   std::_Iosb<int>::showbase # �   std::_Iosb<int>::showpoint ! �    std::_Iosb<int>::showpos  �  @ std::_Iosb<int>::left  �  � std::_Iosb<int>::right " �   std::_Iosb<int>::internal  �   std::_Iosb<int>::dec  �   std::_Iosb<int>::oct  �   std::_Iosb<int>::hex $ �   std::_Iosb<int>::scientific  �    std::_Iosb<int>::fixed 5 :    std::filesystem::_File_time_clock::is_steady " �   0std::_Iosb<int>::hexfloat # �   @std::_Iosb<int>::boolalpha " �  � �std::_Iosb<int>::_Stdio % �  �std::_Iosb<int>::adjustfield # �   std::_Iosb<int>::basefield $ �   0std::_Iosb<int>::floatfield ! �    std::_Iosb<int>::goodbit   �   std::_Iosb<int>::eofbit ! �   std::_Iosb<int>::failbit   �   std::_Iosb<int>::badbit  �   std::_Iosb<int>::in  �   std::_Iosb<int>::out  �   std::_Iosb<int>::ate  �   std::_Iosb<int>::app  �   std::_Iosb<int>::trunc # �  @ std::_Iosb<int>::_Nocreate $ �  � std::_Iosb<int>::_Noreplace   �    std::_Iosb<int>::binary  �    std::_Iosb<int>::beg  �   std::_Iosb<int>::cur  �   std::_Iosb<int>::end , �  @ std::_Iosb<int>::_Default_open_prot  U  < std::ratio<60,1>::num  U   std::ratio<60,1>::den M U  �r ( std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Nx2 I U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<2629746,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx1 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Nx2 O U  �r ( std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Dx2 J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gx J U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,2629746> >::_Gy N U  �X呩std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<31556952,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Nx2 P U  �X呩std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,31556952> >::_Gy K U  ��Q std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Nx2 G U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<86400,1> >::_Dx2 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx1 I U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Nx2 M U  ��Q std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gx H U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1,86400> >::_Gy   U   std::ratio<1,1000>::num   U  �std::ratio<1,1000>::den ) <   donut::math::vector<bool,2>::DIM ) <   donut::math::vector<bool,3>::DIM # U   std::ratio<1,1000000>::num ' U  �@B std::ratio<1,1000000>::den N :   std::_Trivial_cat<char,char,char &,char &>::_Same_size_and_compatible ) <   donut::math::vector<bool,4>::DIM K :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_constructible H :   std::_Trivial_cat<char,char,char &,char &>::_Bitcopy_assignable _ _   std::allocator<std::filesystem::_Find_file_handle>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Same_size_and_compatible � :    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Bitcopy_constructible � :    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro &&,donut::engine::ShaderMacro &>::_Bitcopy_assignable D _   ��std::basic_string_view<char,std::char_traits<char> >::npos � :   std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle,std::filesystem::_Find_file_handle &&,std::filesystem::_Find_file_handle &>::_Bitcopy_assignable : _    std::integral_constant<unsigned __int64,0>::value Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment 9 �   std::chrono::_Time_parse_fields::_Era_begin_wday S _   std::allocator<std::chrono::time_zone>::_Minimum_asan_allocation_alignment / U   std::ratio<1,1000000000000000000>::num 7 U  
�  dС多
std::ratio<1,1000000000000000000>::den � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos U :   std::_String_view_iterator<std::char_traits<char> >::_Unwrap_when_unverified T U  �r ( std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<2629746,1> >::_Dx2 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx1 V U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx1 R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Dx2 Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,2629746> >::_Gy F U  
� 泌�  std::integral_constant<__int64,26297460000000>::value \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment * U   std::ratio<1,26297460000000>::num 2 U  
� 泌�  std::ratio<1,26297460000000>::den � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible ) �    std::_Invoker_functor::_Strategy � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible U U  �X呩std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Nx2 � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable Q U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<31556952,1> >::_Dx2 X _   std::allocator<std::chrono::time_zone_link>::_Minimum_asan_allocation_alignment , �   std::_Invoker_pmf_object::_Strategy S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx1 W U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx1 - �   std::_Invoker_pmf_refwrap::_Strategy S U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Nx2 W U  �X呩std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Dx2 J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gx R U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,31556952> >::_Gy - �   std::_Invoker_pmf_pointer::_Strategy G U  
� <$A std::integral_constant<__int64,315569520000000>::value , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy + U   std::ratio<1,315569520000000>::num 3 U  
� <$A std::ratio<1,315569520000000>::den R U  ��Q std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<86400,1> >::_Dx2 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx1 T U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,86400> >::_Gy D U  
� 纈*�   std::integral_constant<__int64,864000000000>::value ( U   std::ratio<1,864000000000>::num 0 U  
� 纈*�   std::ratio<1,864000000000>::den L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos * <   donut::math::vector<float,3>::DIM U _   std::allocator<std::chrono::leap_second>::_Minimum_asan_allocation_alignment L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Same_size_and_compatible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_constructible � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second const &,std::chrono::leap_second &>::_Bitcopy_assignable J U  std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<3600,1> >::_Dx2 P U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx1   U   std::ratio<1,3600>::num   U  std::ratio<1,3600>::den L U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Nx2 L U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Dx2 K U  std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gx K U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,3600> >::_Gy * <   donut::math::vector<float,4>::DIM * 錏        donut::math::lumaCoefficients  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 * <   donut::math::vector<float,2>::DIM F U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 H U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx1 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Nx2 H U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Dx2 G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gx G U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,1> >::_Gy 4 U  std::integral_constant<__int64,3600>::value ' U  �r ( std::ratio<2629746,1>::num # U   std::ratio<2629746,1>::den : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; _  �std::_Floating_type_traits<double>::_Exponent_mask J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified ' �  ��std::chrono::year::_Year_min % �  �std::chrono::year::_Year_max 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices ) <   donut::math::frustum::numCorners # �        nvrhi::AllSubresources $ �   ��std::strong_ordering::less $ �    std::strong_ordering::equal & �   std::strong_ordering::greater b <    std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::fractional_width ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong           nvrhi::EntireBuffer T U  �r ( std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<2629746,1> >::_Dx2 V U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx1 # U   std::ratio<1,2629746>::num ' U  �r ( std::ratio<1,2629746>::den R U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Nx2 V U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gx Q U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,2629746> >::_Gy A _   std::allocator<char>::_Minimum_asan_allocation_alignment 2 U   std::integral_constant<__int64,12>::value 7 :   std::atomic<unsigned int>::is_always_lock_free R U  ��Q std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Nx2 N U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<86400,1> >::_Dx2 C _   std::_Locked_pointer<std::_Stop_callback_base>::_Lock_mask T U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx1 D _    std::_Locked_pointer<std::_Stop_callback_base>::_Not_locked R _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_not_needed N _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_needed ! U   std::ratio<1,86400>::num % U  ��Q std::ratio<1,86400>::den I _   �黶td::_Locked_pointer<std::_Stop_callback_base>::_Ptr_value_mask : _   std::integral_constant<unsigned __int64,3>::value P U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Nx2 T U  ��Q std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gx O U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,86400> >::_Gy ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity : U  ��: std::integral_constant<__int64,146097>::value X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size 3 U  �std::integral_constant<__int64,400>::value ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size R :    std::filesystem::_Path_iterator<wchar_t const *>::_Unwrap_when_unverified T U  �X呩std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Nx2 P U   std::_Ratio_divide<std::ratio<2629746,1>,std::ratio<31556952,1> >::_Dx2 K :   std::atomic<std::_Stop_callback_base const *>::is_always_lock_free : _   std::integral_constant<unsigned __int64,2>::value V U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx1 R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx1 $ U   std::ratio<1,31556952>::num ( U  �X呩std::ratio<1,31556952>::den R U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Nx2 V U  �X呩std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Dx2 U U  �r ( std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gx Q U   std::_Ratio_multiply<std::ratio<2629746,1>,std::ratio<1,31556952> >::_Gy T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos  H    std::denorm_absent  H   std::denorm_present  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity & :    std::_Num_base::has_quiet_NaN * :    std::_Num_base::has_signaling_NaN # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix � :   std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro const &,donut::engine::ShaderMacro &>::_Same_size_and_compatible � :    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro const &,donut::engine::ShaderMacro &>::_Bitcopy_constructible ) H   std::_Num_float_base::has_denorm � :    std::_Trivial_cat<donut::engine::ShaderMacro,donut::engine::ShaderMacro,donut::engine::ShaderMacro const &,donut::engine::ShaderMacro &>::_Bitcopy_assignable + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed - :   std::_Num_float_base::is_specialized 4 _  @ _Mtx_internal_imp_t::_Critical_section_size * K   std::_Num_float_base::round_style 5 _   _Mtx_internal_imp_t::_Critical_section_align $ �   std::_Num_float_base::radix + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits * �   std::numeric_limits<bool>::digits + :    std::_Aligned<64,8,short,0>::_Fits ) :   std::_Aligned<64,8,int,0>::_Fits - :   std::numeric_limits<char>::is_signed - :    std::numeric_limits<char>::is_modulo * �   std::numeric_limits<char>::digits , �   std::numeric_limits<char>::digits10 4 :   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 :   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10  抏   _Mtx_try  抏   _Mtx_recursive - �    std::integral_constant<int,0>::value 1 :   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10  蔱   std::_INVALID_ARGUMENT  蔱   std::_NO_SUCH_PROCESS & 蔱   std::_OPERATION_NOT_PERMITTED , 蔱   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蔱   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN 1 :   std::numeric_limits<char32_t>::is_modulo . �    std::numeric_limits<char32_t>::digits % �    _Atomic_memory_order_relaxed 0 �  	 std::numeric_limits<char32_t>::digits10 % �   _Atomic_memory_order_consume % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Same_size_and_compatible . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_constructible - �   std::numeric_limits<short>::digits10 � :   std::_Trivial_cat<std::chrono::leap_second,std::chrono::leap_second,std::chrono::leap_second &&,std::chrono::leap_second &>::_Bitcopy_assignable 3 �  \ std::filesystem::path::preferred_separator , :   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 :   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 � :   std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Same_size_and_compatible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_constructible � :    std::_Trivial_cat<std::chrono::time_zone_link,std::chrono::time_zone_link,std::chrono::time_zone_link &&,std::chrono::time_zone_link &>::_Bitcopy_assignable 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10 Q U  �r ( std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<2629746,1> >::_Dx2 + �   std::numeric_limits<float>::digits - �   std::numeric_limits<float>::digits10 1 �  	 std::numeric_limits<float>::max_digits10 1 �  � std::numeric_limits<float>::max_exponent S U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx1 B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE 3 �  & std::numeric_limits<float>::max_exponent10 O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx1 2 �   �僺td::numeric_limits<float>::min_exponent O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Nx2 4 �   �踫td::numeric_limits<float>::min_exponent10 D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask S U  �r ( std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Dx2 O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity N U  6 std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gx N U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,2629746> >::_Gy ':   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Same_size_and_compatible $:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_constructible 4 U  @std::integral_constant<__int64,1600>::value , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 !:   std::_Trivial_cat<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &&,std::basic_string_view<wchar_t,std::char_traits<wchar_t> > &>::_Bitcopy_assignable 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size 7 U  �;緎td::integral_constant<__int64,48699>::value 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 $ U  @std::ratio<1600,48699>::num & U  �;緎td::ratio<1600,48699>::den � :   std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Same_size_and_compatible R U  �X呩std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Nx2 � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_constructible N U   std::_Ratio_divide<std::ratio<86400,1>,std::ratio<31556952,1> >::_Dx2 � :    std::_Trivial_cat<std::chrono::time_zone,std::chrono::time_zone,std::chrono::time_zone &&,std::chrono::time_zone &>::_Bitcopy_assignable T U  ��Q std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx1 P U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Nx2 T U  �X呩std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Dx2 O U  � std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gx O U   std::_Ratio_multiply<std::ratio<86400,1>,std::ratio<1,31556952> >::_Gy ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos    �   �  @ �   std::_General_precision_tables_2<float>::_Max_special_P 8 �  ' std::_General_precision_tables_2<float>::_Max_P $ U  �std::ratio<400,146097>::num ( U  ��: std::ratio<400,146097>::den A �   std::_General_precision_tables_2<double>::_Max_special_P 9 �  5std::_General_precision_tables_2<double>::_Max_P + :    std::_Aligned_storage<72,8>::_Fits * :    std::_Aligned<72,8,char,0>::_Fits + :    std::_Aligned<72,8,short,0>::_Fits ) :   std::_Aligned<72,8,int,0>::_Fits  �   � $ �   std::_Locbase<int>::collate " �   std::_Locbase<int>::ctype % �   std::_Locbase<int>::monetary $ �   std::_Locbase<int>::numeric ! �   std::_Locbase<int>::time % �    std::_Locbase<int>::messages   �  ? std::_Locbase<int>::all ! �    std::_Locbase<int>::none   U  std::ratio<3600,1>::num   U   std::ratio<3600,1>::den a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc const &,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc const &,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc const &,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable  U   std::ratio<24,1>::num  U   std::ratio<24,1>::den I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx1 I U  std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gy 9 U  ��Q std::integral_constant<__int64,86400>::value H :    std::reverse_iterator<wchar_t const *>::_Unwrap_when_unverified - <  `std::_Big_integer_flt::_Maximum_bits - <    std::_Big_integer_flt::_Element_bits . <  s std::_Big_integer_flt::_Element_count 1 U   std::integral_constant<__int64,1>::value % U  ��Q std::ratio<86400,1>::num ! U   std::ratio<86400,1>::den A _   std::allocator<bool>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc const &,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc const &,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc const &,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable  U   std::ratio<7,1>::num D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment  U   std::ratio<7,1>::den I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx1 M U  ��Q std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx2 I _   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gy � _   std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >::_Minimum_asan_allocation_alignment  �1   std::_Consume_header  �1   std::_Generate_header + �!        nvrhi::rt::c_IdentityTransform : U  ��:	 std::integral_constant<__int64,604800>::value J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 :    std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Multi P U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 ( U  ��: std::ratio<146097,400>::num :   std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0>::_Standard L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 $ U  �std::ratio<146097,400>::den L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy T U  ��: std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx1 P U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx1 T U  ��Q std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx2 P U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gx O U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gy B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE < U  �X呩std::integral_constant<__int64,31556952>::value D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable � _   std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >::_Minimum_asan_allocation_alignment a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size ( U  �X呩std::ratio<31556952,1>::num $ U   std::ratio<31556952,1>::den g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size  U   std::ratio<12,1>::num  U   std::ratio<12,1>::den P U  �r ( std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<2629746,1> >::_Dx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Dx2 N U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Nx2 R U  �r ( std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gx M U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,2629746> >::_Gy Q U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx1 ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx1  U   std::ratio<1,12>::num  U   std::ratio<1,12>::den M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx2 3 U  � std::integral_constant<__int64,200>::value M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gx L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gy ; U  �r ( std::integral_constant<__int64,2629746>::value $ U  � std::ratio<200,146097>::num ( U  ��: std::ratio<200,146097>::den x _   std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >::_Minimum_asan_allocation_alignment Q U  �X呩std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Nx2 M U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<31556952,1> >::_Dx2 O U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx1 O U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Nx2 S U  �X呩std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Dx2 N U  H std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gx N U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1,31556952> >::_Gy / :   std::atomic<long>::is_always_lock_free 2 U  2 std::integral_constant<__int64,50>::value - :    std::chrono::system_clock::is_steady $ U   std::ratio<1,10000000>::num ( U  ��枠 std::ratio<1,10000000>::den : U  �� std::integral_constant<__int64,438291>::value T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment  �  _CatchableType " �  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *>  '0  __std_tzdb_error & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  �0  _Ctypevec & �  $_TypeDescriptor$_extraBytes_28  #   uintmax_t     int64_t    _Smtx_t  齚  _Thrd_result  #   rsize_t  �>  __std_fs_find_data &   $_TypeDescriptor$_extraBytes_23 - cZ  $_s__CatchableTypeArray$_extraBytes_32 # )D  __std_fs_reparse_data_buffer Z _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Generic_reparse_buffer> ^  _  __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Mount_point_reparse_buffer> `   __std_fs_reparse_data_buffer::<unnamed-tag>::<unnamed-type-_Symbolic_link_reparse_buffer>  �>  __std_fs_dir_handle  �/  __std_access_rights    _TypeDescriptor & NZ  $_TypeDescriptor$_extraBytes_34  渇  _Stl_critical_section 	 I  tm % �  _s__RTTICompleteObjectLocator2  %�  RayTracingPass & kZ  $_TypeDescriptor$_extraBytes_30 A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const> & 焃  $_TypeDescriptor$_extraBytes_46  �  _s__CatchableType & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const>  銩  __std_fs_filetime E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & �  $_TypeDescriptor$_extraBytes_20  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16   SD  __std_fs_copy_file_result  �5  __std_code_page � 薧  std::_Simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > � 赹  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > z 韃  std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1> i鋇  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > >,1> � 躛  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > � 淾  std::_Default_allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > : 榝  std::_Vector_val<std::_Simple_types<unsigned int> > � O]  std::_Non_trivial_copy<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > � 蚟  std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > p 巉  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> M k^  std::_Normal_allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > � 萟  std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > . 綶  std::_Ptr_base<donut::vfs::IFileSystem> �糬  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > >,1> � 匽  std::_Non_trivial_move_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 盺  std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > � 猑  std::_Compressed_pair<std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::_Compressed_pair<std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,float,1>,1> k ,]  std::_Optional_destruct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,0> � a]  std::_Non_trivial_move<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > > � 瀆  std::allocator_traits<std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > 廭  std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> g\  std::_Hash<std::_Umap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >,0> > [ 坁  std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > ' {I  std::default_delete<wchar_t [0]> . /  std::_Conditionally_enabled_hash<int,1> A C  std::shared_ptr<std::filesystem::_Recursive_dir_enum_impl> ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit  坃  std::_Stop_callback_base  b  std::timed_mutex D �;  std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty - �?  std::reverse_iterator<wchar_t const *> 6 刕  std::allocator_traits<std::allocator<char8_t> > " 媂  std::_Char_traits<char,int>  0  std::_Fs_file  �=  std::optional<int> � 俕  std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > >  "   std::_Atomic_counter_t  N  std::_Num_base & $/  std::hash<std::error_condition> K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > 5 $0  std::_Tzdb_deleter<__std_tzdb_time_zones_info> = 色 std::basic_ostream<wchar_t,std::char_traits<wchar_t> > � z^  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > # �  std::numeric_limits<char8_t>  /,  std::_Big_uint128    std::condition_variable  �=  std::optional<__int64> E m^  std::allocator_traits<std::_Crt_allocator<std::chrono::tzdb> > G ^^  std::allocator_traits<std::allocator<std::chrono::leap_second> > ) 怷  std::_Narrow_char_traits<char,int>  j  std::hash<float> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc> ! �4  std::__floating_decimal_64 S 蘕  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone_link> > \^  std::list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > " 唂  std::_Align_type<double,64>  �5  std::less<void>  /  std::hash<int>  P  std::_Num_int_base  i3  std::ctype<wchar_t> 8 P�  std::initializer_list<donut::engine::ShaderMacro> " �/  std::_System_error_category / 揚  std::_Conditionally_enabled_hash<bool,1> + 琠  std::_Atomic_storage<unsigned int,4>  �5  std::_Format_arg_index  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > / y2  std::codecvt<char32_t,char8_t,_Mbstatet> k =]  std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > f �;  std::pair<std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >,bool> 碶  std::_Uhash_choose_transparency<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,void> u 訵  std::_Tidy_deallocate_guard<std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > > 6 餧  std::allocator_traits<std::allocator<wchar_t> > � s]  std::_Non_trivial_copy_assign<std::_Optional_construct_base<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  `  std::bad_cast  JR  std::equal_to<void> � 粿  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > > o 踈  std::_Conditionally_enabled_hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,1>     std::_Compare_t " u  std::numeric_limits<double> $ d  std::atomic<unsigned __int64>  �  std::__non_rtti_object , K2  std::_Codecvt_guard<char8_t,char16_t> ( @  std::_Basic_container_proxy_ptr12 2 #L  std::allocator<std::chrono::time_zone_link> = 6`  std::_Atomic_pointer<std::_Stop_callback_base const *> > 刦  std::vector<unsigned int,std::allocator<unsigned int> > T Tf  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy   �5  std::_Decode_result<char> 1    std::array<nvrhi::FramebufferAttachment,8>  q  std::_Num_float_base %  std::pointer_traits<wchar_t *>  抈  std::stop_token ` U�  std::_Vector_iterator<std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> > >  �-  std::logic_error 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety / 頚  std::allocator<std::chrono::leap_second> ! 頬  std::char_traits<char32_t>  Q1  std::locale  �1  std::locale::_Locimp  b1  std::locale::facet   k1  std::locale::_Facet_guard  1  std::locale::id [ 飓 std::basic_ostringstream<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > - VL  std::allocator<std::chrono::time_zone>  �  std::_Compare_ncmp s 竂  std::_Default_allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   R  std::numeric_limits<bool> # *Y  std::_WChar_traits<char16_t> P 獻  std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> > T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> Z W  std::_Uninitialized_backout_al<std::allocator<std::filesystem::_Find_file_handle> >  綻  std::stop_source R �9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > h {9  std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >::_Reallocation_policy M i?  std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t> > > � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  F.  std::overflow_error " x5  std::_Basic_format_arg_type , b2  std::_Codecvt_guard<char16_t,char8_t> % L  std::_One_then_variadic_args_t W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * jb  std::_Vb_val<std::allocator<bool> > E 鞢  std::unique_ptr<wchar_t [0],std::default_delete<wchar_t [0]> > j 靅  std::allocator_traits<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >   闿  std::char_traits<wchar_t> \ :  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > r �9  std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> >::_Reallocation_policy  pa  std::recursive_mutex   �  std::pmr::memory_resource  	>  std::pair<int,int> ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> � 鑍  std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > > n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  砞  std::false_type  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy � �:  std::tuple<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! a  std::hash<std::thread::id> $ 
6  std::_Fmt_fixed_buffer_traits  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char16_t> > > c 蠶  std::_Alloc_construct_ptr<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  扟  std::fpos<_Mbstatet> b gQ  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone> > > , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  俛  std::adopt_lock_t o 奓  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_current_zone_info>,__std_tzdb_current_zone_info *,1>  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , n  std::numeric_limits<unsigned __int64> F L  std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >  �0  std::_Locinfo 6 B  std::_Ptr_base<std::filesystem::_Dir_enum_impl> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 4 婯  std::_Atomic_padded<std::chrono::tzdb_list *> s 奤  std::_Uninitialized_backout_al<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > N 諼  std::_Default_allocator_traits<std::allocator<std::chrono::time_zone> > = x�  std::initializer_list<nvrhi::rt::PipelineHitGroupDesc> � 萞  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > > > 9 DJ  std::basic_streambuf<char,std::char_traits<char> > $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16>  �  std::string_view  �  std::wstring_view % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound  Qa  std::_Mutex_base  Z5  std::money_base  縘  std::money_base::pattern  y0  std::_Timevec  胉  std::nostopstate_t  f  std::defer_lock_t   �.  std::_Init_once_completer  絔  std::endian j sC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> > � BC  std::vector<std::filesystem::_Find_file_handle,std::allocator<std::filesystem::_Find_file_handle> >::_Reallocation_policy  �a  std::scoped_lock<> + �2  std::codecvt<wchar_t,char,_Mbstatet> h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> d xK  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::leap_second> > > Q 籡  std::allocator_traits<std::allocator<std::filesystem::_Find_file_handle> > % 5  std::array<nvrhi::Viewport,16> f D�  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> > >    std::_Iterator_base12 � 颸  std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > >  mN  std::_Pocma_values . B0  std::_Tzdb_deleter<__std_tzdb_sys_info> 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> ! /  std::hash<std::error_code> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > N 	@  std::_String_iterator<std::_String_val<std::_Simple_types<char32_t> > > ( 筣  std::ratio<1,1000000000000000000> @ nY  std::_Default_allocator_traits<std::allocator<char32_t> > ( d  std::_Atomic_padded<unsigned int>  �?  std::allocator<char32_t> ? !D  std::unique_ptr<char [0],std::default_delete<char [0]> > $ �  std::_Atomic_integral<long,4>  L<  std::_Flist_unchecked_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,std::_Iterator_base0> R =K  std::basic_ostringstream<char,std::char_traits<char>,std::allocator<char> >  桺  std::hash<bool>     std::streamsize 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty � �;  std::_Compressed_pair<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >,std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> >,1> ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> m �  std::_Tidy_guard<std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > > 2 鼿  std::initializer_list<nvrhi::IBindingSet *> � �:  std::tuple<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> >,std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > " b  std::lock_guard<std::mutex> N 蔝  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone> > ] �:  std::_Tuple_val<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > W \� std::basic_stringbuf<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > S 礥  std::_Uninitialized_backout_al<std::allocator<std::chrono::time_zone_link> >  �  std::hash<long double> � 侤  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > � Q@  std::vector<std::basic_string_view<wchar_t,std::char_traits<wchar_t> >,std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >::_Reallocation_policy W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy [ �<  std::_Flist_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > >   �  std::_Comparison_category  f  std::try_to_lock_t H 蠬  std::initializer_list<nvrhi::RefCountPtr<nvrhi::IBindingLayout> > U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > $ �5  std::_Decode_result<char32_t> # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0 R f  std::_Default_allocator_traits<std::allocator<donut::engine::ShaderMacro> >  w  std::hash<double> H f  std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> > O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> n �:  std::_Tuple_val<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > " 猘  std::_Align_type<double,72> G �;  std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> >  �5  std::_Lazy_locale / Y  std::_Char_traits<char32_t,unsigned int>  >/  std::_System_error < 朿  std::_Atomic_padded<std::_Stop_callback_base const *> ( �)  std::hash<nvrhi::FramebufferInfo>  螸  std::_Fmt_buffer<char> 9 桰  std::allocator<std::filesystem::_Find_file_handle>  �.  std::error_condition % 砞  std::integral_constant<bool,0>  �  std::bad_exception 1 鷈  std::allocator<donut::engine::ShaderMacro> & 肐  std::_Zero_then_variadic_args_t / 甝  std::_General_precision_tables_2<double> ? $`  std::_Atomic_storage<std::_Stop_callback_base const *,8> � ]L  std::_Compressed_pair<std::allocator<std::chrono::time_zone>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >,1>  �  std::u32string N {J  std::basic_stringbuf<char,std::char_traits<char>,std::allocator<char> >  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>  �-  std::invalid_argument   玗  std::char_traits<char8_t> [   std::optional<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > . ]  std::_General_precision_tables_2<float> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 1 臺  std::_Atomic_integral_facade<unsigned int>  瀉  std::cv_status S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> > b L:  std::unique_ptr<__std_tzdb_time_zones_info,std::_Tzdb_deleter<__std_tzdb_time_zones_info> > + rD  std::pair<enum __std_win_error,bool> S  ?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char8_t> > >  �5  std::_Fmt_codec_base<0> J ]  std::allocator_traits<std::allocator<std::chrono::time_zone_link> >  鑐  std::thread  a  std::thread::id S Y?  std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > >  �-  std::length_error  3a  std::jthread F TU  std::_Ref_count_obj2<std::filesystem::_Recursive_dir_enum_impl> � 烮  std::_Compressed_pair<std::allocator<std::filesystem::_Find_file_handle>,std::_Vector_val<std::_Simple_types<std::filesystem::_Find_file_handle> >,1> ! s  std::numeric_limits<float>  �4  std::time_base   }4  std::time_base::dateorder ) �  std::_Atomic_integral_facade<long>  絘  std::mutex % 盤  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " VP  std::hash<unsigned __int64> f 9  std::unique_ptr<__std_tzdb_current_zone_info,std::_Tzdb_deleter<__std_tzdb_current_zone_info> > 1 癒  std::_Flist_node<std::chrono::tzdb,void *>  ]  std::ratio<60,1> k 軰  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_time_zones_info>,__std_tzdb_time_zones_info *,1> : +6  std::_String_view_iterator<std::char_traits<char> >  '  std::exception_ptr  �  std::strong_ordering  ]  std::ratio<1,1000000> % �  std::_Itraits_pointer_strategy C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > )   std::hash<enum nvrhi::BlendFactor> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t>  �.  std::once_flag  �.  std::error_code T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy ' �4  std::pair<char *,enum std::errc> * �=  std::_Optional_destruct_base<int,1> ! �4  std::__floating_decimal_32  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy < �\  std::_Facetptr<std::codecvt<wchar_t,char,_Mbstatet> >  �3  std::_Iosb<int>   �3  std::_Iosb<int>::_Seekdir ! �3  std::_Iosb<int>::_Openmode   �3  std::_Iosb<int>::_Iostate ! �3  std::_Iosb<int>::_Fmtflags # �3  std::_Iosb<int>::_Dummy_enum 7 齖  std::allocator_traits<std::allocator<char32_t> >  ~[  std::nano I f  std::allocator_traits<std::allocator<donut::engine::ShaderMacro> > I 7L  std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > � 馶  std::allocator_traits<std::allocator<std::_List_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> >,void *> > > 1 Y  std::_Char_traits<char16_t,unsigned short> a _<  std::_Flist_const_iterator<std::_Flist_val<std::_Flist_simple_types<std::chrono::tzdb> > > $ �)  std::hash<nvrhi::BufferRange> V ^;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> > l -;  std::vector<std::chrono::leap_second,std::allocator<std::chrono::leap_second> >::_Reallocation_policy T �?  std::_String_const_iterator<std::_String_val<std::_Simple_types<char32_t> > >  "1  std::_Locbase<int> S bX  std::forward_list<std::chrono::tzdb,std::_Crt_allocator<std::chrono::tzdb> > ! 郳  std::char_traits<char16_t>  �  std::tuple<> 5 �<  std::_Atomic_pointer<std::chrono::tzdb_list *> P 燯  std::_Uninitialized_backout_al<std::allocator<std::chrono::leap_second> >  �  std::_Container_base12 W 轡  std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >  	/  std::io_errc  4  std::ios_base  )4  std::ios_base::_Fnarray  #4  std::ios_base::_Iosarray  �3  std::ios_base::Init  �3  std::ios_base::failure  �3  std::ios_base::event ) X  std::numeric_limits<unsigned char> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type   d  std::numeric_limits<long> " 衆  std::initializer_list<char>  �  std::_Invoker_strategy  鯟  std::nothrow_t  檁  std::_Stop_state 荺  std::_Uhash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > $ �  std::_Default_allocate_traits   璍  std::_Fmt_buffer<wchar_t> M 0?  std::_String_iterator<std::_String_val<std::_Simple_types<char8_t> > > N �?  std::_String_iterator<std::_String_val<std::_Simple_types<char16_t> > > 3 瞈  std::allocator_traits<std::allocator<char> > ! `  std::numeric_limits<short>  u   std::_Vbase . S  std::allocator<nvrhi::rt::GeometryDesc> # 醓  std::unique_lock<std::mutex>  痋  std::ratio<2629746,1> ; �  std::basic_string_view<char,std::char_traits<char> > �璡  std::unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::shared_ptr<donut::vfs::IBlob>,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,std::shared_ptr<donut::vfs::IBlob> > > > ! �3  std::ctype<unsigned short> C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > + �5  std::_Grapheme_Break_property_values < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty O 镻  std::unique_ptr<std::_Facet_base,std::default_delete<std::_Facet_base> > . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock � 鯧  std::_Compressed_pair<std::allocator<std::chrono::leap_second>,std::_Vector_val<std::_Simple_types<std::chrono::leap_second> >,1>  �  std::bad_alloc  ^.  std::underflow_error B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1> ) 礚  std::_Fmt_counting_buffer<wchar_t> c 蔏  std::_Compressed_pair<std::_Tzdb_deleter<__std_tzdb_leap_info [0]>,__std_tzdb_leap_info *,1> J 侷  std::_Compressed_pair<std::default_delete<wchar_t [0]>,wchar_t *,1> D oI  std::_Compressed_pair<std::default_delete<char [0]>,char *,1>   騙  std::atomic<unsigned int>  L5  std::messages_base - 遊  std::_Crt_allocator<std::chrono::tzdb> � f  std::_Compressed_pair<std::allocator<donut::engine::ShaderMacro>,std::_Vector_val<std::_Simple_types<donut::engine::ShaderMacro> >,1> g 癥  std::_Normal_allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > >  .  std::out_of_range # f  std::numeric_limits<__int64> i 鮅  std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > " 骵  std::initializer_list<bool>  +3  std::ctype<char> R �  std::_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> > d �:  std::_Tuple_val<std::vector<std::chrono::time_zone,std::allocator<std::chrono::time_zone> > >  h  std::memory_order ! (b  std::recursive_timed_mutex  �4  std::chars_format  T4  std::nullopt_t  V4  std::nullopt_t::_Tag  賉  std::ratio<3600,1> # �  std::_Atomic_storage<long,4> , �<  std::atomic<std::chrono::tzdb_list *> # *  std::hash<nvrhi::BlendState> / 譡  std::shared_ptr<donut::vfs::IFileSystem>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  U  std::in_place_t D jL  std::_Vector_val<std::_Simple_types<std::chrono::time_zone> >  a/  std::system_error < {X  std::_Default_allocator_traits<std::allocator<char> > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > g 楺  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> > >    std::ratio<1,1> ^ �;  std::unique_ptr<__std_tzdb_leap_info [0],std::_Tzdb_deleter<__std_tzdb_leap_info [0]> > 2 楠 std::_Measure_string_prefix_iterator_legacy ) �=  std::_Optional_construct_base<int>   D[  std::forward_iterator_tag  ..  std::runtime_error   
  std::bad_array_new_length E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > / 綾  std::_Atomic_storage<unsigned __int64,8>  舄 std::_Fmt_codec<char,1>  �0  std::_Yarn<char>  �  std::_Container_proxy (   std::_Facetptr<std::ctype<char> > Z WY  std::_Default_allocator_traits<std::allocator<std::filesystem::_Find_file_handle> >  鑕  std::allocator<bool>  �  std::u16string  ^  std::nested_exception  �  std::_Distance_unknown H jX  std::pointer_traits<std::_Flist_node<std::chrono::tzdb,void *> *> 7 K  std::basic_ostream<char,std::char_traits<char> > ( j  std::numeric_limits<unsigned int> < 7U  std::_Ref_count_obj2<std::filesystem::_Dir_enum_impl> E   std::allocator_traits<std::allocator<std::chrono::time_zone> > , 2  std::codecvt<char32_t,char,_Mbstatet>  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> , �2  std::_Codecvt_guard<char8_t,char32_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' w  std::numeric_limits<long double>  /  std::errc " hX  std::pointer_traits<char *> , 凷  std::default_delete<std::_Facet_base> 9 惈 std::basic_ios<wchar_t,std::char_traits<wchar_t> >  v.  std::range_error  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  �4  std::to_chars_result  颽  std::_UInt_is_zero  �  std::_Compare_eq  ~[  std::ratio<1,1000000000>  �?  std::allocator<char16_t> $ hI  std::default_delete<char [0]> . 糱  std::vector<bool,std::allocator<bool> > ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  |[  std::ratio<1,1000>  z[  std::ratio<1,10000000> ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount>  $1  std::_Crt_new_delete % �/  std::_Iostream_error_category2 * 糛  std::_String_constructor_concat_tag 4 c`  std::atomic<std::_Stop_callback_base const *>  �8  std::allocator<char>  愍 std::_Fmt_codec_base<1>    std::nullptr_t . 鏧  std::_Char_traits<char8_t,unsigned int> ' x[  std::_Floating_to_chars_overload & H[  std::random_access_iterator_tag ; RP  std::_Conditionally_enabled_hash<unsigned __int64,1> R 酙  std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> 3 P0  std::_Tzdb_deleter<__std_tzdb_leap_info [0]> @ 闎  std::_Ptr_base<std::filesystem::_Recursive_dir_enum_impl> # 鄀  std::allocator<unsigned int>  �0  std::_Yarn<wchar_t> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1> ; i�  std::initializer_list<nvrhi::rt::PipelineShaderDesc>  b  std::wstring 5 譪  std::_Atomic_integral_facade<unsigned __int64> ' V  std::numeric_limits<signed char> � 獲  std::_Vector_const_iterator<std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > > >  �-  std::domain_error  �  std::u32string_view  �  std::_Container_base  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  5  std::_Big_integer_flt  �  std::_Literal_zero $ cP  std::hash<nvrhi::IResource *>  �4  std::from_chars_result � *L  std::_Compressed_pair<std::allocator<std::chrono::time_zone_link>,std::_Vector_val<std::_Simple_types<std::chrono::time_zone_link> >,1> " w4  std::_Nontrivial_dummy_type 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char> D 譭  std::_Default_allocator_traits<std::allocator<unsigned int> > 8 �6  std::chrono::duration<int,std::ratio<2629746,1> > i u;  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<3600,1> > > & 28  std::chrono::year_month_weekday  u6  std::chrono::day 9 D-  std::chrono::duration<__int64,std::ratio<1,1000> > j %7  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<int,std::ratio<86400,1> > > n :=  std::chrono::time_point<std::chrono::gps_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  7  std::chrono::weekday  �,  std::chrono::nanoseconds  �6  std::chrono::year ' 8  std::chrono::year_month_day_last  -  std::chrono::minutes  �6  std::chrono::days y N=  std::chrono::time_point<std::filesystem::_File_time_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > ? �,  std::chrono::duration<__int64,std::ratio<1,1000000000> > # L7  std::chrono::weekday_indexed 9 �6  std::chrono::duration<int,std::ratio<31556952,1> >  �6  std::chrono::years , fX  std::chrono::duration_values<__int64>  v,  std::chrono::seconds " �8  std::chrono::time_zone_link 6 �6  std::chrono::duration<int,std::ratio<86400,1> > 3 -  std::chrono::duration<int,std::ratio<60,1> > 6 v,  std::chrono::duration<__int64,std::ratio<1,1> > s �,  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   q[  std::chrono::steady_clock   X  std::chrono::system_clock 6 *-  std::chrono::duration<double,std::ratio<60,1> > + X8  std::chrono::year_month_weekday_last  <  std::chrono::tzdb_list + l<  std::chrono::tzdb_list::_Unique_lock + *<  std::chrono::tzdb_list::_Shared_lock ; �-  std::chrono::duration<double,std::ratio<1,1000000> > > �-  std::chrono::duration<double,std::ratio<1,1000000000> > = \,  std::chrono::duration<__int64,std::ratio<1,10000000> > " �7  std::chrono::month_day_last  %7  std::chrono::sys_days n =  std::chrono::time_point<std::chrono::utc_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  �8  std::chrono::time_zone q L,  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >   `7  std::chrono::weekday_last ! �7  std::chrono::month_weekday 5 �,  std::chrono::duration<int,std::ratio<3600,1> >  i7  std::chrono::last_spec & �7  std::chrono::month_weekday_last  :7  std::chrono::local_days j �8  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,1> > > N SK  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > > Z .>  std::chrono::hh_mm_ss<std::chrono::duration<__int64,std::ratio<1,1> > >::<lambda_1>  �8  std::chrono::sys_seconds  �7  std::chrono::year_month 8 Z-  std::chrono::duration<double,std::ratio<1,1000> >  s7  std::chrono::month_day " �7  std::chrono::year_month_day  �,  std::chrono::hours < q-  std::chrono::duration<__int64,std::ratio<1,1000000> > @ m[  std::chrono::zoned_traits<std::chrono::time_zone const *> 5 �,  std::chrono::duration<double,std::ratio<1,1> >  �6  std::chrono::months  89  std::chrono::tzdb e :7  std::chrono::time_point<std::chrono::local_t,std::chrono::duration<int,std::ratio<86400,1> > >  @Y  std::chrono::gps_clock  =Y  std::chrono::tai_clock  �6  std::chrono::month n "=  std::chrono::time_point<std::chrono::tai_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > >  dX  std::chrono::utc_clock  h[  std::chrono::local_t & �=  std::chrono::_Time_parse_fields 8 y=  std::chrono::_Time_parse_fields::_Leap_second_rep = w=  std::chrono::_Time_parse_fields::_Parse_tp_or_duration 3 Y=  std::chrono::_Time_parse_fields::_FieldFlags H g[  std::chrono::duration<__int64,std::ratio<1,1000000000000000000> >  �8  std::chrono::leap_second 8 �,  std::chrono::duration<double,std::ratio<3600,1> >  �2  std::ctype_base  EA  std::filesystem::perms ' 繟  std::filesystem::directory_entry $ JA  std::filesystem::copy_options ( 5A  std::filesystem::filesystem_error 7 rR  std::filesystem::_Path_iterator<wchar_t const *> ) �>  std::filesystem::_Find_file_handle & y>  std::filesystem::_Is_slash_oper . 螧  std::filesystem::_Should_recurse_result $   std::filesystem::perm_options 4 滳  std::filesystem::recursive_directory_iterator . �A  std::filesystem::_File_status_and_error & 9B  std::filesystem::_Dir_enum_impl 0 KB  std::filesystem::_Dir_enum_impl::_Creator @ QB  std::filesystem::_Dir_enum_impl::_Creator::_Create_status ! TA  std::filesystem::file_type . nB  std::filesystem::_Directory_entry_proxy " 揇  std::filesystem::space_info * 桞  std::filesystem::directory_iterator & N=  std::filesystem::file_time_type 0 虰  std::filesystem::_Recursive_dir_enum_impl ) 鞟  std::filesystem::directory_options # gA  std::filesystem::file_status u 霡  std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > ( U[  std::filesystem::_File_time_clock  ?  std::filesystem::path $ �>  std::filesystem::path::format * ?R  std::filesystem::_Normal_conversion ? &� std::basic_streambuf<wchar_t,std::char_traits<wchar_t> > < gU  std::_Wrap<std::filesystem::_Recursive_dir_enum_impl> , �1  std::codecvt<char16_t,char,_Mbstatet> , �2  std::_Codecvt_guard<char32_t,char8_t>  R[  std::char_traits<char> � 镮  std::_Compressed_pair<std::allocator<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > >,std::_Vector_val<std::_Simple_types<std::basic_string_view<wchar_t,std::char_traits<wchar_t> > > >,1>  �.  std::error_category ) �.  std::error_category::_Addr_storage i o:  std::tuple<std::vector<std::chrono::time_zone_link,std::allocator<std::chrono::time_zone_link> > > ! �/  std::_System_error_message  �  std::_Unused_parameter h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1>  l4  std::bad_optional_access A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > , 確  std::_Atomic_integral<unsigned int,4> 7 !B  std::shared_ptr<std::filesystem::_Dir_enum_impl> " DT  std::_Floating_point_string = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> 2 �5  std::_Extended_Pictographic_property_values  �1  std::_Codecvt_mode @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn $ q?  std::ranges::_Iter_move::_Cpo ) o?  std::ranges::_Iter_move::_Cpo::_St % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn    std::_Exact_args_t  P[  std::ratio<86400,1>  V6  std::default_sentinel_t � A  std::pair<std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > >,std::filesystem::_Path_iterator<std::_String_const_iterator<std::_String_val<std::_Simple_types<wchar_t> > > > >  N[  std::ratio<31556952,1> 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8>  =K  std::ostringstream  6  std::_Fmt_buffer_traits \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty  g0  std::_Facet_base ' �)  std::hash<nvrhi::BindingSetItem> " 甔  std::_WChar_traits<wchar_t> 2 �2  std::codecvt<unsigned short,char,_Mbstatet> . �=  std::_Optional_destruct_base<__int64,1> - �=  std::_Optional_construct_base<__int64> # �/  std::_Generic_error_category  扟  std::streampos  B[  std::input_iterator_tag 2 JU  std::_Wrap<std::filesystem::_Dir_enum_impl> X 孲  std::_Compressed_pair<std::default_delete<std::_Facet_base>,std::_Facet_base *,1> 0 芻  std::_Atomic_integral<unsigned __int64,8> , 塪  std::_Atomic_padded<unsigned __int64> ' 綪  std::hash<enum nvrhi::ColorMask> / ,2  std::codecvt<char16_t,char8_t,_Mbstatet> 5 
`  std::_Locked_pointer<std::_Stop_callback_base> 3 豃  std::basic_ios<char,std::char_traits<char> >  �1  std::codecvt_base & 譒  std::_Fmt_counting_buffer<char>  錎  std::bad_function_call O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag ; 賓  std::allocator_traits<std::allocator<unsigned int> > ' 蜠  std::hash<std::filesystem::path> _ ?[  std::allocator_traits<std::_Crt_allocator<std::_Flist_node<std::chrono::tzdb,void *> > > 7 �<  std::_Atomic_storage<std::chrono::tzdb_list *,8>  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > 7 40  std::_Tzdb_deleter<__std_tzdb_current_zone_info> " �  std::_Asan_aligned_pointers F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  q5  std::format_error  �  std::partial_ordering # |5  std::_Decode_result<wchar_t> . <#  std::array<nvrhi::BindingLayoutItem,16>  9[  std::_Default_sentinel P 耎  std::_Default_allocator_traits<std::allocator<std::chrono::leap_second> > $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int> Z 僣  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > p Rc  std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Reallocation_policy   �5  std::_GB11_LeftHand_regex * �5  std::_GB11_LeftHand_regex::_State_t 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access 
 !   wint_t # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling $ �(  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets 0 緀  nvrhi::RefCountPtr<nvrhi::IShaderLibrary>  &  nvrhi::GraphicsState / W  nvrhi::static_vector<nvrhi::Viewport,16>  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray 2 乌  nvrhi::RefCountPtr<nvrhi::rt::IShaderTable> ) 艷  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! 緀  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc 2 馟  nvrhi::RefCountPtr<nvrhi::IComputePipeline>  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer / ペ  nvrhi::RefCountPtr<nvrhi::rt::IPipeline>    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray . 鮞  nvrhi::RefCountPtr<nvrhi::ICommandList>  艷  nvrhi::DeviceHandle   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture ! 蒪  nvrhi::utils::ScopedMarker $ =b  nvrhi::utils::BitSetAllocator . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  �(  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # �(  nvrhi::DescriptorTableHandle     nvrhi::ShaderType  �(  nvrhi::TimerQueryHandle   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  �(  nvrhi::HeapHandle # 馟  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  �(  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  �(  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  �#  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  �(  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 乌  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   ペ  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �(  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # �(  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  鮞  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable  萫  nvrhi::IShaderLibrary  �&  nvrhi::ComputeState  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery  �/  __std_win_error  S0  __std_tzdb_leap_info  �0  lconv   �  __RTTIBaseClassDescriptor & 局  $_TypeDescriptor$_extraBytes_72 
    _off_t    stat  t   int32_t  Z  timespec & JZ  $_TypeDescriptor$_extraBytes_37  cD  __std_fs_file_id 
 !   _ino_t 
 沘  _Cnd_t # 70  __std_tzdb_current_zone_info ' ID  __std_fs_create_directory_result  !   uint16_t  �/  __std_fs_stats ! 﨑  donut::engine::ShaderMacro # 紿  donut::engine::ShaderFactory " 沍  donut::engine::StaticShader  汦  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3 # 錏  donut::math::vector<float,3>  u   donut::math::uint  /F  donut::math::plane # F  donut::math::vector<float,4>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes  F  donut::math::float4 # 揈  donut::math::affine<float,3> " *E  donut::math::vector<bool,2>  GF  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>    _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *> ! )0  __std_tzdb_time_zones_info     intmax_t    terminate_handler  �  _s__RTTIBaseClassArray & VZ  $_TypeDescriptor$_extraBytes_52 
 Y  ldiv_t  �/  __std_fs_file_flags  �0  _Cvtvec  u   _Thrd_id_t - �  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray  �/  __std_fs_copy_options     ptrdiff_t  
  _stat64i32  E0  __std_tzdb_sys_info  �  _PMD      uint8_t  ,  type_info ' �  _s__RTTIClassHierarchyDescriptor  t   errno_t  �/  __std_fs_reparse_tag  \  _lldiv_t  C  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  G  _s__ThrowInfo  c6  __std_fs_convert_result  �/  __std_fs_stats_flags  �  __RTTIBaseClassArray  �  __crt_locale_data_public - �  $_s__CatchableTypeArray$_extraBytes_24  Ya  _Mtx_internal_imp_t & 4Z  $_TypeDescriptor$_extraBytes_25 % �  __RTTIClassHierarchyDescriptor  �0  _Collvec   艭  __std_fs_volume_name_kind 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t 
    fpos_t  �  FILE 
 Ma  _Mtx_t 3 �  __vcrt_va_list_is_reference<wchar_t const *>    mbstate_t  ?  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray  DD  __std_fs_remove_result  靈  _Thrd_t - �  $_s__RTTIBaseClassArray$_extraBytes_16 , �  $_s__RTTIBaseClassArray$_extraBytes_8 - /Z  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  �/  __std_fs_file_attr  �  __std_exception_data 
 u   _dev_t  紺  __std_ulong_and_error  A   __std_tzdb_epoch_milli  \  lldiv_t  Y  _ldiv_t  [  _timespec64     intptr_t  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers    �   H      x)憂^恛閶{籮>鐼末BA<颭~儽&'%;-g�  A    G�膢刉^O郀�/耦��萁n!鮋W VS  �    .QVBL籺'Z鬖b濁蟂�8w,/kr彨�$擠�  �    o�椨�4梠"愜��
}z�$ )鰭荅珽X     黸|�
C�%|�,臍稇l裹垓芻喭,vg�  J   ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  �   鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �   鬀擫搬頼侎谺襔�,鮬锛&�1�
L1-     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  j   *u\{┞稦�3壅阱\繺ěk�6U�  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  5    d蜯�:＠T邱�"猊`�?d�B�#G騋  q   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �   溶�$椉�
悇� 騐`菚y�0O腖悘T     U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  H   鸥鑩0
尪_灯
嶊)梃
濠�琂骢�7  �   觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  �   曀"�H枩U传嫘�"繹q�>窃�8     仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  C   噝"丮誧Nx]0R忧�
t鑅,漩嚛乃  �   +4[(広
倬禼�溞K^洞齹誇*f�5  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  .    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  l   a�傌�抣?�g]}拃洘銌刬H-髛&╟  �   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  E   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  =   歚W%虴�[�,莶CKF�AZⅰq恶�4�  |   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �   �"睱建Bi圀対隤v��cB�'窘�n  C	    狾闘�	C縟�&9N�┲蘻c蟝2  �	   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �	   d穕�璏弦搻H鯫BNU略�T.#汐  �	   �'稌� 变邯D)\欅)	@'1:A:熾/�  <
   D���0�郋鬔G5啚髡J竆)俻w��  �
   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �
   <AE#(�/y鍹籲贚痶蹒-	蒸|邨VWTR     �9俅�(� .]嬝暧烵啤�秾*~w�=[@�  U   蜅�萷l�/费�	廵崹
T,W�&連芿  �   揾配饬`vM|�%
犕�哝煹懿鏈椸  �   c�#�'�縌殹龇D兺f�$x�;]糺z�  %   藂帵穅u籃褤,刺8� 鏹y筈�
?蓴  k   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  �   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   zY{���睃R焤�0聃
扨-瘜}  0
   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  n
   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  �
   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �
   交�,�;+愱`�3p炛秓ee td�	^,  .   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  �   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   Pフ+
V
*戋zydD躩=壠�jA�N盪{u5  
   仫減f╋萩}_遫R衵9弧0Up*q蹩鎍,  K   f扥�,攇(�
}2�祛浧&Y�6橵�  �   �芮�>5�+鮆"�>fw瘛h�=^���  �   R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅     [届T藎秏1潴�藠?鄧j穊亘^a  S   悯R痱v 瓩愿碀"禰J5�>xF痧  �   矨�陘�2{WV�y紥*f�u龘��  �   斝�/5:澫酡Z瞮<箼�漻M洛OIl~      o藾錚\F鄦泭|嚎醖b&惰�_槮  _   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  :   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   饵嶝{郀�穮炗
AD2峵濝k鴖N  �   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰     {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  R   妇舠幸佦郒]泙茸餈u)	�位剎  �   靋!揕�H|}��婡欏B箜围紑^@�銵  �   �颠喲津,嗆y�%\峤'找_廔�Z+�     �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  b   鍂O婒
`�'R{耑4⑩衲>胳糝郪&爳Uz  �   t�j噾捴忊��
敟秊�
渷lH�#  �   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  $   q捉v�z�1乱#嗋�搇闚Q缨外
毛�  b   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  �   �,〓�婆谫K7涄D�
Cf�
X9U▏TG  $   � 罟)M�:J榊?纸i�6R�CS�7膧俇  w   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  �   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  )   E莕q�u伖娽%�9f�+囷�J襇�yz借�0  O   チ畴�
�&u?�#寷K�資 +限^塌>�j  �   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  �   �$晑�~2]�/
S蟦a� �
}A珈弿V緈     癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗  K   V� c鯐鄥杕me綻呥EG磷扂浝W)  �   穫農�.伆l'h��37x,��
fO��  �   +FK茂c�G1灈�7ほ��F�鳺彷餃�     5�\營	6}朖晧�-w氌rJ籠騳榈  I   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1  �   評>lO�1)峅rjf砵"虙片0慹炲�1忺�  �   蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦     L�9[皫zS�6;厝�楿绷]!��t  I   �	玮媔=zY沚�c簐P`尚足,\�>:O  �   �*o驑瓂a�(施眗9歐湬

�  �   嵮楖"qa�$棛獧矇oPc续忴2#
     语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  Z   �0�*е彗9釗獳+U叅[4椪 P"��  �    I嘛襨签.濟;剕��7啧�)煇9触�.  �   )�Q�Ａ9氫�&煝]R埬�楜� 磛庋p�     �)D舼PS橼鈝{#2{r�#獷欲3x(  m   �=蔑藏鄌�
艼�(YWg懀猊	*)  �   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  2   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  �   齝D屜u�偫[篔聤>橷�6酀嘧0稈  �   _O縋[HU-銌�鼪根�鲋薺篮�j��  	   l籴靈LN~噾2u�< 嵓9z0iv&jザ  [   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   �7頔碠<晔@岙�撁k4統N絠熙鶳 �  �   憒峦锴摦懣苍劇o刦澬z�/s▄![�     	{Z�范�F�m猉	痹缠!囃ZtK�T�  Z   険L韱#�簀O闚样�4莿Y丳堟3捜狰  �   v-�+鑟臻U裦@驍�0屽锯
砝簠@  �   
9信�;cGHR{匞U鐉�gPW$Y(厊\^s      N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  P    瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  �    
訍癿褎9P巵┠蝫虵艽"漒蘕聋  �    郖�Χ葦'S詍7,U若眤�M进`  "!   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  g!   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �!   E俋5N�o�0覺H骧"ν鐚擩韒殖@/k  �!   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  ""   繃S,;fi@`騂廩k叉c.2狇x佚�  k"   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �"   dhl12� 蒑�3L� q酺試\垉R^{i�  �"   猯�諽!~�:gn菾�]騈购����'  '#   `k�"�1�^�`�d�.	*貎e挖芺
脑�  i#   鏀q�N�&}
;霂�#�0ncP抝  �#   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �#   �
bH<j峪w�/&d[荨?躹耯=�  $   j轲P[塵5m榤g摏癭 鋍1O骺�*�  h$   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �$   +椬恡�
	#G許�/G候Mc�蜀煟-  �$   龀缬@唶�鱮�瓗禽鐑�^]>Xz豹tN  >%   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  �%   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�  �%   $^IXV嫓進OI蔁
�;T6T@佮m琦�   &   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  M&   鹴y�	宯N卮洗袾uG6E灊搠d�  �&   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �&   副謐�斦=犻媨铩0
龉�3曃譹5D   ''   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  g'   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  �'   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �'   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  2(   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  |(   豊+�丟uJo6粑'@棚荶v�g毩笨C  �(   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  )   ]V驀丱随^梷禂�5雃e飚职up�$�胀e  P)   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �)   匐衏�$=�"�3�a旬SY�
乢�骣�  �)      3*   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  ~*   o忍x:筞e飴刌ed'�g%X鶩赴5�n�  �*   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  �*   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  8+   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  v+   �X�& 嗗�鹄-53腱mN�<杴媽1魫  �+   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  ,   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  _,   �(M↙溋�
q�2,緀!蝺屦碄F觡  �   l      �  �  B   �  �  H   �  �  Y   �  �  �   r  p  U   s  p  �   �  (   K   �  �	  m   �  �    �  �  �   �    q   �    @   �    5   �    x   �    @   �    5   �  �    �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �  �   I  �  N  J  �  B  M  �  �
  O  �  �	  �  �    �  �  �   �  �  �   �    q   �  �    �  �  �   �  �  �   �  �  �   �  �    �  �  �   �  �  �   �  �  �   %  �  �  -  �  �  /  �  �  0  �  �  3  p  �  >  �  �  ?  �  0   j  �  �   k  �  �   �  �  �  �  �  D
  �  �  �  �  �  O   �  �  �   �  p  �  @  p  �  A  p  �  C  �  L
  D  �  L
  c  p  �   �  �  �  �  �  �  �  p  �    �  s    �  �  �  �  )
  (  p  �   i  �
  �  k  p  �  p  �
  �  r  p  �  �  �
  ]  �  �
  ]     p  �     p  >     p  �     p  �     p  >  9   p  �   I   p  @   W   p  �  Z   p  �  b   p  �  c   p  �  �   `  �   �   `  �   �   p  @   �   p  @   �#    x   �'  �  2   �'  �
  �  (  �  �  
(  �
  �  /(  �  @
  5(  p  �  t(  p  >  �(  �
  �  �(  �
  �  �(  �
  �  �(  �
  ]  )  p  �  )  p  �  )  p  �  )  p  �  )  `  �   )  p    �1  �
  �  �1  x   ,   >2  p  �  ?2  �
    a2  �
  �  v2  �
  �  �2  p    �2  p    �2  p  �  �2  `  �   a7  �
    d7  �    e7  �  �   h7  �    i7  �  �   l7  �    m7  �  �   n7  �
  �  o7  x   ,   q7  �
  `  r7  �
  �  s7  x   ,   u7  �
  `  v7  �  �   w7  �  �   7  �
  *   �7  �    �7  �  �   �7  �  �   �7  �    �7  �  �   �7  �  �   �7  �    �7  �  �   �7  �  �   �7  �
  �  �7  �
  �  �7  �  9
  �7  �
  �  �7  �
  c  �7  p  �  �7  p  �  �7  �  �  �7  �
  �  �7  �
  �  �7  �
  �  �7  �
  �  �7  �
  �  �7  `  �  �7  `  �  �7  `  3  �7  p    �7  p    �   �,   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\charconv D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iomanip C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sstream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\clocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\locale.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\chrono C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\RTXPT\Rtxpt\RTXDI\RayTracingPass.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_print.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stop_token D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\optional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xsmf_control.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_tzdb.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\format D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\locale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_format_ucd_tables.hpp D:\RTXPT\External\Donut\include\donut\core\math\math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocbuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\bit C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\streambuf D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xiosbase C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xloctime D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocale D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfacet D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmon D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\RTXPT\Rtxpt\RTXDI\RayTracingPass.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\RTXPT\External\Donut\include\donut\core\log.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\filesystem D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcharconv_ryu_tables.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception D:\RTXPT\External\Donut\include\donut\core\math\frustum.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\istream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ostream D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\RTXPT\External\Donut\include\donut\engine\ShaderFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ios D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocnum D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_xlocinfo_types.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iterator D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xlocmes D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\forward_list C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib D:\RTXPT\External\Donut\include\donut\core\math\quat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\predefined C++ types (compiler internal) C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xfilesystem_abi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp  �       Lk  羜      舚     
 鮺  u   鶁  u  
 泙  v   焵  v  
 Z�      ^�     
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁裥砓>Y7?樰�=      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �      /      5   3      �   �  k G            :      :   9         �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >_   _Bytes  AJ        9  $  >_    _Block_size  AH       1 
   >_    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        r  
 Z      N Z   �  q   (                      H 
 h   r         $LN14  0   _  O_Bytes  O   �   h           :   p  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   `   0   `  
 �   `   �   `  
 �   `   �   `  
 �   `   �   `  
   `     `  
 s  �   w  �  
 �  `   �  `  
 H塡$H塴$H塼$WAVAWH冹 M嬸H嬺L孂H�9L婣L+荌公*I嬃I麒H龙H嬄H凌?H蠰;騰"I嬛�    M嬒M�I嬛H嬑�    I塆�+  L婣I嬋H+螴嬃H鏖H嬟H聋H嬅H凌?H豅;髒@I;鴗!D  H嬛H嬒�    H兦0H兤0M婫I;鴘銵+驧嬒I嬛H嬑�    I塆榕   K�,vH铃H颩咑剱   H兤f怘峍鐷;鷗H�>vH�L婩鳫嬒�    H媈H9_ t#H呟t
H�H嬎�P怘婳 H塤 H吷tH��P怘媈H9_(t#H呟t
H�H嬎�P怘婳(H塤(H吷tH��P怘兦0H兤0I冾卾���I�H嬢H;飔H嬎�    H兠0H;遳颕塷H媆$@H媗$HH媡$PH兡 A_A^_肦   B   c   X   �   >   �   X   
  8   �  =      �   J  � G            �     �  �7        �std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Assign_counted_range<nvrhi::rt::PipelineShaderDesc const *> 
 >{'   this  AJ        !  AW  !     � >q'   _First  AK          AL       �  AL z    0  >_   _Newsize  AP        (  >_    _Oldsize  AI  �      
 >6'    _Mid  AM  �     
  AM �      
 6 �  >!'    _Newlast  AN  �     �  AN �    
  M           亖	
 >6'   _First  AI  �      AI �      >!'   _Last  AM  ~      AM �      N' M        �7   	��67 >#    _Count  AV       ��   AV �      >6'   _Dest  AM  $     Z| 6  AM �      
 6 �  M        j  �;
 M        �  乗 M        �  乗
 >t$    temp  AI  ?    ;  AI �     � " h  N N M        �  乀 N M        �  丒 M        �  丒#	 N N N M        �  �
 M        �  �/ M        �  �/
 >k$    temp  AI      -  N N M        �  �' N M        �  � M        k  �#	 N N N M        I  ��L	
 Z   )   M        /  �� >�    _Result  AK        N N N Z   �7  �7  �7  �7                        0@ z h   t  �  �  �  �  I  �  �  �  �  �  �  #  $  /  j  k  �  �  =  >  l        �   �   !  �7  �7   @   {'  Othis  H   q'  O_First  P   _  O_Newsize  9#      E   97      E   9P      E   9d      E   O  �   �           �  �
     �       c �!   o �I   p �N   q �V   w �g   � �k   y �p   | ��   } ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �z  � ��  � �,   R   0   R  
 �   R   �   R  
 �   R   �   R  
   R     R  
 #  R   '  R  
 3  R   7  R  
 V  R   Z  R  
 y  R   }  R  
 �  R   �  R  
 �  R   �  R  
 �  R   �  R  
 �  R   �  R  
   R   !  R  
 -  R   1  R  
 M  R   Q  R  
 ]  R   a  R  
 �  R   �  R  
 �  R   �  R  
 �  R   �  R  
 �  R   �  R  
 g  R   k  R  
 w  R   {  R  
 I  R   M  R  
 
  R     R  
   R     R  
 &  R   *  R  
 6  R   :  R  
 F  R   J  R  
 `  R   d  R  
 H塡$H塴$H塼$WH冹 I孁H嬯H呉勶   H峲H峍鐷;鷗H�>vH�L婩鳫嬒�    H媈H9_ t#H呟t
H�H嬎�P怘婳 H塤 H吷tH��P怘媈H9_(t#H呟t
H�H嬎�P怘婳(H塤(H吷tH��P怘媈H9_0t#H呟t
H�H嬎�P怘婳0H塤0H吷tH��P怘媈 H9_8t#H呟t
H�H嬎�P怘婳8H塤8H吷tH��P�禙(圙@H兦HH兤HH冺����H嬊H媆$0H媗$8H媡$@H兡 _肁   8      �   8  � G            *       �7        �std::_Copy_n_unchecked4<nvrhi::rt::PipelineHitGroupDesc const *,unsigned __int64,nvrhi::rt::PipelineHitGroupDesc *>  >�'   _First  AJ        ' 2 AJ '       3  ;  `  h  �  �  �  �   >#    _Count  AK          AN        >P'   _Dest  AM        AP          M        j  ��
 M        �  �� M        �  ��
 >t$    temp  AI  �     B  AI '     � " �  N N M        �  �� N M        �  �� M        �  ��#	 N N N M        �  ��
 M        �  �� M        �  ��
 >k$    temp  AI  �     -  N N M        �  �� N M        �  �� M        k  ��#	 N N N M        �  r
 M        �  �� M        �  ��
 >k$    temp  AI  v     -  N N M        �  �� N M        �  | M        k  |#	 N N N M        �  E
 M        �  f M        �  f
 >k$    temp  AI  I     -  N N M        �  ^ N M        �  O M        k  O#	 N N N M        I  'L	
 Z   )   M        /  0 >�    _Result  AK  9       N N                      0@ Z h   �  �  �  �  I  �  �  �  �  �  �  #  $  /  j  k  �  �  =  >  �7   0   �'  O_First  8   #   O_Count  @   P'  O_Dest  9Z       E   9n       E   9�       E   9�       E   9�       E   9�       E   9�       E   9�       E   O�   @           *  `     4       � �   � �'   � �   � �  � �,   W   0   W  
 �   W   �   W  
 �   W   �   W  
   W     W  
 %  W   )  W  
 E  W   I  W  
 U  W   Y  W  
 �  W   �  W  
 �  W   �  W  
 �  W   �  W  
 p  W   t  W  
 9  W   =  W  
 �  W   �  W  
 �  W   �  W  
 �  W   �  W  
 �  W   �  W  
 �  W   �  W  
   W     W  
   W     W  
 $  W   (  W  
 4  W   8  W  
 L  W   P  W  
 H;蕋+H塡$WH冹 H孃H嬞H嬎�    H兠@H;遳颒媆$0H兡 _�   J      �   �  f G            1      1   t(        �std::_Destroy_range<std::allocator<donut::engine::ShaderMacro> >  >c   _First  AI         AJ          AJ 0       >霥   _Last  AK          AM         AK 0       >zc   _Al  AP          AP          D@   
 Z   �'                         H�  h   �(  �(  )  )   0   c  O_First  8   霥  O_Last  @   zc  O_Al  O�   @           1   p     4       > �    B �   > �   B �&   F �,   S   0   S  
 �   S   �   S  
 �   S   �   S  
 �   S   �   S  
 �   S   �   S  
 �   S   �   S  
 �   S   �   S  
   S     S  
   S     S  
 �  S   �  S  
 L塋$ L塂$H塗$H塋$SVWATAUAVAWH冹PM嬦M嬓H嬟L孂L峣L塴$ H�9H墊$(I媢 H塼$0L婣I嬂H+艸柳M吷勽  L;�嘂  H嬑H+螲六I�������I嬃H+罫;�囂  J�!H塗$8L+荌柳I嬋H验I嬃H+罫;纕L墝$�   I瞧���I峃'�<J�L嬯H;翷C鐼;�噵  M嬽I伶L壃$�   I侢   r1I峃'I;�唂  �    H吚凴  H峹'H冪郒塆鳯嫈$�   �$M咑tI嬑�    H孁L嫈$�   �3�L壃$�   H墊$ H嬎H+L$(H六N�,!I铃L風塴$@L壃$�   H玲H�9H塂$0M嬒L嬂I嬙I嬍�    H婦$0H墑$�   I凕uH;辵
L嬶H婦$(�M嬒L嬊H嬘H婰$(�    H壖$�   H嬅M嬒M嬇H嬛H嬋�    怚�H呟tRI媤H;辴H嬎�    H兠@H;辵颕�I媁H+袶冣繦侜   rH兟'L婣鳬+菻岮鳫凐�-  I嬋�    I�?H婦$8H拎H荌塆I�>I塆H兡PA_A^A]A\_^[肏嬈H+肏柳M嬼I伶M嬒L;鄐}H孇I+﨤嬈H嬛H嬒�    I塃 H;鹴+fD  H冿@J�7H嬜�    H峎 I峃 H翔    H;鹵跦孄I�4H;辴H嬒�    H兦@H;颩嬒L嬅I嬙H媽$�   �    隢M�L墑$�   H嬛H嬎�    I塃 H孄H;辴 H嬒�    H兦@H;颩嬒L嬅I嬙H媽$�   �    怘兡PA_A^A]A\_^[描    惕    惕    �      +     �  Y   �  Z   �  Z   �  J   5     �  Z   �  6   �  6   �  J   �  Y   
  Z   $  J   C  Y   Y  Q   _     e  3      �   y  � G            j  #   j  �7        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Insert_counted_range<donut::engine::ShaderMacro const *> 
 >$c   this  AJ        /  AW  /     ;+
 
  D�    >D�   _Where  AI  ,     >�� + 2  AK          AI T     �  B�        [� 
 � lO  >c   _First  AP        ) & AR  )     @�   d� hi �G 4  AR T      D�    >_   _Count  AQ          AT  &     D: .  B�        ej� B  >霥    _Oldlast  AL  D       B0   I     !3�  >.|    _Mylast  B    8     2	 >_    _Unused_capacity  AH  P       0 � AH T      >#     _Newcapacity  AJ  �       AU  �     �5 $ }  AJ �       AU �     f$ $ ]  B�   �     �
 & ��  >_    _Newsize  AH  A      AK  �     �s  � 
 � & AK >    G  D8    >_    _Whereoff  AJ  N    %    >霥    _Constructed_last  AU  _      D@    >_    _Oldsize  AJ  l     �  3 � >c    _Constructed_first  B�   o    �� �  >霥    _Newvec  AM  2     
   AM F    �  B    K    �  >_    _Affected_elements  AH  g        AV  u    �  >霥    _Relocated  AP  �      B�       R  M        )  x��倿 M        )  x��倿& M        (  ��)
1%�(( M        9   ��$	%)
侾
 Z   q   >_    _Block_size  AJ  �     	  AJ �     k a >_    _Ptr_container  AH        AH F    1 �
 >�    _Ptr  AM        AM F    �  M        r  ��
 Z      N N M        r  �'
 Z      N N M        I   
��
 N N N M        �(  ��g >_    _Oldcapacity * AP  M     K  �  � 
 � &9t �Z   AP >    &D �  >_    _Geometric  AH  �     �8 $ i 
 x & AH �     y  ;  M        �(  �� N N- M        �(  佭	I8#� >.|    _Mylast  AU  3     +� �  AU �     f$ $ ] � `  M        5(  .�丼  M        c  �)�*
 Z   �  
 >   _Ptr  AJ 4      >#    _Bytes  AK      1    AK ^     & M        s  �d#�-
 Z   �   >_    _Ptr_container  AP         AP 4    / % >_    _Back_shift  AJ      0  AJ 4    / % N N N M        t(  侂	
 >c   _First  AI  �    r  AI T     �  >霥   _Last  AL  �    i  AL T     �  N N M        t(  偺	
 >c   _First  AM  �    2  AM H    
  N M        �7  倳  >c   _Last  AM  �    H  !  AM �      N M        t(  �	 >c   _First  AM      0  AM H    
  N& Z   �7  )  )  )  �7  )  �7  �(   P           8         0@ f h   �  r  s  t  c  (  9   I   4(  5(  t(  �(  �(  �(  �(  �(  �(  �(  )  )  )  )  �7  �7         $LN159  �   $c  Othis  �   D�  O_Where  �   c  O_First  �   _  O_Count  0   霥  O_Oldlast      .|  O_Mylast  @   霥  O_Constructed_last  �   c  O_Constructed_first  �   霥  O_Relocated  O   �   P          j  �
  '   D      U �/   [ �8   ] �@   ^ �I   _ �W   a �`   b �i   c �s   e ��   i ��   j ��   l �>  m �W  n �g  o �o  r ��  s ��  u ��  w ��  { ��  | ��  } ��  ~ ��  � �T  � �d  � �u  � �}  � ��  � ��  � ��  � ��  � �  � �  � �1  � �H  � �X  f �^  � �d  l ��   \  � F            F      F             �`std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Insert_counted_range<donut::engine::ShaderMacro const *>'::`1'::catch$0 
 >$c   this  EN  �         F  >c   _First  EN  �         F  Z   t(  5(   (                    � /       __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$0        $LN159  �   $c  Nthis  �   D�  N_Where  �   c  N_First  �   _  N_Count  0   霥  N_Oldlast      .|  N_Mylast  @   霥  N_Constructed_last  �   c  N_Constructed_first  �   霥  N_Relocated  O�   8           F   �
     ,       � �   � �%   � �<   � ��   N  � F            �      �             �`std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Insert_counted_range<donut::engine::ShaderMacro const *>'::`1'::catch$2 
 >$c   this  AV  ;     I  EN  �         �  >D�   _Where  AI  '     H  >c   _First  EN  �         �  >_   _Count  AL       <  >霥    _Oldlast  AI  o       >.|    _Mylast  AM  ]     '  >_    _Newsize  EN  8         �  >霥    _Constructed_last  EN  @         �  Z   )  �7  t(               (         � /       __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$2        $LN159  �   $c  Nthis  �   D�  N_Where  �   c  N_First  �   _  N_Count  0   霥  N_Oldlast      .|  N_Mylast  @   霥  N_Constructed_last  �   c  N_Constructed_first  �   霥  N_Relocated  O  �   H           �   �
     <       � �   � �G   � �e   � �w   � �z   � ��   �  � F            D      D             �`std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Insert_counted_range<donut::engine::ShaderMacro const *>'::`1'::catch$1 
 >$c   this  EN  �         D  >D�   _Where  AI  !     #  >c   _First  EN  �         D  >.|    _Mylast  AM  /      
 Z   t(                        � /       __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$3        $LN159  �   $c  Nthis  �   D�  N_Where  �   c  N_First  �   _  N_Count  0   霥  N_Oldlast      .|  N_Mylast  @   霥  N_Constructed_last  �   c  N_Constructed_first  �   霥  N_Relocated  O �   8           D   �
     ,       � �   � �7   � �:   � ��   
  � F            W   
   W             �`std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Insert_counted_range<donut::engine::ShaderMacro const *>'::`1'::catch$4 
 >$c   this  AM       @  EN  �         W  >c   _First  EN  �         W  >.|    _Mylast  AI  %     2  >_    _Newsize  EN  8         W  >霥    _Constructed_last  EN  @         W  >霥    _Relocated  AL  /     (  Z   )  t(   (                     � /       __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$6        $LN159  �   $c  Nthis  �   D�  N_Where  �   c  N_First  �   _  N_Count  0   霥  N_Oldlast      .|  N_Mylast  @   霥  N_Constructed_last  �   c  N_Constructed_first  �   霥  N_Relocated  O  �   @           W   �
     4       � �   � �8   � �F   � �M   � ��   z  � F            <      <             �`std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Insert_counted_range<donut::engine::ShaderMacro const *>'::`1'::catch$3 
 >$c   this  EN  �         <  >c   _First  EN  �         <  >.|    _Mylast  AI       # 
 Z   t(   (                    � /       __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$7        $LN159  �   $c  Nthis  �   D�  N_Where  �   c  N_First  �   _  N_Count  0   霥  N_Oldlast      .|  N_Mylast  @   霥  N_Constructed_last  �   c  N_Constructed_first  �   霥  N_Relocated  O  �   8           <   �
     ,       � �   � �(   � �2   � �,   T   0   T  
 �   T   �   T  
 �   T   �   T  
   T     T  
 6  T   :  T  
 F  T   J  T  
 Z  T   ^  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
   T     T  
 ;  T   ?  T  
 K  T   O  T  
 q  T   u  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
   T     T  
   T     T  
 /  T   3  T  
 Z  T   ^  T  
 j  T   n  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 
  T     T  
 B  T   F  T  
 h  T   l  T  
 |  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 �  T   �  T  
 
  T     T  
 �  T   �  T  
 �  T   �  T  
 �  T     T  
   T     T  
 2  T   6  T  
 B  T   F  T  
   T     T  
 3  T   7  T  
 \  T   `  T  
 x  T   |  T  
 �  T   �  T  
   T     T  
 v  T   z  T  
 �  T   �  T  
 �  T   �  T  
 	  T   	  T  
 	  T   	  T  
 B	  T   F	  T  
 R	  T   V	  T  
 �	  T   �	  T  
 �	  T   �	  T  
 �	  T   �	  T  
 �	  T   �	  T  
 &
  T   *
  T  
 6
  T   :
  T  
 z
  T   ~
  T  
 �
  T   �
  T  
 �
  T   �
  T  
 �
  T   �
  T  
 �  �   �  �  
 �  T   �  T  
   a     a  
 �  a   �  a  
 �  a   �  a  
 (  �   ,  �  
 Y  �   ]  �  
 L  a   P  a  
 �  c   �  c  
 i  c   m  c  
 }  c   �  c  
 �  c   �  c  
 �  c   �  c  
 �  c   �  c  
   c     c  
 )  c   -  c  
 P  c   T  c  
 �  c   �  c  
 �  �   �  �  
 �  �   �  �  
 �  c   �  c  
 T  b   X  b  
   b     b  
 6  b   :  b  
 [  b   _  b  
 }  b   �  b  
 �  �   �  �  
 �  �   �  �  
 �  b   �  b  
 8  e   <  e  
 �  e   �  e  
 	  e   
  e  
 .  e   2  e  
 P  e   T  e  
 w  e   {  e  
 �  e   �  e  
 �  e   �  e  
   �   
  �  
 7  �   ;  �  
 ,  e   0  e  
 �  d   �  d  
 U  d   Y  d  
 z  d   ~  d  
 �  d   �  d  
 �  �   �  �  
 !  �   !  �  
 �!  d   �!  d  
 H塗$SUH冹(H嬯L媴�   H婾@H媿�   �    L媴�   H婾 H媿�   �    3�3设    �!   S   8   N   A   {   H塗$SUWH冹 H嬯H媿�   H玲H嫕�   H薒媴�   H媫 H��    H�3�3设    �3   S   ?   {   H塗$SUVWAVH冹 H嬯H嫷�   H嬑H玲H嫕�   H薍嬛H菱H覮嫷�   M嬑L嬅�    怢嬈I拎L肏伶H�3H媫 H��    M嬈H�H媇0H嬎�    H�3�3设    怋   Z   a   [   s   S      {   H塗$SUH冹(H嬯L媴�   H媇 H�H媿�   �    H媴�   H�3�3设    �$   S   7   {   H塗$SUVWH冹(H嬯H嫿�   L嬒L媴�   H媇 H�H嫷�   H嬑�    怢嬊H�H嬑�    H婨0H�3�3设    �3   Z   B   S   R   {   H塡$H塼$WH冹 I孁H嬺H嬞H;蕋( H嬘H嬒�    H峉 H峅 �    H兦@H兠@H;辵跦媆$0H嬊H媡$8H兡 _�'   6   4   6      �   s  u G            X      E   �7        �std::_Move_unchecked<donut::engine::ShaderMacro *,donut::engine::ShaderMacro *>  >c   _First  AI       2  AJ          >c   _Last  AK          AL       =  >c   _Dest  AM       E  AP                                @ 
 h   �7   0   c  O_First  8   c  O_Last  @   c  O_Dest  O �   0           X   `     $       	 �    �E    �,   [   0   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 �   [   �   [  
 �   [      [  
   [     [  
 �  [   �  [  
 H塡$H塴$H塼$WH冹@I嬮I孁H嬺H嬞L塂$ L塂$(L塋$0H呉t-@ �     L嬅H嬜H嬐�    H兦HH墊$(H兠HH冾u逪嬊H媆$PH媗$XH媡$`H兡@_肑   ^      �   �  � G            y      d   �7        �std::_Uninitialized_copy_n<nvrhi::rt::PipelineHitGroupDesc const *,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >  >�'   _First  AJ        @  AJ @     9 	   >#    _Count  AK          AL       V  >P'   _Dest  AP        @  AP @     9    >D(   _Al  AN       W  AQ          >�'    _UFirst  AI        I  >>W   _Backout  CM          =  (  D     M        Z     N M        �7  @	
 Z       N @                    0@ . h
   
      X   Y   Z   �   �   !  �7  �7   P   �'  O_First  X   #   O_Count  `   P'  O_Dest  h   D(  O_Al      >W  O_Backout  O  �   H           y   p     <       G �    ] �/   ^ �@   _ �W   ^ �a   c ��   �   � F                                �`std::_Uninitialized_copy_n<nvrhi::rt::PipelineHitGroupDesc const *,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >'::`1'::dtor$0  >>W    _Backout  EN                                     �  O,   V   0   V  
 �   V   �   V  
 �   V   �   V  
 �   V   �   V  
   V   
  V  
 &  V   *  V  
 6  V   :  V  
 X  V   \  V  
 h  V   l  V  
 �  V   �  V  
 �  V   �  V  
 �  V   �  V  
 8  f   <  f  
 �  f   �  f  
 H崐    �       ]   H塡$H塼$WH冹@I嬝H嬺H塡$ H塡$(L塋$0H呉tMH峺( H峎豀嬎�    H婳鳫塊 H吷tH��P怘�H塊(H吷tH��R怘兠0H塡$(H兦0H冾u篐嬅H媆$PH媡$XH兡@_�8   5      �   w  � G            �      y   �7        �std::_Uninitialized_copy_n<nvrhi::rt::PipelineShaderDesc const *,std::allocator<nvrhi::rt::PipelineShaderDesc> >  >q'   _First  AJ        0  AJ 0     Y  ?  >#    _Count  AK          AL       n  >6'   _Dest  AP        0  AP 0     Y  :  >�'   _Al  AQ        0  AQ 0     Y  :  >VW   _Backout  CI          Z 
 H  D     M        W    N M        �7  <	 M           < M        �   < M        �  P M        �  W N N M        �  < M        k  D N N N N N @                    0@ N h   �  �  �  �  k           U   V   W   �   �   �   �   !  �7  �7   P   q'  O_First  X   #   O_Count  `   6'  O_Dest  h   �'  O_Al      VW  O_Backout  9L       E   9_       E   O �   P           �   p     D       G �   ] �$   ^ �)   ] �<   _ �l   ^ �v   c ��   �   � F                                �`std::_Uninitialized_copy_n<nvrhi::rt::PipelineShaderDesc const *,std::allocator<nvrhi::rt::PipelineShaderDesc> >'::`1'::dtor$0  >VW    _Backout  EN                                     �  O,   X   0   X  
 �   X   �   X  
 �   X   �   X  
 �   X   �   X  
   X     X  
 "  X   &  X  
 2  X   6  X  
 T  X   X  X  
 d  X   h  X  
 �  X   �  X  
 c  X   g  X  
 s  X   w  X  
 �  X   �  X  
   g     g  
 �  g   �  g  
 H崐    �       _   H塡$H塼$WH冹@I嬝H嬺H孂H塡$ H塡$(L塋$0H呉t6@ H塡$PH嬜H嬎�    怘岾 H峎 �    怘兠@H塡$(H兦@H冾u蜨嬅H媆$XH媡$`H兡@_�<   5   J   5      �   �  � G            u      e   �7        �std::_Uninitialized_copy_n<donut::engine::ShaderMacro const *,std::allocator<donut::engine::ShaderMacro> >  >c   _First  AJ        0  AJ 0     E  '  >#    _Count  AK          AL       Z  >c   _Dest  AP        0  AP 0     E  "  >zc   _Al  AQ        0  AQ 0     E  "  >c    _UFirst  AM       \  >�   _Backout  CI          F  1  D     M        )   N M        �2  0	 M        �2  0 M        �2  0 N N N @                    0@ > h   �  t(  �(  �(  )  )  )  )  )  �2  �2  �2  �2  �7   P   c  O_First  X   #   O_Count  `   c  O_Dest  h   zc  O_Al      �  O_Backout  O �   H           u   p     <       G �   ] �'   ^ �0   _ �X   ^ �b   c ��   �   � F                                �`std::_Uninitialized_copy_n<donut::engine::ShaderMacro const *,std::allocator<donut::engine::ShaderMacro> >'::`1'::dtor$0  >�    _Backout  EN                                     �  O  �   �   � F                                �`std::_Uninitialized_copy_n<donut::engine::ShaderMacro const *,std::allocator<donut::engine::ShaderMacro> >'::`1'::dtor$1  >�    _Backout  EN                                     �  O  ,   Y   0   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 �   Y   �   Y  
 �   Y      Y  
   Y      Y  
 ,  Y   0  Y  
 N  Y   R  Y  
 ^  Y   b  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 �  Y   �  Y  
 h  h   l  h  
   h     h  
 \  l   `  l  
 �  l      l  
 H崐    �       \   H媻P   �       7   I嬂H;�剰   L嬋L岮8L+蒃3��    W繫岪@ O塗業岺萇塗燗@� AH�W�HM塒楬兝@I茾�   E圥圕D∣塗窸塗繟@–DˋH窩L窶塒窱茾�   E圥℉;蕌吤   �   S  � G            �       �   )        �std::_Uninitialized_move<donut::engine::ShaderMacro *,std::allocator<donut::engine::ShaderMacro> >  >霥   _First  AJ           AJ       |  h  >霥   _Last  AK        �  >c   _Dest  AP          AP �       >zc   _Al  AQ          AQ �       D     >�   _Backout  CH     T     G  CH          | 4 G  M        )  
 N% M        )  #(42 M        )  '2 M        )  '2 M        M  L M        -  0p M        �  p N N M        @  L M        �  `�� M          ` N N N N M        M  '
 M        -  0
8 M        �  
8 N N M        @  ' M        �  '��	 M          ' N N N N N N N                        @ � h!   �  �  t  v  M  $  &  -  ?  �  �  �  �  �  @  b  k  l  �    �'  t(  �(  �(  )  )  )  )  )  )  )  )  )      霥  O_First     霥  O_Last     c  O_Dest      zc  O_Al  O �   X           �   p     L       � �   � �   � �#   � �/   � �3   � ��   � ��   � �,   Z   0   Z  
 �   Z   �   Z  
 �   Z   �   Z  
 �   Z   �   Z  
   Z     Z  
   Z     Z  
 1  Z   5  Z  
 A  Z   E  Z  
 p  Z   t  Z  
 �  Z   �  Z  
 h  Z   l  Z  
 H塡$WH冹 I嬝H孃I嬓H嬒�    H婯 H塐 H吷tH��P怘婯(H塐(H吷tH��P怘婯0H塐0H吷tH��P怘婯8H塐8H吷tH��P�禖@圙@H媆$0H兡 _�   5      �   ,  � G            }   
   r            �std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::construct<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc const &>  >D(   __formal  AJ          D0   
 >:'   _Ptr  AK          AM       l  >D'   <_Args_0>  AI  
     j  AP        
  M        �    M        �  W M        �  _ N N M        �  C M        k  K N N M        �  / M        k  7 N N M        �   M        k  # N N N                      0@ " h   �  �  �  �  k  �   �    0   D(  O__formal  8   :'  O_Ptr  @   D'  O<_Args_0>  9+       E   9?       E   9S       E   9g       E   O�   0           }   p     $       � �   � �r   � �,   ^   0   ^  
 �   ^   �   ^  
   ^     ^  
 *  ^   .  ^  
 N  ^   R  ^  
 ^  ^   b  ^  
 �  ^   �  ^  
   ^     ^  
   ^     ^  
 (  ^   ,  ^  
 @  ^   D  ^  
 H�    H嬃�   �   �   s G                   
   �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AJ                                 @�     b%  Othis  O   �   0              �     $       �  �    �  �   �  �,   :   0   :  
 �   :   �   :  
 �   :   �   :  
 H塡$H塼$H墊$AVH冹 3�W�H堿L嬺H堿H嬞H儂H媟vL�2H�������H;�嚻   H茿   H凗wH塹A閮   H嬑H兩H;蟰,H�       �H兝'H嬋�    H嬋H吚trH兝'H冟郒塇1�   H孂H;蔋B鶫峅H侚   rH岮'H;羦E牖H吷t�    L岶H�I嬛H塻H嬋H墈�    H媡$8H嬅H媆$0H媩$@H兡 A^描    惕    惕    虉      �      �           
  3     4      �   �  � G                   Q        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >�   this  AI  +     � �   AJ        +  >�   _Right  AK        �  � k  AK �     �  a U M        D  9.


?
 Z   ~   >#     _New_capacity  AH  �       AJ  �     \  9  AM  C     � = " r ]  AH �       AJ �     `  L  AM �      & M        �  ��	-
= >p   _Fancy_ptr  AH �       C            "  C      9     � G " �  �    M        �  ��1
=  M        �  ��1
=. M        (  ��1		

8/ M        9   ��+%"
D	 Z   �  q   >_    _Block_size  AH  �     O  C  AH �       >_    _Ptr_container  AJ  �     |  d  AJ �      
 >�    _Ptr  AH  �       AH �       M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  j8 M          j*, >_    _Masked  AJ  q     D    AJ �       M        �  �� N N N M        ?   ^ N M        ?   �� >_   _Count  AP  �       N N M        /  +	 >�    _Result  AV  $     � �   M        �  + N N M        @  
$ M        �  ������ M           N N N                       @ v h   �  �  r  x  y  �  #  $  /  2  ?  �  �  �  @  D  �  �  �  �      �  �  '  (  /   9          $LN106  0   �  Othis  8   �  O_Right  O �   8             �     ,       �	 �+   �	 ��   �	 �  �	 �,   5   0   5  
 �   5   �   5  
 �   5   �   5  
   5   	  5  
   5   !  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
   5     5  
 Y  5   ]  5  
 m  5   q  5  
 �  5   �  5  
 h  5   l  5  
 |  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 �  5   �  5  
 .  5   2  5  
 t  5   x  5  
 �  �   �  �  
 �  5   �  5  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   J   %   !   ,   P      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,   )   0   )  
 d   )   h   )  
 t   )   x   )  
 �   )   �   )  
 �   )   �   )  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   J   %   !   ,   S      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,   /   0   /  
 z   /   ~   /  
 �   /   �   /  
 �   /   �   /  
 �   /   �   /  
 H�    H茿    H堿H�    H�H嬃�   V      S      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   -   0   -  
 z   -   ~   -  
   -     -  
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   J   %   !      �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   #   0   #  
 d   #   h   #  
 t   #   x   #  
 �   #   �   #  
 �   #   �   #  
   #     #  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   T  t G            "         �        �nvrhi::RefCountPtr<nvrhi::IBindingLayout>::~RefCountPtr<nvrhi::IBindingLayout> 
 >b%   this  AH         AJ          AH        M        �  GCE
 >t$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   b%  Othis  9       E   O�   0           "   �     $       �  �   �  �   �  �,   ;   0   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
 �   ;   �   ;  
   ;     ;  
 P  ;   T  ;  
 h  ;   l  ;  
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IShader>::~RefCountPtr<nvrhi::IShader> 
 >�$   this  AH         AJ          AH        M        �  GCE
 >k$    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   �$  Othis  9       E   O  �   0           "   �     $       �  �   �  �   �  �,   9   0   9  
 �   9   �   9  
 �   9   �   9  
 �   9   �   9  
 �   9   �   9  
 �   9   �   9  
 B  9   F  9  
 \  9   `  9  
 H�	H吷�    �   P      �   R  � G            
          Y2        �std::_Tidy_guard<std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > >::~_Tidy_guard<std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > > 
 >�   this  AJ         
 Z   
(                          H�     �  Othis  O  �   0           
   p     $       *  �    +  �   .  �,   U   0   U  
   U     U  
 h  U   l  U  
 H塡$WH冹 H媦H�H;遲H嬎�    H兠HH;遳颒媆$0H兡 _�   ?      �   �  � G            2   
   '   Y         �std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::~_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > 
 >/W   this  AJ          AJ          M           	
 >P'   _First  AI         >:'   _Last  AM       #  N                       H�  h   
      �   �   !   0   /W  Othis  O �   0           2   p     $        �
    �'    �,   ]   0   ]  
 �   ]   �   ]  
 �   ]   �   ]  
 6  ]   :  ]  
 V  ]   Z  ]  
 �  ]   �  ]  
 H塡$WH冹 H媦H�H;遲H嬎�    H兠0H;遳颒媆$0H兡 _�   =      �   �  � G            2   
   '   V         �std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> >::~_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 
 >GW   this  AJ          AJ          M           	
 >6'   _First  AI         >!'   _Last  AM       #  N                       H�  h         �   �   !   0   GW  Othis  O �   0           2   p     $        �
    �'    �,   _   0   _  
 �   _   �   _  
 �   _   �   _  
 2  _   6  _  
 R  _   V  _  
 �  _   �  _  
 H塡$WH冹 H媦H�H;遲H嬎�    H兠@H;遳颒媆$0H兡 _�   J      �   �  � G            2   
   '   )        �std::_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> >::~_Uninitialized_backout_al<std::allocator<donut::engine::ShaderMacro> > 
 >�   this  AJ          AJ          M        t(  	
 >c   _First  AI         >霥   _Last  AM       #  N                       H�  h   t(  �(  �(  )  )   0   �  Othis  O   �   0           2   p     $        �
    �'    �,   \   0   \  
 �   \   �   \  
 �   \   �   \  
 ,  \   0  \  
 L  \   P  \  
 �  \   �  \  
 @SH冹 H婹H嬞H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �;      Y         �   �  � G            ^      ^   J        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> > 
 >�   this  AI  
     Q J   AJ        
 ) M        %  ,(
	 M        �   N M        �  ,E M        3  &? M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       D &  " M        s  
"#
!
 Z   �   >_    _Ptr_container  AP  &     7    AP :       >_    _Back_shift  AJ  -     0 
   N N N N N                       H� : h
   �  s  t  K  $  %  3  �  �  �  �  ^  c         $LN35  0   �  Othis  O�   H           ^   �     <       B �   C �
   B �
   C �R   J �X   C �,   7   0   7  
 �   7   �   7  
 �   7   �   7  
 �  7   �  7  
 �  7   �  7  
 ,  7   0  7  
 @  7   D  7  
 f  7   j  7  
 �  �   �  �  
   7     7  
 H塡$VH冹 H�H嬹H呟剣   H墊$0H媦H;遲H嬎�    H兠HH;遳風�H�9庛8庛8H婲H媩$0I+菻鏖H龙H嬄H凌?H蠬�襀菱H侜   rI婬鳫兟'L+罥岪鳫凐w#L嬃I嬋�    3繦�H塅H塅H媆$8H兡 ^描    �+   ?   �      �         �   d  � G            �   
   �   p7        �std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::~vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > 
 >�'   this  AJ          AL       � �  . M        �7  
	I[%	 M        k  I1I M        c  j) 
 Z   �  
 >   _Ptr  AP �       >#    _Bytes  AK  j     F )  " M        s  
s#
#
 Z   �   >_    _Ptr_container  AJ  w     9     AJ �       >_    _Back_shift  AP  ;     u P    AP �       N N N M           "	
 >P'   _First  AI  
     � �   >:'   _Last  AM  "     ,  N N                       @� 6 h   �  s  t  c  e  k  
      �   �   !  �7         $LN47  0   �'  Othis  O�   H           �   �
     <       � �
   � �
   � �   � ��    ��   � �,   D   0   D  
   D     D  
   D     D  
 �  D   �  D  
 �  D   �  D  
 +  D   /  D  
 ?  D   C  D  
 e  D   i  D  
 y  D   }  D  
 �  D   �  D  
 �  D   �  D  
 L  �   P  �  
 x  D   |  D  
 H塡$VH冹 H�H嬹H呟剣   H墊$0H媦H;遲H嬎�    H兠0H;遳風�H斧*H婲H媩$0I+菻鏖H龙H嬄H凌?H蠬�RH菱H侜   rI婬鳫兟'L+罥岪鳫凐w#L嬃I嬋�    3繦�H塅H塅H媆$8H兡 ^描    �+   =   �      �         �   \  � G            �   
   �   t7        �std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::~vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > 
 >{'   this  AJ          AL       � �  . M        �7  
	I[%	 M        r  I1I M        c  j) 
 Z   �  
 >   _Ptr  AP �       >#    _Bytes  AK  j     F )  " M        s  
s#
#
 Z   �   >_    _Ptr_container  AJ  w     9     AJ �       >_    _Back_shift  AP  ;     u P    AP �       N N N M           "	
 >6'   _First  AI  
     � �   >!'   _Last  AM  "     ,  N N                       @� 6 h   �  s  t  c  l  r        �   �   !  �7         $LN47  0   {'  Othis  O�   H           �   �
     <       � �
   � �
   � �   � ��    ��   � �,   A   0   A  
 �   A   �   A  
   A     A  
 �  A   �  A  
 �  A   �  A  
 #  A   '  A  
 7  A   ;  A  
 ]  A   a  A  
 q  A   u  A  
 �  A   �  A  
 �  A   �  A  
 D  �   H  �  
 p  A   t  A  
 �       P      �   ,  � G                       �'        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::~vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> > 
 >$c   this  AJ         
 Z   
(                          H�     $c  Othis  O�   (              �
            � �    � �,   O   0   O  
 �   O   �   O  
 @  O   D  O  
 @SH冹 H嬞H兞L�
    �   D岯    怘�H吷tH�    H��P怘兡 [�   ;            �   M  V G            ?      9   �        �nvrhi::ComputePipelineDesc::~ComputePipelineDesc 
 >�%   this  AI  	     5  AJ        	  M        �  # M        �  #CE
 >k$    temp  AJ  &       AJ 9       N N                      0H�  h        �  �   0   �%  Othis  95       E   O   ,   <   0   <  
 {   <      <  
 �   <   �   <  
 �   <   �   <  
 �   <   �   <  
 I  <   M  <  
 @SH冹 H嬞H兞0L�
    �   D岯    怘岾�    H嬎H兡 [�       ;         (   D   5   A      �   �   L G            9      /   W7        �nvrhi::rt::PipelineDesc::~PipelineDesc 
 >S'   this  AI  	     +  AJ        	                       0H�  h         0   S'  Othis  O  ,   G   0   G  
 q   G   u   G  
 �   G   �   G  
 H塡$WH冹 H嬞H婭83�H吷tH墈8H��P怘婯0H吷tH墈0H��P怘婯(H吷tH墈(H��P怘婯 H吷tH墈 H��P怘婼H凓v,H�翲�H侜   rH兟'L婣鳬+菻岮鳫凐w"I嬋�    H墈H荂   � H媆$0H兡 _描    虘      �         �   �  \ G            �   
   �   �         �nvrhi::rt::PipelineHitGroupDesc::~PipelineHitGroupDesc 
 >:'   this  AI  
     � �   AJ        
  M        J  E_P& M        %  _
,$
 M        �  _ N M        �  ,iF M        3  )lC M        c  o)
 Z   �  
 >   _Ptr  AJ  o     &  
  >#    _Bytes  AK  l     H )  $ M        s  xd#
"
 Z   �   >_    _Ptr_container  AP  �     4    AP �       >_    _Back_shift  AJ  �     1 
   N N N N N N M        �  K M        �  KDE
 >k$    temp  AJ  O       AJ _     P  &  N N M        �  7 M        �  7DE
 >k$    temp  AJ  ;       AJ K       N N M        �  # M        �  #DE
 >k$    temp  AJ  '       AJ 7       N N M        �  
 M        �  MDG
 >t$    temp  AJ         AJ #       N N                      0@� N h   �  s  t  �  �  J  K  �  �  $  %  3  �  �  �  �  ^  c         $LN60  0   :'  Othis  9       E   93       E   9G       E   9[       E   O ,   ?   0   ?  
 �   ?   �   ?  
 �   ?   �   ?  
 i  ?   m  ?  
 �  ?   �  ?  
 �  ?   �  ?  
   ?     ?  
 '  ?   +  ?  
 �  ?   �  ?  
 �  ?   �  ?  
   ?     ?  
   ?     ?  
 j  ?   n  ?  
 z  ?   ~  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 c  �   g  �  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 �  ?   �  ?  
 @SH冹 H嬞H婭(H吷tH荂(    H��P怘婯 H吷tH荂     H��P怘婼H凓v,H�翲�H侜   rH兟'L婣鳬+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    蘫      �         �   �  X G            �      �   �         �nvrhi::rt::PipelineShaderDesc::~PipelineShaderDesc 
 >!'   this  AI  	     � ~   AJ        	  M        J  I9O& M        %  9
,(
	 M        �  9 N M        �  ,CE M        3  )FB M        c  I)
 Z   �  
 >   _Ptr  AJ  I     &  
  >#    _Bytes  AK  F     G )  $ M        s  Rd#
!
 Z   �   >_    _Ptr_container  AP  Z     3    AP j       >_    _Back_shift  AJ  ]     0 
   N N N N N N M        �  ! M        �  !DE
 >k$    temp  AJ  %       AJ 9     O  &  N N M        �  	 M        �  IDE
 >t$    temp  AJ  
       AJ !       N N                      0@� N h   �  s  t  �  �  J  K  �  �  $  %  3  �  �  �  �  ^  c         $LN48  0   !'  Othis  9       E   95       E   O   ,   =   0   =  
 }   =   �   =  
 �   =   �   =  
 e  =   i  =  
 �  =   �  =  
 �  =   �  =  
 �  =     =  
 #  =   '  =  
 �  =   �  =  
 �  =   �  =  
 �  =   �  =  
   =     =  
 �  �   �  �  
 �  =   �  =  
 �  =   �  =  
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐wkI嬋�    H荂0    H荂8   艭  H婼H凓v,H�H�翲侜   rL婣鳫兟'I+菻岮鳫凐w!I嬋�    H荂    H荂   � H兡 [描    �<      �      �         �   |  N G            �      �   �'        �donut::engine::ShaderMacro::~ShaderMacro 
 >霥   this  AI  
     � �   AJ        
  M        J  ITO& M        %  T
,(
	 M        �  T N M        �  ,^E M        3  ^&? M        c  d)
 Z   �  
 >   _Ptr  AJ  a     )  
  >#    _Bytes  AK  d     &  AK �      " M        s  
m#
!
 Z   �   >_    _Ptr_container  AP  q       AP �     #    >_    _Back_shift  AJ  x     
  AJ �       N N N N N N M        J  G$ M        %  -( M        �   N M        �  - M        3  & M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       &  AK �       M        s  
##
 >_    _Ptr_container  AP  '       AP ;     m  c  >_    _Back_shift  AJ  .     
  AJ �       N N N N N N                       @� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN74  0   霥  Othis  O,   J   0   J  
 s   J   w   J  
 �   J   �   J  
 ^  J   b  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
 �  J      J  
 &  J   *  J  
 6  J   :  J  
   J     J  
 @  J   D  J  
 P  J   T  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
 �  J   �  J  
 d  �   h  �  
 H�    H�H兞�       J      "      �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,   .   0   .  
 {   .      .  
 H�    H�H兞�       J      "      �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   8                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   $   0   $  
 e   $   i   $  
 �   $   �   $  
 H塡$WH冹 H孃H嬞H;蕋bH婹H凓v,H�	H�翲侜   rL婣鳫兟'I+菻岮鳫凐wBI嬋�    H荂   3繦塁�OKH塆H荊   �H嬅H媆$0H兡 _描    蘂      �         �   ]  u G            �   
   �   [,        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::operator= 
 >�   this  AI       { o   AJ          >�   _Right  AK        
  AM  
     ~ w   M        -  0[ M        �  [ N N$ M        %  
,
, M        �   N M        �  ,f M        3  &` M        c  %)?
 Z   �  
 >   _Ptr  AJ  "     )  
  >#    _Bytes  AK  %     e & : " M        s  
.#
B
 Z   �   >_    _Ptr_container  AP  2     X  ?  AP F       >_    _Back_shift  AJ  9     Q 
 ?  N N N N N                       @� ^ h   �  s  t  $  %  &  -  3  ?  �  �  �  �  �  �  ^  b  c  k  l   -  -         $LN62  0   �  Othis  8   �  O_Right  O   �   P           �   �     D       � �   � �   � �[   � �i   � �w   � ��   � �,   6   0   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 �   6   �   6  
 �  6   �  6  
 �  6   �  6  
 T  6   X  6  
 h  6   l  6  
 �  6   �  6  
 0  �   4  �  
 t  6   x  6  
 H塡$H塼$WH冹 H孃H嬞H;蕋H儂vH�L婫�    H媤 H9s t#H咑t
H�H嬑�P怘婯 H塻 H吷tH��P怘媤(H9s(t#H咑t
H�H嬑�P怘婯(H塻(H吷tH��P怘媤0H9s0t#H咑t
H�H嬑�P怘婯0H塻0H吷tH��P怘媤8H9s8t#H咑t
H�H嬑�P怘婯8H塻8H吷tH��P�禛@圕@H嬅H媆$0H媡$8H兡 _�)   8      �   P  P G            �      �   �7        �nvrhi::rt::PipelineHitGroupDesc::operator= 
 >:'   this  AI       �  AJ          >D'   __that  AK          AM       �  M        j  ��
 M        �  �� M        �  ��
 >t$    temp  AJ  �       AJ �       N N M        �  �� >t$    tmp  AL  �     =  N M        �  �� M        �  ��#	 N N N M        �  ��
 M        �  �� M        �  ��
 >k$    temp  AJ  �       AJ �     G      N N M        �  �� >k$    tmp  AL  �     -  N M        �  �� M        k  ��#	 N N N M        �  Z
 M        �  { M        �  {
 >k$    temp  AJ  w       AJ �     t     B  J   N N M        �  s >k$    tmp  AL  ^     -  N M        �  d M        k  d#	 N N N M        �  -
 M        �  N M        �  N
 >k$    temp  AJ  J      & AJ Z     �     B  J  o  w   N N M        �  F >k$    tmp  AL  1     -  N M        �  7 M        k  7#	 N N N M        I  L
 Z   )   M        /   >�    _Result  AK  $     	  N N                      0H V h   �  �  �  �  I  �  �  �  �  �  �  #  $  /  j  k  �  �  =  >   0   :'  Othis  8   D'  O__that  9B       E   9V       E   9o       E   9�       E   9�       E   9�       E   9�       E   9�       E   O,   @   0   @  
 u   @   y   @  
 �   @   �   @  
 �   @   �   @  
 �   @   �   @  
 "  @   &  @  
 2  @   6  @  
 r  @   v  @  
   @   "  @  
 .  @   2  @  
 v  @   z  @  
   @   #  @  
 /  @   3  @  
 ~  @   �  @  
 %  @   )  @  
 5  @   9  @  
 �  @   �  @  
 *  @   .  @  
 �  @   �  @  
 �  @   �  @  
 �  @      @  
   @     @  
   @      @  
 ,  @   0  @  
 <  @   @  @  
 L  @   P  @  
 H塡$H塼$WH冹 H孃H嬞H;蕋H儂vH�L婫�    H媤 H9s t#H咑t
H�H嬑�P怘婯 H塻 H吷tH��P怘�(H9{(t#H�t
H�H嬒�P怘婯(H墈(H吷tH��P怘嬅H媆$0H媡$8H兡 _�)   8      �   �  N G            �      �   �7        �nvrhi::rt::PipelineShaderDesc::operator= 
 >!'   this  AI       z  AJ          >*'   __that  AK          AM       L  M        j  Z
 M        �  { M        �  {
 >t$    temp  AJ  w       AJ �       N N M        �  s >t$    tmp  AM  ^     ;  N M        �  d M        �  d#	 N N N M        �  -
 M        �  N M        �  N
 >k$    temp  AJ  J       AJ Z     @      N N M        �  F >k$    tmp  AL  1     c  N M        �  7 M        k  7#	 N N N M        I  L
 Z   )   M        /   >�    _Result  AK  $     	  N N                      0H V h   �  �  �  �  I  �  �  �  �  �  �  #  $  /  j  k  �  �  =  >   0   !'  Othis  8   *'  O__that  9B       E   9V       E   9o       E   9�       E   O  ,   >   0   >  
 s   >   w   >  
 �   >   �   >  
 �   >   �   >  
 �   >   �   >  
   >   !  >  
 -  >   1  >  
 l  >   p  >  
   >     >  
 #  >   '  >  
 j  >   n  >  
   >     >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
 �  >   �  >  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   J      "   0         �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   �  Othis  O ,   *   0   *  
 w   *   {   *  
 �   *   �   *  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   J      "   0         �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �  �   0   �  Othis  O  ,   0   0   0  
 �   0   �   0  
 �   0   �   0  
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   J      "   0         �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @� 
 h   �   0   �  Othis  O ,   &   0   &  
 w   &   {   &  
 �   &   �   &  
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,      0     
 g      k     
 w      {     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
 �      �     
          
 !     %    
 1     5    
 A     E    
 �     �    
 H塡$H塼$H墊$L塼$ UH峫$袶侅�   ML婹H嬟H婾oA嬹H荅'   E嬸H孂M荕呉劘   3繦塃鏗婨gH塃�E�E�EE譎呉tH塙縃荅�   L婨_M吚tH呉�   �   D罫塂稨�E逪�H峌疕嬎L塙�愗   H婾wH呉tH�H嬎L婨�惃   婳(岶�L�翙A�   鼯D嬂A岶�翙鼯H嬎嬓A�掄   闅   H婨gH塃�E�E�EE譎呉tH塙縃荅�   L婨_M吚tH呉�   �   D罫塂稨�E逪婫 H峌疕塃疕嬎H���   H婾wH呉tH�H嬎L婨�惃   H�H峌颒嬎D塽飰u笄E�   ��  L崪$�   I媅I媠I媨 M媠(I嬨]�   �   �  = G            �  !   �  X7        �RayTracingPass::Execute 
 >�   this  AJ        A  AM  A     j > )   commandList  AI  ,     w AK        ,  >t    width  Ah        >  An  >     q >t    height  A   3     t Ai        3  >�%   bindingSet  AP  �     � 2 w  EO  (           D�    >�%   extraBindingSet  EO  0           D�    >�(   descriptorTable  AK  0      v W  EO  8           D�    >"   pushConstants  AK  �     �  �  EO  @           D�    >_   pushConstantSize  EO  H           D�    >�&    state  B    E     o� � 
 >r(    args  D`    >X(    state  B    
    �  M        �  T M        �  T N N M        �  N N M        �#  q N M        �  �� N M        �  	�� >t    divisor  A   �        N M        �  ��	 N M        �  �� M        �  �� N N M        �#  � N M        �  �/ N �                     @ > h   �      �  �  �  �  x  y  �  �#  Y7  Z7  c7   �   �  Othis  �    )  OcommandList  �   t   Owidth  �   t   Oheight  �   �%  ObindingSet  �   �%  OextraBindingSet  �   �(  OdescriptorTable  �   "  OpushConstants  �   _  OpushConstantSize      �&  Ostate  `   r(  Oargs      X(  Ostate  9�       U)   9�       H)   9�       V)   9Z      _)   9s      H)   9�      c)   O�   �           �  �     �       s  �!   t  �N   v  �T   w  �l   x  �q   y  �}   z  ��   {  ��   }  ��     ��   �  ��   �  ��   �  ��   �  �  �  �  �  �&  �  �/  �  �H  �  �L  �  �`  �  �i  �  �y  �  ��  �  �,   I   0   I  
 b   I   f   I  
 r   I   v   I  
 �   I   �   I  
 �   I   �   I  
 �   I   �   I  
 �   I   �   I  
 �   I   �   I  
 	  I   
  I  
 .  I   2  I  
 F  I   J  I  
 |  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
   I     I  
 E  I   I  I  
 m  I   q  I  
 �  I   �  I  
 g  I   k  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 �  I   �  I  
 @USVWATAUAVAWH崿$���H侅�  H�    H3腍墔�   I嬹M孁L嬧L嬹I嬔H�
    �    媴p  A塅(W�吚   H菂�   
   H菂�      �    �吚   �   墔�   �   垍�   茀�    W�E癏荅�   H荅�   f荅�1 H崟�   H峂`�    怘峌癏崓�   �    �W荔D$8E3鞮塴$HA峂@�    H嬝H塂$8H塂$@H峹@H墊$HH岲$8H塂$0H塢楬塢燞岲$8H塃℉塡$PH峌`H嬎�    怘岾 H崟�   �    怘墊$@L�
    A峌@E岴H峂`�    怘婾菻凓v2H�翲婱癏嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚮  �    怘嫊�   H凓v4H�翲媿�   H嬃H侜   rH兟'H婭鳫+罤兝鳫凐�  �    H媴`  L� L婬M+菼六H婽$@H峀$8�    �絟   �  f荄$(  H岲$8H塂$ L�
    L嬈H峊$0I嬒�    I嬚H峀$hH;萾H�L�(I�I�H吷tH��P怘婰$0H吷tL塴$0H��P怚�> u@2鲩F  L塵�W�3�E�E菻塃豅�=    L墊$ L�
    峆D岪H峂歌    怢塵郒嫕�  H呟t(H�H嬎�P怘婨郒婰鸥H塡鸥H吷tH��P怘�E郒嫕�  H呟t(H�H嬎�P怘婨郒婰鸥H塡鸥H吷tH��P怘�E郒嫕x  H呟t(H�H嬎�P怘婨郒婰鸥H塡鸥H吷tH��P怘�E郔�H婱癏;藅"H呟t
H�H嬎�PH婱癏塢癏吷tH��P怚�$L岴癏峊$0I嬏��8  I嬚H峀$pH;萾H�L�(I婲I塚H吷tH��P怘婰$0H吷tL塴$0H��P怚儈 @暺M嬒�   D岯鼿峂歌    怘婱癏吷tL塵癏��P愰�  H婰$8H兞 A�   H�    �    L峀$8L嬈H峊$0I嬒�    I嬚H峀$xH;萾H�L�(I婲I塚H吷tH��P怘婰$0H吷tL塴$0H��P怚儈 u@2鲩8  W纅E�W蒮M fE3�E E0H塃@L�=    L墊$ L�
    峆D岪H峂 �    怢塵HD塵P荅T   荅X   荅\����H媿�  H塋$XH吷tH��P怘媿�  H塋$`H吷tH��P怢墊$ L�
    �   D岯鼿峂拌    怢塵豀峾$XH�H呟t
H�H嬎�P怘婨豀島癏�4艸9t!H呟t
H�H嬎�P怘�H�H吷tH��P怘�E豀呟t
H�H嬎�P怘兦H岲$hH;鴘汭嬢f怚嬚H岴癏肏峂�H;萾H�L�(H婰 H塗 H吷tH��P怘兠H凔(r荋婨豀塃HM嬒�   D岯鼿峂拌    怣嬒�   D岯鶫峀$X�    H嫕x  H呟t(H�H嬎�P怘婨HH婰� H塡� H吷tH��P怘�EHW�E`L塵pH荅x   艵` I婲H�A�   L�    H崟�   �P(L壄�   W�厫   L壄�   H菂�      茀�    I婲H�A�   L�    H崟�   �P(L壄�   A�   H峌`H峂痂    怢�
    �0   D岯襀峂`�    W�E`H荅p   H荅x   H窰itGroupH塃`艵h I婲H�A�   L�    H崟�   �P(怚婲H�A�   L�    H崟�   �P(W纅厫   茀�    H婱H媇H+薎�9庛8庛8I嬃H鏖H龙H嬄H凌?H蠬凓s-�   H峂�    L峂L婨�   H峂`�    H塃棣   L婨I嬋H+薎嬃H鏖H嬺H窿H嬈H凌?H餒凗sIH峿`I;豻&H峿`fD  H嬜H嬎�    H兠HH兦HL婨I;豼浜   H+諰峂H嬒�    H塃�6H峴HL嬅�   H峂`�    H媫H嬣H;鱰H嬎�    H兠HH;遳颒塽L�
    篐   D岯笻峂`�    荅T   荅P(   荅X   I�$L岴餒峊$0I嬏�怘  I嬚H峂圚;萾H�L�(I婲I塚H吷tH��P怘婰$0H吷tL塴$0H��P怚婲H吷剭   H�H峊$0�P(I嬚H峂怘;萾H�L�(I婲 I塚 H吷tH��P怘婰$0H吷tL塴$0H��P怚婲 H吷t=H�E3繦�    �P I婲 H�E3繦�    �P(I婲 H�E3繦�    �P0@��@2鯩嬒�   D岯鼿峂 �    怘峂�    H峂痂    怘媆$8H呟tUH媩$@H;遲H嬎�    H兠@H;遳颒媆$8H婽$HH+親冣繦嬅H侜   rH兟'H媅鳫+肏兝鳫凐w/H嬎�    @镀H媿�   H3惕    H伳�  A_A^A]A\_^[]描    愯    愯    �   �   ?   y   D   M   v   |   �   |   �   |   �   5   �   5   �      9  5   J  5   W  J   h     �     �     
  T   /     ?  K   �  ;   �  :   �           0  �   5  8   J  L   �  ;   �  :   �     6  :   H          )     �  �   �  �   �  R   �  =        K  �   j  �   �  E   �  V   '  @   L  V   g  W   {  ?   �  ?   �     r	  �   �	  �   �	  �   �	     �	  D   �	  A   �	  J   -
     @
  ~   Y
     _
     e
        �   �)  : G            j
  -   j
  U7        �RayTracingPass::Init 
 >�   this  AJ        9  AV  9     1

	  >))   device  AK        6  AT  6     4

  >笻   shaderFactory  AP        3  AW  3     7
uv�� AW �	    � {   >�   shaderName  AL  0     :
W �/ e & AQ        0  AL V      >鵥   extraMacros  AH  �      EO  (           D`   >0    useRayQuery  EO  0           Dh   >u    computeGroupSize  EO  8           Dp   >t$   bindingLayout  AI  8    Z0 � EO  @           Dx   >t$   extraBindingLayout  AI  �    4  AJ        EO  H           D�   >t$   bindlessLayout  AI      4  AJ        EO  P           D�   >僣    macros  D8    >k'   rtPipelineDesc  CP      �    s A  f   D�    >�%    pipelineDesc  B�   C    �� M        �7  侓
 Z   �7   >_    _Length  AQ  �      >_    _Count  AQ  �      M        �7  侓 N N M        a7  
侁 M        7  
侁 N N M        J  A仼埢 M        %  仼4
埉 M        �  4伓埉 M        3  1伖埆  M        c  伱)�
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �    �1 z M        s  佁d垗
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N N M        J  ;乵堮 M        %  乵1
堢 M        �  1亀堢 M        3  .亃堜  M        c  亖)埢
 Z   �  
 >   _Ptr  AH  �      AJ  ~      AH �      >#    _Bytes  AK  z    �. � M        s  亰d埳
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N N M        �1  ��?] M        ?2  ��
4 >�    _Guard " B0       O	\ � i � >  M        a2  �� M        v2  ��,%	 >霥    _Newvec  AI  �     l	�NX AI V    � y M        )  �� M        )  �� M        (  �� M        r  ��
 Z      N N N N N N M        �2  F� >�    _Backout  D�    M        )  � N M        �2  �, M        �2  �, M        �2  �, N N N N N M        >2  �� M        �(  �� N N N M        �'  �� Z   Q  Q   N M        O  �� M        C  &��( M        ?   �� N N M        A  �� M        �  �� M          �� N N N N M        O  UF M        C  &\+) M        ?   )r N N M        A  U M        �  U M          U N N N N M        �  俬 M        �  俬HB
 >k$    temp  AJ  m      AJ ~    �A k � �  B0   w    �h �� B�	  C    �� N N M        �  $侰 M        �  俓 M        �  俓
 >k$    temp  AJ  Y      AJ h      N N M        �  俈 >k$    tmp  AK  F    !  AK h    �  O � �0  N M        �  侰C
 M        �  侾 N N N M        �  倱2 N M        �  倢 N M        �  傔 M        �  傘 M        �  傢 M        �  傢
 >t$    temp  AJ  �      AJ �    s  " J "  N N M        �  傘 N N N M        �  	傉 M        �  	傉 N N M        �  � M        �  � M        �  �! M        �  �!
 >t$    temp  AJ        AJ -    ?  "  N N M        �  � N N N M        �  	�	 M        �  	�	 N N M        �  僄 M        �  僈 M        �  僓 M        �  僓
 >t$    temp  AJ  P      AJ a      N N M        �  僈 N N N M        �  	�= M        �  	�= N N M        �  冃 M        �  冃HB
 >�&    temp  AJ  �      AJ �      B0   �    ? � B`  �    �u � N N M        �  &儵 M        �  兡 M        �  兡
 >�&    temp  AJ  �      AJ �      N N M        �  兗 >�&    tmp  AK  �    #  AK �    '    N M        �  儵C
 M        �  兌 N N N M        �  僥! M        �  儑 M        �  儑
 >k$    temp  AJ  l    &    AJ �      N N M        �  儍 >k$    tmp  AI  h    �  AI �	      N M        �  僸 M        k  僸#
 N N N M        �  � M        �  �GB
 >k$    temp  AJ  	      AJ     D ��  #  N N M        m7  剈 M        �7  剈HB
 >攅    temp  AJ  z      AJ �    �P �^ � �  B0   �    �� �  B�	  N     N N M        l7  &凬 M        m7  刬 M        �7  刬
 >攅    temp  AJ  e      AJ u      N N M        �7  刟 >攅    tmp  AK  Q    #  AK u    �  ^ �y �0  N M        �7  凬C
 M        �7  刐 N N N M        (  �' M        /(  �'
 Z   )   N N M        �  .吚 M        �  呫 M        �  呫 N N M        �  呝 N M        �  吚C M        �  呌 N N N M        �  � M        �  �"# N N M        �  匎 M        �  �
# N N M        �  劘2 N M        q7  劒 M        �7  劒 M        �7  劒 N N N M        u7  劃 M        �7  劃 M        �7  劃 N N N( M        �  匨  > %    <begin>$L0  AM  V    �E  AM �    �UP 
 >�%    i  AI  Y    e  AI V      M        �  厼 M        �  厼	 N N M        �  卙  M        j  卼
 >b%   this  AL  p    � AL V    S N  M        �  厧 M        �  厧 N N M        �  厛 N M        �  厃 M        �  厃#	 N N N N M        �  匳 M        �  匶	 N N N M        �  咰 M        �  咷 M        �  哘 M        �  哘
 >t$    temp  AJ  L      AJ ]      N N M        �  咷 N N N M        �  	�9 M        �  	�9 N N M        O  �" M        C  &�( M        ?   �) N N M        A  � M        �  � M          � N N N N M        r7  嗗
 Z   �7   N M        v7  嗈 N M        O  啝 M        C  &啩' N M        A  啝 M        �  啝 M          啝 N N N N M        v7  問 N M        O  哾 M        C  &唄$ N M        A  哾 M        �  哾 M          哾 N N N N M        i7  場 M        �7  場HB
 >a(    temp  AJ  �      AJ 	      B0   	    YT   B�
  �    � N N M        h7  %埿 M        i7  堦 M        �7  堦
 >a(    temp  AJ  �      AJ �      N N M        �7  堚 >a(    tmp  AK  �    "  AK �    �   + �  N M        �7  埿C	 M        �7  堒 N N N M        n7  �寚�W��
_J M        �7  噴)&e!	;"$	 Z   �7  �7  �7  �7  �7   >�'   _First  AM      E  AM �    �UP  >_    _Oldsize  AL  �     
 >P'    _Mid  AI  �    �  AI �    K >:'    _Newlast  AL  Z    2  AL �      M           坲	
 >P'   _First  AI  r      AI �    K >:'   _Last  AM  o      AM �    �UP  N N N M        w7  噞 N M        e7  塉 M        �7  塉HB
 >Q(    temp  AJ  O	      AJ `	      B0   Y	     B�  $	    : N N M        d7  %�$ M        e7  �> M        �7  �>
 >Q(    temp  AJ  :	      AJ J	      N N M        �7  �6 >Q(    tmp  AK  '	    "  AK J	    g   , 0  N M        �7  �$C	 M        �7  �0 N N N M        �'  壱	5\# M        
(  壱

	5\ M        5(  -�T M        c  �)/
 Z   �  
 >   _Ptr  AH  
      AI  �	    4    AH )
      AI �	    p  ?  >#    _Bytes  AK  
    \   0 '  M        s  �d
9
 Z   �   >_    _Ptr_container  AH  
      AI  
      N N N M        t(  変	
 >c   _First  AI  �	    
  AI �	      >霥   _Last  AM  �	    } P '  AM 1
    #  N N N" Z   �6  �!  �!  �7  �!  �!  �!   �          @         A �h�   �  �  r  s  t  v  w  x  y  �  �  �           
  �  �  �  �  �  �  �  �  �  �  �  �  �  J  K  O  S  n  |  }  �  �  �  �  �  �  �  �  �  �  �  �  �  $  %  3  >  ?  j  k  �  �  �  �  �  �  �  <  A  C  ^  c  �  �  �  �      �  �  '  (  e  
      /   9   I   �   �   !  �#  �'  �'  (  
(  /(  4(  5(  t(  �(  �(  �(  �(  �(  )  )  )  )  )  )  )  �1  �1  �1  �1  �1  >2  ?2  Y2  a2  v2  �2  �2  �2  �2  �2  �2  �2  V7  W7  [7  \7  ]7  ^7  _7  `7  a7  b7  c7  d7  e7  f7  g7  h7  i7  j7  k7  l7  m7  n7  o7  q7  r7  s7  u7  v7  w7  }7  ~7  7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  �7  
 :�  O        $LN1358  @  �  Othis  H  ))  Odevice  P  笻  OshaderFactory  X  �  OshaderName  `  鵥  OextraMacros  h  0   OuseRayQuery  p  u   OcomputeGroupSize  x  t$  ObindingLayout  �  t$  OextraBindingLayout  �  t$  ObindlessLayout  8   僣  Omacros  �   k'  OrtPipelineDesc  �   �%  OpipelineDesc  9d      E   9z      E   9�      E   9�      E   9      E   9)      E   9C      E   9]      E   9|      E   9�      E   9�      �(   9�      E   9�      E   9      E   9q      E   9�      E   9      E   9*      E   9d      E   9�      E   9�      E   9�      E   9�      E   9?      E   9Y      E   9�      胑   9�      胑   9V      胑   9u      胑   9�      �(   9�      E   9	      E   9!	      耑   9F	      E   9\	      E   9v	      ](   9�	      ^(   9�	      ^(   O   �   h          j
  �  *   \        �9      �H   "  �U   $  ��  &  �  (  �  *  �~  +  ��  .  ��  /  ��  0  ��  1  �	  2  �1  3  �=  4  �e  5  ��  6  ��  ;  �  >  �9  ?  ��  @  ��  A  ��  C  ��  D  �-  E  �9  F  �d  G  �  L  ��  W  ��  X  ��  Y  ��  [  �	  \  �	  _  �`	  `  �i	  c  �y	  d  ��	  e  ��	  g  �5
  h  �X
  g  �^
  $  ��   �   I F                                �`RayTracingPass::Init'::`1'::dtor$0  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O �   �   I F                                �`RayTracingPass::Init'::`1'::dtor$1  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O �   �   J F                                �`RayTracingPass::Init'::`1'::dtor$40  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   I F            -      '             �`RayTracingPass::Init'::`1'::dtor$3  >僣    macros  EN  8         '  >k'    rtPipelineDesc  EN  �         '                        �  O �   �   J F                                �`RayTracingPass::Init'::`1'::dtor$42  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F                                �`RayTracingPass::Init'::`1'::dtor$43  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F                                �`RayTracingPass::Init'::`1'::dtor$44  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   I F                                �`RayTracingPass::Init'::`1'::dtor$4  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O �   �   I F                                �`RayTracingPass::Init'::`1'::dtor$6  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O �   �   J F                                �`RayTracingPass::Init'::`1'::dtor$12  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F                                �`RayTracingPass::Init'::`1'::dtor$18  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F                                �`RayTracingPass::Init'::`1'::dtor$21  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F                                �`RayTracingPass::Init'::`1'::dtor$22  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F            -      '             �`RayTracingPass::Init'::`1'::dtor$26  >僣    macros  EN  8         '  >k'    rtPipelineDesc  EN  �         '                        �  O�   �   J F                                �`RayTracingPass::Init'::`1'::dtor$27  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F                                �`RayTracingPass::Init'::`1'::dtor$28  >僣    macros  EN  8           >k'    rtPipelineDesc  EN  �                                  �  O�   �   J F            -      '             �`RayTracingPass::Init'::`1'::dtor$33  >僣    macros  EN  8         '  >k'    rtPipelineDesc  EN  �         '                        �  O,   H   0   H  
 _   H   c   H  
 o   H   s   H  
 �   H   �   H  
 �   H   �   H  
 �   H   �   H  
 �   H   �   H  
 �   H   �   H  
 !  H   %  H  
 A  H   E  H  
 Q  H   U  H  
 w  H   {  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 $  H   (  H  
 <  H   @  H  
 q  H   u  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 8  H   <  H  
 o  H   s  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
   H   	  H  
 &  H   *  H  
   H   �  H  
 �  H   �  H  
 `  H   d  H  
 p  H   t  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 
  H     H  
   H   �  H  
 �  H   �  H  
   H     H  
   H     H  
 $  H   (  H  
 D  H   H  H  
 \  H   `  H  
 �  H   �  H  
 �  H   �  H  
   H   !  H  
 -  H   1  H  
 <
  H   @
  H  
 L
  H   P
  H  
 6  H   :  H  
 F  H   J  H  
 ,  H   0  H  
 <  H   @  H  
 �  H   �  H  
   H     H  
   H     H  
 %  H   )  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 	  H   
  H  
 �  H   �  H  
 �  H   �  H  
   H     H  
 '  H   +  H  
 G  H   K  H  
 _  H   c  H  
 �  H   �  H  
 �  H   �  H  
   H      H  
 ,  H   0  H  
 �  H   �  H  
   H     H  
 #  H   '  H  
 3  H   7  H  
 �  H   �  H  
 �  H   �  H  
 3  H   7  H  
 C  H   G  H  
 �  H      H  
   H     H  
   H      H  
 0  H   4  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H     H  
 �  H   �  H  
   H     H  
 )  H   -  H  
 H  H   L  H  
 X  H   \  H  
 {  H     H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 	  H   
  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 �  H   �  H  
 #   H   '   H  
 3   H   7   H  
 s   H   w   H  
 �   H   �   H  
 z!  H   ~!  H  
 �!  H   �!  H  
 �!  H   �!  H  
 �!  H   �!  H  
 �!  H   �!  H  
 /"  H   3"  H  
 ?"  H   C"  H  
 �"  H   �"  H  
 �"  H   �"  H  
 �"  H   �"  H  
 �"  H   �"  H  
 &  �   &  �  
 u'  H   y'  H  
 �'  H   �'  H  
 �'  H   �'  H  
 �'  H   �'  H  
 �'  H   �'  H  
 �'  H   �'  H  
 �'  H   �'  H  
 �'  H   �'  H  
 �'  H   �'  H  
 (  H   	(  H  
 (  H   (  H  
 %(  H   )(  H  
 5(  H   9(  H  
 E(  H   I(  H  
 U(  H   Y(  H  
 e(  H   i(  H  
 u(  H   y(  H  
 �(  H   �(  H  
 �(  H   �(  H  
 �(  H   �(  H  
 �(  H   �(  H  
 �(  H   �(  H  
 �(  H   �(  H  
 �(  H   �(  H  
 �(  H   �(  H  
 )  H   	)  H  
 )  H   )  H  
 %)  H   ))  H  
 5)  H   9)  H  
 E)  H   I)  H  
 U)  H   Y)  H  
 e)  H   i)  H  
 u)  H   y)  H  
 �)  H   �)  H  
 �)  H   �)  H  
 �)  H   �)  H  
 �)  H   �)  H  
 �)  H   �)  H  
 �)  H   �)  H  
 p+  i   t+  i  
 �+  i   �+  i  
 �+  i   �+  i  
 8,  m   <,  m  
 �,  m   �,  m  
 �,  m   �,  m  
  -  u   -  u  
 I-  u   M-  u  
 v-  u   z-  u  
 �-  t   �-  t  
 .  t   .  t  
 =.  t   A.  t  
 �.  v   �.  v  
 �.  v   �.  v  
 /  v   
/  v  
 X/  w   \/  w  
 �/  w   �/  w  
 �/  w   �/  w  
  0  x   $0  x  
 i0  x   m0  x  
 �0  x   �0  x  
 �0  y   �0  y  
 01  y   41  y  
 ]1  y   a1  y  
 �1  z   �1  z  
 �1  z   �1  z  
 %2  z   )2  z  
 x2  j   |2  j  
 �2  j   �2  j  
 �2  j   �2  j  
 @3  k   D3  k  
 �3  k   �3  k  
 �3  k   �3  k  
 4  n   4  n  
 Q4  n   U4  n  
 ~4  n   �4  n  
 �4  o   �4  o  
 5  o   5  o  
 F5  o   J5  o  
 �5  p   �5  p  
 �5  p   �5  p  
 6  p   6  p  
 `6  q   d6  q  
 �6  q   �6  q  
 �6  q   �6  q  
 (7  r   ,7  r  
 q7  r   u7  r  
 �7  r   �7  r  
 �7  s   �7  s  
 98  s   =8  s  
 f8  s   j8  s  
 H崐�  �       7   H崐�   �       7   @UH冹 H嬯L�
    A�   篅   H崓`  �    H兡 ]�   J   #      H崐8   �       O   H崐�   �       <   H崐�   �       G   H崐`  �       7   H崐`  �       =   H崐�  �       7   @UH冹 H嬯L�
    A�   �0   H崓`  �    H兡 ]�   =   #      H崐`  �       7   H崐�  �       9   @UH冹 H嬯L�
    A�   篐   H崓`  �    H兡 ]�   ?   #      H崐`  �       7   H崐0   �       U   H崐�   �       \   H媻P   �       7   @VAWH冹(I繋�8庛8�L嬄H嬹I;�噇  H塡$@H�H婭H+薍墊$PL塪$XI�9庛8庛8I嬆L塼$ H鏖H龙H嬄H凌?H蠭嬊H嬍H验H+罤;衯M嬿�L�4M;餗B�3�H呟剛   H塴$HH媙H;輙H嬎�    H兠HH;輚風�I嬆H婲H媗$HI+菻鏖H龙H嬄H凌?H蠬�襀菱H侜   rI婬鳫兟'L+罥岪鳫凐噷   L嬃I嬋�    H�>H墌H墌M;鱳wK�鯤��    H侞   r!H岾'H;藇Y�    H吚tIH峹'H冪郒塆H呟tH嬎�    H孁L媎$XH�;H媆$@L媡$ H�>H墌H媩$PH塅H兡(A_^描    惕    惕    虥   ?   �      /     O     �     �  3   �  F      �   s  � G            �     �  �7        �std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Clear_and_reserve_geometric 
 >�'   this  AJ          AL       {h  >_   _Newsize  AK          AP       }� � " AP �     �  	  ` �  �  �   >_    _Newcapacity  AV  q       �  ' M        i  
&
e >_    _Geometric  AV  w       M        �  
&
 N N- M        �7  K�P^	 >:'   _Newvec  AM V      C       �       C      �     � � 
 �   M           K�v M        b   K�v& M        (  �)
!
6, M        9   �%$%%A	 Z   �  q   >_    _Block_size  AJ  )    
  AJ �      >_    _Ptr_container  AH  3      AH V    0 	 " 
 >�    _Ptr  AM  @      AM V      M        r  �.
 Z      N N M        r  並
 Z      N N M        �   
�
 N N N N M        k  ��5 M        c  ��)
 Z   �  
 >   _Ptr  AP �       >#    _Bytes  AK  �     -  AK �      M        s  
��# >_    _Ptr_container  AJ  �       AJ �     �  �  >_    _Back_shift  AP  �     M  AP �     �  �  N N N M           ��	
 >P'   _First  AI  )     �  AI �      >:'   _Last  AN  �     %  N
 Z   g   (                     @ V h   �  r  s  c  (  e  i  j  k  �  
         9   b   �   �   �   !  �7         $LN97  @   �'  Othis  H   _  O_Newsize  O �   �           �  �
  
   t       u �   � �&   � �~   � ��   � ��   � �   � �  � �  � �  � �y  � ��  � ��  � �,   E   0   E  
 �   E   �   E  
 �   E   �   E  
 �   E     E  
 
  E     E  
 !  E   %  E  
 \  E   `  E  
 �  E   �  E  
 &  E   *  E  
 :  E   >  E  
 N  E   R  E  
   E     E  
 *  E   .  E  
 S  E   W  E  
 c  E   g  E  
 �  E   �  E  
 �  E   �  E  
 t  E   x  E  
 �  E   �  E  
 �  E   �  E  
 �  E   �  E  
 �  E     E  
 (  E   ,  E  
 8  E   <  E  
 �  E   �  E  
 �  E   �  E  
 �  E   �  E  
 D  �   H  �  
 �  E   �  E  
 @VAWH冹(I縐UUUUUUL嬄H嬹I;�噃  H塡$@H�H婭H+薍墊$PL塪$XI极*I嬆L塼$ H鏖H龙H嬄H凌?H蠭嬊H嬍H验H+罤;衯M嬿�L�4M;餗B�3�H呟剛   H塴$HH媙H;輙H嬎�    H兠0H;輚風�I嬆H婲H媗$HI+菻鏖H龙H嬄H凌?H蠬�RH菱H侜   rI婬鳫兟'L+罥岪鳫凐噲   L嬃I嬋�    H�>H墌H墌M;鱳sK�vH零H侞   r!H岾'H;藇Y�    H吚tIH峹'H冪郒塆H呟tH嬎�    H孁L媎$XH�;H媆$@L媡$ H�>H墌H媩$PH塅H兡(A_^描    惕    惕    虥   =   �      +     K     ~     �  3   �  C      �   o  � G            �     �  �7        �std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Clear_and_reserve_geometric 
 >{'   this  AJ          AL       wd  >_   _Newsize  AK          AP       y� � " AP �     �  	  ` �  �  �   >_    _Newcapacity  AV  q       �  ' M        p  
&
e >_    _Geometric  AV  w       M        �  
&
 N N- M        �7  G�L^	 >!'   _Newvec  AM R      C       �       C      �     � � 
 �   M           G�r M        c   G�r& M        (  �)
!
6, M        9   �!$%%A	 Z   �  q   >_    _Block_size  AJ  %    
  AJ �      >_    _Ptr_container  AH  /      AH R    0 	 " 
 >�    _Ptr  AM  <      AM R      M        r  �*
 Z      N N M        r  丟
 Z      N N M        �   
�
 N N N N M        r  ��5 M        c  ��)
 Z   �  
 >   _Ptr  AP �       >#    _Bytes  AK  �     -  AK }      M        s  
��# >_    _Ptr_container  AJ  �       AJ �     �  �  >_    _Back_shift  AP  �     M  AP �     �  }  N N N M           ��	
 >6'   _First  AI  )     �  AI }      >!'   _Last  AN  �     %  N
 Z   n   (                     @ V h   �  r  s  c  (  l  p  q  r  �           9   c   �   �   �   !  �7         $LN97  @   {'  Othis  H   _  O_Newsize  O �   �           �  �
  
   t       u �   � �&   � �~   � ��   � ��   � �   � �  � �  � �  � �u  � �}  � ��  � �,   B   0   B  
 �   B   �   B  
 �   B   �   B  
 �   B   �   B  
 	  B   
  B  
   B   !  B  
 X  B   \  B  
 �  B   �  B  
 "  B   &  B  
 6  B   :  B  
 J  B   N  B  
   B     B  
 &  B   *  B  
 O  B   S  B  
 _  B   c  B  
 �  B   �  B  
 �  B   �  B  
 p  B   t  B  
 �  B   �  B  
 �  B   �  B  
 �  B   �  B  
 �  B   �  B  
 $  B   (  B  
 4  B   8  B  
 ~  B   �  B  
 �  B   �  B  
 �  B   �  B  
 @  �   D  �  
 �  B   �  B  
 H冹HH峀$ �    H�    H峀$ �    �
   -      Y      {      �   �   F G                       q        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               �            J �   K �,   3   0   3  
 �   �   �   �  
 �   3   �   3  
 H塡$VH冹 H�H嬹H呟teH墊$0H媦H;遲H嬎�    H兠@H;遳颒�H媀H媩$0H+袶冣繦侜   rL婣鳫兟'I+菻岮鳫凐w I嬋�    3繦�H塅H塅H媆$8H兡 ^描    �'   J   i      �         �   �  � G            �   
   �   
(        �std::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Tidy 
 >$c   this  AJ          AL       { t   M        5(  ;*B M        c  G)
 Z   �  
 >   _Ptr  AJ h       >#    _Bytes  AK  ;     O   2  " M        s  
P#
 
 Z   �   >_    _Ptr_container  AP  T     6    AP h       >_    _Back_shift  AJ  7     S 1   AJ h       N N N M        t(  	
 >c   _First  AI  
     ~ r   >霥   _Last  AM       "  N                       H� 2 h   �  s  t  c  4(  5(  t(  �(  �(  )  )         $LN44  0   $c  Othis  O  �   `           �   �
  	   T       � �
    �    �4    �m   	 �r   
 �v    �z   
 ��    �,   P   0   P  
 �   P   �   P  
 �   P   �   P  
   P   #  P  
 @  P   D  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 �  P   �  P  
 4  P   8  P  
 X  P   \  P  
 �  �   �  �  
 �  P   �  P  
 H冹(H�
    �    �   r             �   w   7 G                     ~        坰td::_Xlen_string 
 Z   �!   (                      @        $LN3  O �   (              �            		 �   
	 �,   4   0   4  
 s   �   w   �  
 �   4   �   4  
 H冹(H�
    �    �   �             �   �   � G                     g        坰td::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Xlength 
 Z   �!   (                      @        $LN3  O   �   (              �
            a �   b �,   F   0   F  
 �   �   �   �  
 �   F   �   F  
 H冹(H�
    �    �   �             �   �   � G                     n        坰td::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Xlength 
 Z   �!   (                      @        $LN3  O   �   (              �
            a �   b �,   C   0   C  
 �   �   �   �  
 �   C   �   C  
 H冹(H�
    �    �   �             �   �   � G                     �(        坰td::vector<donut::engine::ShaderMacro,std::allocator<donut::engine::ShaderMacro> >::_Xlength 
 Z   �!   (                      @        $LN3  O �   (              �
            a �   b �,   Q   0   Q  
 �   �   �   �  
 �   Q   �   Q  
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8   �   �      �      �      ,     O  4   U  3   [        �     r G            `     `  )        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >�   _Ptr  AK          AW       D/  >_   _Count  AL       G4  AP         B M        �7  E
(?SD3$--K
 Z   ~   >#     _New_capacity  AH  �     �  * N  V r  AJ  �       AM  O     =  ^ �  AH �     G  ,  AJ �       M        �7  �� M        ?   �� N N M        �  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        �  ��?�� M        �  ��?��* M        (  ��

*%
u- M        9   ��	)
��
 Z   q   >_    _Block_size  AJ  �     �  �  AJ �       >_    _Ptr_container  AH  �       AH �     }  b 
 >�    _Ptr  AV  �       AV �     ~ V "  M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  X(  M          X' >_    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M        �  
~ N N N M        �  -�W M        3  �&P M        c  �
)/
 Z   �  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        s  
�#
2
 Z   �   >_    _Ptr_container  AP        AP +    4  *  >_    _Back_shift  AJ      
  AJ Z      N N N N N M        �  L4 N M        0  $# >p    _Result  AM  '       AM 8      M        �  ' N N                       @ z h   �  �  r  s  t  �  $  0  3  ?  �  �  �  �  �  �  c  �  �  �    �  �  '  (  /   9   �7  �7         $LN144  @   �  Othis  H   �  O_Ptr  P   _  O_Count e [�  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_1>  O�   h           `  �  
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,   8   0   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �   8   �   8  
 �   8     8  
   8     8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 m  8   q  8  
 }  8   �  8  
 H  8   L  8  
 \  8   `  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 �  8   �  8  
 F  8   J  8  
 k  8   o  8  
 {  8     8  
 �  8   �  8  
 �  8   �  8  
   8     8  
   8   #  8  
 �  8   �  8  
 �  8   �  8  
 \  �   `  �  
   8     8  
 H冹(I拎H嬄I侙   rH婮鳬兝'H+罤兝鳫凐wH嬃I嬓H嬋H兡(�    �    �7      <         �   h  \ G            A      A   5(        �std::allocator<donut::engine::ShaderMacro>::deallocate 
 >鬳   this  AJ          AJ ,       D0   
 >霥   _Ptr  AK        @ /   >_   _Count  AP           M        c  )

 >   _Ptr  AH ,       >#    _Bytes  AP       8 " M        s  
#

 Z   �   >_    _Ptr_container  AJ       (    AJ ,       >_    _Back_shift  AH         AH ,       N N (                      H  h   �  s  c         $LN20  0   鬳  Othis  8   霥  O_Ptr  @   _  O_Count  O�   8           A   p     ,       � �   � �2   � �6   � �,   N   0   N  
 �   N   �   N  
 �   N   �   N  
 �   N   �   N  
 �   N   �   N  
   N   "  N  
 ?  N   C  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 �  N   �  N  
 (  �   ,  �  
 |  N   �  N  
 H婹H�    H呉HE旅   M      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   %   0   %  
 _   %   c   %  
 �   %   �   %  
  d T 4 2p    H           �      �      �    20    2           �      �      �   
 
4 
2p    B           �      �      �    20    <           �      �      �   
 
4 
2p    B           �      �      �    20    <           �      �      �   
 
4 
2p    B           �      �      �    �                  �      �      �    B                 �      �      �    t d 4 2�              �      �         
 
4 
2p    �           �      �          20    ^           �      �      
    T
 4	 2�p`    [           �      �         ! �     [          �      �         [   8          �      �         !       [          �      �         8  T          �      �         !   �     [          �      �         T  `          �      �      %    B             |      1       "           �      �      +   h           4      7             2 B             |      @       "           �      �      :   h           C      F             2 20           |      O       ?           �      �      I   h           R      U             :8  20           |      ^       �           �      �      X   h           a      d             :8  d 4 2p           |      m       �           �      �      g   (           p      s             
P 40 *0 
 
4 
2p           |      |       �           �      �      v   h                 �             >�  d 4 2p           |      �       �           �      �      �   (           �      �             P 40 *0 *0 *0 
 
4 
2`               �      �      �   ! t               �      �      �      s           �      �      �   !                 �      �      �   s   �           �      �      �    B�`      !           �      �      �   !+ +� � t
 4     !          �      �      �   !   �           �      �      �   ! T	 !   �          �      �      �   �   �           �      �      �   !   !   �          �      �      �   �   }          �      �      �   !   �  �  t
  4     !          �      �      �   }  �          �      �      �   !       !          �      �      �   �  �          �      �      �    B                 �      �      �   
 
4 
2`               �      �      �   ! t               �      �      �      s           �      �      �   !                 �      �      �   s   �           �      �      �    B�`      !           �      �      �   !+ +� � t
 4     !          �      �      �   !   �           �      �      �   ! T	 !   �          �      �      �   �   �           �      �      �   !   !   �          �      �      �   �   �          �      �      �   !   �  �  t
  4     !          �      �      �   �  �          �      �      �   !       !          �      �      �   �  �          �      �           B                 �      �          20           |             9           �      �         h                              :-
 ? 
��	��p`0P        �     }      !       j
          �      �         (           $      '   4
    :    �:    �v    *    `2    a<    犅    p�    p�    p%    p>    b    �>    廊�    �>    b    ��    �:    A    �    �    �   7   	   7      7      t      U   "   \   )   7   /   O   6   O   =   O   D   O   J      O   <   V      ]   G   d      i   7   p   =   w   7      p   �   9   �   s   f� "6�"<��D�8<,<,<6.bDf�D�n$v".$H"$."|$X"D$",$t&�*0,4"�&>.�0)"�$D"d$D"�,  2P    -           t      t      *    2P    -           p      p      0    2P    -           s      s      6   ! !� !t !d !4 ! P      �          �      �      <    20    �           �      �      B    B      A           �      �      H   
 
4 
2`               �      �      N   ! t               �      �      N      P           �      �      T   !                 �      �      N   P   �           �      �      Z    B                 �      �      `   
 d
 T	 4 2��p           |      l       �          �      �      f   (           o      r             �   :   4 2p    1           �      �      u   # #�����p`0           |      �       j          �      �      {   8               �      �   	   �    (0          
       �      �      �   �       a   �       c   �       e   � �� Q�
,  BP0      F           a      a      �    2�	p`P0           |      �       �           c      c      �   9            �   �      �   	   �          �   �       b   � 2pP0    D           b      b      �   
 
B	p`P0             |      �       W           e      e      �   9            �   �      �   	   �          �   �       d   � BP0      <           d      d      �         d T 4
 rp           |      �       y           �      �      �   (           �      �   
    @   ]   � d T 4 2p           |      �       *          �      �      �   (           �      �             � 40 *0 *0 *0  d 4
 rp           |             �           �      �      �   (                    
    @6       _         n*. d 4 rp           |             u           �      �         (                    
    @4    �   \      7   v d 4 2p    X           �      �         
 
4 
2p    2           �      �      #   
 
4 
2p    2           �      �      )   
 
4 
2p           |      5       }           �      �      /   (           8      ;             
, *0  0 
 
4 
2p    2           �      �      >    B      :           �      �      D                               �      '      %   Unknown exception                             �      +      %                               �      1      %   bad array new length                                .      \                                 b      h      n                   .?AVbad_array_new_length@std@@     o               ����                      _      /                   .?AVbad_alloc@std@@     o              ����                      e      )                   .?AVexception@std@@     o               ����                      k      #   string too long     ����    ����        ��������Initializing RayTracingPass %s... USE_RAY_QUERY main 0 RayGen Miss HitGroup ClosestHit AnyHit vector too long                                       k      �      �                         �                   �               ����    @                   k      �                                         e      �      �                         �                           �      �              ����    @                   e      �                                         _      �      �                         �                                   �      �      �              ����    @                   _      �      �   (   & 
�        std::exception::`vftable'    J      J  
    �   (   & 
�        std::bad_alloc::`vftable'    P      P  
    �   3   1 
�        std::bad_array_new_length::`vftable'     S      S  
 �"+�a榓嫁.
U:\堈�溞矺叜月o"0h�K蜌�(翼X[�,$1鱺,_bP.�銥e0耑窙俊⒗営F7�'OR�g1h>箞Uk�x@
V艬酰潩�/麧0�4jT<S砗灿8鬸鋲n&坧&蜿歎5[谯^癞�鰤傗R�.W羦班螋鐌狷椾棔滤棾硏�L�訧)#hv瓯�4�?�(庢$Z4磔%訧)#hv瓯寮镱�;蛁玻覬謽Z訧)#hv瓯�;镩垓浄No�:婭(镫挾�;A阢z呼�6牉食靭�/Y3>飖9屓斚=�:=B髯P�<籧濵檵A�+嚤踖p禭�?As贛嚤踖p禭飓D堦縵�6萪O�j�?/�I\K霵婬(τ��4僩'�'項j�-騿
蟐嶇'項j奺�>xG�:z傷┩Q��８!Mq "駺T{.硦娧跥U_憌8HW�UNKx�(繢徨欠碝;m希:b8�4n�*XQ族
Y'�G囥�7鯟溤赝_x颹鸥#b尅�(歌胴'洋m|U鲒�>鲢�6�<#�(棙奱@�=壄湣�佰涍?縻耠�)摪 eU犥赘WC搪Y�滃紞�5亐煬�0:�6O鴯犷A棊膬/S;圾j硘嶀預棊膬j鮔u霓呝遉�$|�,线廈oy巂C.nT@�伢遪_
 *翀O�
�	箩邆5>(H7и篼}箩邆5>螬�0|⒙徇�5>;ㄓ$7� �蹰kL�*胾�,凣e丈X
A�猐肚gf�5蟞Q鉖p鼡9b匄鰦▊5銇撆Ce琈�佗ф%珉I�+ 埚巌;�Ce琈�佗�kM9=Q柋*憓�-J�*憓�-J米@xy� gl��?甯毐*憓�-JUt��V�sⅢ<7�*憓�-J�昸鳐3桉酟y[蜩_�;镩垓浄覃�
6�<�2洑��絁;疲喸=�,!.+跭%I栶賑?Tb灯Y肁炢4Y癏^塔�5紒�捥]{謑p�8G�)Gf]{謑pXK
喐�Uf]{謑pa晹2@/f]{謑p颶,惭5颏\丽Υ冽t錶J�?卢鑂侽&z�0`w3nN鵘J馔翛�4萹�F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这�5�蘫� 菘U睃-9E\$L釉尴 蛮l�(噪嘕-WV8o��腫62V^橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 壛�5YJq覜垒�咞taR�,F_棢杻#Q`�G埩�5YJq覜垒�咞taR�,F_棢杻#Q`�G埳S�8萀D罵��$m翲咞taR�,F_棢杻#Q惕屖碋箎蒘�8萀D罖-;�&Ee箙鹴aR�,F_棢杻#Q惕屖碋箎駂-+柆笩嗿筯'Hdd�a�:_棢杻#Qq噰G�<威C帲晗DYＫ.W瓍鹴aR�,F_棢杻#Qn忭痰�駂-+柆叨馑鏯dd�a�:_棢杻#Qド愨	错剤^裗呔屸鷃兟+d+盷猓酚b���$鏎@呚�!斈怭Fd焤銎攟薑A髠哻8曀黩6婁棐O�)m鰸颢.mrˇ7=�2/邅溣礖憔鳳?譀�騜
�/頉j�[=苯b胼1=3�喟x睟樋SK饂P�)-坓�(鬄�汬'这枅^裗呔屸鷃兟+d+盷猓酚b���$鏎@呚�!斈怭Fd焤銎攟薑A髠哻8曀黩6婁棐O�)m鰸颢.mrˇ7=�2/邅溣礖憔鳳?譀Yw妄R棔沯�[=苯跙跚鸲喟x睟�2sq� 犼R-坓�(鬄�汬'这柹S�8萀D�蝿壒eV鷧鹴aR�,F_棢杻#Q�矶&:薗�3�dU� � ��1�dd�a�:�&瓞疊r"荃輚>虡议wyQ朏菜{.鶚议wyQ朏菜{.鶚议wyQ朏菜{.e乫倈忘f�(,>仉嘕-WV8o�.w⒇衞-坓�(鬄鮳�>i,夿坁裗呔屸懚獲r貂筦绬靻3;�F瞁:2&櫇�"`Z_餔旉>R�-坓�(鬄�汬'这枺袞炗贿筬�(,>�dd�a�:_棢杻#Q�3韠a潐	_簤�p畚佗勫r|騘朳u旟D|��5vy*�杜`癜囫碊u御{�	/‖ⅹ0G#盱谑�0G#盱谑�0G#盱谑j
Fp�禲籹;嗐8�-b(�s/婱n豱婓�4{	1B璈!偎 "��+芵n朄攩#�0G#盱谑褘颠熚e訾陷��紊H葀虥**陕�7摄�Tラ~�&B璈!偎 "��+芵n朄攩#�0G#盱谑q笣銓轊Щs;嗐8��&{-檔4�硓榥4�硓榥4�硓榥4�硓榥4�硓樧DvDK'Q鶘2m46dd�a�:画k湻J處{�
龃禑 閶�(廢蝍一7肟dd�a�:_棢杻#Q砼->9�*骫A焅裚艮�:_�dd�a�:�64_\论齱>�&��幱�厲E-;}k�;dd�a�:劁�<Лgf凳\涯Oc闲�
墸g律巿<筿�9E\$L釉�3,�4q胭9E\$L釉�3,�4q胭斓,k憵凡K劯�dd�a�:_棢杻#Q(�犹_B9E\$L釉�3,�4q胭-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H 楁)X昽�&g冀AH$樐蜆{戒�
ψ笥险就X琡v乼扩s咴�1X'T瓐E}�5摻隃婵%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� �;� タY�脌祦�
監�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       P                .debug$S       (               .debug$T       l                 .rdata         @       5G碚                         7          .text$mn       :      眡�     .debug$S                    .text$mn       �     �k     .debug$S          :           .text$mn    	   *     �#颶     .debug$S    
   �  ,       	    .text$mn       1      瑓w�     .debug$S       �             .text$mn    
   j     f�?�     .debug$S       0"  �       
    .text$x        F      �561
    .text$x        D      賄烋
    .text$x        �      h~
    .text$x        <      �(U
    .text$x        W      
扫
    .text$mn       X      颖�     .debug$S       �             .text$mn       y      嘭衿     .debug$S                    .text$x              S�    .text$mn       �      *VT     .debug$S       �             .text$x              S�    .text$mn       u      猃     .debug$S       (              .text$x              S�    .text$x              ��    .text$mn        �       榺�     .debug$S    !   �              .text$mn    "   }      胚9�     .debug$S    #   p         "    .text$mn    $          �邆     .debug$S    %            $    .text$mn    &        0润�     .debug$S    '     4       &    .text$mn    (   <      .ズ     .debug$S    )   0  
       (    .text$mn    *   <      .ズ     .debug$S    +   L  
       *    .text$mn    ,   !      :著�     .debug$S    -   <         ,    .text$mn    .   2      X于     .debug$S    /   <         .    .text$mn    0   "       坼	     .debug$S    1   �         0    .text$mn    2   "       坼	     .debug$S    3   �         2    .text$mn    4   
      m張�     .debug$S    5   �         4    .text$mn    6   2      nsJ     .debug$S    7   �         6    .text$mn    8   2      凃ㄈ     .debug$S    9   �         8    .text$mn    :   2      �<�     .debug$S    ;   �         :    .text$mn    <   ^      wP�     .debug$S    =   X         <    .text$mn    >   �      ；�/     .debug$S    ?   �         >    .text$mn    @   �      �     .debug$S    A   �         @    .text$mn    B         �%     .debug$S    C   h         B    .text$mn    D   ?      劸惂     .debug$S    E   \         D    .text$mn    F   9      T	�     .debug$S    G   �          F    .text$mn    H   �      麓l     .debug$S    I   �  *       H    .text$mn    J   �      �氽     .debug$S    K   �         J    .text$mn    L   �      f綛a     .debug$S    M   �  $       L    .text$mn    N         ��#     .debug$S    O   �          N    .text$mn    P         ��#     .debug$S    Q   �          P    .text$mn    R   �      	�     .debug$S    S   �         R    .text$mn    T   �      C�-     .debug$S    U   \  4       T    .text$mn    V   �      畸     .debug$S    W   �          V    .text$mn    X   B      贘S     .debug$S    Y             X    .text$mn    Z   B      贘S     .debug$S    [            Z    .text$mn    \   B      贘S     .debug$S    ]   �          \    .text$mn    ^   H       襶.      .debug$S    _   �         ^    .text$mn    `   �      h�$     .debug$S    a   �  6       `    .text$mn    b   j
  ;   6"�     .debug$S    c   �8  �      b    .text$x     d         &啍*b    .text$x     e         mE�b    .text$x     f   -      �%賐    .text$x     g         :�薭    .text$x     h         mE�b    .text$x     i         jF萣    .text$x     j         �0蝏    .text$x     k         �0蝏    .text$x     l         P瞧Xb    .text$x     m   -      -螭乥    .text$x     n         �0蝏    .text$x     o         !�鬮    .text$x     p   -      �U`b    .text$x     q         �0蝏    .text$x     r         "E萷b    .text$x     s         謊�/b    .text$x     t         �腷    .text$mn    u   �     臩髁     .debug$S    v     :       u    .text$mn    w   �     僋Q     .debug$S    x     :       w    .text$mn    y          aJ鄔     .debug$S    z   �          y    .text$mn    {   �      8耾^     .debug$S    |   H         {    .text$mn    }         �ッ     .debug$S    ~   �          }    .text$mn             �ッ     .debug$S    �                .text$mn    �         �ッ     .debug$S    �            �    .text$mn    �         �ッ     .debug$S    �             �    .text$mn    �   `     匮�5     .debug$S    �   �  B       �    .text$mn    �   A      �园     .debug$S    �   �         �    .text$mn    �         崪覩     .debug$S    �   �          �        \       ^        x                �                �                �                �                �                �                               1               I      .        j      P        �      �        �      \        �          i&                   �      (              X        "          i*                   A      ,        f      N        �      *        �      Z        �          i0                         y        3      }        L      &        �      R        �      <        :      �        �      2        �      $        �      0        4      D        Z      J        �      V        �      H        �      T              @        �      w              �        y      >        �      u        p              �      F        	      b        �	      `        ;
      L        `
               *               �                     �        o      B        �      {        9
      �        �
              e                    
        !      4        �              {      	        �              �              �               q              �      :        8      6        �      "        �      8        �              :              f              �              �              �                                          �              �      d        �       i        �!      j        `"              =#      e        $      k        �$      l        �%      m        �&      n        �'      o        d(      p        @)      f        *      q        �*      r        �+      s        �,      t        �-      g        f.      h        A/               T/               g/               |/           memcpy           memmove          $LN13       ^    $LN5        .    $LN10       \    $LN7        (    $LN13       X    $LN10       *    $LN16       Z    $LN3        y    $LN4        y    $LN3       }    $LN4        }    $LN106    &    $LN111      &    $LN62   �   R    $LN66       R    $LN35   ^   <    $LN38       <    $LN144  `  �    $LN151      �    $LN10       2    $LN10       0    $LN18       D    $LN48   �   J    $LN51       J    $LN50       V    $LN60   �   H    $LN63       H    $LN85       T    $LN47   �   @    $LN50       @    $LN97   �  w    $LN101      w    $LN3       �    $LN4        �    $LN47   �   >    $LN50       >    $LN97   �  u    $LN101      u    $LN3           $LN4            $LN11       F    $LN1358 j
  b    $LN1364     b    $LN68       `    $LN74   �   L    $LN77       L    $LN20   A   �    $LN23       �    $LN44   �   {    $LN47       {    $LN3       �    $LN4        �    $LN87           $LN21           $LN159  j  
        �/             �0             �1             3             ,4         $LN164      
    $LN38           $LN95       	    $LN59           $LN46           $LN17           $LN22       :    $LN22       6    $LN38       "    $LN22       8    $LN14   :       $LN17           .xdata      �          F┑@^        R5      �    .pdata      �         X賦鷁        v5      �    .xdata      �          （亵.        �5      �    .pdata      �          T枨.        �5      �    .xdata      �          %蚘%\        �5      �    .pdata      �         惻竗\        6      �    .xdata      �          （亵(        76      �    .pdata      �         2Fb�(        `6      �    .xdata      �          %蚘%X        �6      �    .pdata      �         惻竗X        �6      �    .xdata      �          （亵*        �6      �    .pdata      �         2Fb�*        	7      �    .xdata      �          %蚘%Z        <7      �    .pdata      �         惻竗Z        n7      �    .xdata      �          懐j瀥        �7      �    .pdata      �         Vbv鵼        �7      �    .xdata      �          �9�}        �7      �    .pdata      �         �1皚        8      �    .xdata      �          �F�&        ?8      �    .pdata      �         *!)	&        �8      �    .xdata      �          %蚘%R        �8      �    .pdata      �         寵QR        K9      �    .xdata      �          （亵<        �9      �    .pdata      �         翎珸<        �9      �    .xdata      �          蔜-鍏        H:      �    .pdata      �         愶L�        �:      �    .xdata      �         �qL儏        	;      �    .pdata      �         ~蕉絽        k;      �    .xdata      �         |眳        �;      �    .pdata      �         瞚挨�        /<      �    .xdata      �         S!熐�        �<      �    .pdata      �         �o垍        �<      �    .xdata      �         /
�2        U=      �    .pdata      �         +eS�2        �=      �    .xdata      �   	      �#荤2        �=      �    .xdata      �         j2        >      �    .xdata      �          3狷 2        B>      �    .xdata      �         /
�0        }>      �    .pdata      �         +eS�0        �>      �    .xdata      �   	      �#荤0        �>      �    .xdata      �         j0        >?      �    .xdata      �          3狷 0        �?      �    .xdata      �         蚲7MD        �?      �    .pdata      �         袮韁D        �?      �    .xdata      �   	      �#荤D        #@      �    .xdata      �         jD        S@      �    .xdata      �          愔
~D        堾      �    .xdata      �         蚲7MJ        笯      �    .pdata      �         钘盕J        锧      �    .xdata      �   	      �#荤J        A      �    .xdata      �         jJ        JA      �    .xdata      �          愔
~J        侫      �    .xdata      �         9巳遃        碅      �    .pdata      �         9ˊ綱        驛      �    .xdata      �   	      � )9V        1B      �    .xdata      �         jV        rB      �    .xdata      �          8斱V        笲      �    .xdata      �         �酑H        鶥      �    .pdata      �         秘濰        ,C      �    .xdata      �   	      �#荤H        ]C      �    .xdata      �         jH        慍      �    .xdata      �          wes軭        薈      �    .xdata      �         9巳逿        �C      �    .pdata      �                 @D      �    .xdata      �   	      � )9T        �D      �    .xdata      �         jT        肈      �    .xdata      �           &�T        E      �    .xdata      �          �搀@        OE      �    .pdata      �          *鬰@        繣      �    .xdata      �         帄)@        0F      �    .pdata      �         An矦              �    .xdata      �         炖Ｚ@        G      �    .pdata      �         =厛I@        咷      �    .xdata      �          ,x[鹷        鳪      �    .pdata      �         萣�5w        咹      �    .xdata      �          櫼椬w        I      �    .pdata      �         f	嘞w              �    .xdata      �         '蟍w        1J      �    .pdata      �         "6墁w        繨      �    .xdata      �         娿穠w        OK      �    .pdata      �         橑斎w        轐      �    .xdata      �          髐w        mL      �    .pdata      �         熓h憌        麹      �    .xdata      �         $垕寃        婱      �    .pdata      �         s�w        N      �    .xdata      �          �9��        ㎞      �    .pdata      �         �1皝        O      �    .xdata      �          �搀>        擮      �    .pdata      �          *鬰>        	P      �    .xdata      �         帄)>        }P      �    .pdata      �         An�>        驪      �    .xdata      �         炖Ｚ>        iQ      �    .pdata      �         =厛I>        逹      �    .xdata      �          ,x[鹵        UR      �    .pdata      �         萣�5u        鏡      �    .xdata      �          櫼椬u        xS      �    .pdata      �         f	嘞u        T      �    .xdata      �         '蟍u        濼      �    .pdata      �         "6墁u        1U      �    .xdata      �         娿穠u        腢      �    .pdata      �         濚P`u        WV      �    .xdata      �          髐u        闢      �    .pdata      �         餲u        }W      �    .xdata      �         $垕寀        X      �    .pdata      �         h	�8u              �    .xdata      �          �9�        6Y      �    .pdata      �         �1�        癥      �    .xdata      �         蚲7MF        )Z      �    .pdata      �         VH倸F        SZ      �    .xdata      �   	      �#荤F        |Z      �    .xdata      �         jF        ╖      �    .xdata      �          ;k#菷        赯      �    .xdata      �   $      �1(Lb        [      �    .pdata      �         =鉡錬        赱      �    .xdata      �   	      � )9b        璡      �    .xdata      �   �      p澹b        僝      �    .xdata      �   k       薆�>b        _^      �    .xdata                k筨        5_          .pdata              噖sbb        `         .xdata               k筨        鷃         .pdata              噖sbb        轪         .xdata               k筨        羈         .pdata              噖sbb                 .xdata               2騼擿        坉         .pdata              橛唆`        鹍         .xdata               （亵L        me         .pdata      	        礚        歟      	   .xdata      
         �9��        芿      
   .pdata              s�7鍑        /f         .xdata               �搀{        梖         .pdata      
        O?[4{        g      
   .xdata              T�%~{        tg         .pdata              *i澚{        鋑         .xdata              Ｕ峽        Th         .pdata              ��*2{        膆         .xdata               �9��        4i         .pdata              �1皟                 .xdata               溤h)        j         .pdata              橛唆        遤         .xdata        	      � )9        猭         .xdata              j        xl         .xdata               蓌_	        Lm         .xdata               |釣�        n         .pdata              鉙gI        耼         .xdata              宄�
        io         .pdata              瞀胮
        峱         .xdata        
      B>z]
        皅         .xdata               
豾�
        謗         .xdata              啡a�
        t         .xdata               r%�
        &u          .xdata      !        r%�
        Nv      !   .xdata      "        r%�
        xw      "   .xdata      #         ' 
              #   .xdata      $         M[�
        葃      $   .pdata      %        j蓑�
        鼁      %   .xdata      &        午e�
        /|      &   .pdata      '         媞�
        c}      '   .xdata      (        x嵛
        杶      (   .xdata      )         ��
        �      )   .xdata      *        �騧
        �      *   .xdata      +        r%�
        <�      +   .xdata      ,         �僓
        t�      ,   .xdata      -         g<nd
        獎      -   .pdata      .        套�
        迏      .   .xdata      /        J;u
        �      /   .pdata      0        啁鉥
        E�      0   .xdata      1        x嵛
        x�      1   .xdata      2         ��
        畩      2   .xdata      3        �騧
        陭      3   .xdata      4        r%�
        �      4   .xdata      5         )k籷
        V�      5   .xdata      6         M[�
        審      6   .pdata      7        2Fb�
        缾      7   .voltbl     8                 _volmd      8   .voltbl     9                 _volmd      9   .voltbl     :                 _volmd      :   .voltbl     ;                 _volmd      ;   .voltbl     <                 _volmd      <   .xdata      =        V踫�        髴      =   .pdata      >        粖�        輶      >   .xdata      ?  	      � )9        茡      ?   .xdata      @        遱谸        矓      @   .xdata      A         赸              A   .xdata      B        灩�	        悥      B   .pdata      C        *u廖	        �      C   .xdata      D  	      � )9	              D   .xdata      E        j	        2�      E   .xdata      F         嗞c�	        艠      F   .xdata      G        �:�?        R�      G   .pdata      H        駷tL        4�      H   .xdata      I  	      � )9        �      I   .xdata      J        �$惱        鶝      J   .xdata      K         鰢a        銣      K   .xdata      L        阊瑿        菨      L   .pdata      M        魺颁        潪      M   .xdata      N  	      � )9        r�      N   .xdata      O  
      ��'        J�      O   .xdata      P         �JY        (�      P   .xdata      Q         O�         �      Q   .pdata      R        s杳�        t�      R   .xdata      S         %蚘%:        纰      S   .pdata      T         T枨:        J�      T   .xdata      U         %蚘%6              U   .pdata      V         T枨6        �      V   .xdata      W        �:�"        {�      W   .pdata      X        A刄7"        r�      X   .xdata      Y  	      � )9"        h�      Y   .xdata      Z        j"        a�      Z   .xdata      [         c瀖0"        `�      [   .xdata      \         %蚘%8        Y�      \   .pdata      ]         T枨8        咯      ]   .xdata      ^         �9�        $�      ^   .pdata      _        礝
        仾      _   .rdata      `                     莳     `   .rdata      a         �;�         舄      a   .rdata      b                     �     b   .rdata      c                     2�     c   .rdata      d         �)         T�      d   .xdata$x    e                     ��      e   .xdata$x    f        虼�)               f   .data$r     g  /      嶼�         奴      g   .xdata$x    h  $      4��         戢      h   .data$r     i  $      鎊=         ?�      i   .xdata$x    j  $      銸E�         Y�      j   .data$r     k  $      騏糡         槵      k   .xdata$x    l  $      4��         铂      l       瘳           .rdata      m         燺渾         �      m   .data       n          烀�          *�      n       ^�     n   .rdata      o  "       OF6         叚      o   .rdata      p         �8          凯      p   .rdata      q         旲^         攮      q   .rdata      r         �6F�         霏      r   .rdata      s         箟�0         
�      s   .rdata      t         M罔�         #�      t   .rdata      u  	       許�=         :�      u   .rdata      v         �%�/         U�      v   .rdata      w         之祐         s�      w   .rdata      x         IM         尞      x   .rdata$r    y  $      'e%�         伯      y   .rdata$r    z        �          十      z   .rdata$r    {                     喈      {   .rdata$r    |  $      Gv�:         霎      |   .rdata$r    }  $      'e%�         �      }   .rdata$r    ~        }%B         -�      ~   .rdata$r                         C�         .rdata$r    �  $      `         Y�      �   .rdata$r    �  $      'e%�         x�      �   .rdata$r    �        �弾         洴      �   .rdata$r    �                     集      �   .rdata$r    �  $      H衡�         莜      �       �           .debug$S    �  4          `   .debug$S    �  4          b   .debug$S    �  @          c   .chks64     �  @                �  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_L@YAXPEAX_K1P6AX0@Z2@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z ??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@$$QEAV01@@Z ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ ??0?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ ??1ComputePipelineDesc@nvrhi@@QEAA@XZ ??1PipelineShaderDesc@rt@nvrhi@@QEAA@XZ ??4PipelineShaderDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z ??1PipelineHitGroupDesc@rt@nvrhi@@QEAA@XZ ??4PipelineHitGroupDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z ??1?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ ?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z ?_Xlength@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@CAXXZ ??1?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ ?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z ?_Xlength@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@CAXXZ ??1PipelineDesc@rt@nvrhi@@QEAA@XZ ?Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z ?Execute@RayTracingPass@@QEAAXPEAVICommandList@nvrhi@@HHPEAVIBindingSet@3@1PEAVIDescriptorTable@3@PEBX_K@Z ??1ShaderMacro@engine@donut@@QEAA@XZ ?CreateShader@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@PEBD0PEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@W4ShaderType@5@@Z ?CreateShaderLibrary@ShaderFactory@engine@donut@@QEAA?AV?$RefCountPtr@VIShaderLibrary@nvrhi@@@nvrhi@@PEBDPEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@@Z ?debug@log@donut@@YAXPEBDZZ ?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z ??1?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ ?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ ?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ ??$_Assign_counted_range@PEBUPipelineShaderDesc@rt@nvrhi@@@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAXPEBUPipelineShaderDesc@rt@nvrhi@@_K@Z ??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z ??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z ??1?$_Tidy_guard@V?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@@std@@QEAA@XZ ??$_Uninitialized_copy_n@PEBUPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@0@@Z ??$_Copy_n_unchecked4@PEBUPipelineHitGroupDesc@rt@nvrhi@@_KPEAU123@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@@Z ??$_Uninitialized_copy_n@PEBUPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineShaderDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineShaderDesc@rt@nvrhi@@@0@@Z ??$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z ??$_Uninitialized_move@PEAUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@QEAU123@0PEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z ??$_Move_unchecked@PEAUShaderMacro@engine@donut@@PEAU123@@std@@YAPEAUShaderMacro@engine@donut@@PEAU123@00@Z ??1?$_Uninitialized_backout_al@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ ??1?$_Uninitialized_backout_al@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ ??$construct@UPipelineHitGroupDesc@rt@nvrhi@@AEBU123@@?$_Default_allocator_traits@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@SAXAEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@1@QEAUPipelineHitGroupDesc@rt@nvrhi@@AEBU345@@Z ??1?$_Uninitialized_backout_al@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?catch$0@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA ?catch$1@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA ?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA ?catch$3@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA ?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA ?dtor$0@?0???$_Uninitialized_copy_n@PEBUPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@0@@Z@4HA ?dtor$0@?0???$_Uninitialized_copy_n@PEBUPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineShaderDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineShaderDesc@rt@nvrhi@@@0@@Z@4HA ?dtor$0@?0???$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z@4HA ?dtor$0@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$12@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$18@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$1@?0???$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z@4HA ?dtor$1@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$21@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$22@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$26@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$27@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$28@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$33@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$3@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$40@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$42@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$43@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$44@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$4@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA ?dtor$6@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$0 __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$2 __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$3 __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$6 __catch$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z$7 $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z $unwind$??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@$$QEAV01@@Z $pdata$??4?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV01@$$QEAV01@@Z $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIShader@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIBindingLayout@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $pdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $cppxdata$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $stateUnwindMap$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $ip2state$??1ComputePipelineDesc@nvrhi@@QEAA@XZ $unwind$??1PipelineShaderDesc@rt@nvrhi@@QEAA@XZ $pdata$??1PipelineShaderDesc@rt@nvrhi@@QEAA@XZ $cppxdata$??1PipelineShaderDesc@rt@nvrhi@@QEAA@XZ $stateUnwindMap$??1PipelineShaderDesc@rt@nvrhi@@QEAA@XZ $ip2state$??1PipelineShaderDesc@rt@nvrhi@@QEAA@XZ $unwind$??4PipelineShaderDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $pdata$??4PipelineShaderDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $cppxdata$??4PipelineShaderDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $stateUnwindMap$??4PipelineShaderDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $ip2state$??4PipelineShaderDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $unwind$??1PipelineHitGroupDesc@rt@nvrhi@@QEAA@XZ $pdata$??1PipelineHitGroupDesc@rt@nvrhi@@QEAA@XZ $cppxdata$??1PipelineHitGroupDesc@rt@nvrhi@@QEAA@XZ $stateUnwindMap$??1PipelineHitGroupDesc@rt@nvrhi@@QEAA@XZ $ip2state$??1PipelineHitGroupDesc@rt@nvrhi@@QEAA@XZ $unwind$??4PipelineHitGroupDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $pdata$??4PipelineHitGroupDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $cppxdata$??4PipelineHitGroupDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $stateUnwindMap$??4PipelineHitGroupDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $ip2state$??4PipelineHitGroupDesc@rt@nvrhi@@QEAAAEAU012@AEBU012@@Z $unwind$??1?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $chain$0$??1?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$0$??1?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $chain$1$??1?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$1$??1?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $unwind$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$3$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$3$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$4$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$4$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$5$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$5$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$7$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$7$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$8$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$8$?_Clear_and_reserve_geometric@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $unwind$?_Xlength@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@CAXXZ $unwind$??1?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $chain$0$??1?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$0$??1?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $chain$1$??1?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$1$??1?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $unwind$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$3$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$3$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$4$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$4$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$5$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$5$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$7$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$7$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $chain$8$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$8$?_Clear_and_reserve_geometric@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@AEAAX_K@Z $unwind$?_Xlength@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@CAXXZ $unwind$??1PipelineDesc@rt@nvrhi@@QEAA@XZ $pdata$??1PipelineDesc@rt@nvrhi@@QEAA@XZ $cppxdata$??1PipelineDesc@rt@nvrhi@@QEAA@XZ $stateUnwindMap$??1PipelineDesc@rt@nvrhi@@QEAA@XZ $ip2state$??1PipelineDesc@rt@nvrhi@@QEAA@XZ $unwind$?Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z $pdata$?Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z $cppxdata$?Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z $stateUnwindMap$?Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z $ip2state$?Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z $unwind$?dtor$3@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA $pdata$?dtor$3@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA $unwind$?dtor$26@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA $pdata$?dtor$26@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA $unwind$?dtor$33@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA $pdata$?dtor$33@?0??Init@RayTracingPass@@QEAA_NPEAVIDevice@nvrhi@@AEAVShaderFactory@engine@donut@@PEBDAEBV?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@_NIPEAVIBindingLayout@3@55@Z@4HA $unwind$?Execute@RayTracingPass@@QEAAXPEAVICommandList@nvrhi@@HHPEAVIBindingSet@3@1PEAVIDescriptorTable@3@PEBX_K@Z $pdata$?Execute@RayTracingPass@@QEAAXPEAVICommandList@nvrhi@@HHPEAVIBindingSet@3@1PEAVIDescriptorTable@3@PEBX_K@Z $unwind$??1ShaderMacro@engine@donut@@QEAA@XZ $pdata$??1ShaderMacro@engine@donut@@QEAA@XZ $unwind$?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z $pdata$?deallocate@?$allocator@UShaderMacro@engine@donut@@@std@@QEAAXQEAUShaderMacro@engine@donut@@_K@Z $unwind$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $pdata$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $chain$0$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $pdata$0$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $chain$1$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $pdata$1$?_Tidy@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXXZ $unwind$?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@CAXXZ $unwind$??$_Assign_counted_range@PEBUPipelineShaderDesc@rt@nvrhi@@@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAXPEBUPipelineShaderDesc@rt@nvrhi@@_K@Z $pdata$??$_Assign_counted_range@PEBUPipelineShaderDesc@rt@nvrhi@@@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAXPEBUPipelineShaderDesc@rt@nvrhi@@_K@Z $cppxdata$??$_Assign_counted_range@PEBUPipelineShaderDesc@rt@nvrhi@@@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAXPEBUPipelineShaderDesc@rt@nvrhi@@_K@Z $stateUnwindMap$??$_Assign_counted_range@PEBUPipelineShaderDesc@rt@nvrhi@@@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAXPEBUPipelineShaderDesc@rt@nvrhi@@_K@Z $ip2state$??$_Assign_counted_range@PEBUPipelineShaderDesc@rt@nvrhi@@@?$vector@UPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@AEAAXPEBUPipelineShaderDesc@rt@nvrhi@@_K@Z $unwind$??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $pdata$??$_Destroy_range@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAXPEAUShaderMacro@engine@donut@@QEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $unwind$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $pdata$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $cppxdata$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $stateUnwindMap$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $tryMap$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $handlerMap$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $handlerMap$0$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $handlerMap$1$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $ip2state$??$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z $unwind$?catch$0@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $pdata$?catch$0@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $unwind$?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $pdata$?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $cppxdata$?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $stateUnwindMap$?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $tryMap$?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $handlerMap$?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $ip2state$?catch$2@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $unwind$?catch$1@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $pdata$?catch$1@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $unwind$?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $pdata$?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $cppxdata$?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $stateUnwindMap$?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $tryMap$?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $handlerMap$?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $ip2state$?catch$4@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $unwind$?catch$3@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $pdata$?catch$3@?0???$_Insert_counted_range@PEBUShaderMacro@engine@donut@@@?$vector@UShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@AEAAXV?$_Vector_const_iterator@V?$_Vector_val@U?$_Simple_types@UShaderMacro@engine@donut@@@std@@@std@@@1@PEBUShaderMacro@engine@donut@@_K@Z@4HA $unwind$??$_Uninitialized_copy_n@PEBUPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@0@@Z $pdata$??$_Uninitialized_copy_n@PEBUPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@0@@Z $cppxdata$??$_Uninitialized_copy_n@PEBUPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@0@@Z $stateUnwindMap$??$_Uninitialized_copy_n@PEBUPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@0@@Z $ip2state$??$_Uninitialized_copy_n@PEBUPipelineHitGroupDesc@rt@nvrhi@@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@0@@Z $unwind$??$_Copy_n_unchecked4@PEBUPipelineHitGroupDesc@rt@nvrhi@@_KPEAU123@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@@Z $pdata$??$_Copy_n_unchecked4@PEBUPipelineHitGroupDesc@rt@nvrhi@@_KPEAU123@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@@Z $cppxdata$??$_Copy_n_unchecked4@PEBUPipelineHitGroupDesc@rt@nvrhi@@_KPEAU123@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@@Z $stateUnwindMap$??$_Copy_n_unchecked4@PEBUPipelineHitGroupDesc@rt@nvrhi@@_KPEAU123@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@@Z $ip2state$??$_Copy_n_unchecked4@PEBUPipelineHitGroupDesc@rt@nvrhi@@_KPEAU123@@std@@YAPEAUPipelineHitGroupDesc@rt@nvrhi@@PEBU123@_KPEAU123@@Z $unwind$??$_Uninitialized_copy_n@PEBUPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineShaderDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineShaderDesc@rt@nvrhi@@@0@@Z $pdata$??$_Uninitialized_copy_n@PEBUPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineShaderDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineShaderDesc@rt@nvrhi@@@0@@Z $cppxdata$??$_Uninitialized_copy_n@PEBUPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineShaderDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineShaderDesc@rt@nvrhi@@@0@@Z $stateUnwindMap$??$_Uninitialized_copy_n@PEBUPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineShaderDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineShaderDesc@rt@nvrhi@@@0@@Z $ip2state$??$_Uninitialized_copy_n@PEBUPipelineShaderDesc@rt@nvrhi@@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@YAPEAUPipelineShaderDesc@rt@nvrhi@@PEBU123@_KPEAU123@AEAV?$allocator@UPipelineShaderDesc@rt@nvrhi@@@0@@Z $unwind$??$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $pdata$??$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $cppxdata$??$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $stateUnwindMap$??$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $ip2state$??$_Uninitialized_copy_n@PEBUShaderMacro@engine@donut@@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@YAPEAUShaderMacro@engine@donut@@PEBU123@_KPEAU123@AEAV?$allocator@UShaderMacro@engine@donut@@@0@@Z $unwind$??$_Move_unchecked@PEAUShaderMacro@engine@donut@@PEAU123@@std@@YAPEAUShaderMacro@engine@donut@@PEAU123@00@Z $pdata$??$_Move_unchecked@PEAUShaderMacro@engine@donut@@PEAU123@@std@@YAPEAUShaderMacro@engine@donut@@PEAU123@00@Z $unwind$??1?$_Uninitialized_backout_al@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $pdata$??1?$_Uninitialized_backout_al@V?$allocator@UShaderMacro@engine@donut@@@std@@@std@@QEAA@XZ $unwind$??1?$_Uninitialized_backout_al@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$_Uninitialized_backout_al@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $unwind$??$construct@UPipelineHitGroupDesc@rt@nvrhi@@AEBU123@@?$_Default_allocator_traits@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@SAXAEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@1@QEAUPipelineHitGroupDesc@rt@nvrhi@@AEBU345@@Z $pdata$??$construct@UPipelineHitGroupDesc@rt@nvrhi@@AEBU123@@?$_Default_allocator_traits@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@SAXAEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@1@QEAUPipelineHitGroupDesc@rt@nvrhi@@AEBU345@@Z $cppxdata$??$construct@UPipelineHitGroupDesc@rt@nvrhi@@AEBU123@@?$_Default_allocator_traits@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@SAXAEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@1@QEAUPipelineHitGroupDesc@rt@nvrhi@@AEBU345@@Z $stateUnwindMap$??$construct@UPipelineHitGroupDesc@rt@nvrhi@@AEBU123@@?$_Default_allocator_traits@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@SAXAEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@1@QEAUPipelineHitGroupDesc@rt@nvrhi@@AEBU345@@Z $ip2state$??$construct@UPipelineHitGroupDesc@rt@nvrhi@@AEBU123@@?$_Default_allocator_traits@V?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@std@@@std@@SAXAEAV?$allocator@UPipelineHitGroupDesc@rt@nvrhi@@@1@QEAUPipelineHitGroupDesc@rt@nvrhi@@AEBU345@@Z $unwind$??1?$_Uninitialized_backout_al@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$_Uninitialized_backout_al@V?$allocator@UPipelineShaderDesc@rt@nvrhi@@@std@@@std@@QEAA@XZ $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_C@_0CC@IIPBNFJK@Initializing?5RayTracingPass?5?$CFs?4@ ??_C@_0O@OHMIONC@USE_RAY_QUERY@ ??_C@_04GHJNJNPO@main@ ??_C@_01GBGANLPD@0@ ??_C@_06FCBCKJDI@RayGen@ ??_C@_04INDFNAKP@Miss@ ??_C@_08CEDALIIF@HitGroup@ ??_C@_0L@LLJDGLKI@ClosestHit@ ??_C@_06BECGIOFH@AnyHit@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 __security_cookie 