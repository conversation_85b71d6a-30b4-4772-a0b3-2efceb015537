d�饒O� u      .drectve        P  �,               
 .debug$S        H] .  \�        @ B.debug$T        l   瑡             @ B.rdata          @   �             @ @@.text$mn        :   X� 拰         P`.debug$S          皩 紟        @B.text$mn        }  H� 艕         P`.debug$S        D  蠍 �     d   @B.text$mn            麩              P`.debug$S        P  � k�        @B.text$mn        S  � ^�         P`.debug$S        �
  惀 t�     D   @B.text$x            � ,�         P`.text$x            6� F�         P`.text$x            P� `�         P`.text$mn        <   j� Τ         P`.debug$S        0  某 舸     
   @B.text$mn        <   X� 數         P`.debug$S        L  驳      
   @B.text$mn        !   b� 兎         P`.debug$S        <  椃 痈        @B.text$mn        2   � A�         P`.debug$S        <  U� 懞        @B.text$mn        "   	�              P`.debug$S        �  +� 芳        @B.text$mn        "   W�              P`.debug$S        �  y� �        @B.text$mn        e   タ 
�         P`.debug$S        $  (� L�        @B.text$mn        [   � o�         P`.debug$S           兤 Ｊ        @B.text$mn           � 娝         P`.debug$S        0  斔 奶        @B.text$mn        }    � }�         P`.debug$S           懲 懸        @B.text$mn        �   m� �         P`.debug$S        �  &�          @B.text$mn        `   钯 N�         P`.debug$S        �  b� &�        @B.text$mn        j   谳 D�         P`.debug$S        �  X� 噜        @B.text$mn        `   溽 D�         P`.debug$S        �  X�  �        @B.text$mn           藻              P`.debug$S        �   族 锋        @B.text$mn           箧 �         P`.debug$S        �   �         @B.text$mn           &� 9�         P`.debug$S        �   M� -�        @B.text$mn           i�              P`.debug$S        �  栝 徐        @B.text$mn        +   p� 涰         P`.debug$S        �            @B.text$mn        �   珙 r�         P`.debug$S        �  愶  �        @B.text$mn        B   � R�         P`.debug$S           p� p�        @B.text$mn        B    铘         P`.debug$S          � �        @B.text$mn        B   X� 汌         P`.debug$S        �   个 戴        @B.text$mn        H   瘅              P`.debug$S        �  8�         @B.text$mn        �   � 见         P`.debug$S        �  潲 待        @B.text$mn        �  h� 1     d    P`.debug$S        j   %�     �  @B.text$x            屺 悫         P`.text$x         *   鳕 !�         P`.text$x         *   5� _�         P`.text$x            s� �         P`.text$x            墿 暕         P`.text$x            煩 缉         P`.text$x             譬 姗         P`.text$mn        9   皓              P`.debug$S        L  )� u�        @B.text$mn        O  憩 <�         P`.debug$S        $  x� 湺     F   @B.text$mn           X�              P`.debug$S          j� 偤        @B.text$mn        
   液              P`.debug$S        P  吆 /�     
   @B.text$mn           摷              P`.debug$S        ,  柤 陆        @B.text$mn            � 2�         P`.debug$S        �   P� �        @B.text$mn           P� a�         P`.debug$S        �   u� )�        @B.text$mn           e� v�         P`.debug$S          娎 灹        @B.text$mn        `  诹 :�         P`.debug$S        �  娒 
�     B   @B.text$mn           炍 蔽         P`.debug$S        �   晃 徬        @B.xdata             讼             @0@.pdata             呦 胂        @0@.xdata             	�             @0@.pdata             � �        @0@.xdata             ;�             @0@.pdata             G� S�        @0@.xdata             q�             @0@.pdata             y� 呅        @0@.xdata             Ｐ             @0@.pdata              恍        @0@.xdata             傩             @0@.pdata             嵝 硇        @0@.xdata             �             @0@.pdata             � #�        @0@.xdata             A�             @0@.pdata             I� U�        @0@.xdata             s�             @0@.pdata             {� 囇        @0@.xdata             パ             @0@.pdata             寡 叛        @0@.xdata             阊 餮        @0@.pdata             � !�        @0@.xdata             ?� O�        @0@.pdata             m� y�        @0@.xdata             椧         @0@.pdata             梢 找        @0@.xdata             笠             @0@.pdata              �        @0@.xdata             %�             @0@.pdata             -� 9�        @0@.xdata             W� g�        @0@.pdata             {� 囉        @0@.xdata          	   ビ         @@.xdata             掠 扔        @@.xdata             矣             @@.xdata             沼             @0@.pdata             嵊 碛        @0@.xdata             � �        @0@.pdata             =� I�        @0@.xdata             g� w�        @0@.pdata             曉 ≡        @0@.voltbl            吭               .xdata          (   猎 樵        @0@.pdata              	�        @0@.xdata          	   '� 0�        @@.xdata          .   D� r�        @@.xdata          �   抡             @@.xdata             d�             @0@.pdata             l� x�        @0@.xdata             栔             @0@.pdata             炛         @0@.xdata             戎             @0@.pdata             兄 苤        @0@.xdata                          @0@.pdata             � �        @0@.voltbl            ,�                .xdata             4�             @0@.pdata             @� L�        @0@.xdata             j� z�        @0@.pdata             幾 氉        @0@.xdata          	   缸 磷        @@.xdata             兆 圩        @@.xdata             遄             @@.xdata             枳 �        @0@.pdata             � $�        @0@.xdata          	   B� K�        @@.xdata             _� e�        @@.xdata             o�             @@.xdata             r�             @0@.pdata             z� 嗀        @0@.xdata             へ             @0@.pdata              肛        @0@.xdata             重 蜇        @0@.pdata             � �        @0@.xdata          	   0� 9�        @@.xdata             M� b�        @@.xdata             娰             @@.xdata             戀 ≠        @0@.pdata             蒂 临        @0@.xdata          	   哔 栀        @@.xdata              �        @@.xdata             �             @@.xdata             � /�        @0@.pdata             C� O�        @0@.xdata          	   m� v�        @@.xdata             娳 愙        @@.xdata             氌             @@.xdata              澸 节        @0@.pdata             掩 葳        @0@.xdata          	    �        @@.xdata             � �        @@.xdata             (�             @@.xdata             -�             @0@.pdata             5� A�        @0@.xdata             _� s�        @0@.pdata             囒 撣        @0@.xdata          	   臂 痕        @@.xdata             污 咱        @@.xdata             捋             @@.xdata              遨 �        @0@.pdata             � %�        @0@.xdata          	   C� L�        @@.xdata             `� h�        @@.xdata             r�             @@.xdata             冘             @0@.pdata             嬡 椳        @0@.xdata             弟             @0@.pdata             杰 绍        @0@.rdata             畿 ��        @@@.rdata             �             @@@.rdata             /� G�        @@@.rdata             e� }�        @@@.rdata             涊             @@@.xdata$x           拜 梯        @@@.xdata$x           噍         @@@.data$r         /   � I�        @@�.xdata$x        $   S� w�        @@@.data$r         $   嬣         @@�.xdata$x        $   罐 蒉        @@@.data$r         $   褶 �        @@�.xdata$x        $   � C�        @@@.rdata             W�             @@@.data               g�             @ @�.rdata             囘 熯        @@@.rdata             竭             @0@.rdata             眠             @@@.rdata             走             @@@.rdata             磉             @@@.rdata                          @@@.rdata             
�             @@@.rdata              �             @@@.rdata             <�             @@@.rdata             [�             @@@.rdata             w�             @@@.rdata             栢             @@@.rdata                          @@@.rdata             苦             @@@.rdata             奏             @@@.rdata             鲟             @@@.rdata             �             @@@.rdata             #�             @@@.rdata          !   /�             @@@.rdata             P�             @@@.rdata             j�             @@@.rdata          
   z�             @@@.rdata             囜             @@@.rdata             濁             @@@.rdata             贬             @@@.rdata             漆             @@@.rdata             蒯             @@@.rdata          	   赆             @@@.rdata             筢             @@@.rdata             �             @@@.rdata             �             @@@.rdata             !�             @@@.rdata          (   6� ^�        @@@.rdata             愨             @@@.rdata$r        $   犫 拟        @@@.rdata$r           忖 鲡        @@@.rdata$r            � �        @@@.rdata$r        $   � :�        @@@.rdata$r        $   N� r�        @@@.rdata$r           愩 ゃ        @@@.rdata$r            裸        @@@.rdata$r        $   帚         @@@.rdata$r        $   � 2�        @@@.rdata$r           P� d�        @@@.rdata$r           n� 婁        @@@.rdata$r        $   ㄤ 啼        @@@.data$rs        *   噤 
�        @@�.rdata$r           � (�        @@@.rdata$r           2� >�        @@@.rdata$r        $   H� l�        @@@.rdata$r        $   �� ゅ        @@@.data$rs        6   洛         @@�.rdata$r           � �        @@@.rdata$r            � ,�        @@@.rdata$r        $   6� Z�        @@@.rdata$r        $   n� 掓        @@@.data$rs        O   版 ��        @P�.rdata$r           	� �        @@@.rdata$r           '� ;�        @@@.rdata$r        $   O� s�        @@@.rdata             囩             @0@.rdata             嬬             @P@.rdata             涚             @P@.debug$S        4    哏        @B.debug$S        4   箸 '�        @B.debug$S        @   ;� {�        @B.debug$S        H   忚 阻        @B.debug$S        `   腓 K�        @B.chks64         �  _�              
     /FAILIFMISMATCH:"_CRT_STDIO_ISO_WIDE_SPECIFIERS=0" /FAILIFMISMATCH:"_MSC_VER=1900" /FAILIFMISMATCH:"_ITERATOR_DEBUG_LEVEL=0" /FAILIFMISMATCH:"RuntimeLibrary=MT_StaticRelease" /DEFAULTLIB:"libcpmt" /FAILIFMISMATCH:"annotate_string=0" /FAILIFMISMATCH:"annotate_vector=0" /DEFAULTLIB:"LIBCMT" /DEFAULTLIB:"OLDNAMES" /DEFAULTLIB:"VCOMP"    �   S  [     D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\RenderTargets.obj : <`  �  & y�   & y�  Microsoft (R) Optimizing Compiler  $std  $_Unreachable_sentinel_detail  $_Cpos  $pmr  $_Has_ADL_swap_detail  $_Strong_order  $_Partial_order 
 $rel_ops ! $_Compare_strong_order_fallback " $_Compare_partial_order_fallback  $_Compare_weak_order_fallback  $_Ensure_adl  $_Weak_order  $literals  $string_literals  $string_view_literals  $chrono_literals 	 $chrono 	 $ranges 
 $_Iter_move  $_Cpos 	 $_Begin 	 $_Empty  $_Size 
 $_Iter_swap  $_Unchecked_begin  $_End  $_Swap  $_Unchecked_end 
 $_Rbegin  $_Rend  $_Data  $placeholders  $this_thread  $_Binary_hypot  $nvrhi  $utils  $rt 
 $cluster  $ObjectTypes  $donut 	 $engine  $math 	 $colors  $log 	 $render  �   �1  g :   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Can_memcpy_val j _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_offset h _    std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Memcpy_val_size L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 L U   std::_Ratio_divide<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx1 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1000000000>,std::ratio<1,1> >::_Gy > U  � 蕷;std::integral_constant<__int64,1000000000>::value ] _   ��std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::npos / :   std::atomic<long>::is_always_lock_free L U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Nx2 P U  � 蕷;std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,1000000000> >::_Dx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx1 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx1 R U  � 蕷;std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Nx2 N U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Dx2 M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gx M U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<1000000000,1> >::_Gy T _   std::allocator<nvrhi::rt::GeometryDesc>::_Minimum_asan_allocation_alignment 1 <   donut::math::vector<unsigned int,2>::DIM  U  < std::ratio<60,1>::num  U   std::ratio<60,1>::den ; :   std::atomic<unsigned __int64>::is_always_lock_free ) <   nvrhi::ObjectTypes::SharedHandle - <  �  nvrhi::ObjectTypes::D3D11_Device 4 <  �  nvrhi::ObjectTypes::D3D11_DeviceContext / <  �  nvrhi::ObjectTypes::D3D11_Resource - <  �  nvrhi::ObjectTypes::D3D11_Buffer 7 <  �  nvrhi::ObjectTypes::D3D11_RenderTargetView 7 <  �  nvrhi::ObjectTypes::D3D11_DepthStencilView 9 <  �  nvrhi::ObjectTypes::D3D11_ShaderResourceView : <  �  nvrhi::ObjectTypes::D3D11_UnorderedAccessView - <  �  nvrhi::ObjectTypes::D3D12_Device 3 <  �  nvrhi::ObjectTypes::D3D12_CommandQueue : <  �  nvrhi::ObjectTypes::D3D12_GraphicsCommandList / <  �  nvrhi::ObjectTypes::D3D12_Resource A <  �  nvrhi::ObjectTypes::D3D12_RenderTargetViewDescriptor A <  �  nvrhi::ObjectTypes::D3D12_DepthStencilViewDescriptor F <  �  nvrhi::ObjectTypes::D3D12_ShaderResourceViewGpuDescripror G <  �  nvrhi::ObjectTypes::D3D12_UnorderedAccessViewGpuDescripror 4 <  �	  nvrhi::ObjectTypes::D3D12_RootSignature 4 <  �
  nvrhi::ObjectTypes::D3D12_PipelineState 7 <  �  nvrhi::ObjectTypes::D3D12_CommandAllocator * <  �  nvrhi::ObjectTypes::VK_Device 2 <  �  nvrhi::ObjectTypes::VK_PhysicalDevice , <  �  nvrhi::ObjectTypes::VK_Instance ) <  �  nvrhi::ObjectTypes::VK_Queue 1 <  �  nvrhi::ObjectTypes::VK_CommandBuffer 0 <  �  nvrhi::ObjectTypes::VK_DeviceMemory E _   std::allocator<char16_t>::_Minimum_asan_allocation_alignment * <  �  nvrhi::ObjectTypes::VK_Buffer ) <  �  nvrhi::ObjectTypes::VK_Image - <  �	  nvrhi::ObjectTypes::VK_ImageView < <  �
  nvrhi::ObjectTypes::VK_AccelerationStructureKHR + <  �  nvrhi::ObjectTypes::VK_Sampler 0 <  �  nvrhi::ObjectTypes::VK_ShaderModule . <  �
  nvrhi::ObjectTypes::VK_RenderPass / <  �  nvrhi::ObjectTypes::VK_Framebuffer 2 <  �  nvrhi::ObjectTypes::VK_DescriptorPool 7 <  �  nvrhi::ObjectTypes::VK_DescriptorSetLayout 1 <  �  nvrhi::ObjectTypes::VK_DescriptorSet 2 <  �  nvrhi::ObjectTypes::VK_PipelineLayout , <  �  nvrhi::ObjectTypes::VK_Pipeline , <  �  nvrhi::ObjectTypes::VK_Micromap 3 <  �  nvrhi::ObjectTypes::VK_ImageCreateInfo   U   std::ratio<1,1000>::num   U  �std::ratio<1,1000>::den C _   std::_String_val<std::_Simple_types<char16_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char16_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char16_t> >::_Small_string_capacity d _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_BUF_SIZE f _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Alloc_mask q _   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Small_string_capacity q _  	 std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Least_allocation_size j :   std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Can_memcpy_val m _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_offset k _    std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Memcpy_val_size # U   std::ratio<1,1000000>::num ' U  �@B std::ratio<1,1000000>::den ` _   ��std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::npos 8 :   std::atomic<unsigned long>::is_always_lock_free / <  � nvrhi::rt::cluster::kClasByteAlignment . <   nvrhi::rt::cluster::kClasMaxTriangles - <   nvrhi::rt::cluster::kClasMaxVertices 2 <  ���� nvrhi::rt::cluster::kMaxGeometryIndex � :   std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc,nvrhi::rt::PipelineHitGroupDesc &&,nvrhi::rt::PipelineHitGroupDesc &>::_Bitcopy_assignable : _    std::integral_constant<unsigned __int64,0>::value E _   std::allocator<char32_t>::_Minimum_asan_allocation_alignment � :   std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Same_size_and_compatible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc,nvrhi::rt::PipelineShaderDesc &&,nvrhi::rt::PipelineShaderDesc &>::_Bitcopy_assignable C _   std::_String_val<std::_Simple_types<char32_t> >::_BUF_SIZE E _   std::_String_val<std::_Simple_types<char32_t> >::_Alloc_mask P _   std::_String_val<std::_Simple_types<char32_t> >::_Small_string_capacity d _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_BUF_SIZE f _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Alloc_mask q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Small_string_capacity q _   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Least_allocation_size j :   std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Can_memcpy_val m _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_offset k _    std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Memcpy_val_size � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc,nvrhi::rt::GeometryDesc &&,nvrhi::rt::GeometryDesc &>::_Bitcopy_assignable ) �    std::_Invoker_functor::_Strategy , �   std::_Invoker_pmf_object::_Strategy - �   std::_Invoker_pmf_refwrap::_Strategy ` _   ��std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::npos - �   std::_Invoker_pmf_pointer::_Strategy , �   std::_Invoker_pmd_object::_Strategy - �   std::_Invoker_pmd_refwrap::_Strategy - �   std::_Invoker_pmd_pointer::_Strategy  ;  ��I@donut::math::PI_f " s  
�-DT�!	@donut::math::PI_d ! ;  ��7�5donut::math::epsilon " ;  �  �donut::math::infinity  ;  �  �donut::math::NaN D _   ��std::basic_string_view<char,std::char_traits<char> >::npos : �   std::_Floating_type_traits<float>::_Mantissa_bits : �   std::_Floating_type_traits<float>::_Exponent_bits D �   std::_Floating_type_traits<float>::_Maximum_binary_exponent E �   �俿td::_Floating_type_traits<float>::_Minimum_binary_exponent : �   std::_Floating_type_traits<float>::_Exponent_bias 7 �   std::_Floating_type_traits<float>::_Sign_shift ; �   std::_Floating_type_traits<float>::_Exponent_shift : <  � std::_Floating_type_traits<float>::_Exponent_mask E <  ���� std::_Floating_type_traits<float>::_Normal_mantissa_mask G <  ��� std::_Floating_type_traits<float>::_Denormal_mantissa_mask J <  �  @ std::_Floating_type_traits<float>::_Special_nan_mantissa_mask B <  �   �std::_Floating_type_traits<float>::_Shifted_sign_mask F <  �  �std::_Floating_type_traits<float>::_Shifted_exponent_mask ; �  5 std::_Floating_type_traits<double>::_Mantissa_bits ; �   std::_Floating_type_traits<double>::_Exponent_bits E �  �std::_Floating_type_traits<double>::_Maximum_binary_exponent G �  �黶td::_Floating_type_traits<double>::_Minimum_binary_exponent ; �  �std::_Floating_type_traits<double>::_Exponent_bias 8 �  ? std::_Floating_type_traits<double>::_Sign_shift < �  4 std::_Floating_type_traits<double>::_Exponent_shift ; _  �std::_Floating_type_traits<double>::_Exponent_mask J _  
������� std::_Floating_type_traits<double>::_Normal_mantissa_mask L _  
������� std::_Floating_type_traits<double>::_Denormal_mantissa_mask O _  
�       std::_Floating_type_traits<double>::_Special_nan_mantissa_mask G _  	�       �std::_Floating_type_traits<double>::_Shifted_sign_mask K _  
�      �std::_Floating_type_traits<double>::_Shifted_exponent_mask Z _   std::allocator<nvrhi::rt::PipelineShaderDesc>::_Minimum_asan_allocation_alignment � _   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >::_Minimum_asan_allocation_alignment J _   ��std::basic_string_view<wchar_t,std::char_traits<wchar_t> >::npos $ �   ��std::strong_ordering::less $ �    std::strong_ordering::equal Z:    std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Multi ]:   std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0>::_Standard & �   std::strong_ordering::greater ) <   donut::math::vector<bool,2>::DIM � _   std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >::_Minimum_asan_allocation_alignment \ _   std::allocator<nvrhi::rt::PipelineHitGroupDesc>::_Minimum_asan_allocation_alignment ) <   donut::math::vector<bool,3>::DIM ' �   std::_Comparison_category_none * �   std::_Comparison_category_partial ' �   std::_Comparison_category_weak ) �    std::_Comparison_category_strong J _   ��std::basic_string_view<char8_t,std::char_traits<char8_t> >::npos ) <   donut::math::vector<bool,4>::DIM L _   ��std::basic_string_view<char16_t,std::char_traits<char16_t> >::npos �     m_   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Bucket_size m_   std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Min_buckets g:    std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::_Multi L _   ��std::basic_string_view<char32_t,std::char_traits<char32_t> >::npos * <   donut::math::vector<float,3>::DIM : _   std::integral_constant<unsigned __int64,2>::value � _   std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >::_Minimum_asan_allocation_alignment  <   nvrhi::c_HeaderVersion " <   nvrhi::c_MaxRenderTargets  <   nvrhi::c_MaxViewports % <   nvrhi::c_MaxVertexAttributes # <   nvrhi::c_MaxBindingLayouts & <  � nvrhi::c_MaxBindingsPerLayout 5 <   nvrhi::c_MaxVolatileConstantBuffersPerLayout , <    nvrhi::c_MaxVolatileConstantBuffers % <  � nvrhi::c_MaxPushConstantSize 3 <   nvrhi::c_ConstantBufferOffsetSizeAlignment . :    std::integral_constant<bool,0>::value * <   donut::math::vector<float,4>::DIM * 錏        donut::math::lumaCoefficients * <   donut::math::vector<float,2>::DIM W :   std::_Array_const_iterator<nvrhi::BindingSetItem,128>::_Unwrap_when_unverified  H    std::denorm_absent  H   std::denorm_present a _   std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >::_Minimum_asan_allocation_alignment  K    std::round_toward_zero  K   std::round_to_nearest # H    std::_Num_base::has_denorm ( :    std::_Num_base::has_denorm_loss % :    std::_Num_base::has_infinity & :    std::_Num_base::has_quiet_NaN R :   std::_Array_const_iterator<enum nvrhi::Format,8>::_Unwrap_when_unverified * :    std::_Num_base::has_signaling_NaN # :    std::_Num_base::is_bounded ! :    std::_Num_base::is_exact " :    std::_Num_base::is_iec559 # :    std::_Num_base::is_integer " :    std::_Num_base::is_modulo " :    std::_Num_base::is_signed ' :    std::_Num_base::is_specialized ( :    std::_Num_base::tinyness_before  :    std::_Num_base::traps $ K    std::_Num_base::round_style  �    std::_Num_base::digits ! �    std::_Num_base::digits10 % �    std::_Num_base::max_digits10 % �    std::_Num_base::max_exponent ' �    std::_Num_base::max_exponent10 % �    std::_Num_base::min_exponent ' �    std::_Num_base::min_exponent10  �    std::_Num_base::radix ' :   std::_Num_int_base::is_bounded % :   std::_Num_int_base::is_exact ' :   std::_Num_int_base::is_integer + :   std::_Num_int_base::is_specialized " �   std::_Num_int_base::radix ) H   std::_Num_float_base::has_denorm + :   std::_Num_float_base::has_infinity , :   std::_Num_float_base::has_quiet_NaN 0 :   std::_Num_float_base::has_signaling_NaN ) :   std::_Num_float_base::is_bounded ( :   std::_Num_float_base::is_iec559 ( :   std::_Num_float_base::is_signed - :   std::_Num_float_base::is_specialized * K   std::_Num_float_base::round_style $ �   std::_Num_float_base::radix * �   std::numeric_limits<bool>::digits - :   std::numeric_limits<char>::is_signed ? _   std::_Locked_pointer<std::_Ref_count_base>::_Lock_mask - :    std::numeric_limits<char>::is_modulo @ _    std::_Locked_pointer<std::_Ref_count_base>::_Not_locked * �   std::numeric_limits<char>::digits N _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_not_needed J _   std::_Locked_pointer<std::_Ref_count_base>::_Locked_notify_needed , �   std::numeric_limits<char>::digits10 E _   �黶td::_Locked_pointer<std::_Ref_count_base>::_Ptr_value_mask 7 <  �����nvrhi::TextureSubresourceSet::AllMipLevels 9 <  �����nvrhi::TextureSubresourceSet::AllArraySlices 4 :   std::numeric_limits<signed char>::is_signed 1 �   std::numeric_limits<signed char>::digits 3 �   std::numeric_limits<signed char>::digits10 6 :   std::numeric_limits<unsigned char>::is_modulo 3 �   std::numeric_limits<unsigned char>::digits 5 �   std::numeric_limits<unsigned char>::digits10 # �        nvrhi::AllSubresources 0 :   std::numeric_limits<char8_t>::is_modulo - �   std::numeric_limits<char8_t>::digits / �   std::numeric_limits<char8_t>::digits10 1 :   std::numeric_limits<char16_t>::is_modulo . �   std::numeric_limits<char16_t>::digits 0 �   std::numeric_limits<char16_t>::digits10 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 J U   std::_Ratio_divide<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx1 P U  ��枠 std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx1 L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Nx2 1 :   std::numeric_limits<char32_t>::is_modulo L U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Dx2 . �    std::numeric_limits<char32_t>::digits 0 �  	 std::numeric_limits<char32_t>::digits10 K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,10000000>,std::ratio<1,1> >::_Gy 0 :   std::numeric_limits<wchar_t>::is_modulo - �   std::numeric_limits<wchar_t>::digits / �   std::numeric_limits<wchar_t>::digits10 . :   std::numeric_limits<short>::is_signed + �   std::numeric_limits<short>::digits - �   std::numeric_limits<short>::digits10  �   �  , :   std::numeric_limits<int>::is_signed ) �   std::numeric_limits<int>::digits + �  	 std::numeric_limits<int>::digits10 ) <   donut::math::frustum::numCorners - :   std::numeric_limits<long>::is_signed * �   std::numeric_limits<long>::digits , �  	 std::numeric_limits<long>::digits10 0 :   std::numeric_limits<__int64>::is_signed - �  ? std::numeric_limits<__int64>::digits / �   std::numeric_limits<__int64>::digits10 7 :   std::numeric_limits<unsigned short>::is_modulo 4 �   std::numeric_limits<unsigned short>::digits 6 �   std::numeric_limits<unsigned short>::digits10 5 :   std::numeric_limits<unsigned int>::is_modulo 2 �    std::numeric_limits<unsigned int>::digits 4 �  	 std::numeric_limits<unsigned int>::digits10 6 :   std::numeric_limits<unsigned long>::is_modulo 3 �    std::numeric_limits<unsigned long>::digits 5 �  	 std::numeric_limits<unsigned long>::digits10 9 :   std::numeric_limits<unsigned __int64>::is_modulo 6 �  @ std::numeric_limits<unsigned __int64>::digits 8 �   std::numeric_limits<unsigned __int64>::digits10   U  std::ratio<3600,1>::num  �   ^�    U   std::ratio<3600,1>::den           nvrhi::EntireBuffer + �   std::numeric_limits<float>::digits # <   kPolymorphicLightTypeShift " <   kPolymorphicLightTypeMask - �   std::numeric_limits<float>::digits10 . <  �   kPolymorphicLightShapingEnableBit 1 <  �    kPolymorphicLightIesProfileEnableBit 1 �  	 std::numeric_limits<float>::max_digits10 - ;  �   羕PolymorphicLightMinLog2Radiance - ;  �   BkPolymorphicLightMaxLog2Radiance 1 �  � std::numeric_limits<float>::max_exponent 3 �  & std::numeric_limits<float>::max_exponent10 2 �   �僺td::numeric_limits<float>::min_exponent 4 �   �踫td::numeric_limits<float>::min_exponent10 , �  5 std::numeric_limits<double>::digits . �   std::numeric_limits<double>::digits10 2 �   std::numeric_limits<double>::max_digits10 2 �   std::numeric_limits<double>::max_exponent 4 �  4std::numeric_limits<double>::max_exponent10 4 �  �黶td::numeric_limits<double>::min_exponent 6 �  �威std::numeric_limits<double>::min_exponent10 1 �  5 std::numeric_limits<long double>::digits 3 �   std::numeric_limits<long double>::digits10 7 �   std::numeric_limits<long double>::max_digits10 7 �   std::numeric_limits<long double>::max_exponent 9 �  4std::numeric_limits<long double>::max_exponent10 9 �  �黶td::numeric_limits<long double>::min_exponent ; �  �威std::numeric_limits<long double>::min_exponent10 A _   std::allocator<char>::_Minimum_asan_allocation_alignment 6 :   std::_Iterator_base0::_Unwrap_when_unverified  U   std::ratio<24,1>::num  U   std::ratio<24,1>::den 7 :   std::atomic<unsigned int>::is_always_lock_free I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx1 I U  std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<24,1>,std::ratio<3600,1> >::_Gy � :   std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture> const &,nvrhi::RefCountPtr<nvrhi::ITexture> &>::_Same_size_and_compatible 7 :   std::_Iterator_base12::_Unwrap_when_unverified � :    std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture> const &,nvrhi::RefCountPtr<nvrhi::ITexture> &>::_Bitcopy_constructible � :    std::_Trivial_cat<nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture>,nvrhi::RefCountPtr<nvrhi::ITexture> const &,nvrhi::RefCountPtr<nvrhi::ITexture> &>::_Bitcopy_assignable 9 U  ��Q std::integral_constant<__int64,86400>::value  <   cMaxDeltaLobes 1 U   std::integral_constant<__int64,1>::value C _   std::_Locked_pointer<std::_Stop_callback_base>::_Lock_mask ? _   std::_String_val<std::_Simple_types<char> >::_BUF_SIZE D _    std::_Locked_pointer<std::_Stop_callback_base>::_Not_locked R _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_not_needed N _   std::_Locked_pointer<std::_Stop_callback_base>::_Locked_notify_needed A _   std::_String_val<std::_Simple_types<char> >::_Alloc_mask L _   std::_String_val<std::_Simple_types<char> >::_Small_string_capacity I _   �黶td::_Locked_pointer<std::_Stop_callback_base>::_Ptr_value_mask X _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_BUF_SIZE Z _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Alloc_mask % U  ��Q std::ratio<86400,1>::num e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Small_string_capacity ! U   std::ratio<86400,1>::den e _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Least_allocation_size ^ :   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Can_memcpy_val a _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_offset _ _    std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Memcpy_val_size 1 <   donut::math::vector<unsigned int,3>::DIM # <   cStablePlaneMaxVertexIndex ( <  �����cStablePlaneInvalidBranchID ) <  ����cStablePlaneEnqueuedBranchID " <    cStablePlaneJustStartedID  U   std::ratio<7,1>::num  U   std::ratio<7,1>::den I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx1 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx1 M U  ��Q std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Nx2 I U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Dx2 H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gx H U   std::_Ratio_multiply<std::ratio<7,1>,std::ratio<86400,1> >::_Gy K :   std::atomic<std::_Stop_callback_base const *>::is_always_lock_free : U  ��:	 std::integral_constant<__int64,604800>::value T _   ��std::basic_string<char,std::char_traits<char>,std::allocator<char> >::npos 1 <   donut::math::vector<unsigned int,4>::DIM � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Same_size_and_compatible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_constructible � :   std::_Trivial_cat<nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount,nvrhi::rt::OpacityMicromapUsageCount &,nvrhi::rt::OpacityMicromapUsageCount &>::_Bitcopy_assignable ( U  ��: std::ratio<146097,400>::num $ U  �std::ratio<146097,400>::den T U  ��: std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx1 P U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx1 T U  ��Q std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Nx2 P U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Dx2 O U   std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gx O U  �std::_Ratio_multiply<std::ratio<146097,400>,std::ratio<86400,1> >::_Gy < U  �X呩std::integral_constant<__int64,31556952>::value 4 _  @ _Mtx_internal_imp_t::_Critical_section_size 5 _   _Mtx_internal_imp_t::_Critical_section_align + :    std::_Aligned_storage<64,8>::_Fits * :    std::_Aligned<64,8,char,0>::_Fits + :    std::_Aligned<64,8,short,0>::_Fits ) :   std::_Aligned<64,8,int,0>::_Fits ( U  �X呩std::ratio<31556952,1>::num $ U   std::ratio<31556952,1>::den  抏   _Mtx_try  抏   _Mtx_recursive  U   std::ratio<12,1>::num  U   std::ratio<12,1>::den K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Nx2 K U   std::_Ratio_divide<std::ratio<31556952,1>,std::ratio<12,1> >::_Dx2 Q U  �X呩std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx1 M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx1  蔱   std::_INVALID_ARGUMENT  U   std::ratio<1,12>::num  蔱   std::_NO_SUCH_PROCESS  U   std::ratio<1,12>::den & 蔱   std::_OPERATION_NOT_PERMITTED , 蔱   std::_RESOURCE_DEADLOCK_WOULD_OCCUR - 蔱   std::_RESOURCE_UNAVAILABLE_TRY_AGAIN % �    _Atomic_memory_order_relaxed % �   _Atomic_memory_order_consume M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Nx2 % �   _Atomic_memory_order_acquire % �   _Atomic_memory_order_release M U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Dx2 % �   _Atomic_memory_order_acq_rel % �   _Atomic_memory_order_seq_cst L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gx L U   std::_Ratio_multiply<std::ratio<31556952,1>,std::ratio<1,12> >::_Gy ; U  �r ( std::integral_constant<__int64,2629746>::value - :    std::chrono::system_clock::is_steady $ U   std::ratio<1,10000000>::num ( U  ��枠 std::ratio<1,10000000>::den D _   std::allocator<wchar_t>::_Minimum_asan_allocation_alignment B _   std::_String_val<std::_Simple_types<wchar_t> >::_BUF_SIZE D _   std::_String_val<std::_Simple_types<wchar_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<wchar_t> >::_Small_string_capacity  U   std::ratio<1,1>::num  U   std::ratio<1,1>::den a _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_BUF_SIZE c _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Alloc_mask n _   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Small_string_capacity n _  	 std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Least_allocation_size g :   std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Can_memcpy_val j _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_offset h _    std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Memcpy_val_size ( <   donut::math::vector<int,2>::DIM J U   std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Nx2 N U  ��枠 std::_Ratio_divide<std::ratio<1,1>,std::ratio<1,10000000> >::_Dx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx1 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx1 ( U  ��枠 std::ratio<10000000,1>::num $ U   std::ratio<10000000,1>::den P U  ��枠 std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Nx2 L U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Dx2 K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gx K U   std::_Ratio_multiply<std::ratio<1,1>,std::ratio<10000000,1> >::_Gy ] _   ��std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::npos < U  ��枠 std::integral_constant<__int64,10000000>::value + :    std::_Aligned_storage<72,8>::_Fits * :    std::_Aligned<72,8,char,0>::_Fits + :    std::_Aligned<72,8,short,0>::_Fits - :   std::chrono::steady_clock::is_steady ) :   std::_Aligned<72,8,int,0>::_Fits & U   std::ratio<1,1000000000>::num * U  � 蕷;std::ratio<1,1000000000>::den a _   std::allocator<nvrhi::rt::OpacityMicromapUsageCount>::_Minimum_asan_allocation_alignment O U   std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Nx2 S U  � 蕷;std::_Ratio_divide<std::ratio<3600,1>,std::ratio<1,1000000000> >::_Dx2 Q U  std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx1 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx1 * U  � 蕷;std::ratio<1000000000,1>::num & U   std::ratio<1000000000,1>::den U U  � 蕷;std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Nx2 Q U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Dx2 P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gx P U   std::_Ratio_multiply<std::ratio<3600,1>,std::ratio<1000000000,1> >::_Gy E U  
� 牳0F  std::integral_constant<__int64,3600000000000>::value 1 U  
� 牳0F  std::ratio<3600000000000,1>::num ) U   std::ratio<3600000000000,1>::den A _   std::allocator<bool>::_Minimum_asan_allocation_alignment D _   std::allocator<char8_t>::_Minimum_asan_allocation_alignment 8 :    std::_False_trivial_cat::_Bitcopy_constructible 5 :    std::_False_trivial_cat::_Bitcopy_assignable + �!        nvrhi::rt::c_IdentityTransform I _   std::allocator<unsigned int>::_Minimum_asan_allocation_alignment - �    std::integral_constant<int,0>::value B _   std::_String_val<std::_Simple_types<char8_t> >::_BUF_SIZE D _   std::_String_val<std::_Simple_types<char8_t> >::_Alloc_mask O _   std::_String_val<std::_Simple_types<char8_t> >::_Small_string_capacity . :   std::integral_constant<bool,1>::value a _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_BUF_SIZE c _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Alloc_mask n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Small_string_capacity n _   std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Least_allocation_size  �  _CatchableType " �  _s__RTTIBaseClassDescriptor ? �  __vcrt_assert_va_start_is_not_reference<wchar_t const *> & �  $_TypeDescriptor$_extraBytes_24 6   __vcrt_va_list_is_reference<char const * const> G �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers *>  酕  PolymorphicLightInfo & �  $_TypeDescriptor$_extraBytes_28     int64_t    _Smtx_t  邫  MISHeuristic  齚  _Thrd_result  #   rsize_t &   $_TypeDescriptor$_extraBytes_23 - cZ  $_s__CatchableTypeArray$_extraBytes_32    _TypeDescriptor & NZ  $_TypeDescriptor$_extraBytes_34  G  PathTracerCameraData  渇  _Stl_critical_section 	 I  tm % �  _s__RTTICompleteObjectLocator2 A 
  __vcrt_va_list_is_reference<__crt_locale_pointers * const>  �  _s__CatchableType & �  $_TypeDescriptor$_extraBytes_19 & �  $_TypeDescriptor$_extraBytes_21  #   uint64_t 9   __vcrt_va_list_is_reference<wchar_t const * const> E �  __vcrt_assert_va_start_is_not_reference<wchar_t const * const> & �  $_TypeDescriptor$_extraBytes_20  貴  LightingControlData  p  va_list - �  $_s__CatchableTypeArray$_extraBytes_16 : 榝  std::_Vector_val<std::_Simple_types<unsigned int> > p 巉  std::_Compressed_pair<std::allocator<unsigned int>,std::_Vector_val<std::_Simple_types<unsigned int> >,1> s �  std::_Simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > � Y�  std::_Default_allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > � )�  std::_Default_allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > . /  std::_Conditionally_enabled_hash<int,1> ? sX  std::_Default_allocator_traits<std::allocator<wchar_t> >  �  std::_Lockit 4 ]�  std::_Wrap<donut::engine::FramebufferFactory>  坃  std::_Stop_callback_base  b  std::timed_mutex * pP  std::hash<enum nvrhi::ResourceType> 5 鵐  std::_String_val<std::_Simple_types<char8_t> > < N  std::_String_val<std::_Simple_types<char8_t> >::_Bxty 6 刕  std::allocator_traits<std::allocator<char8_t> > " 媂  std::_Char_traits<char,int>  "   std::_Atomic_counter_t  N  std::_Num_base & $/  std::hash<std::error_condition> K 鮎  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> > # �  std::numeric_limits<char8_t>  /,  std::_Big_uint128    std::condition_variable ) 怷  std::_Narrow_char_traits<char,int>  j  std::hash<float> 6 縍  std::allocator<nvrhi::rt::PipelineHitGroupDesc> R 歿  std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > > \ 絹  std::_Default_allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > " 唂  std::_Align_type<double,64>  /  std::hash<int>  P  std::_Num_int_base " �/  std::_System_error_category / 揚  std::_Conditionally_enabled_hash<bool,1> + 琠  std::_Atomic_storage<unsigned int,4>  H  std::float_denorm_style ? �Y  std::_Default_allocator_traits<std::allocator<char8_t> > 6 餧  std::allocator_traits<std::allocator<wchar_t> >  `  std::bad_cast     std::_Compare_t " u  std::numeric_limits<double> $ d  std::atomic<unsigned __int64>  �  std::__non_rtti_object ( @  std::_Basic_container_proxy_ptr12 � 巤  std::_Compressed_pair<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >,std::_Vector_val<std::_Simple_types<nvrhi::RefCountPtr<nvrhi::ITexture> > >,1> = 6`  std::_Atomic_pointer<std::_Stop_callback_base const *> > 刦  std::vector<unsigned int,std::allocator<unsigned int> > T Tf  std::vector<unsigned int,std::allocator<unsigned int> >::_Reallocation_policy 1    std::array<nvrhi::FramebufferAttachment,8>  q  std::_Num_float_base  抈  std::stop_token  �-  std::logic_error 7 FP  std::_Conditionally_enabled_hash<unsigned int,1> G yP  std::_Conditionally_enabled_hash<enum nvrhi::TextureDimension,1>  �  std::pointer_safety ! 頬  std::char_traits<char32_t>  �  std::_Compare_ncmp   R  std::numeric_limits<bool> # *Y  std::_WChar_traits<char16_t> P絴  std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> T b  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> > i �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_strategy h �  std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Allocation_policy   .  std::_Fake_proxy_ptr_impl  �  std::_Compare_ord * h  std::numeric_limits<unsigned short> ' �)  std::hash<nvrhi::BindingSetDesc> � 納  std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >  綻  std::stop_source � +�  std::allocator_traits<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > � 荝  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineHitGroupDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> >,1>  F.  std::overflow_error % L  std::_One_then_variadic_args_t W >W  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > * jb  std::_Vb_val<std::allocator<bool> >   闿  std::char_traits<wchar_t>  pa  std::recursive_mutex   �  std::pmr::memory_resource ! �  std::array<nvrhi::Rect,16> 4 酭  std::allocator<nvrhi::rt::PipelineShaderDesc> n D!  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > � !  std::vector<nvrhi::rt::OpacityMicromapUsageCount,std::allocator<nvrhi::rt::OpacityMicromapUsageCount> >::_Reallocation_policy  砞  std::false_type S 賴  std::allocator_traits<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > #讄  std::_Compressed_pair<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> >,std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,1>  K  std::float_round_style T �"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> > j Z"  std::vector<nvrhi::rt::GeometryDesc,std::allocator<nvrhi::rt::GeometryDesc> >::_Reallocation_policy ! a  std::hash<std::thread::id>  �  std::string B 襗  std::initializer_list<nvrhi::rt::OpacityMicromapUsageCount> , �#  std::array<nvrhi::BindingSetItem,128> � 镽  std::_Compressed_pair<std::allocator<nvrhi::rt::PipelineShaderDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineShaderDesc> >,1> > 3%  std::array<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5>  俛  std::adopt_lock_t  �  std::weak_ordering � S  std::_Compressed_pair<std::allocator<nvrhi::rt::GeometryDesc>,std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> >,1> , n  std::numeric_limits<unsigned __int64> 9 枤  std::shared_ptr<donut::engine::FramebufferFactory> \ 奩  std::_Default_allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 8 k�  std::_Ptr_base<donut::engine::FramebufferFactory> $ Z  std::numeric_limits<char16_t> 0 �&  std::array<nvrhi::VertexBufferBinding,16> � 獅  std::_Uhash_choose_transparency<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,void> � 憒  std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > % 誠  std::integral_constant<bool,1>   0  std::_Leave_proxy_unbound  Qa  std::_Mutex_base  胉  std::nostopstate_t  f  std::defer_lock_t   �.  std::_Init_once_completer v  std::unordered_map<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet>,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > �   std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >  �a  std::scoped_lock<> h 昅  std::_Compressed_pair<std::allocator<char32_t>,std::_String_val<std::_Simple_types<char32_t> >,1> % 5  std::array<nvrhi::Viewport,16>    std::_Iterator_base12 7  *  std::_Array_const_iterator<enum nvrhi::Format,8> ! /  std::hash<std::error_code> A =  std::basic_string_view<char8_t,std::char_traits<char8_t> > @ nY  std::_Default_allocator_traits<std::allocator<char32_t> > ( d  std::_Atomic_padded<unsigned int>  �?  std::allocator<char32_t> $ �  std::_Atomic_integral<long,4>  桺  std::hash<bool> 6 哅  std::_String_val<std::_Simple_types<char32_t> > =   std::_String_val<std::_Simple_types<char32_t> >::_Bxty ` 僋  std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1> " b  std::lock_guard<std::mutex> � ?�  std::allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > >  �  std::hash<long double> 2 (~  std::equal_to<nvrhi::TextureSubresourceSet> W �  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> > l a  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Construct_strategy k ]  std::basic_string<char16_t,std::char_traits<char16_t>,std::allocator<char16_t> >::_Allocation_policy � 鑯  std::_Uninitialized_backout<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *> o   std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >   �  std::_Comparison_category  f  std::try_to_lock_t � }  std::_Compressed_pair<std::hash<nvrhi::TextureSubresourceSet>,std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1>,1> U 鏨  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> > # ^  std::numeric_limits<wchar_t>  �  std::_Container_base0  w  std::hash<double> O 馳  std::_Default_allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> > & F[  std::bidirectional_iterator_tag . �)  std::hash<nvrhi::TextureSubresourceSet> " 猘  std::_Align_type<double,72> x �  std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > / Y  std::_Char_traits<char32_t,unsigned int>  >/  std::_System_error < 朿  std::_Atomic_padded<std::_Stop_callback_base const *> ( �)  std::hash<nvrhi::FramebufferInfo>  �.  std::error_condition % 砞  std::integral_constant<bool,0>  �  std::bad_exception & 肐  std::_Zero_then_variadic_args_t � 眧  std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > ? $`  std::_Atomic_storage<std::_Stop_callback_base const *,8>  �  std::_Fake_allocator / �"  std::array<nvrhi::BindingLayoutItem,128>  �-  std::invalid_argument   玗  std::char_traits<char8_t> N ]  std::allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> > U VW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::PipelineShaderDesc> > 1 臺  std::_Atomic_integral_facade<unsigned int>  瀉  std::cv_status S ]  std::allocator_traits<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > R cM  std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >  鑐  std::thread  a  std::thread::id  �-  std::length_error  3a  std::jthread ! s  std::numeric_limits<float> ) �  std::_Atomic_integral_facade<long>  絘  std::mutex % 盤  std::hash<enum nvrhi::BlendOp>  �  std::_Ref_count_base " VP  std::hash<unsigned __int64>  ]  std::ratio<60,1>  '  std::exception_ptr  �  std::strong_ordering  ]  std::ratio<1,1000000> % �  std::_Itraits_pointer_strategy C �  std::basic_string_view<char32_t,std::char_traits<char32_t> > )   std::hash<enum nvrhi::BlendFactor> f N  std::_Compressed_pair<std::allocator<char8_t>,std::_String_val<std::_Simple_types<char8_t> >,1> $ \  std::numeric_limits<char32_t>  �.  std::once_flag  �.  std::error_code T *  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> > i �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Construct_strategy h �  std::basic_string<char8_t,std::char_traits<char8_t>,std::allocator<char8_t> >::_Allocation_policy  �  std::exception W �  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> > l )  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Construct_strategy k %  std::basic_string<char32_t,std::char_traits<char32_t>,std::allocator<char32_t> >::_Allocation_policy 7 齖  std::allocator_traits<std::allocator<char32_t> >  ~[  std::nano  �  std::_Iterator_base0 % 鸤  std::initializer_list<char8_t> � 蛝  std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > M 覴  std::_Vector_val<std::_Simple_types<nvrhi::rt::PipelineHitGroupDesc> > 1 Y  std::_Char_traits<char16_t,unsigned short> $ �)  std::hash<nvrhi::BufferRange> ! 郳  std::char_traits<char16_t> � 鍇  std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  �  std::tuple<>  �  std::_Container_base12  	/  std::io_errc ) X  std::numeric_limits<unsigned char> � VM  std::_Compressed_pair<std::allocator<nvrhi::rt::OpacityMicromapUsageCount>,std::_Vector_val<std::_Simple_types<nvrhi::rt::OpacityMicromapUsageCount> >,1>  誠  std::true_type   d  std::numeric_limits<long> " 衆  std::initializer_list<char>  �  std::_Invoker_strategy � [�  std::allocator_traits<std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >  檁  std::_Stop_state $ �  std::_Default_allocate_traits 3 瞈  std::allocator_traits<std::allocator<char> > ! `  std::numeric_limits<short>  u   std::_Vbase . S  std::allocator<nvrhi::rt::GeometryDesc> # 醓  std::unique_lock<std::mutex> ; �  std::basic_string_view<char,std::char_traits<char> > C �  std::basic_string_view<char16_t,std::char_traits<char16_t> > < �)  std::_Array_const_iterator<nvrhi::BindingSetItem,128> 6 繫  std::_String_val<std::_Simple_types<char16_t> > = 軲  std::_String_val<std::_Simple_types<char16_t> >::_Bxty . }P  std::hash<enum nvrhi::TextureDimension> ! �  std::_Shared_ptr_spin_lock  �  std::bad_alloc  ^.  std::underflow_error B 燩  std::_Conditionally_enabled_hash<enum nvrhi::BlendFactor,1>   騙  std::atomic<unsigned int>  .  std::out_of_range # f  std::numeric_limits<__int64> " 骵  std::initializer_list<bool>  h  std::memory_order ! (b  std::recursive_timed_mutex  賉  std::ratio<3600,1> # �  std::_Atomic_storage<long,4> # *  std::hash<nvrhi::BlendState>  �  std::atomic_flag f N>  std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>  a/  std::system_error < {X  std::_Default_allocator_traits<std::allocator<char> > W 軾  std::_Default_allocator_traits<std::allocator<nvrhi::rt::PipelineHitGroupDesc> >    std::ratio<1,1>   D[  std::forward_iterator_tag  ..  std::runtime_error �倈  std::_Compressed_pair<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >,std::_Vector_val<std::_Simple_types<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >,1>   
  std::bad_array_new_length E S  std::_Vector_val<std::_Simple_types<nvrhi::rt::GeometryDesc> > / 綾  std::_Atomic_storage<unsigned __int64,8> > C�  std::_Ref_count_obj2<donut::engine::FramebufferFactory> v �  std::initializer_list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >  �  std::_Container_proxy  鑕  std::allocator<bool> ]蝩  std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> > B 硽  std::initializer_list<nvrhi::RefCountPtr<nvrhi::ITexture> >  ^  std::nested_exception  �  std::_Distance_unknown ( j  std::numeric_limits<unsigned int>  �>  std::allocator<char8_t> @ 篜  std::_Conditionally_enabled_hash<enum nvrhi::ColorMask,1> ) N&  std::array<nvrhi::IBindingSet *,5> K �  std::basic_string<char,std::char_traits<char>,std::allocator<char> > `   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_strategy _   std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Allocation_policy     std::streamoff  �  std::atomic<long> � 藎  std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > & 沎  std::initializer_list<char32_t> d M(  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> > z (  std::vector<nvrhi::rt::PipelineHitGroupDesc,std::allocator<nvrhi::rt::PipelineHitGroupDesc> >::_Reallocation_policy & 抂  std::initializer_list<char16_t> % 圼  std::initializer_list<wchar_t> 4 [  std::_String_constructor_rvalue_allocator_tag C lP  std::_Conditionally_enabled_hash<enum nvrhi::ResourceType,1>   �  std::hash<std::nullptr_t> ' w  std::numeric_limits<long double>  /  std::errc ; 唟  std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> >  v.  std::range_error  z  std::bad_typeid > 璓  std::_Conditionally_enabled_hash<enum nvrhi::BlendOp,1>  颽  std::_UInt_is_zero  �  std::_Compare_eq  ~[  std::ratio<1,1000000000>  �?  std::allocator<char16_t> . 糱  std::vector<bool,std::allocator<bool> > ` �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> > v �'  std::vector<nvrhi::rt::PipelineShaderDesc,std::allocator<nvrhi::rt::PipelineShaderDesc> >::_Reallocation_policy  |[  std::ratio<1,1000>  z[  std::ratio<1,10000000> ; HM  std::allocator<nvrhi::rt::OpacityMicromapUsageCount> % �/  std::_Iostream_error_category2 * 糛  std::_String_constructor_concat_tag 4 c`  std::atomic<std::_Stop_callback_base const *>  �8  std::allocator<char>    std::nullptr_t . 鏧  std::_Char_traits<char8_t,unsigned int> & H[  std::random_access_iterator_tag ; RP  std::_Conditionally_enabled_hash<unsigned __int64,1>  x  std::bad_weak_ptr ) l  std::numeric_limits<unsigned long> 5 鮔  std::_Narrow_char_traits<char8_t,unsigned int>   
  std::_Atomic_padded<long> # 鄀  std::allocator<unsigned int> = 匬  std::_Conditionally_enabled_hash<enum nvrhi::Format,1>  b  std::wstring 5 譪  std::_Atomic_integral_facade<unsigned __int64> ' V  std::numeric_limits<signed char>  �-  std::domain_error  D>  std::allocator<wchar_t> L v[  std::allocator_traits<std::allocator<nvrhi::rt::PipelineShaderDesc> >  �  std::_Literal_zero $ cP  std::hash<nvrhi::IResource *> 1 	*  std::hash<nvrhi::BlendState::RenderTarget>   T  std::numeric_limits<char> D 譭  std::_Default_allocator_traits<std::allocator<unsigned int> > 9 D-  std::chrono::duration<__int64,std::ratio<1,1000> >  �,  std::chrono::nanoseconds ? �,  std::chrono::duration<__int64,std::ratio<1,1000000000> > , fX  std::chrono::duration_values<__int64>  v,  std::chrono::seconds 3 -  std::chrono::duration<int,std::ratio<60,1> > 6 v,  std::chrono::duration<__int64,std::ratio<1,1> > s �,  std::chrono::time_point<std::chrono::steady_clock,std::chrono::duration<__int64,std::ratio<1,1000000000> > >   q[  std::chrono::steady_clock   X  std::chrono::system_clock 6 *-  std::chrono::duration<double,std::ratio<60,1> > ; �-  std::chrono::duration<double,std::ratio<1,1000000> > > �-  std::chrono::duration<double,std::ratio<1,1000000000> > = \,  std::chrono::duration<__int64,std::ratio<1,10000000> > q L,  std::chrono::time_point<std::chrono::system_clock,std::chrono::duration<__int64,std::ratio<1,10000000> > > 5 �,  std::chrono::duration<int,std::ratio<3600,1> > 8 Z-  std::chrono::duration<double,std::ratio<1,1000> > < q-  std::chrono::duration<__int64,std::ratio<1,1000000> > 5 �,  std::chrono::duration<double,std::ratio<1,1> > 8 �,  std::chrono::duration<double,std::ratio<3600,1> > \ ]�  std::_Uninitialized_backout_al<std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >  R[  std::char_traits<char>  �.  std::error_category ) �.  std::error_category::_Addr_storage ! �/  std::_System_error_message  �  std::_Unused_parameter h 螹  std::_Compressed_pair<std::allocator<char16_t>,std::_String_val<std::_Simple_types<char16_t> >,1> A �  std::basic_string_view<wchar_t,std::char_traits<wchar_t> > , 確  std::_Atomic_integral<unsigned int,4> �   std::_List_unchecked_const_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >,std::_Iterator_base0> = _P  std::_Conditionally_enabled_hash<nvrhi::IResource *,1> Q 1~  std::_Compressed_pair<std::equal_to<nvrhi::TextureSubresourceSet>,float,1> @ wY  std::_Default_allocator_traits<std::allocator<char16_t> > ! �+  std::ranges::_Set_union_fn # +  std::ranges::_Unique_copy_fn ' �*  std::ranges::_Replace_copy_if_fn & �*  std::ranges::_Is_partitioned_fn * "  std::ranges::_Uninitialized_fill_fn ( S+  std::ranges::_Stable_partition_fn 7 a  std::ranges::_Uninitialized_value_construct_n_fn ! ,  std::ranges::_Is_sorted_fn # R  std::ranges::_Find_if_not_fn  ,  std::ranges::_Clamp_fn % v+  std::ranges::_Is_heap_until_fn ' �*  std::ranges::_Partition_point_fn ( 	,  std::ranges::_Prev_permutation_fn  W*  std::ranges::_All_of_fn ,   std::ranges::_Uninitialized_move_n_fn ! E  std::ranges::_Destroy_n_fn " �*  std::ranges::_Generate_n_fn / %,  std::ranges::_Lexicographical_compare_fn  E+  std::ranges::_Shuffle_fn ! h+  std::ranges::_Make_heap_fn ' ,  std::ranges::_Is_sorted_until_fn   B*  std::ranges::_Count_if_fn  "+  std::ranges::_Reverse_fn $ 0  std::ranges::_Construct_at_fn  �+  std::ranges::_Minmax_fn & �+  std::ranges::_Minmax_element_fn  �+  std::ranges::_Sort_fn # 7+  std::ranges::_Rotate_copy_fn # +  std::ranges::_Remove_copy_fn # �+  std::ranges::_Nth_element_fn   �*  std::ranges::_Search_n_fn   �*  std::ranges::_Find_end_fn  �*  std::ranges::_Remove_fn " 7  std::ranges::_Destroy_at_fn  D  std::ranges::_Find_fn & 
+  std::ranges::_Remove_copy_if_fn ! �  std::ranges::subrange_kind  I*  std::ranges::_Equal_fn ! }+  std::ranges::_Sort_heap_fn    std::ranges::_Next_fn ! �*  std::ranges::_Remove_if_fn   -*  std::ranges::_For_each_fn   a+  std::ranges::_Pop_heap_fn & �+  std::ranges::_Set_difference_fn ) �+  std::ranges::_Partial_sort_copy_fn  o+  std::ranges::_Is_heap_fn ! Z+  std::ranges::_Push_heap_fn ! L+  std::ranges::_Partition_fn % Y  std::ranges::_Adjacent_find_fn $ �+  std::ranges::_Partial_sort_fn # g  std::ranges::_Max_element_fn  K  std::ranges::_Find_if_fn % �+  std::ranges::_Binary_search_fn " 4*  std::ranges::_For_each_n_fn & �*  std::ranges::_Partition_copy_fn 7 L  std::ranges::_Uninitialized_default_construct_fn  l*  std::ranges::_Copy_n_fn *   std::ranges::_Uninitialized_move_fn $ )+  std::ranges::_Reverse_copy_fn # �+  std::ranges::_Equal_range_fn  �*  std::ranges::_Move_fn $ �*  std::ranges::_Replace_copy_fn   �*  std::ranges::_Generate_fn , 
  std::ranges::_Uninitialized_copy_n_fn   =  std::ranges::_Mismatch_fn   �+  std::ranges::_Includes_fn  ;*  std::ranges::_Count_fn  >+  std::ranges::_Sample_fn  �+  std::ranges::_Merge_fn # �+  std::ranges::_Upper_bound_fn %   std::ranges::_Not_quite_object 5   std::ranges::_Not_quite_object::_Construct_tag % �*  std::ranges::_Move_backward_fn  |  std::ranges::_Min_fn  z*  std::ranges::_Copy_if_fn " �*  std::ranges::_Replace_if_fn & P*  std::ranges::_Is_permutation_fn  /  std::ranges::_Copy_fn  �*  std::ranges::_Replace_fn *   std::ranges::_Uninitialized_copy_fn  >  std::ranges::_Destroy_fn , )  std::ranges::_Uninitialized_fill_n_fn  �*  std::ranges::_Fill_fn ( �+  std::ranges::_Set_intersection_fn % �+  std::ranges::_Inplace_merge_fn 0 �+  std::ranges::_Set_symmetric_difference_fn  (  std::ranges::dangling % s*  std::ranges::_Copy_backward_fn  `  std::ranges::_Search_fn  #  std::ranges::_Prev_fn # �*  std::ranges::_Swap_ranges_fn     std::ranges::_Distance_fn # u  std::ranges::_Min_element_fn ( ,  std::ranges::_Next_permutation_fn # �+  std::ranges::_Lower_bound_fn  +  std::ranges::_Unique_fn  e*  std::ranges::_None_of_fn    std::ranges::_Advance_fn 5 Z  std::ranges::_Uninitialized_value_construct_fn  ^*  std::ranges::_Any_of_fn % �*  std::ranges::_Find_first_of_fn ! �*  std::ranges::_Transform_fn # �+  std::ranges::_Stable_sort_fn  0+  std::ranges::_Rotate_fn  6  std::ranges::_Fill_n_fn  n  std::ranges::_Max_fn 9 S  std::ranges::_Uninitialized_default_construct_n_fn 0   std::_Char_traits<wchar_t,unsigned short> ' y   std::array<enum nvrhi::Format,8> � w|  std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > \ V  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::OpacityMicromapUsageCount> > 5 `>  std::_String_val<std::_Simple_types<wchar_t> > < k>  std::_String_val<std::_Simple_types<wchar_t> >::_Bxty ' �)  std::hash<nvrhi::BindingSetItem> " 甔  std::_WChar_traits<wchar_t> # �/  std::_Generic_error_category  "J  std::streampos  B[  std::input_iterator_tag 0 芻  std::_Atomic_integral<unsigned __int64,8> ^ �  std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > , 塪  std::_Atomic_padded<unsigned __int64> ' 綪  std::hash<enum nvrhi::ColorMask> 5 
`  std::_Locked_pointer<std::_Stop_callback_base>  錎  std::bad_function_call O qW  std::_Uninitialized_backout_al<std::allocator<nvrhi::rt::GeometryDesc> > # J[  std::contiguous_iterator_tag v 鮸  std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> ; 賓  std::allocator_traits<std::allocator<unsigned int> >  JP  std::hash<unsigned int> 7 =[  std::allocator_traits<std::allocator<char16_t> > " �  std::_Asan_aligned_pointers �   std::_Default_allocator_traits<std::allocator<std::_List_node<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,void *> > > m 剉  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > � Sv  std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Reallocation_policy F ;[  std::allocator_traits<std::allocator<nvrhi::rt::GeometryDesc> >  �  std::partial_ordering . <#  std::array<nvrhi::BindingLayoutItem,16> $ 塒  std::hash<enum nvrhi::Format>  b  std::numeric_limits<int> 2 昇  std::_String_val<std::_Simple_types<char> > 9 碞  std::_String_val<std::_Simple_types<char> >::_Bxty  }  std::bad_variant_access # �(  nvrhi::CommandListParameters # �  nvrhi::TextureSubresourceSet  �#  nvrhi::BindingSetDesc  8[  nvrhi::SubresourceTiling $ �(  nvrhi::GraphicsPipelineHandle  �"  nvrhi::ResourceType  u   nvrhi::ObjectType ) �$  nvrhi::RefCountPtr<nvrhi::IShader>  �$  nvrhi::InputLayoutHandle   �%  nvrhi::IndexBufferBinding   4[  nvrhi::MemoryRequirements  #   nvrhi::GpuVirtualAddress 8 Y#  nvrhi::static_vector<nvrhi::BindingLayoutItem,16> " �"  nvrhi::VulkanBindingOffsets  s  nvrhi::ResourceStates . d�  nvrhi::RefCountPtr<nvrhi::IFramebuffer>  &  nvrhi::GraphicsState / W  nvrhi::static_vector<nvrhi::Viewport,16> ! v  nvrhi::SharedResourceFlags  ^  nvrhi::ShaderDesc  �&  nvrhi::IComputePipeline : �&  nvrhi::static_vector<nvrhi::VertexBufferBinding,16>  �  nvrhi::FramebufferDesc  G  nvrhi::Rect  #  nvrhi::BindingSetItem $ #  nvrhi::BindingLayoutItemArray ) 艷  nvrhi::RefCountPtr<nvrhi::IDevice>  u   nvrhi::MipLevel  &  nvrhi::IGraphicsPipeline ! �(  nvrhi::ShaderLibraryHandle  �   nvrhi::FramebufferInfoEx  �$  nvrhi::IShader  �  nvrhi::TextureDesc  �#  nvrhi::ISampler ! �%  nvrhi::VertexBufferBinding ! �%  nvrhi::ComputePipelineDesc  �  nvrhi::SamplerDesc  �  nvrhi::TextureSlice # �(  nvrhi::MeshletPipelineHandle  {  nvrhi::Format  �&  nvrhi::DrawArguments  '  nvrhi::MeshletState  P!  nvrhi::IBuffer    nvrhi::Color + �  nvrhi::static_vector<nvrhi::Rect,16>    nvrhi::ViewportState 6 
$  nvrhi::static_vector<nvrhi::BindingSetItem,128>  �"  nvrhi::BindingLayoutDesc   �  nvrhi::SamplerAddressMode  ()  nvrhi::IDevice ! �%  nvrhi::BindingLayoutHandle ! 
$  nvrhi::BindingSetItemArray . 鮞  nvrhi::RefCountPtr<nvrhi::ICommandList>  艷  nvrhi::DeviceHandle   .[  nvrhi::TiledTextureRegion  '  nvrhi::IMeshletPipeline  �  nvrhi::RasterState & N$  nvrhi::VariableRateShadingState  *[  nvrhi::IStagingTexture ! 蒪  nvrhi::utils::ScopedMarker $ =b  nvrhi::utils::BitSetAllocator . �$  nvrhi::RefCountPtr<nvrhi::IInputLayout>    nvrhi::BufferRange ! �  nvrhi::VertexAttributeDesc " v  nvrhi::ShaderSpecialization 8 l  nvrhi::ShaderSpecialization::<unnamed-type-value>  }  nvrhi::TextureDimension 0 �%  nvrhi::RefCountPtr<nvrhi::IBindingLayout> ' �&  nvrhi::DispatchIndirectArguments  �(  nvrhi::SamplerHandle * �&  nvrhi::DrawIndexedIndirectArguments # �(  nvrhi::DescriptorTableHandle  �(  nvrhi::TimerQueryHandle   #  nvrhi::BindlessLayoutDesc  <  nvrhi::CustomSemantic " (  nvrhi::CustomSemantic::Type ! �%  nvrhi::MeshletPipelineDesc 9 #  nvrhi::static_vector<nvrhi::BindingLayoutItem,128>  麣  nvrhi::FormatInfo  a�  nvrhi::HeapHandle # �(  nvrhi::ComputePipelineHandle  ![  nvrhi::PackedMipDesc  �  nvrhi::RasterFillMode  u   nvrhi::ArraySlice  M  nvrhi::IResource  x&  nvrhi::IBindingSet  [  nvrhi::TileShape ; 7   nvrhi::static_vector<nvrhi::FramebufferAttachment,8> * �(  nvrhi::SamplerFeedbackTextureHandle # $  nvrhi::SinglePassStereoState % �#  nvrhi::ISamplerFeedbackTexture  x(  nvrhi::CommandQueue  �  nvrhi::BlendFactor  �(  nvrhi::EventQueryHandle  �"  nvrhi::BindingLayoutItem  d�  nvrhi::FramebufferHandle 1 �   nvrhi::static_vector<enum nvrhi::Format,8>  賧  nvrhi::BufferHandle  �%  nvrhi::IBindingLayout  N   nvrhi::FramebufferInfo  lH  nvrhi::TextureHandle  [  nvrhi::IEventQuery  �  nvrhi::DepthStencilState . �  nvrhi::DepthStencilState::StencilOpDesc  [  nvrhi::IMessageCallback  �  nvrhi::BlendState & �  nvrhi::BlendState::RenderTarget 3 o&  nvrhi::static_vector<nvrhi::IBindingSet *,5> " �$  nvrhi::GraphicsPipelineDesc H _%  nvrhi::static_vector<nvrhi::RefCountPtr<nvrhi::IBindingLayout>,5> ) 賧  nvrhi::RefCountPtr<nvrhi::IBuffer> $ 齔  nvrhi::TiledTextureCoordinate  鵝  nvrhi::IHeap # �  nvrhi::FramebufferAttachment  o&  nvrhi::BindingSetVector  �(  nvrhi::BindingSetHandle ( 騔  nvrhi::SamplerFeedbackTextureDesc ! _%  nvrhi::BindingLayoutVector ' a�  nvrhi::RefCountPtr<nvrhi::IHeap> " �(  nvrhi::StagingTextureHandle  4  nvrhi::Object  �$  nvrhi::IInputLayout  �  nvrhi::RasterCullMode ' �!  nvrhi::rt::AccelStructBuildFlags  �!  nvrhi::rt::InstanceFlags " s!  nvrhi::rt::IOpacityMicromap  �!  nvrhi::rt::InstanceDesc  S!  nvrhi::rt::GeometryFlags ! �!  nvrhi::rt::GeometrySpheres # 繸  nvrhi::rt::ShaderTableHandle + y!  nvrhi::rt::OpacityMicromapUsageCount $ 5'  nvrhi::rt::PipelineShaderDesc ! "  nvrhi::rt::AccelStructDesc   �(  nvrhi::rt::PipelineHandle ! �!  nvrhi::rt::AffineTransform & O'  nvrhi::rt::PipelineHitGroupDesc  �!  nvrhi::rt::GeometryLss 3 鑊  nvrhi::rt::cluster::OperationBlasBuildParams . 鋃  nvrhi::rt::cluster::OperationMoveParams ( 轟  nvrhi::rt::cluster::OperationDesc 3 赯  nvrhi::rt::cluster::OperationClasBuildParams , 諾  nvrhi::rt::cluster::OperationSizeInfo * 襔  nvrhi::rt::cluster::OperationParams  �!  nvrhi::rt::GeometryType ' �(  nvrhi::rt::OpacityMicromapHandle  �!  nvrhi::rt::GeometryDesc - �!  nvrhi::rt::GeometryDesc::GeomTypeUnion % �   nvrhi::rt::OpacityMicromapDesc # d!  nvrhi::rt::GeometryTriangles  �#  nvrhi::rt::IAccelStruct # �(  nvrhi::rt::AccelStructHandle  h(  nvrhi::rt::IShaderTable ' r(  nvrhi::rt::DispatchRaysArguments  X(  nvrhi::rt::State  �!  nvrhi::rt::GeometryAABBs  k'  nvrhi::rt::PipelineDesc  荶  nvrhi::rt::IPipeline  �  nvrhi::CpuAccessMode  鮞  nvrhi::CommandListHandle # �&  nvrhi::DrawIndirectArguments ! 糧  nvrhi::TextureTilesMapping  l  nvrhi::HeapDesc  �)  nvrhi::ICommandList    nvrhi::BufferDesc  碯  nvrhi::IDescriptorTable * lH  nvrhi::RefCountPtr<nvrhi::ITexture>  �&  nvrhi::ComputeState  &  nvrhi::IFramebuffer  /  nvrhi::Viewport  2$  nvrhi::RenderState  �$  nvrhi::ShaderHandle  �  nvrhi::ITexture  猌  nvrhi::ITimerQuery   �  __RTTIBaseClassDescriptor 
    _off_t    stat  t   int32_t  Z  timespec 
 !   _ino_t 
 沘  _Cnd_t  !   uint16_t ( Cu  donut::engine::FramebufferFactory  WI  donut::math::float4x4 " qE  donut::math::vector<bool,4>  錏  donut::math::float3  揈  donut::math::affine3  EG  donut::math::float2 # 錏  donut::math::vector<float,3> * 謾  donut::math::vector<unsigned int,3>  u   donut::math::uint  /F  donut::math::plane # F  donut::math::vector<float,4>  ZF  donut::math::frustum $ =F  donut::math::frustum::Corners # ;F  donut::math::frustum::Planes % 慫  donut::math::matrix<float,3,4>  (G  donut::math::uint2  {  donut::math::uint4  謾  donut::math::uint3  F  donut::math::float4 % WI  donut::math::matrix<float,4,4> # 揈  donut::math::affine<float,3> " *E  donut::math::vector<bool,2>  GF  donut::math::box3 % 綞  donut::math::matrix<float,3,3> " KE  donut::math::vector<bool,3> # EG  donut::math::vector<float,2> * (G  donut::math::vector<unsigned int,2> * {  donut::math::vector<unsigned int,4> * 臉  donut::render::GBufferRenderTargets M �  __vcrt_assert_va_start_is_not_reference<__crt_locale_pointers * const>    _Mbstatet  �  _locale_t B �  __vcrt_assert_va_start_is_not_reference<char const * const> ; �  __vcrt_va_list_is_reference<__crt_locale_pointers *>     intmax_t    terminate_handler  �  _s__RTTIBaseClassArray 
 Y  ldiv_t  顧  StablePlane  u   _Thrd_id_t - �  $_s__RTTIBaseClassArray$_extraBytes_24  �  _CatchableTypeArray     ptrdiff_t  
  _stat64i32  �  _PMD      uint8_t  ,  type_info ' �  _s__RTTIClassHierarchyDescriptor  t   errno_t  \  _lldiv_t  C  __std_type_info_data & �  $_TypeDescriptor$_extraBytes_27  G  _s__ThrowInfo  �  __RTTIBaseClassArray  �  __crt_locale_data_public - �  $_s__CatchableTypeArray$_extraBytes_24  Ya  _Mtx_internal_imp_t & 4Z  $_TypeDescriptor$_extraBytes_25 % �  __RTTIClassHierarchyDescriptor 0 �  __vcrt_va_list_is_reference<char const *>     __time64_t  鏔  PolymorphicLightInfoEx  �  FILE 
 Ma  _Mtx_t 3 �  __vcrt_va_list_is_reference<wchar_t const *>    mbstate_t  ?  _PMFN  #   uintptr_t  �  _s__CatchableTypeArray  騀  PolymorphicLightInfoFull  靈  _Thrd_t - �  $_s__RTTIBaseClassArray$_extraBytes_16 - /Z  $_s__RTTIBaseClassArray$_extraBytes_32 
 #   size_t 
    time_t  �  __std_exception_data 
 u   _dev_t  \  lldiv_t  Y  _ldiv_t  ;�  RenderTargets  [  _timespec64  u   uint32_t 
 �  _iobuf  �  __crt_locale_pointers   �   �      觟/H泆r序k�;j忌￥^祧蚖S傉F�苡  ?    鉓�鴻�0�<餥.鯚捓K穮壯%!M鳷鯠  �    仸�=堈衰鰭Q	諕呂炲c�8G鎉庶h说  �    +4[(広
倬禼�溞K^洞齹誇*f�5  !    �"鈖@M�骑潆譢aMy1绾鎕瑞lg  _   圽Q&4Y3巷B:C �_%aP縀懮��,褻G  �   k�8.s��鉁�-[粽I*1O鲠-8H� U  �   l籴靈LN~噾2u�< 嵓9z0iv&jザ  +   チ畴�
�&u?�#寷K�資 +限^塌>�j  _   �$晑�~2]�/
S蟦a� �
}A珈弿V緈  �   穫農�.伆l'h��37x,��
fO��  �   ]V驀丱随^梷禂�5雃e飚职up�$�胀e     �(M↙溋�
q�2,緀!蝺屦碄F觡  h   聭権諵K^n腧�'瑧P
W悒黨LZ刴J  �   G�膢刉^O郀�/耦��萁n!鮋W VS  �   x�*緅bC?J1VT%E.S膲饘
骗] 湌釘�  .   o�椨�4梠"愜��
}z�$ )鰭荅珽X  v   穖r�嶣�)�ヌ_漮亰�ww赼6�V�&苺  �   ':掄NB�<蠀ρ�赬葕孭�
�膤1�>  �   矎D5~�董 斦睇! 襰�zㄝ柶董苶綽  &   vw*X髽B糣I鄧�8絀鈬佇tN輕�嵹  _   黸|�
C�%|�,臍稇l裹垓芻喭,vg�  �   猯�諽!~�:gn菾�]騈购����'  �   �	玮媔=zY沚�c簐P`尚足,\�>:O     	{Z�范�F�m猉	痹缠!囃ZtK�T�  Z   渦]k鸦
\4曫r裙NJhHTu6'餍\燪  �   �0�*е彗9釗獳+U叅[4椪 P"��  �   �=蔑藏鄌�
艼�(YWg懀猊	*)  
   娤嗲�:MZ�%?qg�+r嶊嗻2壩檎兡[[n  T   偨蘹将>*A�*8` 3鋗 q斒饟&魗)�  �   `k�"�1�^�`�d�.	*貎e挖芺
脑�  �   喇Zl�3Y\膂hF⒘u庉$cKIP~懱��     逤��瘲� <嬘
嫨X�=杯;鸶Y_謺S(  M   檒Gq$�#嗲RR�錨账��K諻刮g�   �   ┠#憵u1/槳7ⅩO�茤粇虛6"蕿�  �   樁*披B憱祯敛鍭�7� T癀n烬
雚臁  $	   嚵dD芆蠓Re鄄圿J��*煺蜟7繸��  j	   c�#�'�縌殹龇D兺f�$x�;]糺z�  �	   跃� 宍W=往�抶V]扦RD鲭R嵝\,n  �	   o藾錚\F鄦泭|嚎醖b&惰�_槮  0
   鍱�uAV� �6ib4�絪�)吹q滶Ix�!�  m
   yk"&�棎墑�T-亴鏏愐煋搂脠+]�  �
   �'稌� 变邯D)\欅)	@'1:A:熾/�      蔙W憙�6�
嶶Y踈uw[揧?萍EYU`  R   掟橎 ^:盶坔nu8ａ�?鵵)諦讉夎k^  �   �6匚j#�6}hU蜢漡麐"l<�1F陦!>_层�  �   副謐�斦=犻媨铩0
龉�3曃譹5D   �   鏀q�N�&}
;霂�#�0ncP抝  +   寔�殁鴀齓叓�!E�;6陱FE/�qHR0;  k   唴�!}j磗+,]
|�韀:Mり∽|YJ�	�  �   ;o屮G蕞鍐剑辺a岿;q琂謇:謇  �   $^IXV嫓進OI蔁
�;T6T@佮m琦�  '
   J73鏋蠋漱Y 欒jH懹-3狝钞!qf漽慌  s
   蜅駠x馘Qf^��=夸餕V�G窄憫尢25  �
   吖肭�'�
q�m輩�?�1遅;ABK臂欻E{胱  �
   交�,�;+愱`�3p炛秓ee td�	^,  ?   _臒~I��歌�0蘏嘺QU5<蝪祰S  �   馤m湀鎡麹a<2舙T�铂�*b鑎A_9�  �   攼癭幥咉钌鐊k一
洇勪�*w橧P萈�     ?~铤襭拠I葰* 斕皿�)必翖鑫t冴�(L  _   鹴y�	宯N卮洗袾uG6E灊搠d�  �   Ⅴc鞑猗U洏沂藿湙馯鲀�飵T擱}G  �   _O縋[HU-銌�鼪根�鲋薺篮�j��  @   [a$暇灧MZ�+:�xZ,s娿Nf筊韩沎  ~   蜞憚>�/�狌b替T蕚鎸46槹n�洜9  �   V� c鯐鄥杕me綻呥EG磷扂浝W)     窯u摌=#鞱�<>�#搖�篨歟3嵅洶�"7  q   芅嶚o藜t絭\�:醹鉐+裳梇s!蜓銰  �   A縏 �;面褡8歸�-構�壋馵�2�-R癕  �   �X�& 嗗�鹄-53腱mN�<杴媽1魫  D   桅棙�萑�3�<)-~浰-�?>撎�6=Y}  �   ..'Q[﨔$5�" "瀵軇�	?嚟鈣腐赎 ,�  �   �蚅漂 诬=�捷组\轣轡\J铵 p**uUz  ,   ま錗~缃+mBC�$�6C\[懦G)N憓Tf<  |   �嵪=�2}Qコk捑8噣酻:JY?�`  �   癛鰜M{|�)�A捫鋡"馼辣�"鶖繓P痗      f腹暘Tc�+#T3Z�4_��倂A谦葟氵6  ?   鏍娫荿鵲怔�3@H奶a�9羄�:馦`q�  �   �05;a1x隍闥婜蒯rC;�(宙‘乷敝�)  �   绉�~阶妃煔e�&酾%秷P*義蔳3C(笜  #   � 罟)M�:J榊?纸i�6R�CS�7膧俇  v   U�6ACF},
fSr),鵠Rw馃v肟"
p謆��  �   傊P棼r铞
w爉筫y;H+(皈LL��7縮  �    d蜯�:＠T邱�"猊`�?d�B�#G騋  5   溶�$椉�
悇� 騐`菚y�0O腖悘T  �   觑v�#je<d鼋^r
u��闑鯙珢�  �   �(�=傤`羙�$r┮{sq鯹駘� 4楝3硲  �   歚W%虴�[�,莶CKF�AZⅰq恶�4�     R泇XT鲙D頌|砫獚:|G膟C�劫觿�:捅  J   隄�:LwV�=G'V裲稩绛-}}辌t[吁Z  �   L�9[皫zS�6;厝�楿绷]!��t  �   �"睱建Bi圀対隤v��cB�'窘�n  +   语傽�3{;鯽婄犔b虗D~F�� 淏洅翄  l   斝�/5:澫酡Z瞮<箼�漻M洛OIl~  �   譫鰿3鳪v鐇�6瘻x侃�h�3&�  �   繃S,;fi@`騂廩k叉c.2狇x佚�  ,   j轲P[塵5m榤g摏癭 鋍1O骺�*�  u   荽揣�
O馳9A鯍y`lv顿X隠0烨軤�  �   }�2*攒�:�1�%侥�巳5≯-p商0n	�<1     蜖dY�8h 蝆榩<fSLt�猠+约+4軪鞦  @   �*o驑瓂a�(施眗9歐湬

�  �    z�漼嶜q镛�F苜�:壗Wア燤PEx�  �   �&�$禤會k呟u#�碟`Gy癥襲櫏  �    I嘛襨签.濟;剕��7啧�)煇9触�.  '   ,┭0甗�+天没2骟Bw蛁�%"艠E�  _   +FK茂c�G1灈�7ほ��F�鳺彷餃�  �   炕�y蔁瘛71們浂q|Z%P}4諤窑辛  �   瞔吠J澹墇:m﹚毩@4�)Cnf溃iI鈁馠Fx  
   鏫\�$@p菸喝�
蕴柮=!�$F�7IP閯�  ^   +YE擋%1r+套捑@鸋MT61' p廝 飨�  �   !m�#~6蠗4璟飜陷]�絨案翈T3骮�  �   zY{���睃R焤�0聃
扨-瘜}     N8x暠s?侎|�子n�匦饛蝉鼙-�E菲  X   蜅�萷l�/费�	廵崹
T,W�&連芿  �   v�%啧4壽/�.A腔$矜!洎\,Jr敎  �   5�\營	6}朖晧�-w氌rJ籠騳榈  #   �9俅�(� .]嬝暧烵啤�秾*~w�=[@�  e   D���0�郋鬔G5啚髡J竆)俻w��  �   -Z儒餻熒僵z3銌薳WV酱A啁]`0跷  �   豊+�丟uJo6粑'@棚荶v�g毩笨C  9   匐衏�$=�"�3�a旬SY�
乢�骣�  �   a^$JB"w�6.Y佭�奏�g褁|癑塚[3<  �   悯R痱v 瓩愿碀"禰J5�>xF痧      �
bH<j峪w�/&d[荨?躹耯=�  M    矨�陘�2{WV�y紥*f�u龘��  �    +椬恡�
	#G許�/G候Mc�蜀煟-  �    \恳谥:4ea�q俎勊�牦鈢燷zBSP  !   齝D屜u�偫[篔聤>橷�6酀嘧0稈  T!   J�(S�	皓&r悖,@椎�輮� ��*{�  �!   3耴蝂^u賏\╄艇傁�F兼弦 2跨  �!   a�傌�抣?�g]}拃洘銌刬H-髛&╟  "   F*Qy夊C鲐畎�&y偽叼�=�
a櫓�0m億  U"   双:Pj �>[�.ぷ�<齠cUt5'蠙砥  �"   �縁g唱�8
k�邊?熃�<茟蔠8�;gY��  �"   R冈悚3Y	�1P#��(勁灱�涰n跲
  '#   п bC ㄠ8惻 嬎"伭'<[币楖圐緔�  #   疾+凧�:��騙l捼;5c餙3帘�*n熖  �#   S仄�p�'�/2H��g'浗o$鏊^藵捯�  �#   Fp{�悗鉟壍Au4DV�`t9���&*I  $$   )鎋]5岽B鑯 �誽|寋獸辪牚  b$   翧WP漡e索b
奋w鷈楚n
�2呦蔮;#銹  �$   V8追i顚�^�k细�;>牧惺扴	�\s  �$   *u\{┞稦�3壅阱\繺ěk�6U�  %   z�0叐i�%`戉3猂|Ei韍訋�#Q@�  \%   黒O嗸 m 頖哄q褾]帍�$cO膺俐W�  �%   {Oy謐諤酣*攤偷q仌礪Mvk-悃�3  �%   g,狁}杯-^郯�檼fa蒣岈2V鉈m �  &&   �*M�现.凿萰閱寴诃缶鲍6�#�+�4  d&   M]S噴=泥G)w��!&鍌S硚YQD铢g�/  �&   �
萲繒鼽l8�$鱈$st|&h娠>肓"Z�  �&   d潣7熈[$袎o�懠I殑Iy厵唫嬎�  *'   ~`凱u設C�+ 崃腢�(r鐜q6�D覃u  i'   )鹠Kャ鄅w`� 〖I仆鹲V�淪�$@玕  �   d      �  �  B   �  �  H   �  �  Y   �  �  �   b  0  �  r  �  U   s  �  �   �  @  \   �  0  �  �  0  x  �  0  K        Q   �  �  �   �  �  �   �  �  �   J  P   B  R  P   �	  �  �    �  �  �   �  �  �   %  P   �  &  P   �  )  P   +
  0  P   �  3  �  �  >  P   �  ?  P   0   �  P   D
  �  P   �  �  P   O   �  �  �   �  �  �  A  �  �  c  �  �   e  0  �   �  �  �   �  �  �   �  P   �  �  P   �  �  �  �    P   s    P   �  �  P   )
  (  �  �   9   �  �   �   �  @   �'    )   �'  �  �   �'  �	  `  �'  X  j   �'  �  >  �'  �    �'  �  �   (  P   �  (  �	  �  (  �  4  (  �  u  (  p  �  (  �    (  �  �   (  �  �   /(  P   @
  :(  �  �  <(  �    =(  �    >(  �  �  A(  p  X  B(  p  "  C(  X  1   R(  p  %   U(  �  
  V(  �  �  X(  p  '  _(  �  �  n(  �  �  v(  �  >  x(  �    y(  p  C  z(  p  3  {(  �  �  �(  �  �  �(  �  �  �(  �  �  �(  �  @   �(  �  F  �(  �	  �  �(  p  a  �(  �  �  �(  �  �  �(  �  �  �(  �  �  �(  p  <  �(  �  R  �(  �	  �  )  �  9  
)  �  �  )  �  9  +)  �  �   ,)  �  �   *  @  [   
*  �    *  �  �   �*     �  �*  �    �*  �    �*  �    �*  �	  �  �*  �  ,   �*  0  �  �*  0  �  �*  0  d  �*  0  t  �*  P   9
  +  0  5  +  0  z  +  0  �
  +     �   +  0  :  +  P   �  +  0  �   +  0     #+  �  �   '+  �	  �  (+  �	  �  .+  �	  ]  5+  0  &  7+  0  9  ;+  �  G  <+  �  �  @+  �  /  B+  �  �  E+  �  �  F+  �  �  K+  �    P+  �  �  R+  �  �   �   �'   D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\string D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xcall_once.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_sanitizer_annotate_container.hpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\iosfwd D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhiHLSL.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xbit_ops.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wconio.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\nvrhi.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\containers.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\array D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xutility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_iter_core.hpp D:\RTXPT\External\Donut\include\donut\engine\FramebufferFactory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\utility D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\unordered_map D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\initializer_list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xhash D:\RTXPT\External\Donut\include\donut\core\math\frustum.h D:\RTXPT\External\Donut\include\donut\core\math\matrix.h D:\RTXPT\External\Donut\include\donut\core\math\vector.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstddef D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\list D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\algorithm D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xmemory D:\RTXPT\Rtxpt\Shaders\PathTracer\Utils\Utils.hlsli D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\new D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\exception C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\crtdefs.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\use_ansi.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\DI\ReSTIRDIParameters.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiParameters.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\RtxdiTypes.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\LightSampling\RISBufferSegmentParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xnode_handle.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstring.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\utils.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstring D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\mutex C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_chrono.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memory.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ratio D:\RTXPT\Rtxpt\RenderTargets.cpp D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xthreads.h D:\RTXPT\External\Donut\include\donut\core\math\affine.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtimec.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\ctime C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\time.h D:\RTXPT\External\Donut\include\donut\core\math\quat.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\stat.h D:\RTXPT\External\Rtxdi\Include\Rtxdi\ReGIR\ReGIRParameters.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\sys\types.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\intrin0.inl.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_search.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdlib C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\wchar.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vector C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_memcpy_s.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_bit_utils.hpp C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wdirect.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\errno.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xerrc.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\process.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\assert.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_startup.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xpolymorphic_allocator.h D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\misc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_string.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cassert C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wio.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\system_error C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_share.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\__msvc_system_error_abi.hpp D:\RTXPT\External\Donut\include\donut\core\math\box.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_exception.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\eh.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_terminate.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Config.h D:\RTXPT\Rtxpt\RenderTargets.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\compare D:\RTXPT\External\Donut\include\donut\core\math\math.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wtime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cerrno C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdexcept D:\RTXPT\External\Donut\include\donut\core\math\basics.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cctype C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\float.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\ctype.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\sal.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concurrencysal.h D:\RTXPT\Rtxpt\RTXDI\ShaderParameters.h D:\RTXPT\External\Donut\include\donut\render\GBuffer.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vadefs.h D:\RTXPT\External\Donut\include\donut\shaders\view_cb.h D:\RTXPT\External\Donut\include\donut\core\log.h D:\RTXPT\External\Donut\include\donut\core\math\color.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\functional C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wprocess.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xtr1common D:\RTXPT\External\Donut\include\donut\core\math\sphere.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cmath C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdlib.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals_core.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stop_token C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_malloc.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\yvals.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xkeycheck.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\crtdbg.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cfloat D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new_debug.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\climits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_new.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\limits.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Rendering\Materials\IBSDF.hlsli D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cwchar D:\RTXPT\Rtxpt\Shaders\Bindings\BindingDataTypes.hlsli D:\RTXPT\Rtxpt\Shaders\PathTracer\Rendering\Materials\Microfacet.hlsli D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdio D:\RTXPT\Rtxpt\Shaders\PathTracer\Rendering\Materials\LobeType.hlsli C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stdio.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wstdio.h D:\RTXPT\External\Donut\include\donut\shaders\sky_cb.h C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_stdio_config.h D:\RTXPT\Rtxpt\Shaders\PathTracer\StablePlanes.hlsli D:\RTXPT\Rtxpt\Shaders\PathTracer\PathTracerShared.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\LightingTypes.h D:\RTXPT\Rtxpt\Shaders\PathTracer\Lighting\PolymorphicLight.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\thread D:\RTXPT\External\Rtxdi\Include\Rtxdi\GI\ReSTIRGIParameters.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\memory D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\typeinfo D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\vcruntime_typeinfo.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\concepts D:\RTXPT\External\Donut\nvrhi\include\nvrhi\common\resource.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\atomic D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\tuple D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\xatomic_wait.h D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\type_traits D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\cstdint D:\1softwares\VS2022\VC\Tools\MSVC\14.38.33130\include\stdint.h    �       LY}  �<      =     
 蔋  �   蜨  �  
 騌  �   鯮  �  
 P�      T�     
    f ^>![SJ&E�?)j堉   D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\Rtxpt.dir\Release\vc143.pdb 篁裥砓>Y7?樰�=      �?                  �?                  �?    H冹(H岮'H;羦'H嬋�    H嬋H吚tH兝'H冟郒塇鳫兡(描    惕    �   �    /   �    5   �       �   �  k G            :      :   9         �std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>  >_   _Bytes  AJ        9  $  >_    _Block_size  AH       1 
   >_    _Ptr_container  AJ        
 >�    _Ptr  AH  %     	  M        r  
 Z      N Z   �  q   (                      H 
 h   r         $LN14  0   _  O_Bytes  O   �   h           :   �  
   \       �  �   �  �   �  �
   �  �   �  �   �  �%   �  �)   �  �.   �  �4   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 s  5   w  5  
 �  �    �  �   
 H塡$H塴$H塼$WAVAWH冹 I嬭H嬺L嬹H�9H婣H+荋柳L;纕BI嬓�    I�H呿劎   H+�D  H�H�H吷tH��P怘兠H冺u鉏塣轭   H媃L孄L+�I�I;飗lH;鹴<f怘�H9t!H呟t
H�H嬎�P怘�H�H吷tH��P怘兦H兤I媈H;鹵艻+飔 H+驢�H�H吷tH��P怘兠H冺u鉏塣雘N�<荋呿t9f怘�H9t!H呟t
H�H嬎�P怘�H�H吷tH��P怘兦H兤H冺u蒊媬I嬤L;�t3鯤�H吷t
H�3H��P怘兠H;遳錗墌H媆$@H媗$HH媡$PH兡 A_A^_�8   �       �   o
  � G            }     d  +        �std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Assign_counted_range<nvrhi::RefCountPtr<nvrhi::ITexture> const *> 
 >#v   this  AJ        !  AV  !     Z >M   _First  AK         7 ? � e  AL  �     	 . AK �     �   (  1 	 T  e  �  �  �   AL �     � 1 	 B  e o  >_   _Newsize  AP         < : � e * AP �     �   (  T  e  �  �  �   >_    _Oldcapacity  AH  (     �    : h e * AH �     �   %  Q  e  �  �  �   >_    _Oldsize  AW  }     |   r   AW �     �  o 
 >EH    _Mid  AM  �     
  AM �     � - 
 e o  >JH    _Newlast  AW  �     k  AW d      M        ;+  .	? >#    _Count  AN  k       AN P      �  >EH   _Dest  AI  ?       AI P      � � o  >]�   _Backout  CI     g       CI    P      �  M        K+  P M        P+  P M        R+  P M        �'  P M        �  W N N N N N N M        #+  �� M        �  �� M        �  ��
 >�    temp  AI  �     2  N N M        �  �� N M        �'  �� M        �  ��#	 N N N  M        ;+  .�� >#    _Count  AN  �     "  AN �       o  >]�   _Backout  CI     �       CI    �     �   # o  M        K+  �� M        P+  �� M        R+  �� M        �'  �� M        �  �� N N N N N N M        v(  �>	 >EH   _First  AI  >    &  AI d      >JH   _Last  AM  ;    )  AM d      M        �(  丒 M        )  丒 M        �  丒 M        �  丒CE
 >�    temp  AJ  H      AJ E    8    N N N N N! M        <+   �� >M   _First  AL       '- + r e  AL �     � 1 	 B  e k  >#    _Count  AN       I5 & � (  AN P      � � o  >EH   _Dest  AM  $     l e  AM �     � - 
 e o  M        #+  �  M        �  � M        �  �
 >�    temp  AI      4  AI      >  4  N N M        �  � N M        �'  � M        �  �#	 N N N N
 Z    +                        0@ b h   �  t  �  �  �  �  �'  9(  v(  �(  �(  )  )  #+  :+  ;+  <+  C+  D+  E+  K+  P+  R+   @   #v  Othis  H   M  O_First  P   _  O_Newsize  9_       E   9�       E   9�       E   9�       E   9      E   9%      E   9S      E   O �   �           }  �	     �       c �!   o �/   p �4   q �<   s �H   q �P   s �m   � �q   y �v   | ��   } ��   � ��   � ��   � ��   � ��   � ��   � ��   � ��   � �7  � �`  � �,   �    0   �   
 �   �    �   �   
   �    	  �   
 &  �    *  �   
 >  �    B  �   
 N  �    R  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 $  �    (  �   
 c  �    g  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 +  �    /  �   
 ;  �    ?  �   
 _  �    c  �   
 o  �    s  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 U  �    Y  �   
 e  �    i  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 ?  �    C  �   
 O  �    S  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 0  �    4  �   
 D  �    H  �   
 �  �    �  �   
 �  �    �  �   
 
  �    
  �   
 
  �    
  �   
 +
  �    /
  �   
 ;
  �    ?
  �   
 K
  �    O
  �   
 [
  �    _
  �   
 k
  �    o
  �   
 �
  �    �
  �   
 H;蕋fff�     I� H�H兞H;蕌衩   �   �  �G                       �(        �std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > >  >r|   _First  AJ          AJ       
   >r|   _Last  AK          
 >噟   _Val  AP           >鑯   _Backout  CJ            CJ          
   M        �(    N M        �(   N                        H " h   �(  �(  �(  �(  �(  �(  )      r|  O_First     r|  O_Last     噟  O_Val  O  �   H               �     <       � �    � �   � �   � �   � �   � �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 (  �    ,  �   
 <  �    @  �   
   �      �   
 H塡$H塴$ H塋$VWAVH冹 H嬞H�    H�H塓H呉t
H�H嬍�P怘峴H塼$HE3鯠�6L塿L塿A峃(�    H� H堾H塅L塿L塿 L塿(H荈0   H荈8   �  �?H媙A嬑A嬈H柳H凐sv箑   �    H孁H婲H婩(H+罤柳H吚t.H��    H侜   rH兟'L婣鳬+菻岮鳫凐wnI嬋�    H墌H崌�   H塅 H塅(H;鴗$怘�/H兦H;鴘綦H兞H灵H吷t3�H嬇驢獿塻PL塻XL塻`L塻hL塻pH嬅H媆$PH媗$XH兡 A^_^描    �   �   S   �    �   �    �   �    N  �       �   {  [ G            S     S  �*        �donut::engine::FramebufferFactory::FramebufferFactory 
 >2u   this  AI       9%  AJ          D@    >))   device  AK        6  AK 7        M        �  �3 N M        �  �/ N M        �'  �# M        n(  �# M        �(  �# N N N M        �'  �緻#��M
 >評   this  AL  ;       BH   @     ! M        (  @.H{#|M: M        <(  ��j&M/E.$'$,? >_   _Oldsize  AH  �     �  l  AH #      C       �       >r|    _Newend  AH  �       AH #      >_    _Oldcapacity  AH  �     ,    AH �       >r|    _Newvec  AM  �     � [ M  AM      K    M        U(  
�� N M        =(  �� N M        V(  
�� M        (  
�� M        r  
��
 Z      N N N M        �(  ��# >鑯   _Backout  CM           CM         K    M        �(  �� N M        �(  �  N N M        >(  .���� M        c  ��)k
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     � & f % M        s  ��d#
n
 Z   �   >_    _Ptr_container  AP  �     �  k  AP �       >_    _Back_shift  AJ  �     � 9 k  AJ �     k ,   N N N M        �(  .� N N M        A(  �� M        R(  �� N N M        x(  b M        �(  b M        �(  b N N N M        B(  F M        X(  N)# >Lu    _Newhead  AH  W     7  M        _(  	N M        (  	N M        r  	N
 Z      N N N N M        �(  F M        �(  F N N N M        C(  @ N N N M        �  $ M        �  (	 N N                      0@ � h:   �  r  s  v  w  x  y  �  �  c  �  (  9   �   �'  �'  (  (  (  ;(  <(  =(  >(  ?(  @(  A(  B(  C(  O(  R(  U(  V(  X(  _(  n(  w(  x(  {(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  �(  )         $LN174  @   2u  Othis  H   ))  Odevice  93       E   O �               S              (  ��   �   j F                                �`donut::engine::FramebufferFactory::FramebufferFactory'::`1'::dtor$0 
 >2u   this  EN  @                                  �  O   �   �   j F                                �`donut::engine::FramebufferFactory::FramebufferFactory'::`1'::dtor$7 
 >2u   this  EN  @                                  �  O   �   �   j F                                �`donut::engine::FramebufferFactory::FramebufferFactory'::`1'::dtor$8 
 >2u   this  EN  @                                  �  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 )  �    -  �   
 =  �    A  �   
 _  �    c  �   
 o  �    s  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 -  �    1  �   
 A  �    E  �   
 =  �    A  �   
 =  )   A  )  
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 ?	  �    C	  �   
 �	     �	    
 �	     �	    
 P
     T
    
 �
     �
    
 H媻@   H兞�       �    H媻H   H兞�       �    H媻H   H兞�       �    @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   }   %   �    ,   �      �   #  ? G            <      6   �        �std::bad_alloc::bad_alloc 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       H� 
 h   �   0   �  Othis  8   �  O__that  O ,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H�    H�H嬅H兡 [�   }   %   �    ,   �      �   =  U G            <      6   �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AI  	     2  AJ        	  >�   __that  AH         AK          M        �  :$
 Z   �   N                       @�  h   �  �   0   �  Othis  8   �  O__that  O   ,   �    0   �   
 z   �    ~   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H�    H茿    H堿H�    H�H嬃�   �      �      �   �   U G            !           �        �std::bad_array_new_length::bad_array_new_length 
 >�   this  AJ        !  M        �    M        �    N N                        @�  h   �  �      �  Othis  O   �   8           !   �     ,       �  �    �  �   �  �   �  �,   �    0   �   
 z   �    ~   �   
   �      �   
 @SH冹 H嬞H嬄H�
    W繦峉H�H岺�    H嬅H兡 [�   }   %   �       �   �   ? G            2      ,   �        �std::exception::exception 
 >�   this  AI  	     (  AJ        	  >�   _Other  AH         AK         
 Z   �                         H�  0   �  Othis  8   �  O_Other  O �   0           2   �     $       H  �   I  �)   J  �,   �    0   �   
 d   �    h   �   
 t   �    x   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   F  f G            "         �        �nvrhi::RefCountPtr<nvrhi::IDevice>::~RefCountPtr<nvrhi::IDevice> 
 >   this  AH         AJ          AH        M        �  GCE
 >))    temp  AJ  
       AJ        N (                     0H� 
 h   �   0     Othis  9       E   O  �   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 B  �    F  �   
 \  �    `  �   
 H冹(H嬃H�	H吷tH�     H��P怘兡(�   �   H  h G            "         �        �nvrhi::RefCountPtr<nvrhi::ITexture>::~RefCountPtr<nvrhi::ITexture> 
 >JH   this  AH         AJ          AH        M        �  GCE
 >�    temp  AJ  
       AJ        N (                     0H� 
 h   �   0   JH  Othis  9       E   O�   0           "   �     $       �  �   �  �   �  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 D  �    H  �   
 \  �    `  �   
 @SH冹 H嬞H婭H吷t?H婼(H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐w$I嬋�    3繦塁H塁 H塁(H岾H兡 [�    �    �?   �    [   �    `   �       �     �G            e      e   �'        �std::_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> >::~_Hash<std::_Umap_traits<nvrhi::TextureSubresourceSet,nvrhi::RefCountPtr<nvrhi::IFramebuffer>,std::_Uhash_compare<nvrhi::TextureSubresourceSet,std::hash<nvrhi::TextureSubresourceSet>,std::equal_to<nvrhi::TextureSubresourceSet> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > >,0> > 
 >_u   this  AI  	     \ Q   AJ        	  M        �'  H	V" M        (  )I1& M        >(  *F M        c  )!
 Z   �  
 >   _Ptr  AJ >       >#    _Bytes  AK       N   -  " M        s  
&#
$
 Z   �   >_    _Ptr_container  AP  *     :  !  AP >       >_    _Back_shift  AJ  
     W 1 !  AJ >         N N N M        =(   N N N                       @� * h	   �  s  c  �'  (  ;(  =(  >(  w(         $LN35  0   _u  Othis  O ,   �    0   �   
 �  �    �  �   
 	  �    
  �   
 �  �    �  �   
 �  �    �  �   
 *  �    .  �   
 >  �    B  �   
 d  �    h  �   
 x  �    |  �   
 �  '     '  
 @SH冹 H嬞H�	H吷t>H婼H+袶冣鳫侜   rL婣鳫兟'I+菻岮鳫凐wI嬋�    3繦�H塁H塁H兡 [描    �>   �    V   �       �   �  �G            [      [   �'        �std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > > > > 
 >搗   this  AI  	     R K   AJ        	 " M        (  )H1%
 M        >(  *= M        c  )
 Z   �  
 >   _Ptr  AJ =       >#    _Bytes  AK       E   -  " M        s  
%#

 Z   �   >_    _Ptr_container  AP  )     1    AP =       >_    _Back_shift  AJ       N 1   AJ =       
  N N N M        =(   N N                       H� & h   �  s  c  (  ;(  =(  >(  w(         $LN32  0   搗  Othis  O �   8           [   �     ,       > �	   ? �O   D �U   ? �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 l  �    p  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 (  �    ,  �   
 <  �    @  �   
 �  %   �  %  
 �  �    �  �   
 H�    H��         �   �   � G                   
   +        �std::_Ref_count_obj2<donut::engine::FramebufferFactory>::~_Ref_count_obj2<donut::engine::FramebufferFactory> 
 >=�   this  AJ                                 H� 
 h   a      =�  Othis  O�   (              0            2 �
   8 �,   �    0   �   
 �   �    �   �   
   �      �   
 H塡$H塴$H塼$WH冹 H嬹H�H婥3鞨�(H�H呟t1D  H�;H婯 H吷tH塳 H��P惡(   H嬎�    H嬤H�u院(   H�H媆$0H媗$8H媡$@H兡 _�    P   �    y   �       �   �  �G            }      d   �'        �std::list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > >::~list<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> >,std::allocator<std::pair<nvrhi::TextureSubresourceSet const ,nvrhi::RefCountPtr<nvrhi::IFramebuffer> > > > 
 >塿   this  AJ          AL       \   M        (   M        z(  
\ M        �(  \ M        c  \ N N N' M        y(  I*
 >Lu   _Head  AI         >Lu    _Pnode  AI  &     C  >Lu    _Pnext  AM  3     )  AM 0     H  )  M        �(  3
 M        z(  

G M        �(  
G M        c  
G
 Z   �   N N N M        
)  3 M        )  3 M        +)  3 M        ,)  3DE
 >�%    temp  AJ  7       AJ G       N N N N N N N                      0@� R h   �  s  t  c  (  @(  y(  z(  �(  �(  �(  �(  	)  
)  )   )  ))  +)  ,)   0   塿  Othis  9C       E   O �   8           }   p     ,        �    �d    �x    �,   �    0   �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 H塡$H塴$H塼$WH冹 H嬹H�H呟tkH媦3鞨;遲!fD  H�H吷t
H�+H��P怘兠H;遳錒�H媀H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐w(I嬋�    H�.H塶H塶H媆$0H媗$8H媡$@H兡 _描    蘽   �    �   �       �   ;  � G            �      �   �'        �std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::~vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > > 
 >#v   this  AJ          AL       � �  . M        (  I4#	 M        :(  *UJ M        c  Y)%
 Z   �  
 >   _Ptr  AJ z       >#    _Bytes  AK  R     R   -   $ M        s  bd#
(
 Z   �   >_    _Ptr_container  AP  j     :  %  AP z       >_    _Back_shift  AJ  N     V , %  AJ z       N N N M        v(  #
	 >EH   _First  AI       � u   >JH   _Last  AM  #     � {   M        �(  0 M        )  0 M        �  0 M        �  0CE
 >�    temp  AJ  3       AJ 0         N N N N N N                      0@� > h   �  s  t  �  �  c  (  9(  :(  v(  �(  �(  )  )         $LN50  0   #v  Othis  9>       E   O �   8           �   �	     ,       � �   � ��    ��   � �,   �    0   �   
   �      �   
 %  �    )  �   
 �  �    �  �   
 �  �    �  �   
 @  �    D  �   
 T  �    X  �   
 z  �    ~  �   
 �  �    �  �   
 �  �    �  �   
 �  �      �   
 }  �    �  �   
 �  �    �  �   
   ,     ,  
 7  �    ;  �   
 P  �    T  �   
 @SH冹 H婹(H嬞H凓v-H婭H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂     H荂(   艭 H兡 [描    �<   �    [   �       �   �  D G            `      `   �'        �nvrhi::BufferDesc::~BufferDesc 
 >�   this  AI  
     S L   AJ        
  M        J  GM) M        %  -(

 M        �   N M        �  -G M        3  &@ M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        s  
##
"
 Z   �   >_    _Ptr_container  AP  '     8    AP ;       >_    _Back_shift  AJ  .     1 
   N N N N N N                       H� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN39  0   �  Othis  O ,   �    0   �   
 i   �    m   �   
 }   �    �   �   
 ]  �    a  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �     �    
 @SH冹 H嬞H婭pH吷tH荂p    H��P怘婯hH吷tH荂h    H��P怘岾P�    H岾�    怘婯H吷tH荂    H��P怘兡 [�>   �    G   �       �   S  \ G            j      d   �'        �donut::engine::FramebufferFactory::~FramebufferFactory 
 >2u   this  AI  	     `  AJ        	  M        �  L M        �  LDE
 >))    temp  AJ  P       AJ d       N N M        �  ! M        �  !DE
 >�    temp  AJ  %       AJ 9       N N M        �  	 M        �  IDE
 >�    temp  AJ  
       AJ !       N N
 Z   �'                        0H�  h   �  �  �  �  �'   0   2u  Othis  9       E   95       E   9`       E   O �               j               )  �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 K  �    O  �   
 [  �    _  �   
 �  �    �  �   
 �  �    �  �   
 /  �    3  �   
 ?  �    C  �   
 O  �    S  �   
 h  �    l  �   
 @SH冹 H婹8H嬞H凓v-H婭 H�翲侜   rL婣鳫兟'I+菻岮鳫凐w"I嬋�    H荂0    H荂8   艭  H兡 [描    �<   �    [   �       �   �  F G            `      `   *        �nvrhi::TextureDesc::~TextureDesc 
 >y   this  AI  
     S L   AJ        
  M        J  GM) M        %  -(

 M        �   N M        �  -G M        3  &@ M        c  )
 Z   �  
 >   _Ptr  AJ       )  
  >#    _Bytes  AK       E &  " M        s  
##
"
 Z   �   >_    _Ptr_container  AP  '     8    AP ;       >_    _Back_shift  AJ  .     1 
   N N N N N N                       H� > h   �  s  t  J  K  $  %  3  �  �  �  �  ^  c         $LN39  0   y  Othis  O   ,   �    0   �   
 k   �    o   �   
    �    �   �   
 _  �    c  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �     �    
 �     �   �   L G                       a        �std::_Ref_count_base::~_Ref_count_base 
 >�   this  AJ          D                           H�     �  Othis  O  �                  0            ~ �,   �    0   �   
 q   �    u   �   
 �   �    �   �   
 H�    H�H兞�       }      �       �   �   V G                      �        �std::bad_array_new_length::~bad_array_new_length 
 >�   this  AJ          M        �   	
 N                        H�  h   �  �      �  Othis  O ,   �    0   �   
 {   �       �   
 H�    H�H兞�       }      �       �   �   @ G                      �        �std::exception::~exception 
 >�   this  AJ         
 Z   8                          H�     �  Othis  O  �   (              �            Y  �
   Z  �,   �    0   �   
 e   �    i   �   
 �   �    �   �   
 H塡$WH冹 H�H孂L婤3蒆�
H塉H媉H�L塆H呟tDH塼$0����嬈�罜凐uH�H嬎��羢凗u	H�H嬎�PH媡$0H嬊H媆$8H兡 _肏媆$8H嬊H兡 _�   �   �  c G               
   q   �*        �std::shared_ptr<donut::engine::FramebufferFactory>::operator= 
 >s�   this  AJ          AM       n `   >p�   _Right  AK         K &  AK ^       M        �*  3+ M        �*  +, M        �  2 M        b  K	
 N N N N M        �*   M        +  4 M        e   N M        +  ! N N N M        �*  

 M        +  
&D N N                       @� 2 h   b  �  e  �*  �*  �*  �*  +  +  +  +   0   s�  Othis  8   p�  O_Right  9I       �   9[       �   O�   H              0     <       � �
   � �
   � �   � �c   � �f   � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 x  �    |  �   
 �  �    �  �   
 �  �    �  �   
 @SH冹 H�    H嬞H�雎t
簣   �    H嬅H兡 [�	         �       �   �   { G            +      %   %+        �std::_Ref_count_obj2<donut::engine::FramebufferFactory>::`scalar deleting destructor' 
 >=�   this  AI         AJ                                @� 
 h   +   0   =�  Othis  O   ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 孃H嬞H婭pH吷tH荂p    H��P怘婯hH吷tH荂h    H��P怘岾P�    H岾�    怘婯H吷tH荂    H��P怈銮t
簒   H嬎�    H嬅H媆$0H兡 _肈   �    M   �    y   �       �   �  e G            �   
   �   �'        �donut::engine::FramebufferFactory::`scalar deleting destructor' 
 >2u   this  AI       v  AJ          M        �'  

 Z   �'   M        �  R M        �  RDE
 >))    temp  AJ  V       AJ j     !    N N M        �  ' M        �  'DE
 >�    temp  AJ  +       AJ ?       N N M        �   M        �  ODE
 >�    temp  AJ         AJ '       N N N                      0@�  h   �  �  �  �  �'  �'   0   2u  Othis  9#       E   9;       E   9f       E   O,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 #  �    '  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 `  �    d  �   
 p  �    t  �   
 �  �    �  �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   }      �    0   �       �   �   R G            B   
   4   �        �std::bad_alloc::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   }      �    0   �       �     ] G            B   
   4   �        �std::bad_array_new_length::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @�  h   �  �  �   0   �  Othis  O  ,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 H塡$WH冹 H�    H孂H�嬟H兞�    雒t
�   H嬒�    H媆$0H嬊H兡 _�
   }      �    0   �       �   �   R G            B   
   4   �        �std::exception::`scalar deleting destructor' 
 >�   this  AJ          AM       -  M        �  

	
 Z   8   N                       @� 
 h   �   0   �  Othis  O ,   �    0   �   
 w   �    {   �   
 �   �    �   �   
 M吚tBH塡$H塴$H塼$WH冹 I嬹I嬝H嬯H孂H嬒�諬鼿冸u騂媆$0H媗$8H媡$@H兡 _�   �   �  C G            H      H   �        �`vector constructor iterator'  >   __t  AJ        %  AM  %     "  AJ G       >#    __s  AK        "  AN  "       AK G       >#    __n  AI         AP          AP G       >]F   __f  AL       &  AQ          AQ G                             @  0     O__t  8   #   O__s  @   #   O__n  H   ]F  O__f  9(       ]F   O ,   �    0   �   
 g   �    k   �   
 w   �    {   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 A  �    E  �   
 �  �    �  �   
 H塡$WH冹@H嬞H孃H媺�   H��P 禜�    �{ tW离�    �    H�L峀$ H嫇�   L岲$0评 H嬒D$     )D$0�P8H�L峀$0H嫇�   L岲$ W繦嬒D$0    )D$ �P8H媆$PH兡@_�"   �    5   `   c   �   �   �      �   r  : G            �   
   �   �*        �RenderTargets::Clear 
 >�   this  AI  
     �  AJ        
  > )   commandList  AK          AM       �  M        �  �� N M        �  T N
 Z   a+   @                     @  h   �  �  �*   P   �  Othis  X    )  OcommandList  9       �   9l       ,)   9�       ,)   O  �   H           �        <       & �   ' �&   ) �@   * �o   , ��   - �,   �    0   �   
 _   �    c   �   
 o   �    s   �   
 �   �    �   �   
 �   �    �   �   
 N  �    R  �   
 ^  �    b  �   
 n  �    r  �   
 �  �    �  �   
 H塡$L塋$ UVWATAUAVAWH崿$瘕��H侅  H�    H3腍墔   I嬞L嬺H嬹E3�督x  @坹媴�  堿H塓L墎8  H墮@  菂H     菂L     H菂T     W�卄  L壄p  H菂x     D埈`  f菂�  fD壄�  D壄�  fD壄�  D墔@  I凌 D墔D  茀�  )厫  f菂\  !茀�  菂P     茀�  菂�  @   E岴H�    H崓`  �    H岴0@�tW离(    E0 )厫  茀�  I�L崊@  H峊$8I嬑�P(I嬚H崓(  H;萾H�L�(H嫀�   H墫�   H吷tH��P怘婰$8H吷tL塴$8H��P恌菂�   茀�  菂�  @   W�)厫  茀\  $A�   H�    H崓`  �    I�L崊@  H峌餓嬑�P(I嬚H崓0  H;萾H�L�(H嫀�   H墫�   H吷tH��P怘婱餒吷tL塵餒��P惼匼  $A�   H�    H崓`  �    I�L崊@  H峌鳬嬑�P(I嬚H崓8  H;萾H�L�(H嫀�   H墫�   H吷tH��P怘婱鳫吷tL塵鳫��P惼匼  A�
   H�    H崓`  �    I�L崊@  H峌 I嬑�P(I嬚H峂@H;萾H�L�(H婲xH塚xH吷tH��P怘婱 H吷tL塵 H��P惼匼  $A�   H�    H崓`  �    I�L崊@  H峌I嬑�P(I嬚H峂HH;萾H�L�(H嫀�   H墫�   H吷tH��P怘婱H吷tL塵H��P恌菂\  菂L     A�   H�    H崓`  �    I�L崊@  H峊$HI嬑�P(I嬚H峂PH;萾H�L�(H嫀�   H墫�   H吷tH��P怘婰$HH吷tL塴$HH��P恌菂\  $菂L     A�   H�    H崓`  �    I�L崊@  H峊$PI嬑�P(I嬚H峂XH;萾H�L�(H嫀�   H墫�   H吷tH��P怘婰$PH吷tL塴$PH��P怉�   H�    H崓`  �    H嵕�   A�   E孅@ ff�     I�L崊@  H峊$XI嬑�P(I嬚H峂`H;萾H�L�(H�H�H吷tH��P怘婰$XH吷tL塴$XH��P怘兦I冿u岹H�    H崓`  �    I�L崊@  H峊$`I嬑�P(I嬚H峂hH;萾H�L�(H嫀�   H墫�   H吷tH��P怘婰$`H吷tL塴$`H��P怉�   H�    H崓`  �    H嵕   M孅fD  I�L崊@  H峊$hI嬑�P(I嬚H峂pH;萾H�L�(H�H�H吷tH��P怘婰$hH吷tL塴$hH��P怘兦I冿u�W�)厫  茀\  H�       �I�������L嫢x  I凕r<H嵔`  I凕HG絗  H菂p     A�   H�    H嬒�    艷 殡   I嬏H验I嬊H+罫;鄓	M嬶H岯'�+J�!A�   I;臠G鐸峂H侚   r,H岮'H;�喺  H嬋�    H吚勈  H峹'H冪郒塆H吷t
�    H孁�3�H菂p     L壄x      �   塆�   圙艷 I凕v6I峊$H媿`  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嘇  �    H壗`  I�L崊@  H峊$pI嬑�P(E3銩嬙H峂xH;萾H�L� H嫀  H墫  H吷tH��P怘婰$pH吷tL塪$pH��P惼匼  !L嫮x  I凖r<H嵔`  I凖HG絗  H菂p     A�   H�    H嬒�    艷 殛   I嬐H验I嬊H+罫;鑦-H�       �H兝'H嬋�    H吚刐  H峹'H冪郒塆?J�)A�   I;荓G鳬峅H侚   rH岮'H;��  氪H吷t
�    H孁�I孅H菂p     L壗x      �   f塆艷 I凖v5I峌H媿`  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐嚟
  �    H壗`  I�L崊@  H峊$xI嬑�P(I嬙H崓�   H;萾H�L� H嫀�   H墫�   H吷tH��P怘婰$xH吷tL塪$xH��P惼匼  A�   H�    H崓`  �    I�L崊@  H峌�I嬑�P(I嬙H崓�   H;萾H�L� H嫀�   H墫�   H吷tH��P怘婱�H吷tL塭�H��P惼匼  /A�   H�    H崓`  �    I�L崊@  H峌圛嬑�P(I嬙H崓�   H;萾H�L� H嫀   H墫   H吷tH��P怘婱圚吷tL塭圚��P惼匼  $A�   H�    H崓`  �    I�L崊@  H峌怚嬑�P(I嬙H崓�   H;萾H�L� H嫀(  H墫(  H吷tH��P怘婱怘吷tL塭怘��P悑墔T  �   凐�   G翀匽  茀�  茀�   W�)厫  f菂�   茀�  茀\  /菂�  �   D岮H�    H崓`  �    I�L崊@  H峌業嬑�P(I嬙H崓�   H;萾H�L� H婲H塚H吷tH��P怘婱楬吷tL塭楬��P惼厾  茀\  $A�   H�    H崓`  �    (    )厫  茀�  I�L崊@  H峌營嬑�P(I嬙H崓�   H;萾H�L� H婲0H塚0H吷tH��P怘婱燞吷tL塭燞��P惼匼  茀�  A�    H�    H崓`  �    I�L崊@  H峌↖嬑�P(I嬙H崓�   H;萾H�L� H嫀�   H墫�   H吷tH��P怘婱℉吷tL塭℉��P怉�   H�    H崓`  �    I�L崊@  H峌癐嬑�P(I嬙H崓�   H;萾H�L� H嫀�   H墫�   H吷tH��P怘婱癏吷tL塭癏��P惼匼  A�   H�    H崓`  �    I�L崊@  H峌窱嬑�P(I嬙H崓�   H;萾H�L� H婲XH塚XH吷tH��P怘婱窰吷tL塭窰��P怉�   H�    H崓`  �    I�L崊@  H峌繧嬑�P(I嬙H崓�   H;萾H�L� H婲`H塚`H吷tH��P怘婱繦吷tL塭繦��P惼匼  $A�   H�    H崓`  �    I�L崊@  H峌菼嬑�P(I嬙H崓�   H;萾H�L� H婲hH塚hH吷tH��P怘婱菻吷tL塭菻��P惼匼  A�   H�    H崓`  �    I�L崊@  H峌蠭嬑�P(I嬙H崓�   H;萾H�L� H婲pH塚pH吷tH��P怘婱蠬吷tL塭蠬��P悏滰  媴l  墔D  茀\  $A�   H�    H崓`  �    I�L崊@  H峌豂嬑�P(I嬙H崓�   H;萾H�L� H婲8H塚8H吷tH��P怘婱豀吷tL塭豀��P惼匼  &A�   H�    H崓`  �    I�L崊@  H峌郔嬑�P(I嬙H崓�   H;萾H�L� H婲@H塚@H吷tH��P怘婱郒吷tL塭郒��P怉�   H�    H崓`  �    I�L崊@  H峌鐸嬑�P(I嬙H崓�   H;萾H�L� H婲HH塚HH吷tH��P怘婱鐷吷tL塭鐷��P惼匼  f菂�  A�   H�    H崓`  �    I�L崊@  H峊$ I嬑�P(I嬙H崓�   H;萾H�L� H婲 H塚 H吷tH��P怘婰$ H吷tL塪$ H��P怉�   H�    H崓`  �    I�L崊@  H峊$(I嬑�P(I嬙H崓   H;萾H�L� H婲(H塚(H吷tH��P怘婰$(H吷tL塪$(H��P恌菂�    A�
   H�    H崓`  �    I�L崊@  H峊$0I嬑�P(I嬙H崓  H;萾H�L� H婲PH塚PH吷tH��P怘婰$0H吷tL塪$0H��P惞�   �    H嬝H塂$@L�-    H吚t%W� 茾   茾   L�(H岺I嬛�    �I嬡H岰H塃H塢H峌H崕H  �    ����H婱H吷t.嬊�罙凐u"H媇H�H嬎�嬊�罜凐u
H婱H��PH婲8H塋$@H吷tH��P怘嫀H  H兞PA�   H峊$@�    怢�
    �   D岯鵋峀$@�    箞   �    H嬝H墔  H吚t%W� 茾   茾   L�(H岺I嬛�    �I嬡H岰H塃 H塢(H峌 H崕X  �    H婱(H吷t,嬊�罙凐u H媇(H�H嬎��羬�u
H婱(H��PH婲 H塋$0H吷tH��P怘嫀X  H兞PA�   H峊$0�    怢�
    �   D岯鵋峀$0�    H菂�  `   W�吚  L墺�  H菂�     茀�   菂�     f菂�    菂�      f菂�    D墺�  茀�   f菂�   菂�     嫀<  兞灵媶8  兝凌�@拎H�@H玲H墠�  �    �    H菂�     H菂�          �
   f塇艪 H墔�  I�L崊�  H峊$(I嬑�悁   I嬙H崓  H;萾H�L� H嫀�   H墫�   H吷tH��P怘婰$(H吷tL塪$(H��P怘嫊�  H凓v4H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐囷  �    H菂�  @   W�吚  L墺�  H菂�     茀�   H菂�      菂�      茀�   f菂�   D墺�  嫋<  兟陵媶8  兝凌拎H拎H墔�  菂�  @   �    �    H菂�     H菂�          �   塒艪 H墔�  茀�  I�L崊�  H峊$ I嬑�悁   I嬙H崓   H;萾H�L� H嫀�   H墫�   H吷tH��P怘婰$ H吷tL塪$ H��P怘嫊�  H凓v5H�翲媿�  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐噮   �    怘嫊x  H凓v0H�翲媿`  H嬃H侜   rH兟'H婭鳫+罤兝鳫凐w/�    H媿   H3惕    H嫓$`  H伳  A_A^A]A\_^]描    愯    愯    惕    惕    �'   a     �   '  �    <  g   �  �   �  �    Z  �   f  �    �  �   �  �    ?  �   K  �    �  �   �  �    B  �   N  �    �  �   �  �    @  �   L  �    �  �   �  �    �  �   �     �  �      �    !  �   *  �   4  �   w  �      �        O  �    �  �    �  �   �  �   	  �    �	  �   �	  �    �	  �   
  �    n
  �   z
  �    3  �   ?  �    �  �   �  �    �  d   8  �   D  �    �  �   �  �    
  �   +
  �    �
  �   �
  �    �
  �     �    k  �   w  �    �  �   �  �    _  �   k  �    �  �   �  �    C  �   O  �    �      �  �    &     2  �    �  �    �     �  �    �  �    T  �    \  �    o  �    y  �    �  �    �  �    1  �    9  �    L  �    �  �         "     �  �    f  �    �  	   �  	   ?  �    }  �    �  
   �  �    �  �    �  �    �  �    �  �       �   砡  9 G            �  5   �  �*        �RenderTargets::Init 
 >�   this  AJ        >  AL  >     �k  >))   device  AK        ;  AV  ;     �h  >(G   renderSize  AP        �  >(G   displaySize  AI  8     �_ AQ        8  Dh   >0    enableMotionVectors  EO  (           Dp   >0    useReverseProjection  A   H     � EO  0           Dx   >t    backbufferCount  EO  8           D�  
 >�   desc  CK  8   K    	  CK 8   �    *  D@   >   bufferDesc  CK  (   �    	  CK (   �    `  B�      ��� >   surfaceBufferDesc  CK  (   	    	  CK (   D      B�      �Z��  M        �  �9 N M        R  �� M        &  ��' N M        A  �� M        �  �� M          �� N N N N M        �  仚 M        �  仚HB
 >�    temp  AJ  �      AJ �    ?  B8   �    ! B`  j    _ N N M        
*  .乯 M        �  亶 M        �  亶
 >�    temp  AJ  �      AJ �      B@      ��  N N M        �  � >�    tmp  AK  m    +  AK �    N    N M        *  乯C M        �  亂 N N N M        
*  %傰 M        �  � M        �  �
 >�    temp  AJ        AJ       B�      ��  N N M        �  � >�    tmp  AK  �    "  AK     (    N M        *  傰C	 M        �  � N N N M        �  創/! M        �  創HB 
 >�    temp  AJ  �      AJ �      B�  g    b DP    N N M        (  � M        /(  �
 Z   )   N N M        (  劑 M        /(  劑
 Z   )   N N M        
*  +刧 M        �  剣 M        �  剣
 >�    temp  AJ  �      AJ �      B�      ��  N N M        �  剏 >�    tmp  AK  j    (  AK �    #    N M        *  刧C	 M        �  剆 N N N M        (  �9 M        /(  �9
 Z   )   N N M        �  � M        �  �HB
 >�    temp  AJ        AJ &    '  BP  �    � DH    N N M        
*  +冧 M        �  � M        �  �
 >�    temp  AJ  �      AJ       B0      ��  N N M        �  凍 >�    tmp  AK  �    (  AK     6    N M        *  冧C	 M        �  凁 N N N M        (  兌 M        /(  兌
 Z   )   N N M        �  儚 M        �  儚GB
 >�    temp  AJ  �      AJ �    '  B�  c    f D   N N M        
*  +僣 M        �  儍 M        �  儍
 >�    temp  AJ  |      AJ �      B�      ��  N N M        �  僽 >�    tmp  AK  f    (  AK �    4    N M        *  僣C	 M        �  僶 N N N M        (  �6 M        /(  �6
 Z   )   N N M        �  � M        �  �GB
 >�    temp  AJ        AJ /      B�  �    � D    N N M        (  側 M        /(  側
 Z   )   N N M        �  偔 M        �  偔GB
 >�    temp  AJ  �      AJ �      Bx  ~    K D�    N N M        
*  .倊 M        �  偂 M        �  偂
 >�    temp  AJ  �      AJ �      BX      ��  N N M        �  倱 >�    tmp  AK  �    +  AK �    (    N M        *  倊C M        �  倣 N N N M        (  俀 M        /(  俀
 Z   )   N N M        �  �6 M        �  �6GB
 >�    temp  AJ  :      AJ J      B      � D�    N N M        
*  .� M        �  �* M        �  �*
 >�    temp  AJ  #      AJ 6      B�      ��  N N M        �  � >�    tmp  AK  
    +  AK 6    (    N M        *  �C M        �  � N N N M        (  佢 M        /(  佢
 Z   )   N N M        (  収 M        /(  収
 Z   )   N N M        �  厬
/! M        �  厬HB
 
 >�    temp  AJ  �      AJ �      B�  e    d D`    N N M        
*  +卐 M        �  厖 M        �  厖
 >�    temp  AJ  ~      AJ �      B�      ��  N N M        �  厀 >�    tmp  AK  h    (  AK �    #    N M        *  卐C	 M        �  卶 N N N M        �  � M        �  �HB
 >�    temp  AJ        AJ �    k  =  BX  �    � DX    N N M        
*  #匁 M        �  �
 M        �  �

 >�    temp  B8      ��  N N M        �  � N M        *  匁C	 M        �  � N N N M        (  �9 M        /(  �9
 Z   )   N N M        
*  (忢 M        �  �
 M        �  �

 >�    temp  AJ        AJ       B�      ��  N N M        �  � >�    tmp  AK  �    %  AK     1    N M        *  忢C M        �  忺 N N N M        (  徖 M        /(  徖
 Z   )   N N M        �  彫 M        �  彫GB
 >�    temp  AJ  �      AJ �      B�  �    F.  D�    N N M        
*  (弮 M        �  彔 M        �  彔
 >�    temp  AJ  �      AJ �      B�      ��  N N M        �  彉 >�    tmp  AK  �    %  AK �    !    N M        *  弮C M        �  彃 N N N M        (  廣 M        /(  廣
 Z   )   N N M        �  �; M        �  �;GB
 >�    temp  AJ  ?      AJ O      BH      ��  D�    N N M        
*  (� M        �  �/ M        �  �/
 >�    temp  AJ  +      AJ ;      B(      ��  N N M        �  �' >�    tmp  AK      %  AK ;    (    N M        *  �C M        �  �! N N N M        (  庡 M        /(  庡
 Z   )   N N M        �  幐 M        �  幐GB
 >�    temp  AJ  �      AJ �    -  B�  �    :"  D�    N N M        
*  (帍 M        �  幀 M        �  幀
 >�    temp  AJ  �      AJ �      B�      ��  N N M        �  帳 >�    tmp  AK  �    %  AK �    :    N M        *  帍C M        �  帪 N N N M        (  巄 M        /(  巄
 Z   )   N N M        �  嶨 M        �  嶨GB
 >�    temp  AJ  K      AJ [      B�      ��  D�    N N M        
*  (� M        �  �; M        �  �;
 >�    temp  AJ  7      AJ G      Bh      ��  N N M        �  �3 >�    tmp  AK  !    %  AK G    (    N M        *  �C M        �  �- N N N M        (  嶑 M        /(  嶑
 Z   )   N N M        �  嵵 M        �  嵵GB
 >�    temp  AJ  �
      AJ �
      B(  �
    		  D�    N N M        
*  (嵀 M        �  嵤 M        �  嵤
 >�    temp  AJ  �
      AJ �
      B      ��  N N M        �  嵚 >�    tmp  AK  �
    %  AK �
    (    N M        *  嵀C M        �  嵓 N N N M        (  崁 M        /(  崁
 Z   )   N N M        �  峫 M        �  峫GB
 >�    temp  AJ  p
      AJ �
      B�  C
    �	n	  D�    N N M        
*  (岰 M        �  峘 M        �  峘
 >�    temp  AJ  \
      AJ l
      B�      ��  N N M        �  峏 >�    tmp  AK  F
    %  AK l
    !    N M        *  岰C M        �  峈 N N N M        (  � M        /(  �
 Z   )   N N M        �  岥 M        �  岥GB
 >�    temp  AJ  �      AJ 
      Bh  �    �	�	  D�    N N M        
*  .屘 M        �  岋 M        �  岋
 >�    temp  AJ  �      AJ �      BH      ��  N N M        �  屷 >�    tmp  AK  �    +  AK �    (    N M        *  屘C M        �  屰 N N N M        (  専 M        /(  専
 Z   )   N N M        �  寢 M        �  寢GB
 >�    temp  AJ  �      AJ �      B  \    m
U
  D�    N N M        
*  .孿 M        �  � M        �  �
 >�    temp  AJ  x      AJ �      B�      ��  N N M        �  宷 >�    tmp  AK  _    +  AK �    !    N M        *  孿C M        �  宬 N N N M        �*  5捫 M        �*  捫,	 M        �  捹, M        b  採

 N N N N M        �  �
 M        �  �
GB
 >�    temp  AJ        AJ !    "  B�  �    �
�
  D�    N N M        
*  (嬩 M        �  � M        �  �
 >�    temp  AJ  �      AJ 
      B�      ��  N N M        �  孂 >�    tmp  AK  �    %  AK 
    /    N M        *  嬩C M        �  嬻 N N N M        (  嫝 M        /(  嫝
 Z   )   N N M        �  媭 M        �  媭GB
 >�    temp  AJ  �      AJ �    "  BH  W    rZ  D�    N N M        
*  (媁 M        �  媡 M        �  媡
 >�    temp  AJ  p      AJ �      B(      ��  N N M        �  媗 >�    tmp  AK  Z    %  AK �    /    N M        *  媁C M        �  媐 N N N M        (  �, M        /(  �,
 Z   )   N N M        �  娏 M        �  娏GB
 >�    temp  AJ  �
      AJ �
    
  B�  �
    7  D�    N N M        
*  .姃 M        �  姷 M        �  姷
 >�    temp  AJ  �
      AJ �
      B�      ��  N N M        �  姧 >�    tmp  AK  �
    +  AK �
    v    N M        *  姃C M        �  姟 N N N M        (  奺 M        /(  奺
 Z   )   N N M        �  奐 M        �  奐GB
 >�    temp  AJ  N
      AJ ^
      B�  
    ��  D�    N N M        
*  .� M        �  �> M        �  �>
 >�    temp  AJ  7
      AJ J
      Bh      ��  N N M        �  �0 >�    tmp  AK  
    +  AK J
    (    N M        *  �C M        �  �* N N N M        (  夘 M        /(  夘
 Z   )   N N M        �  売 M        �  売GB
 >�    temp  AJ  �	      AJ �	      B(  �	    %


  D�    N N M        
*  .墹 M        �  壡 M        �  壡
 >�    temp  AJ  �	      AJ �	      B      ��  N N M        �  壒 >�    tmp  AK  �	    +  AK �	    (    N M        *  墹C M        �  壋 N N N M        (  墂 M        /(  墂
 Z   )   N N M        �  塟 M        �  塟HB
 >�    temp  AJ  _	      AJ p	      B�  +	    �
�
  Dx    N N M        
*  .�+ M        �  塏 M        �  塏
 >�    temp  AJ  G	      AJ Z	      B�      ��  N N M        �  堾 >�    tmp  AK  .	    +  AK Z	    *    N M        *  �+C M        �  �: N N N  M        (  �矅�x���  M        /(  �矅�x���0 M        )  囥M+Dixk�@ M        +  �,&+'5嵶 >#    _New_capacity  AH  G      AJ      * "   AK  D    
  AW  N    1�# &  AH K      AJ K    k [ � * � �
 AW 	    �
�
  Co      t      >_    _Old_capacity  AJ  2    M    AU  �    H  AJ K      AU 	    � M        �*  埢 M        ?   埢 N N$ M        �  圞	0� >p    _Fancy_ptr  AM  �      AM �    H	� M        �  圞4� M        �  圞4�. M        (  圞4
	
%
�+ M        9   圞()$	�%
 Z   q   >_    _Block_size  AH  �      AH K    k ^ >_    _Ptr_container  AH  S      AH �    # �

 >�    _Ptr  AM  d      AM �    H	� M        r  圞
 Z      N N M        r  垳
 Z      N N N N N M        �  �28 M          �2- N N M        �  5堏嵼 M        3  1堔嵸  M        c  堣)嵀
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH 
	      >#    _Bytes  AK  �    1  AK �      M        s  堮d嵒
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N M        �  L� N M        0  囸
 >嘚   this  AM  �      >p    _Result  AM      )  AM 	    � N N N N M        �  嚻 M        �  嚻HB
 >�    temp  AJ  �      AJ �    S B   B�  �    2 Dp    N N M        
*  .嚄 M        �  嚭 M        �  嚭
 >�    temp  AJ  �      AJ �      B�      ��  N N M        �  嚞 >�   tmp  AK  �     & AK �    �  U  �  �  1 _�
 C       �     . C      �    
  /  o  �  �  21 y�
 N M        *  嚄F	 M        �  嚘 N N N M        (  丄�: M        /(  丄�:) M        )  �:!+D��* M        +  啑&%J+'!6 >#    _New_capacity  AH  �      AJ  �    F   ?   AU  �        AH �      AJ �    � - u * �  Cm      �      >_    _Old_capacity  AJ  �    )  AT  U    H  AJ �      AT �      M        �*  � M        ?   � N N M        �  喡F >p    _Fancy_ptr  AM        AM     �� 5 T
 � � M        �  F喥 M        �  F喥  M        (  喥)
,%
" M        9   喯$	() >_    _Block_size  AH  �    	  AH �    � � >_    _Ptr_container  AH  �      AH     �" �
 >�    _Ptr  AM  �      AM     �� 5 T
 � � M        r  嗆
 Z      N N M        r  � 
 Z      N N N N N M        �  %啙 M          啙+	 N N M        �  6嘐 M        3  1嘕 M        c  嘥)
 Z   �  
 >   _Ptr  AH  T      AJ  Q      AH v      >#    _Bytes  AK  J    1  AK �      M        s  嘳d >_    _Ptr_container  AH  h      AJ  e      N N N N N M        �  L唝 N M        0  哰
 >嘚   this  AM  b      >p    _Result  AM  n    )  AM �    ;u 5 � 
 " ' N N N N M        �  �	 M        �  �	HB
 >�    temp  AJ        AJ �    �  = �   B  �    � Dh    N N M        
*  #呭 M        �  咠 M        �  咠
 >�    temp  B�
      ��  N N M        �  咓 N M        *  呭C	 M        �  咇 N N N M        �*  �%
 Z   +  
 >#v   this  AJ  !      N M        �'  �	 M        �  �# N N M        +  抯% Z     �*   >=�    _Rx  AI  �    i *   AK  �      AI     �� �  D   M        7+  捈 N M        5+  拸 M        �  	拻 N N N M        �*  扝
 Z   +  
 >#v   this  AJ  D      N M        (  �/ M        /(  �/
 Z   )   N N M        �'  �(	 M        �  �1# N N M        �*  <戩 M        �*  戩. M        �  扂, M        b  �
 N N N N M        +  憡!% Z     �*   >=�    _Rx  AI  �    s /   AK  �      AI (    X  B@   �    �  M        7+  懾 N M        5+  懌 M        �  	懏 N N N M        �  憈 M        �  憈HB
 >�    temp  AJ  y      AJ �      B0   �    � B�  K    ~f  N N M        
*  (慘 M        �  慼 M        �  慼
 >�    temp  AJ  d      AJ t      Bh      ��  N N M        �  慲 >�    tmp  AK  N    %  AK t         N M        *  慘C M        �  慫 N N N M        (  � M        /(  �
 Z   )   N N M        �  慆 M        �  慆HB
 >�    temp  AJ        AJ       B(   
    �~ �  B(  �    ��  N N M        
*  (愓 M        �  愹 M        �  愹
 >�    temp  AJ  �      AJ �      B      ��  N N M        �  愱 >�    tmp  AK  �    %  AK �    ,    N M        *  愓C M        �  愪 N N N M        (  惂 M        /(  惂
 Z   )   N N M        �  悜 M        �  悜HB
 >�    temp  AJ  �      AJ �      B    �    )[   B�  h    aI  N N M        
*  (恏 M        �  悈 M        �  悈
 >�    temp  AJ  �      AJ �      B�      ��  N N M        �  恾 >�    tmp  AK  k    %  AK �    #    N M        *  恏C M        �  恮 N N N M        (  �: M        /(  �:
 Z   )   N N M        �  � M        �  �GB
 >�    temp  AJ        AJ *    $  Bh  �    ��  D�    N N M        J  A敀�+ M        %  敀4
� M        �  4敓� M        3  1敘�  M        c  敩)侊
 Z   �  
 >   _Ptr  AH  �      AJ  �      AH �      >#    _Bytes  AK  �     1 � M        s  數d価
 Z   �   >_    _Ptr_container  AH  �      AJ  �      N N N N N N M        �'  攟 M        (  攟HB
 >�     temp  AJ  �      AJ �    �  *  B(   �    >&  Bp      ��  N N M        �'  .擬 M        �'  攑 M        (  攑
 >�     temp  AJ  i      AJ |      BP      ��  N N M        (  攂 >�     tmp  AK  P    +  AK |        N M        (  擬C M        (  擻 N N N M        (  @擋 M        /(  @擋 M        )  @擋  M        +  擋
++
 M        �*  � M        ?   � N N M        �  
擋 >p    _Fancy_ptr  AH  �    9  M        �  
擋 M        �  
擋 M        (  
擋 M        r  
擋
 Z      N N N N N N N N N M        �*  !撆 M        �*  撆 >u     tileCountY  A   �        M        �*  撗 >u     tileCountX  A   �    	    N N N M        R  揯 M        &  揺' N M        A  揯 M        �  揯 M          揯 N N N N M        J  =朌g M        %  朌0
Z M        �  朌 N M        �  0朡Z M        3  -朤W M        c  朸)/
 Z   �  
 >   _Ptr  AH  ^      AJ  [      AH |      >#    _Bytes  AK  T    \ - *  M        s  杇d
9
 Z   �   >_    _Ptr_container  AH  r      AJ  o      N N N N N N M        J  A��� M        %  �4
�� M        �  4��� M        3  1���  M        c  �)��
 Z   �  
 >   _Ptr  AH        AJ        AH >      >#    _Bytes  AK      � 1 �  M        s  �%d��
 Z   �   >_    _Ptr_container  AH  0      AJ  -      N N N N N N M        �'  曥 M        (  曥HB
 >�     temp  AJ  �      AJ     �  + Y &  B    �    � �   B�	      ��  N N M        �'  .暯 M        �'  曕 M        (  曕
 >�     temp  AJ  �      AJ �      B�	      ��  N N M        (  曇 >�     tmp  AK  �    +  AK �        N M        (  暯C M        (  曁 N N N M        (  >昤 M        /(  >昤 M        )  >昤  M        +  昤
++
 M        �*  晙 M        ?   晙 N N M        �  
昤 >p    _Fancy_ptr  AH  j    >  M        �  
昤 M        �  
昤 M        (  
昤 M        r  
昤
 Z      N N N N N N N N N M        �*  �- M        �*  �- >u     tileCountY  A   3    7    M        �*  �9 >u     tileCountX  A   ?    	    N N N M        R  斸 M        &  旇' N M        A  斸 M        �  斸 M          斸 N N N N Z   �*  �!  �*  �!             8         A ^hV   �  �  b  r  s  t  v  �  �  �  �  �  J  K  R  S  �  �  $  %  &  )  0  3  >  ?  �  �  �  �  �  �  �  <  A  ^  c  �  �  �  �  �      �  �  '  (  /   9   �'  �'  �'  �'  �'  (  (  (  (  /(  *  *  *  
*  *  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  �*  +  +  +  +  +  5+  6+  7+  J+  N+  
 :   O        $LN1830  P  �  Othis  X  ))  Odevice  `  (G  OrenderSize  h  (G  OdisplaySize   p  0   OenableMotionVectors ! x  0   OuseReverseProjection  �  t   ObackbufferCount  @  �  Odesc  �    ObufferDesc  �    OsurfaceBufferDesc  9g      �(   9�      E   9�      E   9      �(   92      E   9F      E   9{      �(   9�      E   9�      E   9�      �(   9      E   9+      E   9`      �(   9�      E   9�      E   9�      �(   9      E   9"      E   9d      �(   9�      E   9�      E   9�      �(   9      E   9+      E   9b      �(   9�      E   9�      E   9�      �(   9      E   9      E   9�      �(   9�      E   9�      E   9(	      �(   9V	      E   9l	      E   9�	      �(   9�	      E   9�	      E   9
      �(   9F
      E   9Z
      E   9�
      �(   9�
      E   9�
      E   9T      �(   9|      E   9�      E   9�      �(   9	      E   9      E   9Y      �(   9�      E   9�      E   9�      �(   9�      E   9
      E   9@
      �(   9h
      E   9|
      E   9�
      �(   9�
      E   9�
      E   9      �(   9C      E   9W      E   9�      �(   9�      E   9�      E   9      �(   97      E   9K      E   9�      �(   9�      E   9�      E   9�      �(   9      E   9&      E   9e      �(   9�      E   9�      E   9�      �(   9�      E   9      E   9H      �(   9p      E   9�      E   ^�     <�   9      �   9%      �   99      E   ^x     <�   9�      �   9      �   9      E   9G      �(   9x      E   9�      E   9�      �(   9�      E   9�      E   O �   �          �    �   �      &  �A   '  �L   (  �U   )  �Y   *  �`   +  �{   7  ��   -  ��   ?  ��   1  ��   .  ��   /  ��   5  ��   6  ��   >  ��   9  ��   <  �  @  �
  B  �  C  �+  D  �N  E  �U  F  ��  L  ��  I  ��  J  ��  K  ��  M  ��  N  ��  O  �J  P  �Q  Q  �j  R  ��  T  ��  U  ��  V  �/  X  �6  Y  �O  Z  ��  \  ��  ]  ��  _  ��  `  �&  d  �/  b  �9  e  �R  f  ��  g  ��  f  ��  i  �/  h  �9  k  �P  l  ��  m  ��  l  ��  o  �  n  �,  r  �3  t  �:  u  �{  v  ��  y  ��  z  �	  {  �p	  }  �w	  ~  ��	    ��	  �  ��	  �  �
  �  �^
  �  �e
  �  �~
  �  ��
  �  ��
  �  ��
  �  ��
  �  �  �  �  �  �  �  �  �  �"  �  �,  �  �C  �  ��  �  ��  �  ��  �  ��  �  ��  �  ��  �  �!  �  �(  �  �/  �  �H  �  ��  �  ��  �  �
  �  �
  �  �/
  �  ��
  �  ��
  �  ��
  �  ��
  �  �
  �  �[  �  �b  �  �{  �  ��  �  ��  �  ��  �  ��  �  ��  �  �O  �  �V  �  �o  �  ��  �  ��  �  �*  �  �1  �  �:  �  �S  �  ��  �  ��  �  �  �  �  �  �6  �  ��  �  �(  �  �s  �  �    �P   �^   ��   ��   ��   ��  	 ��  
 ��  
 ��   �5   ��   ��   ��   �   �-   �V   �`   ��   ��   �   �D   ��  z  ��   ��   ��   �   H F                                �`RenderTargets::Init'::`1'::dtor$0  >(G   displaySize  EN  h         
 >�    desc  EN  @                                 �  O   �   �   I F                               �`RenderTargets::Init'::`1'::dtor$50  >(G   displaySize  EN  h         
 >�    desc  EN  @                                �  O  �   �   I F            *      $             �`RenderTargets::Init'::`1'::dtor$32  >(G   displaySize  EN  h        $ 
 >�    desc  EN  @        $                        �  O  �   �   I F                                �`RenderTargets::Init'::`1'::dtor$53  >(G   displaySize  EN  h         
 >�    desc  EN  @                                �  O  �   �   I F            *      $             �`RenderTargets::Init'::`1'::dtor$35  >(G   displaySize  EN  h        $ 
 >�    desc  EN  @        $                        �  O  �   �   I F                                �`RenderTargets::Init'::`1'::dtor$36  >(G   displaySize  EN  h         
 >�    desc  EN  @                                 �  O  �   �   I F                                �`RenderTargets::Init'::`1'::dtor$38  >(G   displaySize  EN  h         
 >�    desc  EN  @                                 �  O  ,   �    0   �   
 ^   �    b   �   
 n   �    r   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �    
  �   
 @  �    D  �   
 w  �    {  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
    �      �   
 1  �    5  �   
 E  �    I  �   
 U  �    Y  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 A  �    E  �   
 Q  �    U  �   
 a  �    e  �   
 �  �    �  �   
 �  �    �  �   
 d  �    h  �   
 t  �    x  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 }  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
 �  �    �  �   
 �  �    �  �   
 	  �    	  �   
 x	  �    |	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �	  �    �	  �   
 �
  �    �
  �   
 �
  �    �
  �   
 �
  �    �
  �   
 ^  �    b  �   
 n  �    r  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 r
  �    v
  �   
 �
  �    �
  �   
 �
  �    �
  �   
   �      �   
   �      �   
 '  �    +  �   
 k  �    o  �   
 {  �      �   
 X  �    \  �   
 h  �    l  �   
 x  �    |  �   
 �  �    �  �   
 �  �      �   
 
  �      �   
 Q  �    U  �   
 a  �    e  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 +  �    /  �   
 ;  �    ?  �   
   �    �  �   
 �  �    �  �   
 ,  �    0  �   
 <  �    @  �   
 P  �    T  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 :  �    >  �   
 J  �    N  �   
 '  �    +  �   
 7  �    ;  �   
 G  �    K  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 $  �    (  �   
 4  �    8  �   
   �      �   
 !  �    %  �   
 1  �    5  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �    "  �   
 �  �    �  �   
   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
 �  �    �  �   
 �  �    �  �   
   �    	  �   
 ~  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 h  �    l  �   
 x  �    |  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 R   �    V   �   
 b   �    f   �   
 r   �    v   �   
 �   �    �   �   
 �   �    �   �   
 �!  �    �!  �   
 �!  �    �!  �   
 �!  �    �!  �   
 <"  �    @"  �   
 L"  �    P"  �   
 \"  �    `"  �   
 �"  �    �"  �   
 �"  �    �"  �   
 �#  �    �#  �   
 �#  �    �#  �   
 �#  �    �#  �   
 &$  �    *$  �   
 6$  �    :$  �   
 F$  �    J$  �   
 �$  �    �$  �   
 �$  �    �$  �   
 �%  �    �%  �   
 �%  �    �%  �   
 �%  �    �%  �   
 F&  �    J&  �   
 V&  �    Z&  �   
 f&  �    j&  �   
 �&  �    �&  �   
 �&  �    �&  �   
 �'  �    �'  �   
 �'  �    �'  �   
 �'  �    �'  �   
 0(  �    4(  �   
 @(  �    D(  �   
 P(  �    T(  �   
 �(  �    �(  �   
 �(  �    �(  �   
 �)  �    �)  �   
 �)  �    �)  �   
 �)  �    �)  �   
 *  �    *  �   
 **  �    .*  �   
 :*  �    >*  �   
 ~*  �    �*  �   
 �*  �    �*  �   
 k+  �    o+  �   
 {+  �    +  �   
 �+  �    �+  �   
 ,  �    ,  �   
 ,  �    ,  �   
 $,  �    (,  �   
 h,  �    l,  �   
 x,  �    |,  �   
 U-  �    Y-  �   
 e-  �    i-  �   
 u-  �    y-  �   
 �-  �    �-  �   
 �-  �    .  �   
 .  �    .  �   
 R.  �    V.  �   
 b.  �    f.  �   
 ?/  �    C/  �   
 O/  �    S/  �   
 _/  �    c/  �   
 �/  �    �/  �   
 �/  �    �/  �   
 �/  �    �/  �   
 <0  �    @0  �   
 L0  �    P0  �   
 v1  �    z1  �   
 �1  �    �1  �   
 �1  �    �1  �   
 �1  �    �1  �   
 �1  �    �1  �   
 �1  �    �1  �   
 �1  �    �1  �   
 2  �    
2  �   
 .2  �    22  �   
 B2  �    F2  �   
 R2  �    V2  �   
 b2  �    f2  �   
 �2  �    �2  �   
 �2  �    �2  �   
 �3  �    �3  �   
 �3  �    �3  �   
 4  �    	4  �   
 4  �    4  �   
 84  �    <4  �   
 H4  �    L4  �   
 m5  �    q5  �   
 }5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 �5  �    �5  �   
 6  �    6  �   
 #6  �    '6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 �6  �    �6  �   
 7  �    #7  �   
 /7  �    37  �   
 C7  �    G7  �   
 �7  �    �7  �   
 �7  �    �7  �   
 �7  �    �7  �   
 8  �     8  �   
 ,8  �    08  �   
 X8  �    \8  �   
 l8  �    p8  �   
 {9  �    9  �   
 �9  �    �9  �   
 �9  �    �9  �   
 �9  �    �9  �   
 �9  �    �9  �   
 �9  �    �9  �   
 :  �    :  �   
 :  �    #:  �   
 /:  �    3:  �   
 ?:  �    C:  �   
 �:  �    �:  �   
 �:  �    �:  �   
 n;  �    r;  �   
 ~;  �    �;  �   
 �;  �    �;  �   
 �;  �    �;  �   
 �;  �    �;  �   
 �;  �    �;  �   
 =  �    =  �   
 =  �    =  �   
 +=  �    /=  �   
 L=  �    P=  �   
 \=  �    `=  �   
 �=  �    �=  �   
 �=  �    �=  �   
 >  �    >  �   
 :>  �    >>  �   
 J>  �    N>  �   
 �>  �    �>  �   
 �>  �    �>  �   
 �>  �    �>  �   
 Z?  �    ^?  �   
 @  �    @  �   
 扏  �    朄  �   
   �    狜  �   
 禓  �    篅  �   
 _A  �    cA  �   
 燘  �      �   
 碆  �    窧  �   
 腂  �    菳  �   
 訠  �    谺  �   
 C  �    僀  �   
 廋  �    揅  �   
 烠  �      �   
 疌  �    矯  �   
  D  �    $D  �   
 0D  �    4D  �   
 @D  �    DD  �   
 凞  �    圖  �   
 擠  �    楧  �   
 qE  �    uE  �   
 丒  �    匛  �   
 慐  �    旹  �   
 〦  �    璄  �   
 F  �    F  �   
 *F  �    .F  �   
 :F  �    >F  �   
 ~F  �    侳  �   
 嶧  �    扚  �   
 kG  �    oG  �   
 {G  �    G  �   
 婫  �    廏  �   
   �      �   
 H  �    H  �   
 $H  �    (H  �   
 4H  �    8H  �   
 xH  �    |H  �   
 圚  �    孒  �   
 eI  �    iI  �   
 uI  �    yI  �   
 匢  �    塈  �   
 RJ  �    VJ  �   
 bJ  �    fJ  �   
 rJ  �    vJ  �   
 揓  �    桱  �   
 霬  �    餔  �   
 麶  �     K  �   
 gK  �    kK  �   
 wK  �    {K  �   
 婯  �    廗  �   
 烱  �      �   
 L  �    L  �   
  L  �    $L  �   
 0L  �    4L  �   
 tL  �    xL  �   
 凩  �    圠  �   
   �    ㎝  �   
 侼  �    哊  �   
 罭  �    臢  �   
 #P  �    'P  �   
 3P  �    7P  �   
 CP  �    GP  �   
 dP  �    hP  �   
 糚  �    繮  �   
 蘌  �    蠵  �   
 漄  �      �   
 璔  �    盦  �   
 絈  �    罳  �   
 轖  �    釷  �   
 7R  �    ;R  �   
 GR  �    KR  �   
 睷  �    禦  �   
 翿  �    芌  �   
 赗  �    轗  �   
 頡  �    騌  �   
 _S  �    cS  �   
 oS  �    sS  �   
 S  �    僑  �   
 肧  �    荢  �   
 覵  �    譙  �   
 鬞  �    鳷  �   
 裊  �    誙  �   
 V  �    V  �   
 RX      VX     
 oY  �    sY  �   
 Y  �    僘  �   
 廦  �    揧  �   
 焂  �      �   
 痀  �    砓  �   
 縔  �    肶  �   
 蟉  �    覻  �   
 遈  �    鉟  �   
 颵  �    骙  �   
 �Y  �    Z  �   
 Z  �    Z  �   
 Z  �    #Z  �   
 /Z  �    3Z  �   
 ?Z  �    CZ  �   
 OZ  �    SZ  �   
 _Z  �    cZ  �   
 oZ  �    sZ  �   
 Z  �    僙  �   
 廧  �    揨  �   
 焃  �      �   
 痁  �    砕  �   
 縕  �    肸  �   
 蟌  �    覼  �   
 遉  �    鉠  �   
 颶  �    骦  �   
 �Z  �    [  �   
 [  �    [  �   
 [  �    #[  �   
 /[  �    3[  �   
 ?[  �    C[  �   
 O[  �    S[  �   
 _[  �    c[  �   
 o[  �    s[  �   
 [  �    僛  �   
 廩  �    揫  �   
 焄  �      �   
 痆  �    砙  �   
 縖  �    肹  �   
 蟍  �    覽  �   
 遊  �    鉡  �   
 颷  �    骩  �   
 �[  �    \  �   
 \  �    \  �   
 \  �    #\  �   
 /\  �    3\  �   
 ?\  �    C\  �   
 O\  �    S\  �   
 _\  �    c\  �   
 o\  �    s\  �   
 \  �    僜  �   
 廫  �    揬  �   
 焅  �      �   
 痋  �    砛  �   
 縗  �    肻  �   
 蟎  �    覾  �   
 運  �    鉢  �   
 颸  �    骪  �   
 �\  �    ]  �   
 ]  �    ]  �   
 ]  �    #]  �   
 /]  �    3]  �   
 ?]  �    C]  �   
 O]  �    S]  �   
 _]  �    c]  �   
 o]  �    s]  �   
 ]  �    僝  �   
 廬  �    揮  �   
 焆  �      �   
 痌  �    砞  �   
 縘  �    胅  �   
 蟏  �    覿  �   
 遌  �    鉣  �   
 颹  �    骫  �   
 �]  �    ^  �   
 ^  �    ^  �   
 ^  �    #^  �   
 /^  �    3^  �   
 ?^  �    C^  �   
 O^  �    S^  �   
 _^  �    c^  �   
 o^  �    s^  �   
 ^  �    僞  �   
 廭  �    揯  �   
 焇  �      �   
 痎  �    砠  �   
 縙  �    胇  �   
 蟐  �    觀  �   
 過  �    鉤  �   
 颺  �    骬  �   
 �^  �    _  �   
 _  �    _  �   
 _  �    #_  �   
 /_  �    3_  �   
 ?_  �    C_  �   
 O_  �    S_  �   
 __  �    c_  �   
 o_  �    s_  �   
 _  �    僟  �   
 廮  �    揰  �   
 焈  �      �   
 痏  �    砡  �   
 萠  �    蘝  �   
 豥  �    躣  �   
 $e  �    (e  �   
 Ge  �    Ke  �   
 渆     爀    
 閑     韊    
 f     f    
 `f  �    df  �   
 璮  �    眆  �   
 衒  �    詅  �   
 $g     (g    
 qg     ug    
 攇     榞    
 鑗      靏     
 5h      9h     
 Xh      \h     
 琱     癶    
 鵫     齢    
 i      i    
 pi     ti    
 絠     羒    
 鄆     鋓    
 H崐@  �       �    @UH冹 H嬯L�
    A�   �   H峂@�    H兡 ]�   �        �    @UH冹 H嬯L�
    A�   �   H峂0�    H兡 ]�   �        �    H崐�  �       �    H崐�  �       �    @UH冹 H嬯簣   H婱@�    H兡 ]�   �    @UH冹 H嬯簣   H媿  �    H兡 ]�   �    H嬄H凌 9�8  u'9�<  uI嬂H凌 D9丂  u9丏  uD9	暲冒�   �   �  E G            9       8   �*        �RenderTargets::IsUpdateRequired 
 >�   this  AJ        9  >(G   renderSize  AK        9  >(G   displaySize  AP        9  >u    sampleCount  Ai        9  M        �*  
% N M        +   M           N N M        �*  

 N M        +  
  M           N N                        @  h     �*  +      �  Othis     (G  OrenderSize     (G  OdisplaySize      u   OsampleCount  O �   @           9        4         �    ! �5   # �6   ! �8   # �,   �    0   �   
 j   �    n   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 H塡$H塴$H塼$WAVAWH冹 L嬄H嬹I�������I;��  H�H婭H+薍六H嬔H殃I嬊H+翲;葀M嬿�L�4
M;餗B�3�H呟tgH媙H;輙H�H吷t
H�;H��P怘兠H;輚錒�H媀H+袶冣鳫侜   rH兟'L婣鳬+菻岮鳫凐噦   I嬋�    H�>H墌H墌M;鱳pJ��    H侞   r!H岾'H;藇V�    H吚tFH峹'H冪郒塆H呟tH嬎�    H孁H�>H墌H�;H塅H媆$@H媗$HH媡$PH兡 A_A^_描    惕    惕    炭   �    �   �      �    >  �    D  �    J  �       �   �  � G            O     O   +        �std::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Clear_and_reserve_geometric 
 >#v   this  AJ          AL       1
  >_   _Newsize  AK          AP       3U � & AP p     �   :  S  �  �  �   >_    _Newcapacity  AV  S     �   �   M        (+  1ne >_    _Oldcapacity  AJ  8     8   " AJ p     �    @ x  �  �   >_    _Geometric  AV  Y       M        .+  1 N N& M        '+  ��G#! >JH   _Newvec  AM     '  C       b       C      p     � � 
 �   M        B+  G��o M        F+  G��o& M        (  ��)
!
3, M        9   ��$%%>	 Z   �  q   >_    _Block_size  AJ  �     
  AJ C      >_    _Ptr_container  AH  �       AH     -   
 >�    _Ptr  AM  �       AM     '  M        r  ��
 Z      N N M        r  �

 Z      N N M        �   
��
 N N N N M        :(  .�� M        c  ��)
 Z   �  
 >   _Ptr  AJ �       >#    _Bytes  AK  �     1    AK =       M        s  ��d# >_    _Ptr_container  AP  �       AP �     �  z  >_    _Back_shift  AJ  �     0  AJ �     �  z  N N N M        v(  k	 >EH   _First  AI  4     �  AI =      >JH   _Last  AN  k     c  AN �     { `   M        �(  p M        )  p M        �  p M        �  pCE
 >�    temp  AJ  s       AJ p         N N N N N
 Z   &+                        0@ ^ h   �  r  s  �  �  c  (  9   �   9(  :(  v(  �(  �(  )  )  '+  (+  )+  .+  B+  F+         $LN89  @   #v  Othis  H   _  O_Newsize  9~       E   O   �   �           O  �	  
   t       u �   � �1   � �`   � �g   � ��   � ��   � ��   � ��   � ��   � �$  � �=  � �I  � �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �    
  �   
   �      �   
 *  �    .  �   
 i  �    m  �   
 �  �    �  �   
 �  �    �  �   
   �    
  �   
 m  �    q  �   
 �  �    �  �   
 �  �    �  �   
 a  �    e  �   
 q  �    u  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 8  �    <  �   
 H  �    L  �   
 r  �    v  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 N  .   R  .  
 �  �    �  �   
 �  �    �  �   
 H吷tH��   H�`�   �   �   k G                      +        �std::_Ref_count_obj2<donut::engine::FramebufferFactory>::_Delete_this 
 >=�   this  AJ                                 @�     =�  Othis  9
       A�   O   �   0              0     $       C �    D �   E �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 H婣H兞3襀�    �     g G            
       
   +        �std::_Ref_count_obj2<donut::engine::FramebufferFactory>::_Destroy 
 >=�   this  AJ          M        @+   
 >?u   _Obj  AJ         N                        @� 
 h   @+      =�  Othis  9
       Au   O�   (           
   0            ? �    @ �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
   �      �   
 (  �    ,  �   
 3烂   �   �   H G                      c        �std::_Ref_count_base::_Get_deleter 
 >�   this  AJ          D    >   __formal  AK          D                           @�     �  Othis       O__formal  O�   0              0     $       � �    � �   � �,   �    0   �   
 m   �    q   �   
 �   �    �   �   
 �   �       �   
 H冹HH峀$ �    H�    H峀$ �    �
   �       �            �   �   F G                       q        坰td::_Throw_bad_array_new_length 
 Z   �   H                      @        $LN3  O  �   (               8            J �   K �,   �    0   �   
 �      �     
 �   �    �   �   
 H冹(H�
    �    �   �      �       �   w   7 G                     ~        坰td::_Xlen_string 
 Z   �!   (                      @        $LN3  O �   (              P             		 �   
	 �,   �    0   �   
 s      w     
 �   �    �   �   
 H冹(H�
    �    �         �       �   �   � G                     &+        坰td::vector<nvrhi::RefCountPtr<nvrhi::ITexture>,std::allocator<nvrhi::RefCountPtr<nvrhi::ITexture> > >::_Xlength 
 Z   �!   (                      @        $LN3  O  �   (              �	            a �   b �,   �    0   �   
 �   0   �   0  
 �   �    �   �   
 H塡$H塴$VWAWH冹 H媔I嬸L孃H嬞L;舧!H孂H凖vH�9H塹H嬒�    �7 轶   H�������H;�圅   H嬑L塼$@H兩H;蟱:H嬚H嬊H殃H+翲;鑧)H�*H孂H;菻B鳫岹H=   r9H岺'H;�喅   �H�       �H兞'�    H吚剾   L峱'I冩郔塅H吚t
H嬋�    L嬸�E3鯨嬈H塻I嬜H墈I嬑�    A�6 H凖v-H�H峌H侜   rL婣鳫兟'I+菻岮鳫凐w2I嬋�    L�3L媡$@H媗$PH嬅H媆$HH兡 A__^描    惕    惕    �8      �   �    �   �    �      ,  �    O  �    U  �    [  �       �     r G            `     `  )        �std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign 
 >�   this  AI       A&	  AJ         
 >�   _Ptr  AK          AW       D/  >_   _Count  AL       G4  AP         B M        +  E
(?SD3$--K
 Z   ~   >#     _New_capacity  AH  �     �  * N  V r  AJ  �       AM  O     =  ^ �  AH �     G  ,  AJ �       M        �*  �� M        ?   �� N N M        �  ��?�� >p    _Fancy_ptr  AV  �       AV �     ~ V "  M        �  ��?�� M        �  ��?��* M        (  ��

*%
u- M        9   ��	)
��
 Z   q   >_    _Block_size  AJ  �     �  �  AJ �       >_    _Ptr_container  AH  �       AH �     }  b 
 >�    _Ptr  AV  �       AV �     ~ V "  M        r  ��
 Z      N N M        r  ��
 Z      N N N N N M        �  X(  M          X' >_    _Masked  AJ  d     ~ 4  I   q 
  AJ �       M        �  
~ N N N M        �  -�W M        3  �&P M        c  �
)/
 Z   �  
 >   _Ptr  AJ      *  
  >#    _Bytes  AK  
    &  AK Z     # M        s  
�#
2
 Z   �   >_    _Ptr_container  AP        AP +    4  *  >_    _Back_shift  AJ      
  AJ Z      N N N N N M        �  L4 N M        0  $# >p    _Result  AM  '       AM 8      M        �  ' N N                       H z h   �  �  r  s  t  �  $  0  3  ?  �  �  �  �  �  �  c  �  �  �    �  �  '  (  /   9   �*  +         $LN144  @   �  Othis  H   �  O_Ptr  P   _  O_Count e 鞙  std::basic_string<char,std::char_traits<char>,std::allocator<char> >::assign::__l2::<lambda_1>  O�   h           `  P   
   \       +
 �   -
 �$   /
 �0   0
 �4   1
 �<   2
 �@   4
 �E   7
 �8  >
 �N  7
 �,   �    0   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �    �   �   
 �   �      �   
   �      �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 m  �    q  �   
 }  �    �  �   
 H  �    L  �   
 \  �    `  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 �  �    �  �   
 F  �    J  �   
 k  �    o  �   
 {  �      �   
 �  �    �  �   
 �  �    �  �   
   �      �   
   �    #  �   
 �  �    �  �   
 �  �    �  �   
 \     `    
   �      �   
 H婹H�    H呉HE旅   �      �   �   : G                      �        �std::exception::what 
 >�   this  AJ                                 @     �  Othis  O�   0              �     $       ^  �    _  �   `  �,   �    0   �   
 _   �    c   �   
 �   �    �   �   
  d T 4 2p    H           
      
      9    20    2                       ?   
 
4 
2p    B                       E    20    <                       K   
 
4 
2p    B                       Q    20    <                       W   
 
4 
2p    B                       ]    �                              c    B                             i    T
 4	 2�p`    [                       o   ! �     [                      o   [   8                      u   !       [                      o   8  T                      {   !   �     [                      o   T  `                      �    20    `                       �    20    `                       �    B                   �       "                       �   h           �      �          �    2
 
4 
2p    -                       �   ! d     -                      �   -   q                       �   !       -                      �   q                          �   9K5 $4l $b ����
p`P               	       �       �          !      !      �   (           �      �   
    	>    f    �    �    �        �
F       �    	   �             �                 #   �    *   �    殭�D�@�@�@�@�D�D�D�D�DmD�D�@�@�@}@�@�@�@�@�@�@�@�@�@�@�D�D�D$h�462
Z�46��DM�D% L 2P                           �    2P    *           �       �       �    2P                            �    2P    *                         �   ���
 
4
 
rp    �           "      "      �    B                   �       "           #      #      �   h           �      �          �    2 d T 4 2p                 �       }           $      $      �   h           �                �    � 20    [           &      &          20    e           (      (          T 4
 2�p`                        S          *      *         (                        6    .    .       �       �                f>� 20                 &       j           +      +          h           )      ,          �    :8 N d T 4 2p                 5       �           -      -      /   h           8      ;          �    |
 d
 T	 4 2��p                 D       O          /      /      >   (           G      J          �    ��  B                 1      1      M   
 
4 
2p                 Y       �           2      2      S   h           \      _          �    F8 N
 d
 T	 4 2��p                 h       }          3      3      b   (           k      n       08   �    n P� ` \, T 20    +           4      4      q    B      :           6      6      w                                     �       �    Unknown exception                                   �       �                                *      �       �    bad array new length                                �       �                                 �      �      �                   .?AVbad_array_new_length@std@@     �               ����                      �      �                    .?AVbad_alloc@std@@     �              ����                      �      �                    .?AVexception@std@@     �               ����                      �      �    string too long     ����    ����        ��������                            B      �       �    Depth ScreenMotionVectors DenoiserMotionVectors Throughput StableRadianceBuffer StablePlanesHeader DenoiserDiffRadianceHitDist DenoiserOutDiffRadianceHitDist DenoiserSpecRadianceHitDist DenoiserOutSpecRadianceHitDist DenoiserOutValidation DenoiserViewspaceZ DenoiserNormalRoughness SecondarySurfacePositionNormal SecondarySurfaceRadiance AccumulatedRadiance OutputColor DenoiserDisocclusionThresholdMix CombinedHistoryClampRelax RRDiffuseAlbedo RRSpecAlbedo RRNormalsAndRoughness RRSpecMotionVectors ProcessedOutputColor TemporalFeedback1 TemporalFeedback2 LdrColor LdrColorScratch PreUIColor StablePlanesBuffer SurfaceData(GBuffer)                                             Q      �       �       �        �    vector too long                                       �                                                                       ����    @                   �                                               �      !                               $                           '                    ����    @                   �      !                                         �      -      *                         0                                   3      '                    ����    @                   �      -                   .?AV_Ref_count_base@std@@     �                         <                   ?               ����    @                   6      9                                         E      H      B                   .?AVFramebufferFactory@engine@donut@@     �                         K                   N               ����    @                   E      H                                         T      W      Q                   .?AV?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@     �                         Z                           ]      ?              ����    @                   T      W     �?  �?  �?          �?  �?  �?  �?   �   (   & 
�        std::exception::`vftable'    }      }  
    �   (   & 
�        std::bad_alloc::`vftable'    �      �  
    �   3   1 
�        std::bad_array_new_length::`vftable'     �      �  
    �   ;   9 
�        donut::engine::FramebufferFactory::`vftable'     �      �  
    �   Q   O 
�        std::_Ref_count_obj2<donut::engine::FramebufferFactory>::`vftable'               
 郝�C�(韾�F玌刦k網叜月o"0h�K蜌�(a鵄o碱詳�e眗洢鲜7r菃��(！
Z�+吀'魑蹽|貓靶I面/纍K岢R妱隂S蛁7F蠍I�諜L硣臂ep禭�?As贛嚤踖p禭飓D堦縵�6萪O�7铤	Jk揓K霵婬([�骠駱�'項j.r)=e麋'項j�搨吝p扯鋲+^{3�6闖;:尮湞\夂嫑�>gS4劫Fk{~AQ爻�嶥�)笖Hゆ7阒捭鎁
(B�锢4�q跔猕up泸睨袙"F�?&�K厨^絚IT葑D�:Q蚴0k浱U鞶琺� 端祆癜~t籴�垕gk嶀預棊膬/S;圾j硘嶀預棊膬� 圈� 墽烅樽V談靏�]i96慁7-�
Br�膇乚,逐hP鱨�'! 送c箩邆5>(H7и篼}箩邆5>螬�0|⒙徇�5>;ㄓ$7� �蹰kL�*胾�,�bi6瑽祲櫟倜3U欝餖
瀕爒慔T枟灚弻Y�周7�+c↙E|馸sNr徧觍攧搑徧觍攧搑蕪蝤.�滣笫�[$7铳F屬籌�}婬﹍娊^�7［Y1&鄙躔7颦硣K牏->灔旔k癙顨aS抳�$K�$蟊惺��R�縟%I栶賑?T�>�/�.9|f]{謑p]�T厑胖f]{謑p3b扞鞍VQ丽Υ貿�薪nN鵘J�^w�7嶦F{'yZ祼垩寯啦烹嘕-WV8o3,�4q胭9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o穗嘕-WV8o��&{-�9E\$L釉蕤;[純o速l窢蠯跻5R犵�喪-坓�(鬄�汬'这朸橑┏譁璵�M%>mb�2+x�Dk乭玣襰�=缣*≌槗Rjc�"$錀�釟瓘縺賠窽 夒嘕-WV8o;き8乿る嘕-WV8o;き8乿ち�5YJq覜垒�咞taR�,F_棢杻#Q`�G坕|级�喸朏菜{.鵪�
�薼醕3\S>瓗�?"盿劔噌碮z{4$蠳ㄗ罽愪�"�"j:5{
dd�a�:o)3�2彻(鹒 &[邩议wyQ E<礼\樢閣yQj;想=虡议wyQ5R犵�喪樢閣yQj;想=�:J硠志�rлn}�=沤�y�!R了5YJq覜垒�咞taR�,F_棢杻#Q`�G�.�?覡憵凡K劯蹍鹴aR�,F_棢杻#Q鴃
吚鵊嘕-WV8om�M%>mb雵J-WV8o.*~襠[
BG嶊4K鋆7篓<帏dd�a�:KiｎT襏簙� |4蒘�8萀D磷Kiv褍| 咞taR�,F_棢杻#Q�,g世偫�.�?覡coq�	a%咞taR�,F_棢杻#Q=泳脸餿眩袞炗贿�嶺�	��dd�a�:_棢杻#Q侙刔跛阉-坓�(鬄�汬'这柾﨏帲晗D� 蛮l�(詤鹴aR�,F_棢杻#Q薝鄫5繉Ｐ枮踊吖%騏�唖,dd�a�:镑肐溱磧Q搕g 钵殡嘕-WV8o额	hQ�)-坓�(鬄�/ｎ	蜍R潗幭恫V�l洯T顫悗隙睼逎悗隙睼�`o;镠�:蓰咨氖M�6揋g鱮�=E洼}A+|[皁��(涫阏琁礛鰉��輢A+|怱蘨俀捸K��H潗幭恫V�埭T鐒03*:1j�墣嗡=&蕟妍聦.6抱懘O囹x鹦暀?=涴切e4抾�5洇峖鹟败f*麫)搧f陀t�=孡�A挛Q頀e矗��8帏蛕缜F鷹嶮�о}浸[c�zi(Q蜥�>噲�全飡弎渔膲Dct腡旓e舛詖监芟譨u�縜塟�
�?=磮者V庼D俹T砵藚� 摉C6郧榛�<ll`~乕�%;讃m}燔{醚�"桶b`(先,4��;儗�%G>禡h��\&2�"�:邍A愦靮鸬2�>料C5��\&2�%ZZ�$为赞G刹~赣 "^惋砤��\&2渿#qM�5<A:蓰咨难栨熸� o0岚
殦"�:邍A愦靮鸬2�>料C5��\&2�=�1Y儧T�"�:邍A愦靮鸬2�>料C5��\&2�}nH镨�%ZZ�$为赞G刹~赣 "^惋砤揉^笵A傮躥噟c龌斱/x蛶;� タY�脌祦�
監�鵊�龤a将挮儢�        @comp.idy���   @feat.00����   @vol.md    ��   .drectve       P                .debug$S       H]              .debug$T       l                 .rdata         @       5G碚                         7          .text$mn       :      眡�     .debug$S                    .text$mn       }     変     .debug$S       D  d           .text$mn    	           _葓�     .debug$S    
   P         	    .text$mn       S     睪     .debug$S       �
  D           .text$x     
         ��:    .text$x              曍譧    .text$x              �c    .text$mn       <      .ズ     .debug$S       0  
           .text$mn       <      .ズ     .debug$S       L  
           .text$mn       !      :著�     .debug$S       <             .text$mn       2      X于     .debug$S       <             .text$mn       "       坼	     .debug$S       �             .text$mn       "       坼	     .debug$S       �             .text$mn       e      D远     .debug$S       $             .text$mn       [       荘�     .debug$S                     .text$mn              峦諡     .debug$S    !   0              .text$mn    "   }      庹V     .debug$S    #             "    .text$mn    $   �      吖     .debug$S    %   �          $    .text$mn    &   `      板@�     .debug$S    '   �         &    .text$mn    (   j      ゴ瓋     .debug$S    )   �         (    .text$mn    *   `      ,     .debug$S    +   �         *    .text$mn    ,          .B+�     .debug$S    -   �          ,    .text$mn    .         ��#     .debug$S    /   �          .    .text$mn    0         ��#     .debug$S    1   �          0    .text$mn    2          w�     .debug$S    3   �         2    .text$mn    4   +      擗}�     .debug$S    5   �          4    .text$mn    6   �      猜     .debug$S    7   �         6    .text$mn    8   B      贘S     .debug$S    9             8    .text$mn    :   B      贘S     .debug$S    ;            :    .text$mn    <   B      贘S     .debug$S    =   �          <    .text$mn    >   H       襶.      .debug$S    ?   �         >    .text$mn    @   �      3"�     .debug$S    A   �         @    .text$mn    B   �  d   鉬缠     .debug$S    C   j  �      B    .text$x     D         妬N翨    .text$x     E   *      ?}髑B    .text$x     F   *      它姐B    .text$x     G         B窽B    .text$x     H         B窽B    .text$x     I         �#4 B    .text$x     J          咇馎B    .text$mn    K   9       鋤�!     .debug$S    L   L         K    .text$mn    M   O     纭
     .debug$S    N   $  F       M    .text$mn    O          c淖�     .debug$S    P            O    .text$mn    Q   
       肷瞰     .debug$S    R   P  
       Q    .text$mn    S          �猴     .debug$S    T   ,         S    .text$mn    U          aJ鄔     .debug$S    V   �          U    .text$mn    W         �ッ     .debug$S    X   �          W    .text$mn    Y         �ッ     .debug$S    Z            Y    .text$mn    [   `     匮�5     .debug$S    \   �  B       [    .text$mn    ]         崪覩     .debug$S    ^   �          ]        \       >        x                �                �                �                �                �                                              /              P      0        j      ]        �      <        �          i�                    �              �      8                  i�                    '              L      .        q              �      :        �          i�                    �      U              W        2      [        �               �      *        �      &        �      ,              S        Z              �      2        �      B        +      K        r      @        �              �      "        �              �              �              4	      (        `	      $        �	      M        w
      Y        �
               e      6        �          i�                    �              �      	        4               y      Q        �      O              4        _          i�                    �              �      
        L      D        �      E              F        k      G        �      H        +      I        �      J        �              9              �               �               �               �           memcpy           memmove          $LN13       >    $LN5            $LN10       <    $LN7            $LN13       8    $LN10           $LN16       :    $LN3        U    $LN4        U    $LN3       W    $LN4        W    $LN144  `  [    $LN151      [    $LN39   `   *    $LN42       *    $LN39   `   &    $LN42       &    $LN10           $LN37       2    $LN1830 �  B    $LN1837     B    $LN27       @    $LN10           $LN125      "    $LN32   [       $LN35           $LN35   e       $LN38           $LN174  S      $LN177          $LN27       (    $LN50   �   $    $LN53       $    $LN89   O  M    $LN93       M    $LN3       Y    $LN4        Y    $LN31       6    $LN186          $LN8        4    $LN14   :       $LN17           .xdata      _          F┑@>        �      _    .pdata      `         X賦�>        �      `    .xdata      a          （亵        !      a    .pdata      b          T枨        J      b    .xdata      c          %蚘%<        r      c    .pdata      d         惻竗<        �      d    .xdata      e          （亵        �      e    .pdata      f         2Fb�        �      f    .xdata      g          %蚘%8              g    .pdata      h         惻竗8        7      h    .xdata      i          （亵        ]      i    .pdata      j         2Fb�        �      j    .xdata      k          %蚘%:        �      k    .pdata      l         惻竗:        �      l    .xdata      m          懐j濽        '      m    .pdata      n         Vbv鵘        W      n    .xdata      o          �9�W        �      o    .pdata      p         �1癢        �      p    .xdata      q          蔜-錥        �      q    .pdata      r         愶L[        (      r    .xdata      s         �qL僛        �      s    .pdata      t         ~蕉絒        �      t    .xdata      u         |盵        L      u    .pdata      v         瞚挨[        �      v    .xdata      w         S!熐[              w    .pdata      x         �o圼        r      x    .xdata      y          （亵*        �      y    .pdata      z         粻胄*        �      z    .xdata      {          （亵&              {    .pdata      |         粻胄&        D      |    .xdata      }         /
�        h      }    .pdata      ~         +eS�        �      ~    .xdata         	      �#荤        �          .xdata      �         j              �    .xdata      �          3狷         Y      �    .xdata      �          ��2        �      �    .pdata      �         噖sb2        �      �    .xdata      �         覮ol2        B      �    .pdata      �         Kd�2        �      �    .xdata      �         k�$�2        �      �    .pdata      �         熓2        J      �    .voltbl     �          �婏2    _volmd      �    .xdata      �   (      ��'B        �      �    .pdata      �         桴鏰B        �      �    .xdata      �   	      � )9B        Q      �    .xdata      �   .      `6B        �      �    .xdata      �   �       c#鏊B              �    .xdata      �          k笲        e      �    .pdata      �         �$剧B        �      �    .xdata      �          k笲        4       �    .pdata      �         瀪秇B        �       �    .xdata      �          k笲        !      �    .pdata      �         Vbv鵅        k!      �    .xdata      �          k笲        �!      �    .pdata      �         瀪秇B        :"      �    .voltbl     �          "鸅    _volmd      �    .xdata      �          瀫"@        �"      �    .pdata      �         9y@        �"      �    .xdata      �         /
�        #      �    .pdata      �         +eS�        U#      �    .xdata      �   	      �#荤        �#      �    .xdata      �         j        �#      �    .xdata      �          3狷         	$      �    .xdata      �         vQ9	"        D$      �    .pdata      �         A刄7"        "%      �    .xdata      �   	      �#荤"        �%      �    .xdata      �         j"        �&      �    .xdata      �          強S�"        �'      �    .xdata      �          （亵        �(      �    .pdata      �         愶L        �)      �    .xdata      �          （亵        d*      �    .pdata      �         弋�        �+      �    .xdata      �         鸝�        O-      �    .pdata      �         �迃        �-      �    .xdata      �   	      � )9        �-      �    .xdata      �         4�2�        %.      �    .xdata      �          ���        t.      �    .xdata      �         蚲7M(        �.      �    .pdata      �         s�+A(        �.      �    .xdata      �   	      �#荤(        $/      �    .xdata      �         j(        Z/      �    .xdata      �          x憵	(        �/      �    .xdata      �         vQ9	$        �/      �    .pdata      �         栝$        Q0      �    .xdata      �   	      �#荤$        �0      �    .xdata      �         j$        \1      �    .xdata      �          竷 n$        �1      �    .xdata      �          溤h)M        p2      �    .pdata      �         踣蔞M        3      �    .xdata      �   	      � )9M        �3      �    .xdata      �         jM        W4      �    .xdata      �           3%M        5      �    .xdata      �          �9�Y        �5      �    .pdata      �         �1癥        /6      �    .xdata      �         �酑6        �6      �    .pdata      �         寵Q6        �6      �    .xdata      �   	      �#荤6        )7      �    .xdata      �         j6        d7      �    .xdata      �          耚zu6        �7      �    .xdata      �          溤h)        �7      �    .pdata      �         鯊"�        �8      �    .xdata      �   	      � )9        �9      �    .xdata      �         伏a        �:      �    .xdata      �          謪$=        �;      �    .xdata      �          （亵4        �<      �    .pdata      �          ~�4        =      �    .xdata      �          �9�        R=      �    .pdata      �         礝
        �=      �    .rdata      �                      >     �    .rdata      �          �;�         ">      �    .rdata      �                      I>     �    .rdata      �                      `>     �    .rdata      �          �)         �>      �    .xdata$x    �                      �>      �    .xdata$x    �         虼�)         �>      �    .data$r     �   /      嶼�         �>      �    .xdata$x    �   $      4��         ?      �    .data$r     �   $      鎊=         m?      �    .xdata$x    �   $      銸E�         �?      �    .data$r     �   $      騏糡         �?      �    .xdata$x    �   $      4��         �?      �        @           .rdata      �          燺渾         2@      �    .data       �           烀�          X@      �        孈     �    .rdata      �                      矦     �    .rdata      �          �1鳳         蹳      �    .rdata      �          竉`�         鬇      �    .rdata      �          ?凇<         A      �    .rdata      �          Q鵿�         FA      �    .rdata      �          a��         dA      �    .rdata      �          暃榅         岮      �    .rdata      �          疓         碅      �    .rdata      �          a�=�         銩      �    .rdata      �           軺         B      �    .rdata      �          頮�8         GB      �    .rdata      �          憫�         zB      �    .rdata      �          ˇg               �    .rdata      �          q         薆      �    .rdata      �          iB{^         鰾      �    .rdata      �          )ガ         *C      �    .rdata      �          2��         WC      �    .rdata      �          G葍         C      �    .rdata      �   !       �<癄         滳      �    .rdata      �          闵�         袰      �    .rdata      �          %Y         �C      �    .rdata      �   
       脻�9         #D      �    .rdata      �          �,'�         CD      �    .rdata      �          敔茽         mD      �    .rdata      �          *o�         旸      �    .rdata      �          }嘛         紻      �    .rdata      �          �.锬         鉊      �    .rdata      �   	       茇,�         	E      �    .rdata      �          `?N4         $E      �    .rdata      �          钇�         HE      �    .rdata      �          垡�         fE      �    .rdata      �          a�         岴      �    .rdata      �   (                   糆     �    .rdata      �          IM         﨓      �    .rdata$r    �   $      'e%�         $F      �    .rdata$r    �         �          <F      �    .rdata$r    �                      RF      �    .rdata$r    �   $      Gv�:         hF      �    .rdata$r       $      'e%�         嘑          .rdata$r            }%B         烣         .rdata$r                         礔         .rdata$r      $      `         薋         .rdata$r      $      'e%�         闒         .rdata$r            �弾         
G         .rdata$r                         .G         .rdata$r      $      H衡�         OG         .data$rs      *      8V綊         yG         .rdata$r    	        �          橤      	   .rdata$r    
                     礕      
   .rdata$r      $      Gv�:         袵         .rdata$r      $      'e%�         鯣         .data$rs    
  6      ム蜀          H      
   .rdata$r            �          LH         .rdata$r                         tH         .rdata$r      $      Gv�:         淗         .rdata$r      $      'e%�         虷         .data$rs      O      t�         I         .rdata$r            }%B         UI         .rdata$r                         朓         .rdata$r      $      `         譏         .rdata               v靛�         !J             1J           .rdata               鞔         CJ         .rdata               _�         jJ         _fltused         .debug$S      4          �    .debug$S      4          �    .debug$S      @          �    .debug$S      H          �    .debug$S      `          �    .chks64       �                慗  ?lumaCoefficients@math@donut@@3U?$vector@M$02@12@B ?c_IdentityTransform@rt@nvrhi@@3QBMB ??_H@YAXPEAX_K1P6APEAX0@Z@Z ??_M@YAXPEAX_K1P6AX0@Z@Z ??2@YAPEAX_K@Z ??3@YAXPEAX_K@Z __std_terminate _invalid_parameter_noinfo_noreturn ?_Xlength_error@std@@YAXPEBD@Z __std_exception_copy __std_exception_destroy ??0exception@std@@QEAA@AEBV01@@Z ??1exception@std@@UEAA@XZ ?what@exception@std@@UEBAPEBDXZ ??_Gexception@std@@UEAAPEAXI@Z ??_Eexception@std@@UEAAPEAXI@Z ??0bad_alloc@std@@QEAA@AEBV01@@Z ??_Gbad_alloc@std@@UEAAPEAXI@Z ??_Ebad_alloc@std@@UEAAPEAXI@Z ??0bad_array_new_length@std@@QEAA@XZ ??1bad_array_new_length@std@@UEAA@XZ ??0bad_array_new_length@std@@QEAA@AEBV01@@Z ??_Gbad_array_new_length@std@@UEAAPEAXI@Z ??_Ebad_array_new_length@std@@UEAAPEAXI@Z ?_Throw_bad_array_new_length@std@@YAXXZ ?_Xlen_string@std@@YAXXZ ?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ?getFormatInfo@nvrhi@@YAAEBUFormatInfo@1@W4Format@1@@Z ??1TextureDesc@nvrhi@@QEAA@XZ ??1BufferDesc@nvrhi@@QEAA@XZ ??1_Ref_count_base@std@@UEAA@XZ ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ ??4?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAAAEAV01@$$QEAV01@@Z ?Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z ?IsUpdateRequired@RenderTargets@@QEBA_NU?$vector@I$01@math@donut@@0I@Z ?Clear@RenderTargets@@QEAAXPEAVICommandList@nvrhi@@@Z ??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ ??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ??1?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ ??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z ??1FramebufferFactory@engine@donut@@UEAA@XZ ??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ ?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z ?_Xlength@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ ?GetFramebuffer@FramebufferFactory@engine@donut@@UEAAPEAVIFramebuffer@nvrhi@@AEBUTextureSubresourceSet@5@@Z ??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z ??_EFramebufferFactory@engine@donut@@UEAAPEAXI@Z ??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@0@0AEBV10@@Z ??1?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@UEAA@XZ ?_Destroy@?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@EEAAXXZ ?_Delete_this@?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@EEAAXXZ ??_G?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@UEAAPEAXI@Z ??_E?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@UEAAPEAXI@Z ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ?dtor$0@?0???0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$0@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA ?dtor$32@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA ?dtor$35@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA ?dtor$36@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA ?dtor$38@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA ?dtor$50@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA ?dtor$53@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA ?dtor$7@?0???0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA ?dtor$8@?0???0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z@4HA _CxxThrowException __CxxFrameHandler4 __GSHandlerCheck_EH4 __security_check_cookie $unwind$??_H@YAXPEAX_K1P6APEAX0@Z@Z $pdata$??_H@YAXPEAX_K1P6APEAX0@Z@Z $unwind$??0exception@std@@QEAA@AEBV01@@Z $pdata$??0exception@std@@QEAA@AEBV01@@Z $unwind$??_Gexception@std@@UEAAPEAXI@Z $pdata$??_Gexception@std@@UEAAPEAXI@Z $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z $pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z $pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z $unwind$?_Throw_bad_array_new_length@std@@YAXXZ $pdata$?_Throw_bad_array_new_length@std@@YAXXZ $unwind$?_Xlen_string@std@@YAXXZ $pdata$?_Xlen_string@std@@YAXXZ $unwind$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$0$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$1$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $chain$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $pdata$2$?assign@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z $unwind$??1TextureDesc@nvrhi@@QEAA@XZ $pdata$??1TextureDesc@nvrhi@@QEAA@XZ $unwind$??1BufferDesc@nvrhi@@QEAA@XZ $pdata$??1BufferDesc@nvrhi@@QEAA@XZ $unwind$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??4?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAAAEAV01@$$QEAV01@@Z $pdata$??4?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAAAEAV01@$$QEAV01@@Z $chain$0$??4?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAAAEAV01@$$QEAV01@@Z $pdata$0$??4?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAAAEAV01@$$QEAV01@@Z $chain$1$??4?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAAAEAV01@$$QEAV01@@Z $pdata$1$??4?$shared_ptr@VFramebufferFactory@engine@donut@@@std@@QEAAAEAV01@$$QEAV01@@Z $unwind$?Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z $pdata$?Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z $cppxdata$?Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z $stateUnwindMap$?Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z $ip2state$?Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z $unwind$?dtor$50@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $pdata$?dtor$50@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $unwind$?dtor$32@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $pdata$?dtor$32@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $unwind$?dtor$53@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $pdata$?dtor$53@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $unwind$?dtor$35@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $pdata$?dtor$35@?0??Init@RenderTargets@@QEAAXPEAVIDevice@nvrhi@@U?$vector@I$01@math@donut@@1_N2H@Z@4HA $unwind$?Clear@RenderTargets@@QEAAXPEAVICommandList@nvrhi@@@Z $pdata$?Clear@RenderTargets@@QEAAXPEAVICommandList@nvrhi@@@Z $unwind$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $pdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $cppxdata$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $stateUnwindMap$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $ip2state$??1?$RefCountPtr@VIDevice@nvrhi@@@nvrhi@@QEAA@XZ $unwind$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $pdata$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $cppxdata$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $stateUnwindMap$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $ip2state$??1?$list@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@2@@std@@QEAA@XZ $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ $unwind$??1?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ $pdata$??1?$_Hash@V?$_Umap_traits@UTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@V?$_Uhash_compare@UTextureSubresourceSet@nvrhi@@U?$hash@UTextureSubresourceSet@nvrhi@@@std@@U?$equal_to@UTextureSubresourceSet@nvrhi@@@4@@std@@V?$allocator@U?$pair@$$CBUTextureSubresourceSet@nvrhi@@V?$RefCountPtr@VIFramebuffer@nvrhi@@@2@@std@@@5@$0A@@std@@@std@@QEAA@XZ $unwind$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $pdata$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $cppxdata$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $stateUnwindMap$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $ip2state$??0FramebufferFactory@engine@donut@@QEAA@PEAVIDevice@nvrhi@@@Z $unwind$??1FramebufferFactory@engine@donut@@UEAA@XZ $pdata$??1FramebufferFactory@engine@donut@@UEAA@XZ $cppxdata$??1FramebufferFactory@engine@donut@@UEAA@XZ $stateUnwindMap$??1FramebufferFactory@engine@donut@@UEAA@XZ $ip2state$??1FramebufferFactory@engine@donut@@UEAA@XZ $unwind$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $pdata$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $cppxdata$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $stateUnwindMap$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $ip2state$??1?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@QEAA@XZ $unwind$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $pdata$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $cppxdata$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $stateUnwindMap$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $ip2state$?_Clear_and_reserve_geometric@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAX_K@Z $unwind$?_Xlength@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ $pdata$?_Xlength@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@CAXXZ $unwind$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $pdata$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $cppxdata$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $stateUnwindMap$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $ip2state$??_GFramebufferFactory@engine@donut@@UEAAPEAXI@Z $unwind$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $pdata$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $cppxdata$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $stateUnwindMap$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $ip2state$??$_Assign_counted_range@PEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@?$vector@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@V?$allocator@V?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@@std@@@std@@AEAAXPEBV?$RefCountPtr@VITexture@nvrhi@@@nvrhi@@_K@Z $unwind$??_G?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@UEAAPEAXI@Z $pdata$??_G?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@UEAAPEAXI@Z $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z $pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ??_7exception@std@@6B@ ??_C@_0BC@EOODALEL@Unknown?5exception@ ??_7bad_alloc@std@@6B@ ??_7bad_array_new_length@std@@6B@ ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ _TI3?AVbad_array_new_length@std@@ _CTA3?AVbad_array_new_length@std@@ ??_R0?AVbad_array_new_length@std@@@8 _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 ??_R0?AVbad_alloc@std@@@8 _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 ??_R0?AVexception@std@@@8 _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 ??_7type_info@@6B@ ??_C@_0BA@JFNIOLAK@string?5too?5long@ ?AllSubresources@nvrhi@@3UTextureSubresourceSet@1@B ?EntireBuffer@nvrhi@@3UBufferRange@1@B ??_7FramebufferFactory@engine@donut@@6B@ ??_C@_05BOMFGPKE@Depth@ ??_C@_0BE@HKEKDLMK@ScreenMotionVectors@ ??_C@_0BG@GLHHJECA@DenoiserMotionVectors@ ??_C@_0L@DJAELHEC@Throughput@ ??_C@_0BF@DMOHPCBF@StableRadianceBuffer@ ??_C@_0BD@HNGPKNAF@StablePlanesHeader@ ??_C@_0BM@LHHBMPLJ@DenoiserDiffRadianceHitDist@ ??_C@_0BP@HMMLJGCO@DenoiserOutDiffRadianceHitDist@ ??_C@_0BM@GFNKFFDG@DenoiserSpecRadianceHitDist@ ??_C@_0BP@KOGAAMKB@DenoiserOutSpecRadianceHitDist@ ??_C@_0BG@PGEHNPBD@DenoiserOutValidation@ ??_C@_0BD@CGJAJADB@DenoiserViewspaceZ@ ??_C@_0BI@CNONMNNK@DenoiserNormalRoughness@ ??_C@_0BP@MIINBBCG@SecondarySurfacePositionNormal@ ??_C@_0BJ@LKJFOPFJ@SecondarySurfaceRadiance@ ??_C@_0BE@GGDBINEA@AccumulatedRadiance@ ??_C@_0M@HOCCFNH@OutputColor@ ??_C@_0CB@CFCMPJMJ@DenoiserDisocclusionThresholdMi@ ??_C@_0BK@IGBGIICO@CombinedHistoryClampRelax@ ??_C@_0BA@EKGBKILG@RRDiffuseAlbedo@ ??_C@_0N@MJGDCELO@RRSpecAlbedo@ ??_C@_0BG@KHPBGCKJ@RRNormalsAndRoughness@ ??_C@_0BE@FAOMMEOG@RRSpecMotionVectors@ ??_C@_0BF@CJEBAGK@ProcessedOutputColor@ ??_C@_0BC@HHCGENKD@TemporalFeedback1@ ??_C@_0BC@FMALBOGA@TemporalFeedback2@ ??_C@_08LDNKDDIN@LdrColor@ ??_C@_0BA@CHAKILMK@LdrColorScratch@ ??_C@_0L@HJAHIIPN@PreUIColor@ ??_C@_0BD@MCOAOEEL@StablePlanesBuffer@ ??_C@_0BF@DHPFDEBF@SurfaceData?$CIGBuffer?$CJ@ ??_7?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@6B@ ??_C@_0BA@FOIKENOD@vector?5too?5long@ ??_R4exception@std@@6B@ ??_R3exception@std@@8 ??_R2exception@std@@8 ??_R1A@?0A@EA@exception@std@@8 ??_R4bad_alloc@std@@6B@ ??_R3bad_alloc@std@@8 ??_R2bad_alloc@std@@8 ??_R1A@?0A@EA@bad_alloc@std@@8 ??_R4bad_array_new_length@std@@6B@ ??_R3bad_array_new_length@std@@8 ??_R2bad_array_new_length@std@@8 ??_R1A@?0A@EA@bad_array_new_length@std@@8 ??_R0?AV_Ref_count_base@std@@@8 ??_R3_Ref_count_base@std@@8 ??_R2_Ref_count_base@std@@8 ??_R1A@?0A@EA@_Ref_count_base@std@@8 ??_R4FramebufferFactory@engine@donut@@6B@ ??_R0?AVFramebufferFactory@engine@donut@@@8 ??_R3FramebufferFactory@engine@donut@@8 ??_R2FramebufferFactory@engine@donut@@8 ??_R1A@?0A@EA@FramebufferFactory@engine@donut@@8 ??_R4?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@6B@ ??_R0?AV?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@@8 ??_R3?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@8 ??_R2?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@8 ??_R1A@?0A@EA@?$_Ref_count_obj2@VFramebufferFactory@engine@donut@@@std@@8 __real@3f800000 __security_cookie __xmm@00000000000000003f8000003f800000 __xmm@3f8000003f8000003f8000003f800000 