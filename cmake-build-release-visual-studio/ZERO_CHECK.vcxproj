<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{58F23B37-BE28-30E3-A181-C3BE205A48B0}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\e3a505b6f528ac900a17054adb207b3e\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/RTXPT/cmake-build-release-visual-studio/RTXPathTracing.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDependentOption.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CPack.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CPackComponent.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CTest.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CTestTargets.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CTestUseLaunchers.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCXXSymbolExists.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckStructHasMember.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckSymbolExists.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckTypeSize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\DartConfiguration.tcl.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FetchContent.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FetchContent\CMakeLists.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindOpenMP.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindThreads.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\GenerateExportHeader.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\exportheader.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Templates\CPackConfig.cmake.in;D:\RTXPT\CMakeLists.txt;D:\RTXPT\External\CMakeLists.txt;D:\RTXPT\External\Donut\CMakeLists.txt;D:\RTXPT\External\Donut\ShaderMake\CMakeLists.txt;D:\RTXPT\External\Donut\cmake\FindSTREAMLINE.cmake;D:\RTXPT\External\Donut\compileshaders.cmake;D:\RTXPT\External\Donut\donut-app.cmake;D:\RTXPT\External\Donut\donut-core.cmake;D:\RTXPT\External\Donut\donut-engine.cmake;D:\RTXPT\External\Donut\donut-render.cmake;D:\RTXPT\External\Donut\nvrhi\CMakeLists.txt;D:\RTXPT\External\Donut\nvrhi\cmake\FindNVAPI.cmake;D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\CMakeLists.txt;D:\RTXPT\External\Donut\shaders\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\glfw\CMake\glfw3.pc.in;D:\RTXPT\External\Donut\thirdparty\glfw\CMake\glfw3Config.cmake.in;D:\RTXPT\External\Donut\thirdparty\glfw\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\glfw\src\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\imgui.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\PreventInBuildInstalls.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\PreventInSourceBuilds.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp\src\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\version.in;D:\RTXPT\External\Donut\thirdparty\miniz\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\miniz\miniz.pc.in;D:\RTXPT\External\Nrd\CMakeLists.txt;D:\RTXPT\External\Omm\CMakeLists.txt;D:\RTXPT\External\Omm\external\CMakeLists.txt;D:\RTXPT\External\Omm\external\glm\CMakeLists.txt;D:\RTXPT\External\Omm\external\glm\glm\CMakeLists.txt;D:\RTXPT\External\Omm\external\lz4\build\cmake\CMakeLists.txt;D:\RTXPT\External\Omm\external\lz4\lib\liblz4.pc.in;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\CMakeLists.txt;D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi\CMakeLists.txt;D:\RTXPT\External\Omm\libraries\omm-lib\CMakeLists.txt;D:\RTXPT\External\Omm\support\scripts\CMakeLists.txt;D:\RTXPT\External\Rtxdi\CMakeLists.txt;D:\RTXPT\External\Streamline\CMakeLists.txt;D:\RTXPT\External\cxxopts\CMakeLists.txt;D:\RTXPT\External\cxxopts\cmake\cxxopts.cmake;D:\RTXPT\External\cxxopts\include\CMakeLists.txt;D:\RTXPT\Rtxpt\CMakeLists.txt;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeSystem.cmake;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-src\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\src\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\src\lib_json\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\include\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\miniz\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\ShaderMake\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\shaders\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\streamline\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Nrd\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-build\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\glm\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\glm\glm\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\lz4\build\cmake\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\xxHash\cmake_unofficial\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-lib\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-gpu-nvrhi\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\support\scripts\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\cxxopts\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\cxxopts\include\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT -BD:/RTXPT/cmake-build-release-visual-studio --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/RTXPT/cmake-build-release-visual-studio/RTXPathTracing.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\BasicConfigVersion-SameMajorVersion.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeDependentOption.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CPack.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CPackComponent.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CTest.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CTestTargets.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CTestUseLaunchers.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCXXSymbolExists.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckIncludeFile.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckLibraryExists.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckStructHasMember.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckSymbolExists.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CheckTypeSize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\DartConfiguration.tcl.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FetchContent.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FetchContent\CMakeLists.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindOpenMP.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\FindThreads.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\GNUInstallDirs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\GenerateExportHeader.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\WriteBasicConfigVersionFile.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\exportheader.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Templates\CPackConfig.cmake.in;D:\RTXPT\CMakeLists.txt;D:\RTXPT\External\CMakeLists.txt;D:\RTXPT\External\Donut\CMakeLists.txt;D:\RTXPT\External\Donut\ShaderMake\CMakeLists.txt;D:\RTXPT\External\Donut\cmake\FindSTREAMLINE.cmake;D:\RTXPT\External\Donut\compileshaders.cmake;D:\RTXPT\External\Donut\donut-app.cmake;D:\RTXPT\External\Donut\donut-core.cmake;D:\RTXPT\External\Donut\donut-engine.cmake;D:\RTXPT\External\Donut\donut-render.cmake;D:\RTXPT\External\Donut\nvrhi\CMakeLists.txt;D:\RTXPT\External\Donut\nvrhi\cmake\FindNVAPI.cmake;D:\RTXPT\External\Donut\nvrhi\thirdparty\DirectX-Headers\CMakeLists.txt;D:\RTXPT\External\Donut\shaders\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\glfw\CMake\glfw3.pc.in;D:\RTXPT\External\Donut\thirdparty\glfw\CMake\glfw3Config.cmake.in;D:\RTXPT\External\Donut\thirdparty\glfw\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\glfw\src\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\imgui.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\PreventInBuildInstalls.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp\include\PreventInSourceBuilds.cmake;D:\RTXPT\External\Donut\thirdparty\jsoncpp\src\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\src\lib_json\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\jsoncpp\version.in;D:\RTXPT\External\Donut\thirdparty\miniz\CMakeLists.txt;D:\RTXPT\External\Donut\thirdparty\miniz\miniz.pc.in;D:\RTXPT\External\Nrd\CMakeLists.txt;D:\RTXPT\External\Omm\CMakeLists.txt;D:\RTXPT\External\Omm\external\CMakeLists.txt;D:\RTXPT\External\Omm\external\glm\CMakeLists.txt;D:\RTXPT\External\Omm\external\glm\glm\CMakeLists.txt;D:\RTXPT\External\Omm\external\lz4\build\cmake\CMakeLists.txt;D:\RTXPT\External\Omm\external\lz4\lib\liblz4.pc.in;D:\RTXPT\External\Omm\external\xxHash\cmake_unofficial\CMakeLists.txt;D:\RTXPT\External\Omm\libraries\omm-gpu-nvrhi\CMakeLists.txt;D:\RTXPT\External\Omm\libraries\omm-lib\CMakeLists.txt;D:\RTXPT\External\Omm\support\scripts\CMakeLists.txt;D:\RTXPT\External\Rtxdi\CMakeLists.txt;D:\RTXPT\External\Streamline\CMakeLists.txt;D:\RTXPT\External\cxxopts\CMakeLists.txt;D:\RTXPT\External\cxxopts\cmake\cxxopts.cmake;D:\RTXPT\External\cxxopts\include\CMakeLists.txt;D:\RTXPT\Rtxpt\CMakeLists.txt;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeCXXCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeRCCompiler.cmake;D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\4.0.2\CMakeSystem.cmake;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-src\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\RTXPT\cmake-build-release-visual-studio\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\src\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\src\lib_json\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\jsoncpp\include\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\glfw\src\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\thirdparty\miniz\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\ShaderMake\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\nvrhi\thirdparty\DirectX-Headers\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Donut\shaders\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\streamline\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Nrd\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-build\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\glm\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\glm\glm\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\lz4\build\cmake\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\external\xxHash\cmake_unofficial\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-lib\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\libraries\omm-gpu-nvrhi\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Omm\support\scripts\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\Rtxdi\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\cxxopts\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\External\cxxopts\include\CMakeFiles\generate.stamp;D:\RTXPT\cmake-build-release-visual-studio\Rtxpt\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>