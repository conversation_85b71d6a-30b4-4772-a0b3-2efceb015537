<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{85AFA472-BD39-382A-836E-0079CBFF8F1A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild -BD:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\PatchInfo.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\RepositoryInfo.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\UpdateInfo.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\cfgcmd.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitclone.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitupdate.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\mkdirs.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\4.0.2\CMakeSystem.cmake;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\tmp\mathlib-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\ZERO_CHECK.vcxproj">
      <Project>{B8019F5B-59B3-370B-ACF0-37CAA676E16E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate.vcxproj">
      <Project>{427BFB5E-D558-3AE5-BA0E-16647D3F721B}</Project>
      <Name>mathlib-populate</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>