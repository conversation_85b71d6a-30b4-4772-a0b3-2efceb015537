<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{427BFB5E-D558-3AE5-BA0E-16647D3F721B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>mathlib-populate</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-mkdir.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Creating directories for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -Dcfgdir=/Debug -P D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/tmp/mathlib-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing download step (git clone) for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\RTXPT\cmake-build-release-visual-studio\_deps
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/tmp/mathlib-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\mathlib-populate-gitinfo.txt;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-update.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing update step for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-src
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/tmp/mathlib-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\tmp\mathlib-populate-gitupdate.cmake;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\mathlib-populate-update-info.txt;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-patch.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No patch step for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\mathlib-populate-patch-info.txt;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-configure.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No configure step for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\tmp\mathlib-populate-cfgcmd.txt;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-build.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No build step for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-install.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No install step for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\da37ff0650432dfc3ba0cfe7adef9dc1\mathlib-populate-test.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No test step for 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-build
if %errorlevel% neq 0 goto :cmEnd
D:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\96776cab38c038229c55a32c344e9d26\mathlib-populate-complete.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Completed 'mathlib-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E make_directory D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/CMakeFiles/Debug/mathlib-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -E touch D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/mathlib-populate-prefix/src/mathlib-populate-stamp/Debug/mathlib-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-install;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-mkdir;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-download;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-update;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-patch;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-configure;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-build;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\src\mathlib-populate-stamp\Debug\mathlib-populate-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\Debug\mathlib-populate-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\2302acab6173ef46e0ccec4fb29a1011\mathlib-populate.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\Debug\mathlib-populate-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\mathlib-populate</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\bin\cmake.exe" -SD:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild -BD:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild --check-stamp-file D:/RTXPT/cmake-build-release-visual-studio/_deps/mathlib-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\PatchInfo.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\RepositoryInfo.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\UpdateInfo.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\cfgcmd.txt.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitclone.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitupdate.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\mkdirs.cmake.in;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\JetBrains\CLion 2025.2\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\4.0.2\CMakeSystem.cmake;D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\mathlib-populate-prefix\tmp\mathlib-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\CMakeFiles\mathlib-populate">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\RTXPT\cmake-build-release-visual-studio\_deps\mathlib-subbuild\ZERO_CHECK.vcxproj">
      <Project>{B8019F5B-59B3-370B-ACF0-37CAA676E16E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>