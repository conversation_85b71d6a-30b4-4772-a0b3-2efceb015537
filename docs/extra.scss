// NOTE: Please use this file to perform modifications on default style sheets.
//
// You need to install the official Sass CLI tool:
// npm install -g sass
//
// Run this command to regenerate extra.css after you're finished with changes:
// sass --style=compressed extra.scss extra.css
//
// Alternatively you can use online services to regenerate extra.css.


// Default text color for page contents
$default-text-color: hsl(0,0%,30%);

// Page header, footer, table rows, inline codes and definition lists
$header-footer-background-color: hsl(0,0%,95%);

// Page header, footer links and navigation bar background
$header-footer-link-color: hsl(0,0%,40%);

// Doxygen navigation bar links
$navbar-link-color: $header-footer-background-color;

// Page content background color
$content-background-color: hsl(0,0%,100%);

// Bold, italic, h1, h2, ... and table of contents
$heading-color: hsl(0,0%,10%);

// Function, enum and macro definition separator
$def-separator-color: $header-footer-background-color;

// Base color hue
$base-hue: 24;

// Default color used for links
$default-link-color: hsl($base-hue,100%,50%);

// Doxygen navigation bar active tab
$tab-text-color: hsl(0,0%,100%);
$tab-background-color1: $default-link-color;
$tab-background-color2: lighten(adjust-hue($tab-background-color1, 10), 10%);

// Table borders
$default-border-color: $default-link-color;

// Table header
$table-text-color: $tab-text-color;
$table-background-color1: $tab-background-color1;
$table-background-color2: $tab-background-color2;

// Table of contents, data structure index and prototypes
$toc-background-color1: hsl(0,0%,90%);
$toc-background-color2: lighten($toc-background-color1, 5%);

// Function prototype parameters color
$prototype-param-color: darken($default-link-color, 25%);

// Message box color: note, pre, post and invariant
$box-note-color: hsl(103,80%,85%);

// Message box color: warning and attention
$box-warning-color: hsl(34,80%,85%);

// Message box color: deprecated and bug
$box-bug-color: hsl(333,80%,85%);

// Message box color: todo and test
$box-todo-color: hsl(200,80%,85%);

// Message box helper function
@mixin message-box($base-color){
	background:linear-gradient(to bottom,lighten($base-color, 5%) 0%,$base-color 100%);
	box-shadow:inset 0 0 32px darken($base-color, 5%);
	color:darken($base-color, 67%);
	border:2px solid desaturate(darken($base-color, 10%), 20%);
}

.sm-dox,.sm-dox a,.sm-dox a:focus,.sm-dox a:active,.sm-dox a:hover,.sm-dox a.highlighted,.sm-dox ul a:hover {
	background:none;
	text-shadow:none;
}

.sm-dox a span.sub-arrow {
	border-color:$navbar-link-color transparent transparent transparent;
}

.sm-dox a span.sub-arrow:active,.sm-dox a span.sub-arrow:focus,.sm-dox a span.sub-arrow:hover,.sm-dox a:hover span.sub-arrow {
	border-color:$default-link-color transparent transparent transparent;
}

.sm-dox ul a span.sub-arrow:active,.sm-dox ul a span.sub-arrow:focus,.sm-dox ul a span.sub-arrow:hover,.sm-dox ul a:hover span.sub-arrow {
	border-color:transparent transparent transparent $default-link-color;
}

.sm-dox ul a:hover {
	background:$header-footer-link-color;
	text-shadow:none;
}

.sm-dox ul.sm-nowrap a {
	color:$default-text-color;
	text-shadow:none;
}

#main-nav,#main-menu,#main-menu a,#main-menu a:visited,#main-menu a:hover,#main-menu li,.memdoc,dl.reflist dd,div.toc li,.ah,span.lineno,span.lineno a,span.lineno a:hover,.note code,.pre code,.post code,.invariant code,.warning code,.attention code,.deprecated code,.bug code,.todo code,.test code,.doxtable code,.markdownTable code {
	background:none;
}

#titlearea,.footer,.contents,div.header,.memdoc,table.doxtable td,table.doxtable th,table.markdownTable td,table.markdownTable th,hr,.memSeparator {
	border:none;
}

#main-menu a,#main-menu a:visited,#main-menu a:hover,#main-menu li,.reflist dt a.el,.levels span,.directory .levels span {
	text-shadow:none;
}

.memdoc,dl.reflist dd {
	box-shadow:none;
}

div.headertitle,.note code,.pre code,.post code,.invariant code,.warning code,.attention code,.deprecated code,.bug code,.todo code,.test code,table.doxtable code,table.markdownTable code {
	padding:0;
}

#nav-path,.directory .levels,span.lineno {
	display:none;
}

html,#titlearea,.footer,tr.even,.directory tr.even,.doxtable tr:nth-child(even),tr.markdownTableBody:nth-child(even),.mdescLeft,.mdescRight,.memItemLeft,.memItemRight,code,.markdownTableRowEven {
	background:$header-footer-background-color;
}

body {
	color:$default-text-color;
}

div.title {
    font-size: 170%;
    margin: 1em 0 0.5em 0;
}

h1,h2,h2.groupheader,h3,div.toc h3,h4,h5,h6,strong,em {
	color:$heading-color;
	border-bottom:none;
}

h1 {
	padding-top:0.5em;
	font-size:150%;
}

h2 {
	padding-top:0.5em;
	margin-bottom:0;
	font-size:130%;
}

h3 {
	padding-top:0.5em;
	margin-bottom:0;
	font-size:110%;
}

.glfwheader {
	font-size:16px;
	min-height:64px;
	max-width:920px;
	padding:0 32px;
	margin:0 auto;

	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: flex-start;
	align-items: center;
	align-content: stretch;
}

#glfwhome {
	line-height:64px;
	padding-right:48px;
	color:$header-footer-link-color;
	font-size:2.5em;
	background:url("https://www.glfw.org/css/arrow.png") no-repeat right;
}

.glfwnavbar {
	list-style-type:none;
	margin:0 0 0 auto;
	float:right;
}

#glfwhome,.glfwnavbar li {
	float:left;
}

.glfwnavbar a,.glfwnavbar a:visited {
	line-height:64px;
	margin-left:2em;
	display:block;
	color:$header-footer-link-color;
}

.glfwnavbar {
	padding-left: 0;
}

#glfwhome,.glfwnavbar a,.glfwnavbar a:visited {
	transition:.35s ease;
}

#titlearea,.footer {
	color:$header-footer-link-color;
}

address.footer {
	text-align:center;
	padding:2em;
	margin-top:3em;
}

#top {
	background:$header-footer-link-color;
}

#main-nav {
	max-width:960px;
	margin:0 auto;
	font-size:13px;
}

#main-menu {
	max-width:920px;
	margin:0 auto;
	font-size:13px;
}

.memtitle {
	display:none;
}

.memproto,.memname {
	font-weight:bold;
	text-shadow:none;
}

#main-menu {
	min-height:36px;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	justify-content: flex-start;
	align-items: center;
	align-content: stretch;
}

#main-menu a:focus {
    outline-style: none;
}

#main-menu a,#main-menu a:visited,#main-menu a:hover,#main-menu li {
	color:$navbar-link-color;
}

#main-menu li ul.sm-nowrap li a {
	color:$default-text-color;
}

#main-menu li ul.sm-nowrap li a:hover {
	color:$default-link-color;
}

#main-menu > li:last-child {
	margin: 0 0 0 auto;
}

.contents {
	min-height:590px;
}

div.contents,div.header {
	max-width:920px;
	margin:0 auto;
	padding:0 32px;
	background:$content-background-color none;
}

table.doxtable th,table.markdownTable th,dl.reflist dt {
	background:linear-gradient(to bottom,$table-background-color2 0%,$table-background-color1 100%);
	box-shadow:inset 0 0 32px $table-background-color1;
	text-shadow:0 -1px 1px darken($table-background-color1, 15%);
	text-align:left;
	color:$table-text-color;
}

dl.reflist dt a.el {
	color:$default-link-color;
	padding:.2em;
	border-radius:4px;
	background-color:lighten($default-link-color, 40%);
}

div.toc {
	float:right;
	width:35%;
}

@media screen and (max-width:600px) {
	div.toc {
		float:none;
		width:inherit;
		margin:0;
	}
}

div.toc h3 {
	font-size:1.17em;
}

div.toc ul {
	padding-left:1.5em;
}

div.toc li {
	font-size:1em;
	padding-left:0;
	list-style-type:disc;
}

div.toc {
    li.level2, li.level3 {
        margin-left:0.5em;
    }
}

div.toc,.memproto,div.qindex,div.ah {
	background:linear-gradient(to bottom,$toc-background-color2 0%,$toc-background-color1 100%);
	box-shadow:inset 0 0 32px $toc-background-color1;
	text-shadow:0 1px 1px lighten($toc-background-color2, 10%);
	color:$heading-color;
	border:2px solid $toc-background-color1;
	border-radius:4px;
}

.paramname {
	color:$prototype-param-color;
}

dl.reflist dt {
	border:2px solid $default-border-color;
	border-top-left-radius:4px;
	border-top-right-radius:4px;
	border-bottom:none;
}

dl.reflist dd {
	border:2px solid $default-border-color;
	border-bottom-right-radius:4px;
	border-bottom-left-radius:4px;
	border-top:none;
}

table.doxtable,table.markdownTable {
	border-collapse:inherit;
	border-spacing:0;
	border:2px solid $default-border-color;
	border-radius:4px;
}

a,a:hover,a:visited,a:visited:hover,.contents a:visited,.el,a.el:visited,#glfwhome:hover,#main-menu a:hover,span.lineno a:hover {
	color:$default-link-color;
	text-decoration:none;
}

div.directory {
	border-collapse:inherit;
	border-spacing:0;
	border:2px solid $default-border-color;
	border-radius:4px;
}

hr,.memSeparator {
	height:2px;
	background:linear-gradient(to right,$def-separator-color 0%,darken($def-separator-color, 10%) 50%,$def-separator-color 100%);
}

dl.note,dl.pre,dl.post,dl.invariant {
	@include message-box($box-note-color);
}

dl.warning,dl.attention {
	@include message-box($box-warning-color);
}

dl.deprecated,dl.bug {
	@include message-box($box-bug-color);
}

dl.todo,dl.test {
	@include message-box($box-todo-color);
}

dl.note,dl.pre,dl.post,dl.invariant,dl.warning,dl.attention,dl.deprecated,dl.bug,dl.todo,dl.test {
	border-radius:4px;
	padding:1em;
	text-shadow:0 1px 1px hsl(0,0%,100%);
	margin:1em 0;
}

.note a,.pre a,.post a,.invariant a,.warning a,.attention a,.deprecated a,.bug a,.todo a,.test a,.note a:visited,.pre a:visited,.post a:visited,.invariant a:visited,.warning a:visited,.attention a:visited,.deprecated a:visited,.bug a:visited,.todo a:visited,.test a:visited {
	color:inherit;
}

div.line {
	line-height:inherit;
}

div.fragment,pre.fragment {
	background:hsl(0,0%,95%);
	border-radius:4px;
	border:none;
	padding:1em;
	overflow:auto;
	border-left:4px solid hsl(0,0%,80%);
	margin:1em 0;
}

.lineno a,.lineno a:visited,.line,pre.fragment {
	color:$default-text-color;
}

span.preprocessor,span.comment {
	color:hsl(193,100%,30%);
}

a.code,a.code:visited {
	color:hsl(18,100%,45%);
}

span.keyword,span.keywordtype,span.keywordflow {
	color:darken($default-text-color, 5%);
	font-weight:bold;
}

span.stringliteral {
	color:hsl(261,100%,30%);
}

code {
	padding:.1em;
	border-radius:4px;
}
