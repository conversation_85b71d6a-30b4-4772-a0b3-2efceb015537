{"asset": {"generator": "Khronos glTF Blender I/O v3.4.50", "version": "2.0"}, "extensionsUsed": ["KHR_materials_transmission"], "scene": 0, "scenes": [{"name": "Scene", "nodes": [0]}], "nodes": [{"mesh": 0, "name": "subset_7.004", "scale": [1, 1, 1.2000000476837158]}], "materials": [{"extensions": {"KHR_materials_transmission": {"transmissionFactor": 1}}, "name": "window_glass", "pbrMetallicRoughness": {"metallicFactor": 0, "roughnessFactor": 0}}, {"name": "window_wood_painted", "pbrMetallicRoughness": {"baseColorFactor": [0.0027596501167863607, 0.05226895585656166, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}], "meshes": [{"name": "subset_7.003", "primitives": [{"attributes": {"POSITION": 0, "TEXCOORD_0": 1, "NORMAL": 2, "TANGENT": 3}, "indices": 4, "material": 0}, {"attributes": {"POSITION": 5, "TEXCOORD_0": 6, "NORMAL": 7, "TANGENT": 8}, "indices": 4, "material": 0}, {"attributes": {"POSITION": 9, "TEXCOORD_0": 10, "NORMAL": 11, "TANGENT": 12}, "indices": 13, "material": 1}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 6, "max": [0.022909492254257202, 1.1308543682098389, 1.1562691926956177], "min": [0.004669934511184692, -1.1457765102386475, -1.1524841785430908], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 6, "type": "VEC2"}, {"bufferView": 2, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 3, "componentType": 5126, "count": 6, "type": "VEC4"}, {"bufferView": 4, "componentType": 5123, "count": 6, "type": "SCALAR"}, {"bufferView": 5, "componentType": 5126, "count": 6, "max": [0.004154771566390991, 1.1321121454238892, 1.1562691926956177], "min": [-0.014084607362747192, -1.1445186138153076, -1.1524841785430908], "type": "VEC3"}, {"bufferView": 6, "componentType": 5126, "count": 6, "type": "VEC2"}, {"bufferView": 7, "componentType": 5126, "count": 6, "type": "VEC3"}, {"bufferView": 8, "componentType": 5126, "count": 6, "type": "VEC4"}, {"bufferView": 9, "componentType": 5126, "count": 325, "max": [0.04153698682785034, 1.1645004749298096, 1.157753348350525], "min": [-0.051368653774261475, -1.1594417095184326, -1.158166527748108], "type": "VEC3"}, {"bufferView": 10, "componentType": 5126, "count": 325, "type": "VEC2"}, {"bufferView": 11, "componentType": 5126, "count": 325, "type": "VEC3"}, {"bufferView": 12, "componentType": 5126, "count": 325, "type": "VEC4"}, {"bufferView": 13, "componentType": 5123, "count": 1464, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 72, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 72, "target": 34962}, {"buffer": 0, "byteLength": 72, "byteOffset": 120, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 192, "target": 34962}, {"buffer": 0, "byteLength": 12, "byteOffset": 288, "target": 34963}, {"buffer": 0, "byteLength": 72, "byteOffset": 300, "target": 34962}, {"buffer": 0, "byteLength": 48, "byteOffset": 372, "target": 34962}, {"buffer": 0, "byteLength": 72, "byteOffset": 420, "target": 34962}, {"buffer": 0, "byteLength": 96, "byteOffset": 492, "target": 34962}, {"buffer": 0, "byteLength": 3900, "byteOffset": 588, "target": 34962}, {"buffer": 0, "byteLength": 2600, "byteOffset": 4488, "target": 34962}, {"buffer": 0, "byteLength": 3900, "byteOffset": 7088, "target": 34962}, {"buffer": 0, "byteLength": 5200, "byteOffset": 10988, "target": 34962}, {"buffer": 0, "byteLength": 2928, "byteOffset": 16188, "target": 34963}], "buffers": [{"byteLength": 19116, "uri": "window.bin"}]}