AccumulationPass.hlsl -T cs -E main
DebugLines.hlsl -T vs -E main_vs -D DRAW_LINES_SHADERS=1
DebugLines.hlsl -T ps -E main_ps -D DRAW_LINES_SHADERS=1
ZoomTool.hlsl -T cs -E main
Shaders/ShaderDebug.hlsli -T vs -E main_vs -D DRAW_TRIANGLES_SHADERS=1
Shaders/ShaderDebug.hlsli -T ps -E main_ps -D DRAW_TRIANGLES_SHADERS=1
Shaders/ShaderDebug.hlsli -T vs -E main_vs -D DRAW_LINES_SHADERS=1
Shaders/ShaderDebug.hlsli -T ps -E main_ps -D DRAW_LINES_SHADERS=1
Shaders/ShaderDebug.hlsli -T ps -E main -D BLEND_DEBUG_BUFFER=1
ToneMapper/ToneMapping.hlsl -T ps -E main_ps
ToneMapper/ToneMapping.hlsl -T cs -E capture_cs
ToneMapper/luminance_ps.hlsl -T ps -E main
PostProcess.hlsl -T cs -E main -D STABLE_PLANES_DEBUG_VIZ=1
PostProcess.hlsl -T cs -E main -D DENOISER_PREPARE_INPUTS=1 -D USE_RELAX={0,1}
PostProcess.hlsl -T cs -E main -D DENOISER_FINAL_MERGE=1 -D USE_RELAX={0,1}
PostProcess.hlsl -T cs -E main -D DENOISER_PREPARE_INPUTS=1 -D DENOISER_DLSS_RR=1
PostProcess.hlsl -T cs -E main -D DUMMY_PLACEHOLDER_EFFECT=1
Lighting/Distant/EnvMapBaker.hlsl -T cs -E LowResPrePassLayerCS
Lighting/Distant/EnvMapBaker.hlsl -T cs -E BaseLayerCS
Lighting/Distant/EnvMapBaker.hlsl -T cs -E MIPReduceCS
Lighting/Distant/EnvMapImportanceSamplingBaker.hlsl -T cs -E BuildMIPDescentImportanceMapCS
Lighting/Distant/BC6UCompress.hlsl -T cs -E CSMain -D QUALITY=0
Lighting/Distant/BC6UCompress.hlsl -T cs -E CSMain -D QUALITY=1
Lighting/LightsBaker.hlsl -T cs -E ResetPastToCurrentHistory
Lighting/LightsBaker.hlsl -T cs -E EnvLightsBackupPast
Lighting/LightsBaker.hlsl -T cs -E EnvLightsSubdivideBase
Lighting/LightsBaker.hlsl -T cs -E EnvLightsSubdivideBoost
Lighting/LightsBaker.hlsl -T cs -E EnvLightsFillLookupMap
Lighting/LightsBaker.hlsl -T cs -E EnvLightsMapPastToCurrent
Lighting/LightsBaker.hlsl -T cs -E BakeEmissiveTriangles
Lighting/LightsBaker.hlsl -T cs -E ResetLightProxyCounters
Lighting/LightsBaker.hlsl -T cs -E ComputeWeights
Lighting/LightsBaker.hlsl -T cs -E ComputeProxyCounts
Lighting/LightsBaker.hlsl -T cs -E ComputeProxyBaselineOffsets
Lighting/LightsBaker.hlsl -T cs -E CreateProxyJobs
Lighting/LightsBaker.hlsl -T cs -E ExecuteProxyJobs
Lighting/LightsBaker.hlsl -T cs -E DebugDrawLights
Lighting/LightsBaker.hlsl -T cs -E ClearFeedbackHistory
Lighting/LightsBaker.hlsl -T cs -E UpdateFeedbackIndices
Lighting/LightsBaker.hlsl -T cs -E ProcessFeedbackHistoryP0
Lighting/LightsBaker.hlsl -T cs -E ProcessFeedbackHistoryP1
Lighting/LightsBaker.hlsl -T cs -E ProcessFeedbackHistoryP2
Lighting/LightsBaker.hlsl -T cs -E ProcessFeedbackHistoryP3a
Lighting/LightsBaker.hlsl -T cs -E ProcessFeedbackHistoryP3b
Lighting/LightsBaker.hlsl -T cs -E ProcessFeedbackHistoryP3c
Lighting/LightsBaker.hlsl -T cs -E ProcessFeedbackHistoryDebugViz
GPUSort/GPUSort.hlsl -T cs -E SetupIndirect
GPUSort/GPUSort.hlsl -T cs -E Count
GPUSort/GPUSort.hlsl -T cs -E CountIIFP -D RTXPT_GPUSORT_FIRST_PASS_INIT_INDICES=1
GPUSort/GPUSort.hlsl -T cs -E CountReduce
GPUSort/GPUSort.hlsl -T cs -E ScanPrefix
GPUSort/GPUSort.hlsl -T cs -E ScanAdd
GPUSort/GPUSort.hlsl -T cs -E Scatter
GPUSort/GPUSort.hlsl -T cs -E ScatterIIFP -D RTXPT_GPUSORT_FIRST_PASS_INIT_INDICES=1
GPUSort/GPUSort.hlsl -T cs -E Validate
ExportVisibilityBuffer.hlsl -T cs -E main
RTXDI/PrepareLights.hlsl -T cs -E main
RTXDI/PreprocessEnvironmentMap.hlsl -T cs -E main -D INPUT_ENVIRONMENT_MAP={0,1}
RTXDI/PresampleLights.hlsl -T cs -E main
RTXDI/PresampleEnvironmentMap.hlsl -T cs -E main
RTXDI/PresampleReGIR.hlsl -T cs -E main -D RTXDI_REGIR_MODE={RTXDI_REGIR_GRID,RTXDI_REGIR_ONION}
RTXDI/GenerateInitialSamples.hlsl -T cs -E main -D USE_RAY_QUERY=1 -D RTXDI_REGIR_MODE={RTXDI_REGIR_DISABLED,RTXDI_REGIR_GRID,RTXDI_REGIR_ONION}
RTXDI/GenerateInitialSamples.hlsl -T lib -D USE_RAY_QUERY=0 -D RTXDI_REGIR_MODE={RTXDI_REGIR_DISABLED,RTXDI_REGIR_GRID,RTXDI_REGIR_ONION}
RTXDI/TemporalResampling.hlsl -T cs -E main -D USE_RAY_QUERY=1
RTXDI/TemporalResampling.hlsl -T lib -D USE_RAY_QUERY=0
RTXDI/SpatialResampling.hlsl -T cs -E main -D USE_RAY_QUERY=1
RTXDI/SpatialResampling.hlsl -T lib -D USE_RAY_QUERY=0
RTXDI/DIFinalShading.hlsl -T cs -E main -D USE_RAY_QUERY=1
RTXDI/GITemporalResampling.hlsl -T cs -D USE_RAY_QUERY=1
RTXDI/GISpatialResampling.hlsl -T cs -D USE_RAY_QUERY=1
RTXDI/GIFinalShading.hlsl -T cs -D USE_RAY_QUERY=1
RTXDI/FusedDIGIFinalShading.hlsl -T cs -D USE_RAY_QUERY=1
