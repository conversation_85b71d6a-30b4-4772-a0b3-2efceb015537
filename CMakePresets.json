{"version": 2, "configurePresets": [{"name": "base", "displayName": "Basic Config", "description": "Basic build using Ninja generator", "generator": "Ninja", "hidden": true, "binaryDir": "${sourceDir}/out/build/${presetName}", "cacheVariables": {"CMAKE_INSTALL_PREFIX": "${sourceDir}/out/install/${presetName}"}}, {"name": "x64", "architecture": {"value": "x64", "strategy": "external"}, "hidden": true}, {"name": "x86", "architecture": {"value": "x86", "strategy": "external"}, "hidden": true}, {"name": "ARM64", "architecture": {"value": "arm64", "strategy": "external"}, "hidden": true}, {"name": "ARM64EC", "architecture": {"value": "arm64ec", "strategy": "external"}, "environment": {"CFLAGS": "/arm64EC", "CXXFLAGS": "/arm64EC"}, "hidden": true}, {"name": "Debug", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}, "hidden": true}, {"name": "Release", "cacheVariables": {"CMAKE_BUILD_TYPE": "RelWithDebInfo"}, "hidden": true}, {"name": "MSVC", "hidden": true, "cacheVariables": {"CMAKE_CXX_COMPILER": "cl.exe", "CMAKE_C_COMPILER": "cl.exe"}, "toolset": {"value": "host=x64", "strategy": "external"}}, {"name": "Clang", "hidden": true, "cacheVariables": {"CMAKE_CXX_COMPILER": "clang-cl.exe", "CMAKE_C_COMPILER": "clang-cl.exe"}, "toolset": {"value": "host=x64", "strategy": "external"}}, {"name": "Clang-X86", "environment": {"CFLAGS": "-m32", "CXXFLAGS": "-m32"}, "hidden": true}, {"name": "Clang-AArch64", "environment": {"CFLAGS": "--target=arm64-pc-windows-msvc", "CXXFLAGS": "--target=arm64-pc-windows-msvc"}, "hidden": true}, {"name": "GNUC", "hidden": true, "cacheVariables": {"CMAKE_CXX_COMPILER": "g++.exe", "CMAKE_C_COMPILER": "gcc.exe", "DXHEADERS_BUILD_TEST": false}, "toolset": {"value": "host=x64", "strategy": "external"}}, {"name": "Intel", "hidden": true, "cacheVariables": {"CMAKE_CXX_COMPILER": "icl.exe", "DXHEADERS_BUILD_GOOGLE_TEST": false, "DXHEADERS_BUILD_TEST": false}, "toolset": {"value": "host=x64", "strategy": "external"}}, {"name": "IntelLLVM", "hidden": true, "cacheVariables": {"CMAKE_CXX_COMPILER": "icx.exe", "DXHEADERS_BUILD_GOOGLE_TEST": false}, "toolset": {"value": "host=x64", "strategy": "external"}}, {"name": "UWP", "cacheVariables": {"CMAKE_SYSTEM_NAME": "WindowsStore", "CMAKE_SYSTEM_VERSION": "10.0"}, "hidden": true}, {"name": "NoTests", "hidden": true, "cacheVariables": {"DXHEADERS_BUILD_TEST": false, "DXHEADERS_BUILD_GOOGLE_TEST": false}}, {"name": "x64-Debug", "description": "MSVC for x64 (Debug)", "inherits": ["base", "x64", "Debug", "MSVC"]}, {"name": "x64-Release", "description": "MSVC for x64 (Release)", "inherits": ["base", "x64", "Release", "MSVC"]}, {"name": "x86-Debug", "description": "MSVC for x86 (Debug)", "inherits": ["base", "x86", "Debug", "MSVC"]}, {"name": "x86-Release", "description": "MSVC for x86 (Release)", "inherits": ["base", "x86", "Release", "MSVC"]}, {"name": "arm64-Debug", "description": "MSVC for ARM64 (Debug)", "inherits": ["base", "ARM64", "Debug", "MSVC"]}, {"name": "arm64-Release", "description": "MSVC for ARM64 (Release)", "inherits": ["base", "ARM64", "Release", "MSVC"]}, {"name": "arm64ec-Debug", "description": "MSVC for ARM64EC (Debug)", "inherits": ["base", "ARM64EC", "Debug", "MSVC"]}, {"name": "arm64ec-Release", "description": "MSVC for ARM64EC (Release)", "inherits": ["base", "ARM64EC", "Release", "MSVC"]}, {"name": "x64-Debug-UWP", "description": "MSVC for x64 (Debug) for UWP", "inherits": ["base", "x64", "Debug", "MSVC", "UWP"]}, {"name": "x64-Release-UWP", "description": "MSVC for x64 (Release) for UWP", "inherits": ["base", "x64", "Release", "MSVC", "UWP"]}, {"name": "x86-Debug-UWP", "description": "MSVC for x86 (Debug) for UWP", "inherits": ["base", "x86", "Debug", "MSVC", "UWP"]}, {"name": "x86-Release-UWP", "description": "MSVC for x86 (Release) for UWP", "inherits": ["base", "x86", "Release", "MSVC", "UWP"]}, {"name": "arm64-Debug-UWP", "description": "MSVC for ARM64 (Debug) for UWP", "inherits": ["base", "ARM64", "Debug", "MSVC", "UWP"]}, {"name": "arm64-Release-UWP", "description": "MSVC for ARM64 (Release) for UWP", "inherits": ["base", "ARM64", "Release", "MSVC", "UWP"]}, {"name": "x64-Debug-Clang", "description": "Clang/LLVM for x64 (Debug)", "inherits": ["base", "x64", "Debug", "Clang"]}, {"name": "x64-Release-Clang", "description": "Clang/LLVM for x64 (Release)", "inherits": ["base", "x64", "Release", "Clang"]}, {"name": "x86-Debug-Clang", "description": "Clang/LLVM for x86 (Debug)", "inherits": ["base", "x86", "Debug", "Clang", "Clang-X86"]}, {"name": "x86-Release-Clang", "description": "Clang/LLVM for x86 (Release)", "inherits": ["base", "x86", "Release", "Clang", "Clang-X86"]}, {"name": "arm64-Debug-Clang", "description": "Clang/LLVM for AArch64 (Debug)", "inherits": ["base", "ARM64", "Debug", "Clang", "Clang-AArch64"]}, {"name": "arm64-Release-Clang", "description": "Clang/LLVM for AArch64 (Release)", "inherits": ["base", "ARM64", "Release", "Clang", "Clang-AArch64"]}, {"name": "x64-Debug-UWP-Clang", "description": "Clang/LLVM for x64 (Debug) for UWP", "inherits": ["base", "x64", "Debug", "Clang", "UWP"]}, {"name": "x64-Release-UWP-Clang", "description": "Clang/LLVM for x64 (Release) for UWP", "inherits": ["base", "x64", "Release", "Clang", "UWP"]}, {"name": "x86-Debug-UWP-Clang", "description": "Clang/LLVM for x86 (Debug) for UWP", "inherits": ["base", "x86", "Debug", "Clang", "Clang-X86", "UWP"]}, {"name": "x86-Release-UWP-Clang", "description": "Clang/LLVM for x86 (Release) for UWP", "inherits": ["base", "x86", "Release", "Clang", "Clang-X86", "UWP"]}, {"name": "arm64-Debug-UWP-Clang", "description": "Clang/LLVM for AArch64 (Debug) for UWP", "inherits": ["base", "ARM64", "Debug", "Clang", "Clang-AArch64", "UWP"]}, {"name": "arm64-Release-UWP-Clang", "description": "Clang/LLVM for AArch64 (Release) for UWP", "inherits": ["base", "ARM64", "Release", "Clang", "Clang-AArch64", "UWP"]}, {"name": "x64-Debug-ICC", "description": "Intel Classic Compiler (Debug)", "inherits": ["base", "x64", "Debug", "Intel"]}, {"name": "x64-Release-ICC", "description": "Intel Classic Compiler (Release)", "inherits": ["base", "x64", "Release", "Intel"]}, {"name": "x86-Debug-ICC", "description": "Intel Classic Compiler (Debug)", "inherits": ["base", "x86", "Debug", "Intel"]}, {"name": "x86-Release-ICC", "description": "Intel Classic Compiler (Release)", "inherits": ["base", "x86", "Release", "Intel"]}, {"name": "x64-Debug-ICX", "description": "Intel oneAPI Compiler (Debug)", "inherits": ["base", "x64", "Debug", "IntelLLVM"]}, {"name": "x64-Release-ICX", "description": "Intel oneAPI Compiler (Release)", "inherits": ["base", "x64", "Release", "IntelLLVM"]}, {"name": "x86-Debug-ICX", "description": "Intel oneAPI Compiler (Debug)", "inherits": ["base", "x86", "Debug", "IntelLLVM"]}, {"name": "x86-Release-ICX", "description": "Intel oneAPI Compiler (Release)", "inherits": ["base", "x86", "Release", "IntelLLVM"]}, {"name": "x64-Debug-MinGW", "description": "MinG-W64 (Debug)", "inherits": ["base", "x64", "Debug", "GNUC"], "environment": {"PATH": "$penv{PATH};c:/mingw64/bin"}}, {"name": "x64-Release-MinGW", "description": "MinG-W64 (Release)", "inherits": ["base", "x64", "Release", "GNUC"], "environment": {"PATH": "$penv{PATH};c:/mingw64/bin"}}, {"name": "x86-Debug-MinGW", "description": "MinG-W32 (Debug)", "inherits": ["base", "x86", "Debug", "GNUC"], "environment": {"PATH": "$penv{PATH};c:/mingw32/bin"}}, {"name": "x86-Release-MinGW", "description": "MinG-W32 (Release)", "inherits": ["base", "x86", "Release", "GNUC"], "environment": {"PATH": "$penv{PATH};c:/mingw32/bin"}}, {"name": "x64-Debug-WSL", "description": "WSL x64 (Debug) for Windows 11", "inherits": ["base", "x64", "Debug"]}, {"name": "x64-Release-WSL", "description": "WSL x64 (Release) for Windows 11", "inherits": ["base", "x64", "Release"]}, {"name": "arm64-Debug-WSL", "description": "WSL AArch64 (Debug) for Windows 11", "inherits": ["base", "ARM64", "Debug"]}, {"name": "arm64-Release-WSL", "description": "WSL AArch64 (Release) for Windows 11", "inherits": ["base", "ARM64", "Release"]}, {"name": "x64-Debug-Linux", "description": "Linux x64 (Debug)", "inherits": ["base", "x64", "Debug", "NoTests"]}, {"name": "x64-Release-Linux", "description": "Linux x64 (Release)", "inherits": ["base", "x64", "Release", "NoTests"]}, {"name": "arm64-Debug-Linux", "description": "Linux AArch64 (Debug)", "inherits": ["base", "ARM64", "Debug", "NoTests"]}, {"name": "arm64-Release-Linux", "description": "Linux AArch64 (Release)", "inherits": ["base", "ARM64", "Release", "NoTests"]}], "testPresets": [{"name": "x64-Debug", "configurePreset": "x64-Debug"}, {"name": "x64-Release", "configurePreset": "x64-Release"}, {"name": "x86-Debug", "configurePreset": "x86-Debug"}, {"name": "x86-Release", "configurePreset": "x86-Release"}, {"name": "arm64-Debug", "configurePreset": "arm64-Debug"}, {"name": "arm64-Release", "configurePreset": "arm64-Release"}, {"name": "arm64ec-Debug", "configurePreset": "arm64ec-Debug"}, {"name": "arm64ec-Release", "configurePreset": "arm64ec-Release"}]}